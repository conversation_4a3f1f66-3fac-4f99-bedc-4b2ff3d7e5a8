{{> partials/main }}

<head>

    {{> partials/title-meta title="Input Spin" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}

{{> partials/page-heading title="Input Spin" sub-title="Form" }}

<div class="grid grid-cols-12">
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Basic</h6>
        </div>
        <div class="card-body">
            <div class="flex flex-wrap gap-3">
                <div x-data="{ count: 1 }">
                    <div class="input-spin-group input-spin-primary">
                        <button @click="count > 0 && count--" class="input-spin-minus"><i class="size-4" data-lucide="minus"></i></button>
                        <input type="text" x-model="count" class="input-spin form-input" readonly>
                        <button @click="count++" class="input-spin-plus"><i class="size-4" data-lucide="plus"></i></button>
                    </div>
                </div>
                <div x-data="{ count: 1 }">
                    <div class="input-spin-group input-spin-purple">
                        <button @click="count > 0 && count--" class="input-spin-minus"><i class="size-4" data-lucide="minus"></i></button>
                        <input type="text" x-model="count" class="input-spin form-input" readonly>
                        <button @click="count++" class="input-spin-plus"><i class="size-4" data-lucide="plus"></i></button>
                    </div>
                </div>
                <div x-data="{ count: 1 }">
                    <div class="input-spin-group input-spin-green">
                        <button @click="count > 0 && count--" class="input-spin-minus"><i class="size-4" data-lucide="minus"></i></button>
                        <input type="text" x-model="count" class="input-spin form-input" readonly>
                        <button @click="count++" class="input-spin-plus"><i class="size-4" data-lucide="plus"></i></button>
                    </div>
                </div>
                <div x-data="{ count: 1 }">
                    <div class="input-spin-group input-spin-red">
                        <button @click="count > 0 && count--" class="input-spin-minus"><i class="size-4" data-lucide="minus"></i></button>
                        <input type="text" x-model="count" class="input-spin form-input" readonly>
                        <button @click="count++" class="input-spin-plus"><i class="size-4" data-lucide="plus"></i></button>
                    </div>
                </div>
                <div x-data="{ count: 1 }">
                    <div class="input-spin-group input-spin-yellow">
                        <button @click="count > 0 && count--" class="input-spin-minus"><i class="size-4" data-lucide="minus"></i></button>
                        <input type="text" x-model="count" class="input-spin form-input" readonly>
                        <button @click="count++" class="input-spin-plus"><i class="size-4" data-lucide="plus"></i></button>
                    </div>
                </div>
                <div x-data="{ count: 1 }">
                    <div class="input-spin-group input-spin-sky">
                        <button @click="count > 0 && count--" class="input-spin-minus"><i class="size-4" data-lucide="minus"></i></button>
                        <input type="text" x-model="count" class="input-spin form-input" readonly>
                        <button @click="count++" class="input-spin-plus"><i class="size-4" data-lucide="plus"></i></button>
                    </div>
                </div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Solid Example</h6>
        </div>
        <div class="card-body">
            <div class="flex flex-wrap gap-3">
                <div x-data="{ count: 1 }">
                    <div class="input-spin-group input-spin-solid-primary">
                        <button @click="count > 0 && count--" class="input-spin-minus"><i class="size-4" data-lucide="minus"></i></button>
                        <input type="text" x-model="count" class="input-spin form-input" readonly>
                        <button @click="count++" class="input-spin-plus"><i class="size-4" data-lucide="plus"></i></button>
                    </div>
                </div>
                <div x-data="{ count: 1 }">
                    <div class="input-spin-group input-spin-solid-purple">
                        <button @click="count > 0 && count--" class="input-spin-minus"><i class="size-4" data-lucide="minus"></i></button>
                        <input type="text" x-model="count" class="input-spin form-input" readonly>
                        <button @click="count++" class="input-spin-plus"><i class="size-4" data-lucide="plus"></i></button>
                    </div>
                </div>
                <div x-data="{ count: 1 }">
                    <div class="input-spin-group input-spin-solid-green">
                        <button @click="count > 0 && count--" class="input-spin-minus"><i class="size-4" data-lucide="minus"></i></button>
                        <input type="text" x-model="count" class="input-spin form-input" readonly>
                        <button @click="count++" class="input-spin-plus"><i class="size-4" data-lucide="plus"></i></button>
                    </div>
                </div>
                <div x-data="{ count: 23 }">
                    <div class="input-spin-group input-spin-solid-orange">
                        <button @click="count > 0 && count--" class="input-spin-minus"><i class="size-4" data-lucide="minus"></i></button>
                        <input type="text" x-model="count" class="input-spin form-input" readonly>
                        <button @click="count++" class="input-spin-plus"><i class="size-4" data-lucide="plus"></i></button>
                    </div>
                </div>
                <div x-data="{ count: 10 }">
                    <div class="input-spin-group input-spin-solid-yellow">
                        <button @click="count > 0 && count--" class="input-spin-minus"><i class="size-4" data-lucide="minus"></i></button>
                        <input type="text" x-model="count" class="input-spin form-input" readonly>
                        <button @click="count++" class="input-spin-plus"><i class="size-4" data-lucide="plus"></i></button>
                    </div>
                </div>
                <div x-data="{ count: 5 }">
                    <div class="input-spin-group input-spin-solid-sky">
                        <button @click="count > 0 && count--" class="input-spin-minus"><i class="size-4" data-lucide="minus"></i></button>
                        <input type="text" x-model="count" class="input-spin form-input" readonly>
                        <button @click="count++" class="input-spin-plus"><i class="size-4" data-lucide="plus"></i></button>
                    </div>
                </div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Boxed Example</h6>
        </div>
        <div class="card-body">
            <div class="flex flex-wrap gap-3">
                <div x-data="{ count: 1 }">
                    <div class="flex items-center w-32 p-1 text-center border border-gray-200 rounded-md dark:border-dark-800">
                        <button @click="count > 0 && count--" class="flex items-center justify-center text-gray-500 transition duration-200 ease-linear dark:text-dark-500 minus size-8 shrink-0 hover:text-primary-500 dark:hover:text-primary-500"><i class="size-4" data-lucide="minus"></i></button>
                        <input type="text" x-model="count" class="h-8 p-0 text-center border-0 rounded-none form-input" readonly>
                        <button @click="count++" class="flex items-center justify-center text-gray-500 transition duration-200 ease-linear dark:text-dark-500 plus size-8 shrink-0 hover:text-primary-500 dark:hover:text-primary-500"><i class="size-4" data-lucide="plus"></i></button>
                    </div>
                </div>
                <div x-data="{ count: 1 }">
                    <div class="flex items-center w-32 p-1 text-center border border-gray-200 rounded-md dark:border-dark-800">
                        <button @click="count > 0 && count--" class="flex items-center justify-center transition duration-200 ease-linear rounded-md text-primary-500 minus size-8 shrink-0 bg-primary-500/20 hover:text-primary-700"><i class="size-4" data-lucide="minus"></i></button>
                        <input type="text" x-model="count" class="h-8 p-0 text-center border-0 rounded-none form-input" readonly>
                        <button @click="count++" class="flex items-center justify-center transition duration-200 ease-linear rounded-md text-primary-500 plus size-8 shrink-0 bg-primary-500/20 hover:text-primary-700"><i class="size-4" data-lucide="plus"></i></button>
                    </div>
                </div>
                <div x-data="{ count: 1 }">
                    <div class="flex items-center w-32 p-1 text-center border border-gray-200 rounded-md dark:border-dark-800">
                        <button @click="count > 0 && count--" class="flex items-center justify-center text-purple-500 transition duration-200 ease-linear rounded-md bg-purple-500/20 minus size-8 shrink-0 hover:text-purple-700"><i class="size-4" data-lucide="minus"></i></button>
                        <input type="text" x-model="count" class="h-8 p-0 text-center border-0 rounded-none form-input" readonly>
                        <button @click="count++" class="flex items-center justify-center text-purple-500 transition duration-200 ease-linear rounded-md bg-purple-500/20 plus size-8 shrink-0 hover:text-purple-700"><i class="size-4" data-lucide="plus"></i></button>
                    </div>
                </div>
                <div x-data="{ count: 1 }">
                    <div class="flex items-center w-32 p-1 text-center border border-gray-200 rounded-md dark:border-dark-800">
                        <button @click="count > 0 && count--" class="flex items-center justify-center text-green-200 transition duration-200 ease-linear bg-green-500 rounded-md minus size-8 shrink-0 hover:text-white"><i class="size-4" data-lucide="minus"></i></button>
                        <input type="text" x-model="count" class="h-8 p-0 text-center border-0 rounded-none form-input" readonly>
                        <button @click="count++" class="flex items-center justify-center text-green-200 transition duration-200 ease-linear bg-green-500 rounded-md plus size-8 shrink-0 hover:text-white"><i class="size-4" data-lucide="plus"></i></button>
                    </div>
                </div>
                <div x-data="{ count: 1 }">
                    <div class="flex items-center w-32 p-1 text-center border border-gray-200 rounded-md dark:border-dark-800">
                        <button @click="count > 0 && count--" class="flex items-center justify-center text-pink-200 transition duration-200 ease-linear bg-pink-500 rounded-md minus size-8 shrink-0 hover:text-white"><i class="size-4" data-lucide="minus"></i></button>
                        <input type="text" x-model="count" class="h-8 p-0 text-center border-0 rounded-none form-input" readonly>
                        <button @click="count++" class="flex items-center justify-center text-pink-200 transition duration-200 ease-linear bg-pink-500 rounded-md plus size-8 shrink-0 hover:text-white"><i class="size-4" data-lucide="plus"></i></button>
                    </div>
                </div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Skin Example</h6>
        </div>
        <div class="card-body">
            <div class="flex flex-wrap gap-3">
                <div x-data="{ count: 10 }">
                    <div class="flex items-center w-20 p-1 text-center border border-gray-200 rounded-md dark:border-dark-800">
                        <div class="flex flex-col">
                            <button @click="count++" class="flex items-center justify-center text-gray-500 transition duration-200 ease-linear dark:text-dark-500 plus hover:text-primary-500 dark:hover:text-primary-500"><i class="size-4" data-lucide="chevron-up"></i></button>
                            <button @click="count > 0 && count--" class="flex items-center justify-center text-gray-500 transition duration-200 ease-linear dark:text-dark-500 minus hover:text-primary-500 dark:hover:text-primary-500"><i class="size-4" data-lucide="chevron-down"></i></button>
                        </div>
                        <input type="text" x-model="count" class="h-8 p-0 text-center border-0 rounded-none form-input" readonly>
                    </div>
                </div>
                <div x-data="{ count: 20 }">
                    <div class="flex items-center w-20 p-1 text-center border border-gray-200 rounded-md dark:border-dark-800">
                        <input type="text" x-model="count" class="h-8 p-0 text-center border-0 rounded-none form-input" readonly>
                        <div class="flex flex-col">
                            <button @click="count++" class="flex items-center justify-center text-gray-500 transition duration-200 ease-linear dark:text-dark-500 plus hover:text-primary-500 dark:hover:text-primary-500"><i class="size-4" data-lucide="chevron-up"></i></button>
                            <button @click="count > 0 && count--" class="flex items-center justify-center text-gray-500 transition duration-200 ease-linear dark:text-dark-500 minus hover:text-primary-500 dark:hover:text-primary-500"><i class="size-4" data-lucide="chevron-down"></i></button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div><!--end col-->
</div><!--end grid-->

</div>
{{> partials/footer }}
</div>


{{> partials/vendor-scripts }}

<script type="module" src="assets/js/main.js"></script>

</body>
</html>