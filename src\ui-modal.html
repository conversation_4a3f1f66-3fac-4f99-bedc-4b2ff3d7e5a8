{{> partials/main }}

<head>

    {{> partials/title-meta title="Modal" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}

{{> partials/page-heading title="Modal" sub-title="UI" }}

<div class="grid grid-cols-1 gap-x-space">
    <div class="card">
        <div class="card-header">
            <h6 class="card-title">Basic</h6>
        </div>
        <div class="card-body">
            <button data-modal-target="defaultModal" type="button" class="btn btn-primary">Default Modal</button>
            <div id="defaultModal" class="!hidden modal show">
                <div class="modal-wrap modal-center">
                    <div class="modal-header">
                        <h6>Modal Heading</h6>
                        <button data-modal-close="defaultModal" class="link link-red"><i data-lucide="x" class="size-5"></i></button>
                    </div>
                    <div class="modal-content">
                        <h6 class="mb-3">Modal Content</h6>
                        <p class="text-gray-500 dark:text-dark-500">They all have something to say beyond the words on the page. They can come across as casual or neutral, exotic or graphic.</p>
                    </div>
                    <div class="modal-footer">
                        <h6>Modal Footer</h6>
                    </div>
                </div>
            </div>
        </div>
    </div><!--end -->
    <div class="card">
        <div class="card-header">
            <h6 class="card-title">Modal Position</h6>
        </div>
        <div class="card-body">
            <div class="flex flex-wrap gap-2">
                <button data-modal-target="centerModal" type="button" class="btn btn-sub-gray">Center Modal</button>
                <button data-modal-target="topModal" type="button" class="btn btn-sub-gray">Top Modal</button>
                <button data-modal-target="topLeftModal" type="button" class="btn btn-sub-gray">Top Left Modal</button>
                <button data-modal-target="topRightModal" type="button" class="btn btn-sub-gray">Top Right Modal</button>
                <button data-modal-target="rightBottomModal" type="button" class="btn btn-sub-gray">Bottom Right Modal</button>
                <button data-modal-target="leftBottomModal" type="button" class="btn btn-sub-gray">Bottom Left Modal</button>
            </div>
            <div id="centerModal" class="!hidden modal show">
                <div class="modal-wrap modal-center">
                    <div class="modal-header">
                        <h6>Center Modal</h6>
                        <button data-modal-close="centerModal" class="link link-red"><i data-lucide="x" class="size-5"></i></button>
                    </div>
                    <div class="modal-content">
                        <h6 class="mb-3">Modal Content</h6>
                        <p class="text-gray-500 dark:text-dark-500">They all have something to say beyond the words on the page. They can come across as casual or neutral, exotic or graphic.</p>
                    </div>
                    <div class="modal-footer">
                        <h6>Modal Footer</h6>
                    </div>
                </div>
            </div><!--end-->

            <div id="topModal" class="!hidden modal show">
                <div class="modal-wrap modal-top">
                    <div class="modal-header">
                        <h6>Top Modal</h6>
                        <button data-modal-close="topModal" class="link link-red"><i data-lucide="x" class="size-5"></i></button>
                    </div>
                    <div class="modal-content">
                        <h6 class="mb-3">Modal Content</h6>
                        <p class="text-gray-500 dark:text-dark-500">They all have something to say beyond the words on the page. They can come across as casual or neutral, exotic or graphic.</p>
                    </div>
                    <div class="modal-footer">
                        <h6>Modal Footer</h6>
                    </div>
                </div>
            </div><!--end-->

            <div id="topLeftModal" class="!hidden modal show">
                <div class="modal-wrap modal-tl">
                    <div class="modal-header">
                        <h6>Top Modal</h6>
                        <button data-modal-close="topLeftModal" class="link link-red"><i data-lucide="x" class="size-5"></i></button>
                    </div>
                    <div class="modal-content">
                        <h6 class="mb-3">Modal Content</h6>
                        <p class="text-gray-500 dark:text-dark-500">They all have something to say beyond the words on the page. They can come across as casual or neutral, exotic or graphic.</p>
                    </div>
                    <div class="modal-footer">
                        <h6>Modal Footer</h6>
                    </div>
                </div>
            </div><!--end-->

            <div id="topRightModal" class="!hidden modal show">
                <div class="modal-wrap modal-tr">
                    <div class="modal-header">
                        <h6>Top Modal</h6>
                        <button data-modal-close="topRightModal" class="link link-red"><i data-lucide="x" class="size-5"></i></button>
                    </div>
                    <div class="modal-content">
                        <h6 class="mb-3">Modal Content</h6>
                        <p class="text-gray-500 dark:text-dark-500">They all have something to say beyond the words on the page. They can come across as casual or neutral, exotic or graphic.</p>
                    </div>
                    <div class="modal-footer">
                        <h6>Modal Footer</h6>
                    </div>
                </div>
            </div><!--end-->

            <div id="rightBottomModal" class="!hidden modal show">
                <div class="modal-wrap modal-br">
                    <div class="modal-header">
                        <h6>Top Modal</h6>
                        <button data-modal-close="rightBottomModal" class="link link-red"><i data-lucide="x" class="size-5"></i></button>
                    </div>
                    <div class="modal-content">
                        <h6 class="mb-3">Modal Content</h6>
                        <p class="text-gray-500 dark:text-dark-500">They all have something to say beyond the words on the page. They can come across as casual or neutral, exotic or graphic.</p>
                    </div>
                    <div class="modal-footer">
                        <h6>Modal Footer</h6>
                    </div>
                </div>
            </div><!--end-->

            <div id="leftBottomModal" class="!hidden modal show">
                <div class="modal-wrap modal-bl">
                    <div class="modal-header">
                        <h6>Top Modal</h6>
                        <button data-modal-close="leftBottomModal" class="link link-red"><i data-lucide="x" class="size-5"></i></button>
                    </div>
                    <div class="modal-content">
                        <h6 class="mb-3">Modal Content</h6>
                        <p class="text-gray-500 dark:text-dark-500">They all have something to say beyond the words on the page. They can come across as casual or neutral, exotic or graphic.</p>
                    </div>
                    <div class="modal-footer">
                        <h6>Modal Footer</h6>
                    </div>
                </div>
            </div><!--end-->
        </div>
    </div><!--end -->
    <div class="card">
        <div class="card-header">
            <h6 class="card-title">Size Modal</h6>
        </div>
        <div class="card-body">
            <div class="flex flex-wrap items-center gap-2">
                <button data-modal-target="extraSmallModal" type="button" class="btn btn-sub-gray">Extra small</button>
                <button data-modal-target="smallModal" type="button" class="btn btn-sub-gray">Small</button>
                <button data-modal-target="largeModal" type="button" class="btn btn-sub-gray">Large</button>
                <button data-modal-target="extraLargeModal" type="button" class="btn btn-sub-gray">Extra Large</button>
                <button data-modal-target="extraExtraLargeModal" type="button" class="btn btn-sub-gray">Extra Extra Large</button>
            </div>
            <div id="extraSmallModal" class="!hidden modal show">
                <div class="modal-wrap modal-xs modal-center">
                    <div class="modal-header">
                        <h6>Extra Small Modal</h6>
                        <button data-modal-close="extraSmallModal" class="link link-red"><i data-lucide="x" class="size-5"></i></button>
                    </div>
                    <div class="modal-content">
                        <h6 class="mb-2">Modal Content</h6>
                        <p class="text-gray-500 dark:text-dark-500">They all have something to say beyond the words on the page. They can come across as casual or neutral, exotic or graphic.</p>
                    </div>
                    <div class="modal-footer">
                        <h6>Modal Footer</h6>
                    </div>
                </div>
            </div><!--end modal-->

            <div id="smallModal" class="!hidden modal show">
                <div class="modal-wrap modal-sm modal-center">
                    <div class="modal-header">
                        <h6>Small Modal</h6>
                        <button data-modal-close="smallModal" class="link link-red"><i data-lucide="x" class="size-5"></i></button>
                    </div>
                    <div class="modal-content">
                        <h6 class="mb-2">Modal Content</h6>
                        <p class="text-gray-500 dark:text-dark-500">They all have something to say beyond the words on the page. They can come across as casual or neutral, exotic or graphic.</p>
                    </div>
                    <div class="modal-footer">
                        <h6>Modal Footer</h6>
                    </div>
                </div>
            </div><!--end modal-->

            <div id="largeModal" class="!hidden modal show">
                <div class="modal-wrap modal-lg modal-center">
                    <div class="modal-header">
                        <h6>Large Modal</h6>
                        <button data-modal-close="largeModal" class="link link-red"><i data-lucide="x" class="size-5"></i></button>
                    </div>
                    <div class="modal-content">
                        <h6 class="mb-2">Modal Content</h6>
                        <p class="text-gray-500 dark:text-dark-500">They all have something to say beyond the words on the page. They can come across as casual or neutral, exotic or graphic.</p>
                    </div>
                    <div class="modal-footer">
                        <h6>Modal Footer</h6>
                    </div>
                </div>
            </div><!--end modal-->

            <div id="extraLargeModal" class="!hidden modal show">
                <div class="modal-wrap modal-xl modal-center">
                    <div class="modal-header">
                        <h6>Extra Large Modal</h6>
                        <button data-modal-close="extraLargeModal" class="link link-red"><i data-lucide="x" class="size-5"></i></button>
                    </div>
                    <div class="modal-content">
                        <h6 class="mb-2">Modal Content</h6>
                        <p class="text-gray-500 dark:text-dark-500">They all have something to say beyond the words on the page. They can come across as casual or neutral, exotic or graphic.</p>
                    </div>
                    <div class="modal-footer">
                        <h6>Modal Footer</h6>
                    </div>
                </div>
            </div><!--end modal-->

            <div id="extraExtraLargeModal" class="!hidden modal show" data-aos="flip-right">
                <div class="modal-wrap modal-2xl modal-center">
                    <div class="modal-header">
                        <h6>Extra Large Modal</h6>
                        <button data-modal-close="extraExtraLargeModal" class="link link-red"><i data-lucide="x" class="size-5"></i></button>
                    </div>
                    <div class="modal-content">
                        <h6 class="mb-2">Modal Content</h6>
                        <p class="text-gray-500 dark:text-dark-500">They all have something to say beyond the words on the page. They can come across as casual or neutral, exotic or graphic.</p>
                    </div>
                    <div class="modal-footer">
                        <h6>Modal Footer</h6>
                    </div>
                </div>
            </div><!--end modal-->

        </div>
    </div><!--end -->
</div><!--end grid-->

</div>
{{> partials/footer }}
</div>


{{> partials/vendor-scripts }}
<script type="module" src="assets/js/ui/advanced-animation.init.js"></script>
<script type="module" src="assets/js/main.js"></script>

</body>
</html>