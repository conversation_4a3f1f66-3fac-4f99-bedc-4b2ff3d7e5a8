{{> partials/main }}

<head>
    {{> partials/title-meta title="Cashier Interface (POS)" }}
    <link rel="stylesheet" href="/assets/libs/virtual-select-plugin/virtual-select.min.css">
    {{> partials/head-css }}
</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}

<div class="grid grid-cols-12 gap-5" x-data="posSystem">
    <div class="col-span-9">
        <div class="flex items-center gap-3 mb-1">
            <h6>Categories</h6>
        </div>
        <div class="swiper mySwiper group/swiper">
            <div class="swiper-wrapper py-4" id="category-wrapper">
                <template x-for="category in categories" :key="category.id">
                    <div class="swiper-slide" :data-category="category.id">
                        <div class="bg-green-500/10 p-5 border border-green-500/20 rounded-md text-center shadow-lg shadow-gray-100 dark:shadow-dark-850 cursor-pointer" :class="{ 'active': currentCategory === category.id }" @click="selectCategory(category.id)">
                            <img :src="category.icon" :alt="category.name" class="size-10 mx-auto outline outline-dashed outline-green-500/50 outline-offset-4 rounded-full">
                            <h6 class="mt-4" x-text="category.name"></h6>
                            <p class="text-gray-500 dark:text-dark-500 text-13 mt-1" x-text="'Items ' + category.itemCount"></p>
                        </div>
                    </div>
                </template>
            </div>
            <div class="swiper-button-next after:font-remix after:text-2xl after:text-primary-500 size-6 bg-white rounded-full opacity-0 group-hover/swiper:opacity-100 transition ease-linear duration-300 after:content-['\ea6e']"></div>
            <div class="swiper-button-prev after:font-remix after:text-2xl after:text-primary-500 size-6 bg-white rounded-full opacity-0 group-hover/swiper:opacity-100 transition ease-linear duration-300 after:content-['\ea64']"></div>
        </div>

        <h6 class="mb-3">Select Menu</h6>

        <div class="grid grid-cols-3 gap-x-5" id="products-grid">
            <template x-for="product in filteredProducts" :key="product.id">
                <div class="card relative" :data-product-category="product.category">
                    <div class="card-body p-3">
                        <div x-show="product.available === 0" class="absolute left-0 top-3 flex items-center justify-center bg-red-500 text-white px-3 py-1 text-13 rounded-r-md z-10 text-sm font-medium">
                            <span>Out of Stock</span>
                        </div>
                        <div class="flex gap-5">
                            <div class="bg-gray-100 shrink-0 rounded-md dark:bg-dark-850 relative">
                                <img :src="product.image" :alt="product.name" class="h-28 shrink-0">
                            </div>
                            <div class="grow flex flex-col">
                                <h6 class="mb-2 line-clamp-2 capitalize" x-text="product.name"></h6>
                                <div class="flex gap-2 items-center mb-auto">
                                    <p class="text-gray-500 dark:text-dark-500 text-13" x-text="product.available + ' Available'"></p>
                                    <p class="text-gray-500 dark:text-dark-500 text-13" x-text="product.sold + ' Sold'">
                                    </p>
                                </div>
                                <h5 x-text="'$' + product.price"></h5>
                            </div>
                        </div>

                        <div x-data="{ 
                            quantity: 1,
                            selectedSize: 'm',
                            selectedSugarLevel: 'low'
                        }">
                            <div class="flex items-center gap-5">
                                <!-- Size options for products that have sizes -->
                                <template x-if="product.hasSize && product.available > 0">
                                    <div class="flex gap-6">
                                        <div class="shrink-0">
                                            <p class="mt-4 text-gray-500 dark:text-dark-500 text-14">Size</p>
                                        <div class="flex items-center gap-3 mt-3 font-medium shrink-0 *:size-8 *:bg-gray-100 dark:*:bg-dark-850 *:inline-flex *:items-center *:justify-center *:rounded-full *:outline *:outline-dashed *:outline-offset-2 *:outline-gray-200 *:dark:outline-dark-800">
                                                <a href="#!" class="text-gray-500 dark:text-dark-500 [&.active]:bg-primary-500 [&.active]:text-white [&.active]:outline-primary-500/75" :class="{ 'active': selectedSize === 's' }" @click.prevent="selectedSize = 's'">S</a>
                                                <a href="#!" class="text-gray-500 dark:text-dark-500 [&.active]:bg-primary-500 [&.active]:text-white [&.active]:outline-primary-500/75" :class="{ 'active': selectedSize === 'm' }" @click.prevent="selectedSize = 'm'">M</a>
                                                <a href="#!" class="text-gray-500 dark:text-dark-500 [&.active]:bg-primary-500 [&.active]:text-white [&.active]:outline-primary-500/75" :class="{ 'active': selectedSize === 'l' }" @click.prevent="selectedSize = 'l'">L</a>
                                            </div>
                                        </div>
                                    </div>
                                </template>

                                <!-- Sugar level options for products that have sugar levels -->
                                <template x-if="product.hasSugarLevel && product.available > 0">
                                    <div class="shrink-0">
                                        <p class="mt-4 text-gray-500 dark:text-dark-500 text-14">Sugar Level</p>
                                        <div class="flex gap-3 mt-3 text-gray-500 dark:text-dark-500">
                                            <label class="shrink-0">
                                                <input type="radio" name="sugar" class="hidden peer" value="low" x-model="selectedSugarLevel">
                                                <div class="flex items-center justify-center size-8 outline outline-offset-2 outline-dashed outline-gray-200 dark:outline-dark-800 bg-gray-100 dark:bg-dark-850 rounded-md cursor-pointer text-center transition-all 
                                               peer-checked:outline-green-500/50 peer-checked:bg-green-500/15 peer-checked:text-green-500">
                                                    <span class="block font-medium">L</span>
                                                </div>
                                            </label>
                                            <label class="shrink-0">
                                                <input type="radio" name="sugar" class="hidden peer" value="medium" x-model="selectedSugarLevel">
                                                <div class="flex items-center justify-center size-8 outline outline-offset-2 outline-dashed outline-gray-200 dark:outline-dark-800 bg-gray-100 dark:bg-dark-850 rounded-md cursor-pointer text-center transition-all 
                                    peer-checked:outline-yellow-500/50 peer-checked:bg-yellow-500/15 peer-checked:text-yellow-500">
                                                    <span class="block font-medium">M</span>
                                                </div>
                                            </label>
                                            <label class="shrink-0">
                                                <input type="radio" name="sugar" class="hidden peer" value="high" x-model="selectedSugarLevel">
                                                <div class="flex items-center justify-center size-8 outline outline-offset-2 outline-dashed outline-gray-200 dark:outline-dark-800 bg-gray-100 dark:bg-dark-850 rounded-md cursor-pointer text-center transition-all
                                    peer-checked:outline-red-500/50 peer-checked:bg-red-500/15 peer-checked:text-red-500">
                                                    <span class="block font-medium">H</span>
                                                </div>
                                            </label>
                                        </div>
                                    </div>
                                </template>
                            </div>

                            <template x-if="product.available > 0">
                                <div>
                                    <p class="mt-5 text-gray-500 dark:text-dark-500 text-14">Quantity</p>
                                    <div class="mt-2">
                                        <div class="flex items-center w-32 p-1 text-center border border-gray-200 rounded-md dark:border-dark-800">
                                            <button @click="quantity > 1 && quantity--" class="flex items-center justify-center transition duration-200 ease-linear rounded-md text-primary-500 minus size-8 shrink-0 bg-primary-500/20 hover:text-primary-700">
                                                <i class="size-4" data-lucide="minus"></i>
                                            </button>
                                            <input type="text" x-model="quantity" class="h-8 p-0 text-center border-0 rounded-none form-input" readonly>
                                            <button @click="quantity++" class="flex items-center justify-center transition duration-200 ease-linear rounded-md text-primary-500 plus size-8 shrink-0 bg-primary-500/20 hover:text-primary-700">
                                                <i class="size-4" data-lucide="plus"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="mt-4">
                                        <button type="button" class="btn btn-sub-red w-full" @click="addItemToCart(product, quantity, { 
                                                size: product.hasSize ? selectedSize : null,
                                                sugarLevel: product.hasSugarLevel ? selectedSugarLevel : null
                                            })">
                                            Add To Cart
                                        </button>
                                    </div>
                                </div>
                            </template>
                        </div>
                    </div>
                </div>
            </template>
        </div>
    </div>

    <div class="col-span-3">
        <div class="card sticky top-[90px]" x-data="{ 
            openTab: 1,
            activeClasses: 'bg-gray-100 text-gray-800 dark:bg-dark-850 dark:text-dark-50',
            inactiveClasses: 'hover:text-primary-500'
        }">
            <div class="card-header">
                <h6 class="mb-3">Order ID: #SRB1542</h6>
                <ul class="flex p-1 border border-gray-200 dark:border-dark-800 rounded-md *:grow">
                    <li @click="openTab = 1">
                        <button type="button" :class="openTab === 1 ? activeClasses : inactiveClasses" class="relative block px-3 py-1 w-full rounded-md link text-13">
                            Dine IN
                        </button>
                    </li>
                    <li @click="openTab = 2">
                        <button type="button" :class="openTab === 2 ? activeClasses : inactiveClasses" class="relative block px-3 py-1 w-full rounded-md link text-13">
                            Takeaway
                        </button>
                    </li>
                    <li @click="openTab = 3">
                        <button type="button" :class="openTab === 3 ? activeClasses : inactiveClasses" class="relative block px-3 py-1 w-full rounded-md link text-13">
                            Delivery
                        </button>
                    </li>
                </ul>
            </div>
            <div x-show="openTab === 1">
                <div class="card-body">
                    <h6 class="mb-3">Customer Information</h6>
                    <div class="mb-2">
                        <label for="customerNameInput" class="form-label">Customer Name</label>
                        <input type="text" id="customerNameInput" class="form-input" placeholder="Enter customer name">
                    </div>
                    <div class="mb-2">
                        <label for="contactNumberInput" class="form-label">Contact Number</label>
                        <input type="text" id="contactNumberInput" class="form-input" placeholder="Enter contact number">
                    </div>
                    <div>
                        <label for="tableLocation" class="form-label">Table Location</label>
                        <div id="tableLocation" placeholder="Select table"></div>
                    </div>
                </div>
                <div class="card-body border-t border-gray-200 dark:border-dark-800">
                    <div class="flex items-center justify-between gap-3 mb-4">
                        <h6 class="grow">Order Items:</h6>
                        <a href="#!" class="link link-red underline shrink-0" @click="clearCart()">Clear all items</a>
                    </div>
                    <div class="*:border-gray-200 *:dark:border-dark-800 *:mb-3 *:border-dashed">
                        <template x-for="item in cartItems" :key="item.id">
                            <div class="relative [*:not(:last-child)]:border-b [*:not(:last-child)]:pb-3">
                                <div class="flex items-center justify-between mb-2">
                                    <h6 class="line-clamp-1 capitalize" x-text="item.name"></h6>
                                    <button @click="removeFromCart(item.id)" class="text-red-500 hover:text-red-700">
                                        <i class="size-4" data-lucide="x"></i>
                                    </button>
                                </div>
                                <div class="*:flex *:items-center space-y-1">
                                    <p><span class="text-gray-500 dark:text-dark-500 ltr:mr-auto rtl:ml-auto">Items:</span>
                                        <span x-text="item.quantity"></span>
                                    </p>
                                    <template x-if="item.size">
                                        <p><span class="text-gray-500 dark:text-dark-500 ltr:mr-auto rtl:ml-auto">Cup Size:</span>
                                            <span x-text="item.size"></span>
                                        </p>
                                    </template>
                                    <template x-if="item.sugarLevel">
                                        <p><span class="text-gray-500 dark:text-dark-500 ltr:mr-auto rtl:ml-auto">Sugar Level:</span>
                                            <span x-text="item.sugarLevel"></span>
                                        </p>
                                    </template>
                                    <p><span class="text-gray-500 dark:text-dark-500 ltr:mr-auto rtl:ml-auto">Amount:</span>
                                        <span x-text="'$' + item.price"></span>
                                    </p>
                                    <p><span class="text-gray-500 dark:text-dark-500 ltr:mr-auto rtl:ml-auto">Total Amount:</span>
                                        <span x-text="'$' + item.total"></span>
                                    </p>
                                </div>
                            </div>
                        </template>
                    </div>

                    <div class="alert alert-sky mb-4">
                        <div class="flex items-start gap-2">
                            <span>Notes:</span>
                            <div class="grow">
                                <textarea x-model="customerInfo.notes" class="form-input h-auto text-13 bg-transparent border-0 p-0 focus:ring-0 resize-none w-full" rows="2" maxlength="50" placeholder="Add special notes for this order..."></textarea>
                                <div class="text-right text-12 text-gray-500 hidden">
                                    <span x-text="customerInfo.notes.length"></span>/50
                                </div>
                            </div>
                        </div>
                    </div>

                    <h6 class="mb-2">Order Summary:</h6>
                    <div class="*:flex *:items-center space-y-1">
                        <p><span class="text-gray-500 dark:text-dark-500 ltr:mr-auto rtl:ml-auto">Total Items:</span>
                            <strong class="font-semibold" x-text="cartItemCount"></strong>
                        </p>
                        <p><span class="text-gray-500 dark:text-dark-500 ltr:mr-auto rtl:ml-auto">Sub Total:</span>
                            <strong class="font-semibold" x-text="'$' + cartTotal.toFixed(2)"></strong>
                        </p>
                        <template x-if="orderType === 'delivery'">
                            <p><span class="text-gray-500 dark:text-dark-500 ltr:mr-auto rtl:ml-auto">Delivery Fee:</span>
                                <strong class="font-semibold">$3.50</strong>
                            </p>
                        </template>
                        <p><span class="text-gray-500 dark:text-dark-500 ltr:mr-auto rtl:ml-auto">Tax(5.6%):</span>
                            <strong class="font-semibold" x-text="'$' + tax.toFixed(2)"></strong>
                        </p>
                        <p><span class="text-gray-500 dark:text-dark-500 ltr:mr-auto rtl:ml-auto">Total:</span>
                            <strong class="font-semibold" x-text="'$' + grandTotal.toFixed(2)"></strong>
                        </p>
                    </div>
                </div>
                <div class="card-body pt-0">
                    <h6 class="mb-2">Payment Method:</h6>
                    <div class="flex items-center gap-3 mt-3 font-medium shrink-0 *:py-1.5 *:px-4 *:bg-gray-100 *:dark:bg-dark-850 *:inline-flex *:items-center *:justify-center *:rounded-md" x-data="{ activeLink: 'card' }">
                        <a href="#!" class="text-gray-500 dark:text-dark-500 [&.active]:bg-green-500/15 [&.active]:text-green-500" x-bind:class="{ 'active': activeLink === 'cash' }" @click.prevent="activeLink = 'cash'"><i data-lucide="coins" class="size-4 ltr:mr-1 rtl:ml-1"></i> Cash</a>
                        <a href="#!" data-modal-target="payWithCardModal" class="text-gray-500 dark:text-dark-500 [&.active]:bg-green-500/15 [&.active]:text-green-500" x-bind:class="{ 'active': activeLink === 'card' }" @click.prevent="activeLink = 'card'"><i data-lucide="credit-card" class="size-4 ltr:mr-1 rtl:ml-1"></i> Card</a>
                        <a href="#!" data-modal-target="payWithOnlineModal" class="text-gray-500 dark:text-dark-500 [&.active]:bg-green-500/15 [&.active]:text-green-500" x-bind:class="{ 'active': activeLink === 'scan' }" @click.prevent="activeLink = 'scan'"><i data-lucide="scan-barcode" class="size-4 ltr:mr-1 rtl:ml-1"></i> Scan</a>
                    </div>
                    <a href="#!" class="btn btn-primary w-full mt-5">Place Order</a>
                </div>
            </div>
            <div x-show="openTab === 2">
                <div class="card-body">
                    <h6 class="mb-3">Customer Information</h6>
                    <div class="mb-2">
                        <label for="takeawayCustomerName" class="form-label">Customer Name</label>
                        <input type="text" id="takeawayCustomerName" class="form-input" placeholder="Enter customer name">
                    </div>
                    <div class="mb-2">
                        <label for="takeawayContactNumber" class="form-label">Contact Number</label>
                        <input type="text" id="takeawayContactNumber" class="form-input" placeholder="Enter contact number">
                    </div>
                    <div class="mb-2">
                        <label for="takeawayTime" class="form-label">Pickup Time</label>
                        <input type="time" id="takeawayTime" class="form-input" data-provider="timepickr" data-min-time="10:30" data-max-time="12:30" placeholder="Select pickup time">
                    </div>
                </div>
                <div class="card-body border-t border-gray-200 dark:border-dark-800">
                    <div class="flex items-center justify-between gap-3 mb-4">
                        <h6 class="grow">Order Items:</h6>
                        <a href="#!" class="link link-red underline shrink-0" @click="clearCart()">Clear all items</a>
                    </div>
                    <div class="*:border-gray-200 *:dark:border-dark-800 *:mb-3 *:border-dashed">
                        <template x-for="item in cartItems" :key="item.id">
                            <div class="relative [*:not(:last-child)]:border-b [*:not(:last-child)]:pb-3">
                                <div class="flex items-center justify-between mb-2">
                                    <h6 class="line-clamp-1 capitalize" x-text="item.name"></h6>
                                    <button @click="removeFromCart(item.id)" class="text-red-500 hover:text-red-700">
                                        <i class="size-4" data-lucide="x"></i>
                                    </button>
                                </div>
                                <div class="*:flex *:items-center space-y-1">
                                    <p><span class="text-gray-500 dark:text-dark-500 ltr:mr-auto rtl:ml-auto">Items:</span>
                                        <span x-text="item.quantity"></span>
                                    </p>
                                    <template x-if="item.size">
                                        <p><span class="text-gray-500 dark:text-dark-500 ltr:mr-auto rtl:ml-auto">Cup Size:</span>
                                            <span x-text="item.size"></span>
                                        </p>
                                    </template>
                                    <template x-if="item.sugarLevel">
                                        <p><span class="text-gray-500 dark:text-dark-500 ltr:mr-auto rtl:ml-auto">Sugar Level:</span>
                                            <span x-text="item.sugarLevel"></span>
                                        </p>
                                    </template>
                                    <p><span class="text-gray-500 dark:text-dark-500 ltr:mr-auto rtl:ml-auto">Amount:</span>
                                        <span x-text="'$' + item.price"></span>
                                    </p>
                                    <p><span class="text-gray-500 dark:text-dark-500 ltr:mr-auto rtl:ml-auto">Total Amount:</span>
                                        <span x-text="'$' + item.total"></span>
                                    </p>
                                </div>
                            </div>
                        </template>
                    </div>

                    <div class="alert alert-sky mb-4">
                        <div class="flex items-start gap-2">
                            <span>Notes:</span>
                            <div class="grow">
                                <textarea x-model="customerInfo.notes" class="form-input h-auto text-13 bg-transparent border-0 p-0 focus:ring-0 resize-none w-full" rows="2" maxlength="50" placeholder="Add special notes for this order..."></textarea>
                                <div class="text-right text-12 text-gray-500 hidden">
                                    <span x-text="customerInfo.notes.length"></span>/50
                                </div>
                            </div>
                        </div>
                    </div>

                    <h6 class="mb-2">Order Summary:</h6>
                    <div class="*:flex *:items-center space-y-1">
                        <p><span class="text-gray-500 dark:text-dark-500 ltr:mr-auto rtl:ml-auto">Total Items:</span>
                            <strong class="font-semibold" x-text="cartItemCount"></strong>
                        </p>
                        <p><span class="text-gray-500 dark:text-dark-500 ltr:mr-auto rtl:ml-auto">Sub Total:</span>
                            <strong class="font-semibold" x-text="'$' + cartTotal.toFixed(2)"></strong>
                        </p>
                        <p><span class="text-gray-500 dark:text-dark-500 ltr:mr-auto rtl:ml-auto">Tax(5.6%):</span>
                            <strong class="font-semibold" x-text="'$' + tax.toFixed(2)"></strong>
                        </p>
                        <p><span class="text-gray-500 dark:text-dark-500 ltr:mr-auto rtl:ml-auto">Total:</span>
                            <strong class="font-semibold" x-text="'$' + grandTotal.toFixed(2)"></strong>
                        </p>
                    </div>
                </div>
                <div class="card-body pt-0">
                    <h6 class="mb-2">Payment Method:</h6>
                    <div class="flex items-center gap-3 mt-3 font-medium shrink-0 *:py-1.5 *:px-4 *:bg-gray-100 *:dark:bg-dark-850 *:inline-flex *:items-center *:justify-center *:rounded-md" x-data="{ activeLink: 'cash' }">
                        <a href="#!" class="text-gray-500 dark:text-dark-500 [&.active]:bg-green-500/15 [&.active]:text-green-500" x-bind:class="{ 'active': activeLink === 'cash' }" @click.prevent="activeLink = 'cash'"><i data-lucide="coins" class="size-4 ltr:mr-1 rtl:ml-1"></i> Cash</a>
                        <a href="#!" data-modal-target="payWithCardModal" class="text-gray-500 dark:text-dark-500 [&.active]:bg-green-500/15 [&.active]:text-green-500" x-bind:class="{ 'active': activeLink === 'card' }" @click.prevent="activeLink = 'card'"><i data-lucide="credit-card" class="size-4 ltr:mr-1 rtl:ml-1"></i> Card</a>
                    </div>
                    <a href="#!" class="btn btn-primary w-full mt-5">Place Order</a>
                </div>
            </div>
            <div x-show="openTab === 3">
                <div class="card-body">
                    <h6 class="mb-3">Customer Information</h6>
                    <div class="mb-2">
                        <label for="deliveryCustomerName" class="form-label">Customer Name</label>
                        <input type="text" id="deliveryCustomerName" class="form-input" placeholder="Enter customer name">
                    </div>
                    <div class="mb-2">
                        <label for="deliveryContactNumber" class="form-label">Contact Number</label>
                        <input type="text" id="deliveryContactNumber" class="form-input" placeholder="Enter contact number">
                    </div>
                    <div class="mb-2">
                        <label for="deliveryAddress" class="form-label">Delivery Address</label>
                        <textarea id="deliveryAddress" rows="2" class="h-auto form-input" placeholder="Enter full delivery address"></textarea>
                    </div>
                    <div class="mb-2">
                        <label for="deliveryTime" class="form-label">Delivery Time</label>
                        <input type="datetime-local" id="deliveryTime" class="form-input" placeholder="Select delivery time" data-provider="flatpickr" data-date-format="d M, Y H:i" data-enable-time type="text">
                    </div>
                    <div class="mb-2">
                        <label for="deliveryNotes" class="form-label">Delivery Instructions</label>
                        <textarea id="deliveryNotes" rows="2" class="h-auto form-input" placeholder="Any special instructions for delivery"></textarea>
                    </div>
                </div>
                <div class="card-body border-t border-gray-200 dark:border-dark-800">
                    <div class="flex items-center justify-between gap-3 mb-4">
                        <h6 class="grow">Order Items:</h6>
                        <a href="#!" class="link link-red underline shrink-0" @click="clearCart()">Clear all items</a>
                    </div>
                    <div class="*:border-gray-200 *:dark:border-dark-800 *:mb-3 *:border-dashed">
                        <template x-for="item in cartItems" :key="item.id">
                            <div class="relative [*:not(:last-child)]:border-b [*:not(:last-child)]:pb-3">
                                <div class="flex items-center justify-between mb-2">
                                    <h6 class="line-clamp-1 capitalize" x-text="item.name"></h6>
                                    <button @click="removeFromCart(item.id)" class="text-red-500 hover:text-red-700">
                                        <i class="size-4" data-lucide="x"></i>
                                    </button>
                                </div>
                                <div class="*:flex *:items-center space-y-1">
                                    <p><span class="text-gray-500 dark:text-dark-500 ltr:mr-auto rtl:ml-auto">Items:</span>
                                        <span x-text="item.quantity"></span>
                                    </p>
                                    <template x-if="item.size">
                                        <p><span class="text-gray-500 dark:text-dark-500 ltr:mr-auto rtl:ml-auto">Cup Size:</span>
                                            <span x-text="item.size"></span>
                                        </p>
                                    </template>
                                    <template x-if="item.sugarLevel">
                                        <p><span class="text-gray-500 dark:text-dark-500 ltr:mr-auto rtl:ml-auto">Sugar Level:</span>
                                            <span x-text="item.sugarLevel"></span>
                                        </p>
                                    </template>
                                    <p><span class="text-gray-500 dark:text-dark-500 ltr:mr-auto rtl:ml-auto">Amount:</span>
                                        <span x-text="'$' + item.price"></span>
                                    </p>
                                    <p><span class="text-gray-500 dark:text-dark-500 ltr:mr-auto rtl:ml-auto">Total Amount:</span>
                                        <span x-text="'$' + item.total"></span>
                                    </p>
                                </div>
                            </div>
                        </template>
                    </div>

                    <div class="alert alert-sky mb-4">
                        <div class="flex items-start gap-2">
                            <span>Notes:</span>
                            <div class="grow">
                                <textarea x-model="customerInfo.notes" class="form-input h-auto text-13 bg-transparent border-0 p-0 focus:ring-0 resize-none w-full" rows="2" maxlength="50" placeholder="Add special notes for this order..."></textarea>
                                <div class="text-right text-12 text-gray-500 hidden">
                                    <span x-text="customerInfo.notes.length"></span>/50
                                </div>
                            </div>
                        </div>
                    </div>

                    <h6 class="mb-2">Order Summary:</h6>
                    <div class="*:flex *:items-center space-y-1">
                        <p><span class="text-gray-500 dark:text-dark-500 ltr:mr-auto rtl:ml-auto">Total Items:</span>
                            <strong class="font-semibold" x-text="cartItemCount"></strong>
                        </p>
                        <p><span class="text-gray-500 dark:text-dark-500 ltr:mr-auto rtl:ml-auto">Sub Total:</span>
                            <strong class="font-semibold" x-text="'$' + cartTotal.toFixed(2)"></strong>
                        </p>
                        <p><span class="text-gray-500 dark:text-dark-500 ltr:mr-auto rtl:ml-auto">Delivery Fee:</span>
                            <strong class="font-semibold">$3.50</strong>
                        </p>
                        <p><span class="text-gray-500 dark:text-dark-500 ltr:mr-auto rtl:ml-auto">Tax(5.6%):</span>
                            <strong class="font-semibold" x-text="'$' + tax.toFixed(2)"></strong>
                        </p>
                        <p><span class="text-gray-500 dark:text-dark-500 ltr:mr-auto rtl:ml-auto">Total:</span>
                            <strong class="font-semibold" x-text="'$' + grandTotal.toFixed(2)"></strong>
                        </p>
                    </div>
                </div>
                <div class="card-body pt-0">
                    <h6 class="mb-2">Payment Method:</h6>
                    <div class="flex items-center gap-3 mt-3 font-medium shrink-0 *:py-1.5 *:px-4 *:bg-gray-100 *:dark:bg-dark-850 *:inline-flex *:items-center *:justify-center *:rounded-md" x-data="{ activeLink: 'card' }">
                        <a href="#!" class="text-gray-500 dark:text-dark-500 [&.active]:bg-green-500/15 [&.active]:text-green-500" x-bind:class="{ 'active': activeLink === 'cash' }" @click.prevent="activeLink = 'cash'"><i data-lucide="coins" class="size-4 ltr:mr-1 rtl:ml-1"></i> Cash</a>
                        <a href="#!" data-modal-target="payWithCardModal" class="text-gray-500 dark:text-dark-500 [&.active]:bg-green-500/15 [&.active]:text-green-500" x-bind:class="{ 'active': activeLink === 'card' }" @click.prevent="activeLink = 'card'"><i data-lucide="credit-card" class="size-4 ltr:mr-1 rtl:ml-1"></i> Card</a>
                        <a href="#!" data-modal-target="payWithOnlineModal" class="text-gray-500 dark:text-dark-500 [&.active]:bg-green-500/15 [&.active]:text-green-500" x-bind:class="{ 'active': activeLink === 'online' }" @click.prevent="activeLink = 'online'"><i data-lucide="globe" class="size-4 ltr:mr-1 rtl:ml-1"></i> Online</a>
                    </div>
                    <a href="#!" class="btn btn-primary w-full mt-5">Place Order</a>
                </div>
            </div>
        </div>
    </div>
</div>

{{> partials/footer }}
</div>

{{> partials/vendor-scripts }}
<script type="module" src="assets/js/dashboards/pos-user-interface.init.js"></script>
<script type="module" src="assets/js/main.js"></script>

</body>

</html>