{"products": [{"id": 1, "name": "Fresh Lettuce", "description": "Burger Ingredients", "sku": "BUR001", "category": "Burger Ingredients", "stock": 2, "unit": "kg", "minStock": 10, "maxStock": 50, "price": 2.99, "cost": 1.2, "status": "active"}, {"id": 2, "name": "Fresh Tomatoes", "description": "Burger Ingredients", "sku": "BUR002", "category": "Burger Ingredients", "stock": 15, "unit": "kg", "minStock": 8, "maxStock": 30, "price": 3.49, "cost": 1.4, "status": "low"}, {"id": 3, "name": "<PERSON><PERSON><PERSON>", "description": "Pizza Ingredients", "sku": "PIZ001", "category": "Pizza Ingredients", "stock": 20, "unit": "kg", "minStock": 10, "maxStock": 40, "price": 8.99, "cost": 5.4, "status": "active"}, {"id": 4, "name": "Pizza Sauce", "description": "Pizza Ingredients", "sku": "PIZ002", "category": "Pizza Ingredients", "stock": 30, "unit": "liters", "minStock": 15, "maxStock": 60, "price": 4.99, "cost": 2.0, "status": "active"}, {"id": 5, "name": "<PERSON> Rice", "description": "Rice Dish Ingredients", "sku": "RCE001", "category": "Rice Dish Ingredients", "stock": 50, "unit": "kg", "minStock": 20, "maxStock": 100, "price": 3.99, "cost": 1.8, "status": "active"}, {"id": 6, "name": "Soy Sauce", "description": "Rice Dish Ingredients", "sku": "RCE002", "category": "Rice Dish Ingredients", "stock": 10, "unit": "liters", "minStock": 5, "maxStock": 20, "price": 5.99, "cost": 2.4, "status": "low"}, {"id": 7, "name": "Coffee Beans", "description": "Drinks Ingredients", "sku": "DRK001", "category": "Drinks Ingredients", "stock": 15, "unit": "kg", "minStock": 5, "maxStock": 30, "price": 12.99, "cost": 7.8, "status": "active"}, {"id": 8, "name": "Fresh Orange Juice", "description": "Drinks Ingredients", "sku": "DRK002", "category": "Drinks Ingredients", "stock": 8, "unit": "liters", "minStock": 4, "maxStock": 16, "price": 4.99, "cost": 2.0, "status": "low"}, {"id": 9, "name": "Chicken Breast", "description": "Meat Products", "sku": "MET001", "category": "Meat Products", "stock": 35, "unit": "kg", "minStock": 15, "maxStock": 70, "price": 7.99, "cost": 4.8, "status": "active"}, {"id": 10, "name": "Ground Beef", "description": "Meat Products", "sku": "MET002", "category": "Meat Products", "stock": 12, "unit": "kg", "minStock": 10, "maxStock": 40, "price": 8.49, "cost": 5.1, "status": "low"}, {"id": 11, "name": "Pizza Boxes", "description": "Packaging Materials", "sku": "PKG001", "category": "Packaging", "stock": 200, "unit": "pieces", "minStock": 100, "maxStock": 500, "price": 0.5, "cost": 0.2, "status": "active"}, {"id": 12, "name": "Paper Bags", "description": "Packaging Materials", "sku": "PKG002", "category": "Packaging", "stock": 500, "unit": "pieces", "minStock": 200, "maxStock": 1000, "price": 0.15, "cost": 0.06, "status": "active"}, {"id": 13, "name": "Coca Cola", "description": "Beverages", "sku": "BEV001", "category": "Beverages", "stock": 48, "unit": "cans", "minStock": 24, "maxStock": 96, "price": 1.5, "cost": 0.6, "status": "active"}, {"id": 14, "name": "Sprite", "description": "Beverages", "sku": "BEV002", "category": "Beverages", "stock": 20, "unit": "cans", "minStock": 24, "maxStock": 96, "price": 1.5, "cost": 0.6, "status": "low"}, {"id": 15, "name": "Paper Napkins", "description": "Disposables", "sku": "DSP001", "category": "Disposables", "stock": 1000, "unit": "pieces", "minStock": 500, "maxStock": 2000, "price": 0.05, "cost": 0.02, "status": "active"}, {"id": 16, "name": "Plastic Forks", "description": "Disposables", "sku": "DSP002", "category": "Disposables", "stock": 400, "unit": "pieces", "minStock": 200, "maxStock": 1000, "price": 0.1, "cost": 0.04, "status": "low"}, {"id": 17, "name": "Olive Oil", "description": "Cooking Essentials", "sku": "ESS001", "category": "Cooking Essentials", "stock": 15, "unit": "liters", "minStock": 5, "maxStock": 30, "price": 9.99, "cost": 4.0, "status": "active"}, {"id": 18, "name": "Black Pepper", "description": "Cooking Essentials", "sku": "ESS002", "category": "Cooking Essentials", "stock": 2, "unit": "kg", "minStock": 1, "maxStock": 5, "price": 15.99, "cost": 8.0, "status": "low"}, {"id": 19, "name": "Chocolate Cake", "description": "Desserts", "sku": "DSR001", "category": "Desserts", "stock": 0, "unit": "pieces", "minStock": 3, "maxStock": 10, "price": 24.99, "cost": 12.5, "status": "active"}, {"id": 20, "name": "Ice Cream", "description": "Desserts", "sku": "DSR002", "category": "Desserts", "stock": 8, "unit": "liters", "minStock": 4, "maxStock": 16, "price": 8.99, "cost": 4.5, "status": "active"}, {"id": 21, "name": "Fresh Salmon", "description": "Seafood Products", "sku": "SEA001", "category": "Seafood", "stock": 8, "unit": "kg", "minStock": 5, "maxStock": 20, "price": 18.99, "cost": 11.4, "status": "active"}, {"id": 22, "name": "<PERSON>mp", "description": "Seafood Products", "sku": "SEA002", "category": "Seafood", "stock": 6, "unit": "kg", "minStock": 4, "maxStock": 15, "price": 22.99, "cost": 13.8, "status": "low"}, {"id": 23, "name": "Fresh Milk", "description": "Dairy Products", "sku": "DRY001", "category": "Dairy", "stock": 25, "unit": "liters", "minStock": 10, "maxStock": 50, "price": 3.99, "cost": 2.4, "status": "active"}, {"id": 24, "name": "Butter", "description": "Dairy Products", "sku": "DRY002", "category": "Dairy", "stock": 15, "unit": "kg", "minStock": 8, "maxStock": 30, "price": 6.99, "cost": 4.2, "status": "active"}, {"id": 25, "name": "Fresh Bread", "description": "<PERSON><PERSON> Items", "sku": "BKR001", "category": "<PERSON><PERSON>", "stock": 30, "unit": "loaves", "minStock": 15, "maxStock": 60, "price": 2.99, "cost": 1.2, "status": "active"}, {"id": 26, "name": "Croissants", "description": "<PERSON><PERSON> Items", "sku": "BKR002", "category": "<PERSON><PERSON>", "stock": 40, "unit": "pieces", "minStock": 20, "maxStock": 80, "price": 1.99, "cost": 0.8, "status": "active"}, {"id": 27, "name": "Fresh Spinach", "description": "Vegetables", "sku": "VEG001", "category": "Vegetables", "stock": 12, "unit": "kg", "minStock": 6, "maxStock": 25, "price": 3.49, "cost": 1.4, "status": "active"}, {"id": 28, "name": "Fresh Carrots", "description": "Vegetables", "sku": "VEG002", "category": "Vegetables", "stock": 20, "unit": "kg", "minStock": 10, "maxStock": 40, "price": 2.49, "cost": 1.0, "status": "active"}, {"id": 29, "name": "Fresh Apples", "description": "Fruits", "sku": "FRT001", "category": "Fruits", "stock": 25, "unit": "kg", "minStock": 12, "maxStock": 50, "price": 3.99, "cost": 1.6, "status": "active"}, {"id": 30, "name": "Fresh Bananas", "description": "Fruits", "sku": "FRT002", "category": "Fruits", "stock": 30, "unit": "kg", "minStock": 15, "maxStock": 60, "price": 2.99, "cost": 1.2, "status": "active"}, {"id": 31, "name": "Mineral Water", "description": "Bottled Water", "sku": "WTR001", "category": "Beverages", "stock": 100, "unit": "bottles", "minStock": 50, "maxStock": 200, "price": 0.99, "cost": 0.4, "status": "active"}, {"id": 32, "name": "Sparkling Water", "description": "Bottled Water", "sku": "WTR002", "category": "Beverages", "stock": 80, "unit": "bottles", "minStock": 40, "maxStock": 160, "price": 1.49, "cost": 0.6, "status": "active"}]}