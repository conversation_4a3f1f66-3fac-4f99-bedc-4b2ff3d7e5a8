{{> partials/main }}

<head>

    {{> partials/title-meta title="Payroll" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}
        {{> partials/page-heading title="Payroll" sub-title="Teachers" }}

        <div class="grid grid-cols-12 gap-x-space" x-data="payrollTable()">
            <div class="col-span-12 card">
                <div class="card-header">
                    <div class="flex flex-wrap justify-between gap-4">
                        <div>
                            <div class="relative group/form grow">
                                <input type="email" class="ltr:pl-9 rtl:pr-9 form-input ltr:group-[&.right]/form:pr-9 rtl:group-[&.right]/form:pl-9 ltr:group-[&.right]/form:pl-4 rtl:group-[&.right]/form:pr-4" placeholder="Search for ..." x-model="searchTerm" @input="filteredPayrolls">
                                <button class="absolute inset-y-0 flex items-center ltr:left-3 rtl:right-3 lt:group-[&.right]/form:right-3 rtl:group-[&.right]/form:left-3 ltr:group-[&.right]/form:left-auto rtl:group-[&.right]/form:right-auto focus:outline-hidden">
                                    <i data-lucide="search" class="text-gray-500 dark:text-dark-500 size-4 fill-gray-100 dark:fill-dark-850"></i>
                                </button>
                            </div>
                        </div>
                        <div>
                            <div id="sorting" class="w-full" placeholder="Sorting By" @change="filteredPayrolls"></div>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div >
                        <div class="overflow-x-auto whitespace-nowrap">
                            <table class="table flush">
                                <tbody>
                                    <tr class="text-gray-500 bg-gray-100 dark:bg-dark-850 dark:text-dark-500">
                                        <th x-on:click="sort('teacherName')" class="!font-medium cursor-pointer">Teacher Name <span x-show="sortBy === 'teacherName'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                        <th x-on:click="sort('email')" class="!font-medium cursor-pointer">Email <span x-show="sortBy === 'email'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                        <th x-on:click="sort('gross')" class="!font-medium cursor-pointer">Phone <span x-show="sortBy === 'gross'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                        <th x-on:click="sort('taxes')" class="!font-medium cursor-pointer">Taxes <span x-show="sortBy === 'taxes'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                        <th x-on:click="sort('netSalary')" class="!font-medium cursor-pointer">Salary <span x-show="sortBy === 'netSalary'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                        <th x-on:click="sort('performance')" class="!font-medium cursor-pointer">Performance <span x-show="sortBy === 'performance'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                        <th x-on:click="sort('status')" class="!font-medium cursor-pointer">Status <span x-show="sortBy === 'status'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                    </tr>
                                    <template x-for="(payroll, index) in displayedPayrolls" :key="index">
                                        <tr>
                                            <td>
                                                <div class="flex items-center gap-3">
                                                    <div class="relative text-gray-500 bg-gray-100 rounded-full size-6 dark:bg-dark-850 dark:text-dark-500">
                                                        <img x-show="payroll.image" :src="payroll.image" alt="" class="rounded-full" @error="removeImage($event)">
                                                        <span x-show="!payroll.image" x-text="payroll.avatarText" class="absolute inset-0 flex items-center justify-center font-semibold text-gray-500 bg-gray-100 rounded-full dark:bg-dark-850 dark:text-dark-500 text-11"></span>
                                                    </div>
                                                    <div>
                                                        <h6><a href="apps-school-teachers-overview.html" x-text="payroll.teacherName"></a></h6>
                                                    </div>
                                                </div>
                                            </td>
                                            <td x-text="payroll.email"></td>
                                            <td x-text="payroll.gross"></td>
                                            <td x-text="payroll.taxes"></td>
                                            <td x-text="payroll.netSalary"></td>
                                            <td>
                                                <span x-text="payroll.performance" :class="{
                                                    'badge badge-orange': payroll.performance === 'Excellent',
                                                    'badge badge-purple': payroll.performance === 'Good',
                                                    'badge badge-sky': payroll.performance === 'Satisfactory',
                                                }"></span>
                                            </td>
                                            <td>
                                                <span x-text="payroll.status" :class="{
                                                    'badge badge-green': payroll.status === 'Active',
                                                    'badge badge-yellow': payroll.status === 'Inactive'
                                                }"></span>
                                            </td>
                                        </tr>
                                    </template>
                                    <tr>
                                        <template x-if="displayedPayrolls.length == 0">
                                            <td colspan="10" class="!p-8">
                                                <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" class="mx-auto size-12" viewBox="0 0 48 48">
                                                    <linearGradient id="SVGID_1__h35ynqzIJzH4_gr1" x1="34.598" x2="15.982" y1="15.982" y2="34.598" gradientUnits="userSpaceOnUse">
                                                        <stop offset="0" stop-color="#60e8fe"></stop>
                                                        <stop offset=".033" stop-color="#6ae9fe"></stop>
                                                        <stop offset=".197" stop-color="#97f0fe"></stop>
                                                        <stop offset=".362" stop-color="#bdf5ff"></stop>
                                                        <stop offset=".525" stop-color="#dafaff"></stop>
                                                        <stop offset=".687" stop-color="#eefdff"></stop>
                                                        <stop offset=".846" stop-color="#fbfeff"></stop>
                                                        <stop offset="1" stop-color="#fff"></stop>
                                                    </linearGradient>
                                                    <path fill="url(#SVGID_1__h35ynqzIJzH4_gr1)" d="M40.036,33.826L31.68,25.6c0.847-1.739,1.335-3.684,1.335-5.748c0-7.27-5.894-13.164-13.164-13.164	S6.688,12.582,6.688,19.852c0,7.27,5.894,13.164,13.164,13.164c2.056,0,3.995-0.485,5.728-1.326l3.914,4.015l4.331,4.331	c1.715,1.715,4.496,1.715,6.211,0C41.751,38.321,41.751,35.541,40.036,33.826z"></path>
                                                    <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M31.95,25.739l8.086,8.086c1.715,1.715,1.715,4.496,0,6.211l0,0c-1.715,1.715-4.496,1.715-6.211,0	l-4.331-4.331"></path>
                                                    <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M7.525,24.511c-1.771-4.694-0.767-10.196,3.011-13.975c3.847-3.847,9.48-4.817,14.228-2.912"></path>
                                                    <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M30.856,12.603c3.376,5.114,2.814,12.063-1.688,16.565c-4.858,4.858-12.565,5.129-17.741,0.814"></path>
                                                </svg>
                                                <p class="mt-2 text-center text-gray-500 dark:text-dark-500">No matching records found</p>
                                            </td>
                                        </template>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="grid grid-cols-12 gap-5 mt-5 items-center" x-show="displayedPayrolls.length > 0">
                            <div class="col-span-12 md:col-span-6 justify-center md:justify-start flex flex-wrap">
                                <p class="text-gray-500 dark:text-dark-500">Showing <b x-text="showingStart"></b> - <b x-text="showingEnd"></b> of <b x-text="filterPayrolls.length"></b> Results</p>
                            </div>
                            <div class="col-span-12 md:col-span-6">
                                <div class="flex justify-center md:justify-end pagination pagination-primary">
                                    <button @click="prevPage()" :disabled="currentPage === 1" class="pagination-pre">
                                        <i data-lucide="chevron-left" class="mr-1 ltr:inline-block rtl:hidden size-4"></i>
                                        <i data-lucide="chevron-right" class="ml-1 ltr:hidden rtl:inline-block size-4"></i>
                                        Prev
                                    </button>
                                    <template x-for="page in totalPages" :key="page">
                                        <button @click="gotoPage(page)" :class="{ 'active': currentPage === page }" class="pagination-item">
                                            <span x-text="page"></span>
                                        </button>
                                    </template>
                                    <button @click="nextPage()" :disabled="currentPage === totalPages" class="pagination-next">
                                        Next
                                        <i data-lucide="chevron-right" class="ml-1 ltr:inline-block rtl:hidden size-4"></i>
                                        <i data-lucide="chevron-left" class="mr-1 ltr:hidden rtl:inline-block size-4"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div><!--end col-->
        </div><!--end grid-->

    </div>
    {{> partials/footer }}
</div>

<!--delete modal-->
<div id="deleteModal" class="!hidden modal show">
    <div class="modal-wrap modal-xs modal-center">
        <div class="text-center modal-content p-7">
            <div class="flex items-center justify-center mx-auto mb-4 text-red-500 rounded-full bg-red-500/10 size-14 backdrop-blur-xl">
                <i data-lucide="trash-2" class="size-6"></i>
            </div>
            <h5 class="mb-4">Are you sure you want to delete this teacher ?</h5>
            <div class="flex items-center justify-center gap-2">
                <button class="btn btn-red">Delete</button>
                <button data-modal-close="deleteModal" class="btn link link-primary">Cancel</button>
            </div>
        </div>
    </div>
</div><!--end-->

{{> partials/vendor-scripts }}

<script type="module" src="assets/js/apps/school/teachers/payroll.init.js"></script>

<script type="module" src="assets/js/main.js"></script>

</body>
</html>