{{> partials/main }}

<head>

    {{> partials/title-meta title="Sign Up" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

<div class="relative flex items-center justify-center min-h-screen py-12 bg-center bg-cover bg-[url('../images/others/auth.jpg')]">
    <div class="absolute inset-0 bg-gray-950/50"></div>
    <div class="container relative">
        <div class="grid grid-cols-12">
            <div class="col-span-12 mb-0 border-none shadow-none md:col-span-10 lg:col-span-6 xl:col-span-4 md:col-start-2 lg:col-start-4 xl:col-start-5 card bg-white/10 backdrop-blur-md">
                <div class="md:p-10 card-body">
                    <div class="mb-5 text-center">
                        <a href="#!"><img src="assets/images/logo-white.png" alt="" class="h-8 mx-auto"></a>
                    </div>
                    <h4 class="mb-2 leading-relaxed text-center text-white">Create a New Account</h4>
                    <p class="mb-5 text-center text-white/75">Already have an account? <a href="auth-signin-modern.html" class="font-medium text-white">Sign In</a></p>
                    <form x-data="formValidation()" @submit.prevent="validateForm">
                        <div class="grid grid-cols-12 gap-4 mt-5">
                            <div class="col-span-12 md:col-span-6">
                                <label for="firstNameInput" class="form-label text-white/75">First Name</label>
                                <input type="text" id="firstNameInput" class="text-white border-none form-input bg-white/10 placeholder:text-white/75" placeholder="Enter your first name"
                                       x-model="form.firstName" @input="validateField('firstName')">
                                <p x-show="errors.firstName" class="text-sm text-red-500" x-text="errors.firstName"></p>
                            </div>
                            <div class="col-span-12 md:col-span-6">
                                <label for="lastNameInput" class="form-label text-white/75">Last Name</label>
                                <input type="text" id="lastNameInput" class="text-white border-none form-input bg-white/10 placeholder:text-white/75" placeholder="Enter your last name"
                                       x-model="form.lastName" @input="validateField('lastName')">
                                <p x-show="errors.lastName" class="text-sm text-red-500" x-text="errors.lastName"></p>
                            </div>
                            <div class="col-span-12 md:col-span-6">
                                <label for="userNameInput" class="form-label text-white/75">Username</label>
                                <input type="text" id="userNameInput" class="text-white border-none form-input bg-white/10 placeholder:text-white/75" placeholder="Enter your username"
                                       x-model="form.userName" @input="validateField('userName')">
                                <p x-show="errors.userName" class="text-sm text-red-500" x-text="errors.userName"></p>
                            </div>
                            <div class="col-span-12 md:col-span-6">
                                <label for="emailInput" class="form-label text-white/75">Email</label>
                                <input type="email" id="emailInput" class="text-white border-none form-input bg-white/10 placeholder:text-white/75" placeholder="Enter your email"
                                       x-model="form.email" @input="validateField('email')">
                                <p x-show="errors.email" class="text-sm text-red-500" x-text="errors.email"></p>
                            </div>
                            <div class="col-span-12">
                                <div x-data="{ show: false }">
                                    <label for="passwordInput" class="form-label text-white/75">Password</label>
                                    <div class="relative">
                                        <input type="password" id="passwordInput" x-bind:type="show ? 'text' : 'password'" class="text-white border-none ltr:pr-8 rtl:pl-8 form-input bg-white/10 placeholder:text-white/75" placeholder="Enter your password"
                                               x-model="form.password" @input="validateField('password')">
                                        <button type="button" @click="show = !show" class="absolute inset-y-0 flex items-center text-gray-500 ltr:right-3 rtl:left-3 focus:outline-hidden">
                                            <i data-lucide="eye" x-show="show" class="size-5"></i>
                                            <i data-lucide="eye-off" x-show="!show" class="size-5"></i>
                                        </button>
                                    </div>
                                    <p x-show="errors.password" class="text-sm text-red-500" x-text="errors.password"></p>
                                </div>
                            </div>
                            <div class="col-span-12">
                                <div x-data="{ show: false }">
                                    <label for="confirmPasswordInput" class="form-label text-white/75">Confirm Password</label>
                                    <div class="relative">
                                        <input type="password" id="confirmPasswordInput" x-bind:type="show ? 'text' : 'password'" class="text-white border-none ltr:pr-8 rtl:pl-8 form-input bg-white/10 placeholder:text-white/75" placeholder="Enter your confirm password"
                                               x-model="form.confirmPassword" @input="validateField('confirmPassword')">
                                        <button type="button" @click="show = !show" class="absolute inset-y-0 flex items-center text-gray-500 ltr:right-3 rtl:left-3 focus:outline-hidden">
                                            <i data-lucide="eye" x-show="show" class="size-5"></i>
                                            <i data-lucide="eye-off" x-show="!show" class="size-5"></i>
                                        </button>
                                    </div>
                                    <p x-show="errors.confirmPassword" class="text-sm text-red-500" x-text="errors.confirmPassword"></p>
                                </div>
                            </div>
                            <div class="col-span-12">
                                <div class="items-start input-check-group grow">
                                    <input id="checkboxBasic1" class="border-0 input-check bg-white/10 shrink-0" type="checkbox" x-model="form.terms" @change="validateField('terms')" />
                                    <label for="checkboxBasic1" class="input-check-label text-white/75">By creating an account, you agree to all of our terms condition & policies.</label>
                                </div>
                                <p x-show="errors.terms" class="text-sm text-red-500" x-text="errors.terms"></p>
                            </div>
                            <div class="col-span-12">
                                <button type="submit" class="w-full btn btn-primary">Sign Up</button>
                            </div>
                        </div>
                    </form>
                    <div class="relative my-5 text-center text-white/75">
                        <p>OR</p>
                    </div>

                    <div class="space-y-2">
                        <button type="button" class="w-full border-white/10 text-white/75 btn hover:bg-white/10 hover:text-white"><img src="assets/images/others/google.png" alt="" class="inline-block h-4 ltr:mr-1 rtl:ml-1"> SignIn Vie Google</button>
                        <button type="button" class="w-full border-white/10 text-white/75 btn hover:bg-white/10 hover:text-white"><i data-lucide="facebook" class="inline-block ltr:mr-1 rtl:ml-1 size-4 text-primary-500"></i> SignIn Vie Facebook</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{{> partials/vendor-scripts }}

<script type="module" src="assets/js/main.js"></script>

<script type="module"  src="assets/js/auth/signup-validation.js"></script>

</body>
</html>