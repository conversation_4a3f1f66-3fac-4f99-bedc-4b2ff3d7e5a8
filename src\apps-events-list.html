{{> partials/main }}

<head>

    {{> partials/title-meta title="List View" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}
{{> partials/page-heading title="List View" sub-title="Events" }}


<div x-data="eventsTable()">
    <div class="flex flex-wrap items-center gap-5">
        <h6 class="grow">Event Listings (<span x-text="totalEvents"></span>)</h6>
        <div class="flex flex-wrap items-center gap-2 shrink-0">
            <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown shrink-0">
                <button x-ref="button" x-on:click="toggle()" title="dropdown-button" :aria-expanded="open.toString()" type="button" title="dropdown-button" class="btn btn-sub-gray btn-icon btn-icon-text">
                    <i data-lucide="sliders-horizontal" class="size-4"></i>
                </button>

                <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu !w-64" dropdown-position="right">
                    <ul>
                        <li>
                            <a href="#!" x-on:click="init , sort(null) " class="dropdown-item">
                                No Sorting
                            </a>
                        </li>
                        <li>
                            <a href="#!" x-on:click="sort('eventName')" class="dropdown-item">
                                Alphabetical (A -> Z)
                            </a>
                        </li>
                        <li>
                            <a href="#!" x-on:click="sort('eventName')" class="dropdown-item">
                                Reverse Alphabetical (Z -> A)
                            </a>
                        </li>
                        <li>
                            <a href="#!" x-on:click="sort('status')" class="dropdown-item">
                                Status
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
            <button class="btn btn-primary shrink-0" data-modal-target="addEventModal" @click="handleModal('showAddEventForm')"><i data-lucide="plus" class="inline-block size-4"></i> <span class="align-baseline">Add New Event</span></button>

        </div>
    </div>
    <div class="mt-5 overflow-x-auto">
        <table class="table whitespace-nowrap">
            <tbody>
                <tr class="text-gray-500 bg-gray-100 dark:bg-dark-850 dark:text-dark-500">
                    <th x-on:click="sort('eventName')" class="!font-medium cursor-pointer">Event Name <span x-show="sortBy === 'eventName'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                    <th x-on:click="sort('eventDate')" class="!font-medium cursor-pointer">Event Date <span x-show="sortBy === 'eventDate'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                    <th x-on:click="sort('peopleSize')" class="!font-medium cursor-pointer">People Size <span x-show="sortBy === 'peopleSize'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                    <th x-on:click="sort('location')" class="!font-medium cursor-pointer">Location <span x-show="sortBy === 'location'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                    <th x-on:click="sort('eventType')" class="!font-medium cursor-pointer">Event Type <span x-show="sortBy === 'eventType'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                    <th x-on:click="sort('price')" class="!font-medium cursor-pointer">Price <span x-show="sortBy === 'price'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                    <th x-on:click="sort('status')" class="!font-medium cursor-pointer">Status <span x-show="sortBy === 'status'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                    <th class="!font-medium">Action</th>
                </tr>
                <template x-for="(event, index) in displayedEvents" :key="index">
                    <tr>
                        <td>
                            <div class="flex items-center gap-2">
                                <div class="flex items-center justify-center p-1 border border-gray-200 rounded-sm size-9 dark:border-dark-800">
                                    <img :src="event.image" alt="" class="rounded-sm">
                                </div>
                                <h6><a href="apps-event-overview.html" x-text="event.eventName"></a></h6>
                            </div>
                        </td>
                        <td x-text="formatDate(event.eventDate)"></td>
                        <td x-text="event.peopleSize"></td>
                        <td x-text="event.location"></td>
                        <td x-text="event.eventType"></td>
                        <td x-text="`$${event.price}`"></td>
                        <td>
                            <span x-text="event.status" :class="{
                                'badge badge-green': event.status === 'Published',
                                'badge badge-red': event.status === 'Expired',
                                'badge badge-yellow': event.status === 'Coming Soon'
                            }"></span>
                        </td>
                        <td>
                            <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown">
                                <button x-ref="button" x-on:click="toggle()" :aria-expanded="open.toString()" type="button" title="dropdown-button" class="flex items-center text-gray-500 dark:text-dark-500">
                                    <i class="ri-more-2-fill"></i>
                                </button>
                                <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden">
                                    <ul>
                                        <li>
                                            <a href="apps-event-overview.html" class="dropdown-item">
                                                <i class="align-middle ltr:mr-2 rtl:ml-2 ri-eye-line"></i> <span>Overview</span>
                                            </a>
                                        </li>
                                        <li>
                                            <a href="#!" data-modal-target="addEventModal" @click="editEvent(event.ID)" class="dropdown-item">
                                                <i class="align-middle ltr:mr-2 rtl:ml-2 ri-pencil-line"></i> <span>Edit</span>
                                            </a>
                                        </li>
                                        <li>
                                            <a href="#!" @click="deleteEvent(event.eventName); close()" class="dropdown-item">
                                                <i class="align-middle ltr:mr-2 rtl:ml-2 ri-delete-bin-line"></i> <span>Delete</span>
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </td>
                    </tr>
                </template>
            </tbody>
        </table>
        <div x-show="events.length === 0" class="p-4 text-center">No Events Available.</div>
    </div>
    <div class="grid grid-cols-12 gap-5 my-5 items-center">
        <div class="col-span-12 md:col-span-6 justify-center md:justify-start flex flex-wrap">
            <p class="text-gray-500 dark:text-dark-500">Showing <b x-text="showingStart"></b> - <b x-text="showingEnd"></b> of <b x-text="filterEvents.length"></b> Results</p>
        </div>
        <div class="col-span-12 md:col-span-6">
            <div class="flex justify-center md:justify-end pagination pagination-primary">
                <button @click="prevPage()" :disabled="currentPage === 1" class="pagination-pre">
                    <i data-lucide="chevron-left" class="mr-1 ltr:inline-block rtl:hidden size-4"></i>
                    <i data-lucide="chevron-right" class="ml-1 ltr:hidden rtl:inline-block size-4"></i>
                    Prev
                </button>
                <template x-for="page in totalPages" :key="page">
                    <button @click="gotoPage(page)" :class="{ 'active': currentPage === page }" class="pagination-item">
                        <span x-text="page"></span>
                    </button>
                </template>
                <button @click="nextPage()" :disabled="currentPage === totalPages" class="pagination-next">
                    Next
                    <i data-lucide="chevron-right" class="ml-1 ltr:inline-block rtl:hidden size-4"></i>
                    <i data-lucide="chevron-left" class="mr-1 ltr:hidden rtl:inline-block size-4"></i>
                </button>
            </div>
        </div>
    </div>
    
    <div id="addEventModal" class="!hidden modal show" :class="{'show d-block': showAddEventForm || showEditEventForm}" x-show="showAddEventForm || showEditEventForm">
        <div class="modal-wrap modal-center">
            <div class="modal-header">
                <h6 x-text="showAddEventForm ? 'Add New Event' : 'Edit Event'">Add New Event</h6>
                <button data-modal-close="addEventModal" class="link link-red ltr:float-end rtl:float-start"><i data-lucide="x" class="size-5"></i></button>
            </div>
            <div class="modal-content">
                <div class="grid grid-cols-12 gap-5">
                    <div class="col-span-12">
                        <h6 class="form-label">Event Logo</h6>
                        <div>
                            <label for="logo">
                                <span class="inline-flex items-center justify-center w-full h-32 overflow-hidden bg-gray-100 border border-gray-200 rounded-md cursor-pointer dark:bg-dark-850 dark:border-dark-800">
                                    <img x-show="eventForm.image" :src="eventForm.image" class="object-cover h-24">
                                    <span x-show="!eventForm.image" class="flex flex-col items-center text-gray-500">
                                        <i data-lucide="upload"></i>
                                        <span class="block mt-3">Upload Your Event Logo</span>
                                    </span>
                                </span>
                            </label>
                            <div class="hidden">
                                <label class="block">
                                    <span class="sr-only">Choose profile photo</span>
                                    <input type="file" name="logo" id="logo" @change="fileChosen" class="block w-full text-sm file:rounded-md focus:outline-0 text-slate-500 file:mr-4 file:py-2 file:px-4 file:border-0 file:text-sm file:font-semibold file:bg-violet-50 file:text-violet-700 hover:file:bg-violet-100" />
                                </label>
                            </div>
                        </div>
                        <span x-show="errors.image" class="text-red-500" x-text="errors.image"></span>                        
                    </div>
                    <div class="col-span-12">
                        <label for="eventNameInput" class="form-label">Event Name</label>
                        <input type="text" id="eventNameInput" class="form-input" placeholder="Event name" x-model="eventForm.eventName" @input="validateField('eventName', eventForm.eventName, 'Event name is required.')">
                        <span x-show="errors.eventName" class="text-red-500" x-text="errors.eventName"></span>
                    </div>
                    <div class="col-span-6">
                        <label for="eventDateInput" class="form-label">Event Date</label>
                        <input type="text" placeholder="YYYY-MM-DD" id="eventDateInput" class="form-input" data-provider="flatpickr" data-date-format="Y-m-d" x-model="eventForm.eventDate" @input="validateField('eventDate', eventForm.eventDate, 'Date is required.')">
                        <span x-show="errors.eventDate" class="text-red-500" x-text="errors.eventDate"></span>
                    </div>
                    <div class="col-span-6">
                        <label for="eventDurationInput" class="form-label">Event Duration</label>
                        <input type="text" placeholder="00:00" id="eventDurationInput" class="form-input" data-provider="timepickr" data-time-basic x-model="eventForm.time" @input="validateField('time', eventForm.time, 'Time is required.')">
                        <span x-show="errors.time" class="text-red-500" x-text="errors.time"></span>
                    </div>
                    <div class="col-span-6">
                        <label for="peopleInput" class="form-label">Total People</label>
                        <input type="number" placeholder="0" id="peopleInput" class="form-input" x-model="eventForm.peopleSize" @input="validateField('peopleSize', eventForm.peopleSize, 'People size is required.')">
                        <span x-show="errors.peopleSize" class="text-red-500" x-text="errors.peopleSize"></span>
                    </div>
                    <div class="col-span-6">
                        <label for="priceInput" class="form-label">Price</label>
                        <input type="number" placeholder="$00.00" id="priceInput" class="form-input" x-model="eventForm.price" @input="validateField('price', eventForm.price, 'Price is required.')">
                        <span x-show="errors.price" class="text-red-500" x-text="errors.price"></span>
                    </div>
                    <div class="col-span-12">
                        <label for="locationInput" class="form-label">Location</label>
                        <input type="text" placeholder="Enter event location" id="locationInput" class="form-input" x-model="eventForm.location" @input="validateField('location', eventForm.location, 'Location is required.')">
                        <span x-show="errors.location" class="text-red-500" x-text="errors.location"></span>
                    </div>
                    <div class="col-span-6">
                        <label for="eventTypeSelect" class="form-label">Event Type</label>
                        <div id="eventTypeSelect" placeholder="Select event type" x-model="eventForm.eventType" @change="validateField('eventType', $event.target.value )"></div>
                        <span x-show="errors.eventType" class="text-red-500" x-text="errors.eventType"></span>
                    </div>
                    <div class="col-span-6">
                        <label for="StatusSelect" class="form-label">Status</label>
                        <div type="text" id="StatusSelect" placeholder="Select status" x-model="eventForm.status" @change="validateField('status', $event.target.value)"></div>
                        <span x-show="errors.status" class="text-red-500" x-text="errors.status"></span>
                    </div>      
                </div>
                <div class="flex justify-end gap-2 mt-5">
                    <button type="button" data-modal-close="addEventModal" class="btn btn-active-red">Cancel</button>
                    <button class="btn btn-primary" @click="submitForm()" x-text="showAddEventForm ? 'Add Event' : 'Edit Event'">Add Event</button>
                </div>
            </div>
        </div>  
    </div>
</div>

</div>
{{> partials/footer }}
</div>

{{> partials/vendor-scripts }}

<script type="module" src="assets/js/apps/events/list.init.js"></script>

<script type="module" src="assets/js/main.js"></script>

</body>
</html>