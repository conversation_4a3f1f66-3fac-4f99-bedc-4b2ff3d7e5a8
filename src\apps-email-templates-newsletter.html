{{> partials/main }}

<head>

    {{> partials/title-meta title="NewsLetter" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

<div class="relative min-h-screen">
    <div class="page-wrapper pt-[calc(theme('spacing.topbar')_*_1.2)] group-data-[layout=horizontal]:pt-[calc(theme('spacing.topbar')_*_1.9)]">
        {{> partials/page-heading title="NewsLetter Email Template" sub-title="Templates" }}

        <div class="mb-space">
            <div style="max-width: 500px;margin:0 auto;border: 1px solid #e5e7eb;border-radius: 0.375rem; background-color: #ffffff; background-image: linear-gradient(to top, rgba(240, 177, 0, .10), rgba(255, 255, 255, 0));">
                <div style="padding: 2rem;">
                    <div style="margin-bottom: 15px;">
                        <img src="assets/images/main-logo.png" alt="" style="height: 1.2rem;margin: 0 auto;">
                    </div>
                    <h6 style="font-size: 24px;font-weight: bold;text-align: center;max-width: 300px;margin: 0 auto 20px;color: #000;">Shout our with a <span style="color: #358ffc;">NewsLetter Projects</span></h6>
                    <img src="assets/images/email/templates/news.png" alt="" style="margin: 0 auto;display: block;">
                    <p style="color: #6b7280; font-size: 15px;text-align: center;max-width: 400px;margin: 20px auto 16px;">Working with an easy-to-use editor will ne a domiex. Your future you will thank you.</p>
                    <div style="text-align: center;">
                        <button type="button" style="display: inline-block;background-color: #358ffc;color: #fff;cursor: pointer;padding: 0.5625rem 1.5rem;font-size: 0.875rem;border-radius: 0.375rem;">
                            Start a Project
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" style="width: 20px;height: 20px;display: inline-block;">
                                <path d="M16.1716 10.9999L10.8076 5.63589L12.2218 4.22168L20 11.9999L12.2218 19.778L10.8076 18.3638L16.1716 12.9999H4V10.9999H16.1716Z"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="mb-space">
            <h6 class="underline my-3">Dark Mode</h6>
            <div style="max-width: 500px;margin:0 auto;border: 1px solid #1d293d;border-radius: 0.375rem; background-color: #020618; background-image: linear-gradient(to top, rgba(240, 177, 0, .10), rgba(255, 255, 255, 0));">
                <div style="padding: 2rem;">
                    <div style="margin-bottom: 15px;">
                        <img src="assets/images/logo-white.png" alt="" style="height: 1.2rem;margin: 0 auto;">
                    </div>
                    <h6 style="font-size: 24px;font-weight: bold;text-align: center;max-width: 300px;margin: 0 auto 20px;color: #fff;">Shout our with a <span style="color: #358ffc;">NewsLetter Projects</span></h6>
                    <img src="assets/images/email/templates/news.png" alt="" style="margin: 0 auto;display: block;">
                    <p style="color: #62748e; font-size: 15px;text-align: center;max-width: 400px;margin: 20px auto 16px;">Working with an easy-to-use editor will ne a domiex. Your future you will thank you.</p>
                    <div style="text-align: center;">
                        <button type="button" style="display: inline-block;background-color: #358ffc;color: #fff;cursor: pointer;padding: 0.5625rem 1.5rem;font-size: 0.875rem;border-radius: 0.375rem;">
                            Start a Project
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" style="width: 20px;height: 20px;display: inline-block;">
                                <path d="M16.1716 10.9999L10.8076 5.63589L12.2218 4.22168L20 11.9999L12.2218 19.778L10.8076 18.3638L16.1716 12.9999H4V10.9999H16.1716Z"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>

    </div>
    {{> partials/footer }}
</div>

{{> partials/vendor-scripts }}

<script type="module" src="assets/js/main.js"></script>

</body>
</html>