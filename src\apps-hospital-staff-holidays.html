{{> partials/main }}

<head>

    {{> partials/title-meta title="Holidays" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}
{{> partials/page-heading title="Holidays" sub-title="Staff" }}

<div class="grid grid-cols-12 gap-x-space" x-data="holidaysTable()">
    <div class="col-span-12 xl:col-span-4 2xl:col-span-3">
        <div class="bg-gray-100 card dark:bg-dark-850">
            <div class="card-body">
                <div class="mx-5 mb-4">
                    <img src="assets/images/hospital/img-01.png" alt="">
                </div>
                <div class="flex flex-col gap-4">
                    <div class="relative pl-5 before:absolute before:left-0 before:top-1.5 before:size-2 before:bg-primary-500 before:rounded-full">
                        <p class="mb-2 badge badge-gray">Today - 21 Jan, 2024</p>
                        <h6>World Religion Day</h6>
                    </div>
                    <div class="relative pl-5 before:absolute before:left-0 before:top-1.5 before:size-2 before:bg-yellow-500 before:rounded-full">
                        <p class="mb-2 badge badge-gray">Upcoming - 04 Feb, 2024</p>
                        <h6>World Cancer Day</h6>
                    </div>
                    <div class="relative pl-5 before:absolute before:left-0 before:top-1.5 before:size-2 before:bg-red-500 before:rounded-full">
                        <p class="mb-2 badge badge-gray">Upcoming - 25 May, 2024</p>
                        <h6>African Liberation Day</h6>
                    </div>
                    <div class="relative pl-5 before:absolute before:left-0 before:top-1.5 before:size-2 before:bg-red-500 before:rounded-full">
                        <p class="mb-2 badge badge-gray">Upcoming - 29 Sep, 2024</p>
                        <h6>World Heart Day</h6>
                    </div>
                </div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 xl:col-span-8 2xl:col-span-9 card">
        <div class="flex items-center gap-3 card-header">
            <h6 class="card-title grow">Holiday</h6>
            <button type="button" class="btn btn-primary shrink-0" @click="handleModal('showAddHolidayForm')" data-modal-target="addHolidayModal"><i data-lucide="circle-plus" class="inline-block mr-1 size-4"></i> Add Holiday</button>
        </div>
        <div class="pt-0 card-body">
            <div>
                <div data-simplebar class="table-box whitespace-nowrap">
                    <table class="table flush">
                        <tbody>
                            <tr class="text-gray-500 bg-gray-100 dark:bg-dark-850 dark:text-dark-500">
                                <th x-on:click="sort('name')" class="!font-medium cursor-pointer">Holiday Name <span x-show="sortBy === 'name'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th x-on:click="sort('date')" class="!font-medium cursor-pointer">Date <span x-show="sortBy === 'date'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th x-on:click="sort('day')" class="!font-medium cursor-pointer">Day <span x-show="sortBy === 'day'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th class="!font-medium">Action</th>
                            </tr>
                            <template x-for="(holiday, index) in displayedHolidays" :key="index">
                                <tr class="*:px-3 *:py-2.5">
                                    <td x-text="holiday.name"></td>
                                    <td x-text="holiday.formattedDate"></td>
                                    <td x-text="holiday.day"></td>
                                    <td>
                                        <div class="flex items-center gap-2">
                                            <button class="btn btn-sub-gray btn-icon !size-8 rounded-md" title="edit" @click="editHoliday(index)" data-modal-target="addHolidayModal"><i class="ri-pencil-line"></i></button>
                                            <button class="btn btn-sub-red btn-icon !size-8 rounded-md" title="delete" @click="deleteHolidayList = holiday.holidayID" data-modal-target="deleteModal"><i class="ri-delete-bin-line"></i></button>
                                        </div>
                                    </td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                </div>
                <div class="grid grid-cols-12 gap-5 mt-5 items-center">
                    <div class="col-span-12 md:col-span-6 justify-center md:justify-start flex flex-wrap">
                        <p class="text-gray-500 dark:text-dark-500">Showing <b x-text="showingStart"></b> - <b x-text="showingEnd"></b> of <b x-text="holidays.length"></b> Results</p>
                    </div>
                    <div class="col-span-12 md:col-span-6">
                        <div class="flex justify-center md:justify-end pagination pagination-primary">
                            <button @click="prevPage()" :disabled="currentPage === 1" class="pagination-pre">
                                <i data-lucide="chevron-left" class="mr-1 ltr:inline-block rtl:hidden size-4"></i>
                                <i data-lucide="chevron-right" class="ml-1 ltr:hidden rtl:inline-block size-4"></i>
                                Prev
                            </button>
                            <template x-for="page in totalPages" :key="page">
                                <button @click="gotoPage(page)" :class="{ 'active': currentPage === page }" class="pagination-item">
                                    <span x-text="page"></span>
                                </button>
                            </template>
                            <button @click="nextPage()" :disabled="currentPage === totalPages" class="pagination-next">
                                Next
                                <i data-lucide="chevron-right" class="ml-1 ltr:inline-block rtl:hidden size-4"></i>
                                <i data-lucide="chevron-left" class="mr-1 ltr:hidden rtl:inline-block size-4"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div><!--end col-->

    <div id="addHolidayModal" class="!hidden modal show" :class="{'show d-block': showAddHolidayForm || showEditHolidayForm}" x-show="showAddHolidayForm || showEditHolidayForm">
        <div class="modal-wrap modal-center">
            <div class="modal-header">
                <h6 class="modal-title" x-text="showAddHolidayForm ? 'Add Holiday' : 'Edit Holiday'">Add Holiday</h6>
                <button data-modal-close="addHolidayModal" class="link link-red"><i data-lucide="x" class="size-5"></i></button>
            </div>
            <div class="modal-content">
                <form action="#!">
                    <div class="grid grid-cols-12 gap-space">
                        <div class="col-span-12">
                            <label for="holidayInput" class="form-label">Holiday <span class="text-red-500">*</span></label>
                            <input type="text" id="holidayInput" class="form-input" placeholder="Enter holiday name" x-model="holidayForm.name" @input="validateField('name', holidayForm.name, 'Holiday name is required.')">
                            <span x-show="errors.name" class="text-red-500" x-text="errors.name"></span>
                        </div>
                        <div class="col-span-12">
                            <label for="dateInput" class="form-label">Date <span class="text-red-500">*</span></label>
                            <input type="text" id="dateInput" class="form-input" placeholder="Select date" data-provider="flatpickr" data-date-format="Y-m-d" x-model="holidayForm.date" @input="validateField('date', holidayForm.date, 'Date is required.')">
                            <span x-show="errors.date" class="text-red-500" x-text="errors.date"></span>
                        </div>
                        <div class="col-span-12">
                            <label for="daysInput" class="form-label">Day Name <span class="text-red-500">*</span></label>
                            <input type="text" id="daysInput" class="form-input" placeholder="Enter date name" x-model="holidayForm.day" @input="validateField('day', holidayForm.day, 'Day is required.')">
                            <span x-show="errors.day" class="text-red-500" x-text="errors.day"></span>
                        </div>
                        <div class="col-span-12">
                            <div class="flex items-center justify-end gap-2">
                                <button type="button" class="btn btn-active-red" data-modal-close="addHolidayModal"><i data-lucide="x" class="inline-block size-4"></i> <span class="align-baseline">Close</span></button>
                                <a href="#!" class="btn btn-primary" x-text="showAddHolidayForm ? 'Add Holiday' : 'Update Holiday'" @click="submitForm()"><i data-lucide="plus" class="inline-block mr-1 size-4"></i> Add Holiday</a>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!--delete modal-->
    <div id="deleteModal" class="!hidden modal show">
        <div class="modal-wrap modal-xs modal-center">
            <div class="text-center modal-content p-7">
                <div class="flex items-center justify-center mx-auto mb-4 text-red-500 rounded-full bg-red-500/10 size-14 backdrop-blur-xl">
                    <i data-lucide="trash-2" class="size-6"></i>
                </div>
                <h5 class="mb-4">Are you sure you want to delete this holiday ?</h5>
                <div class="flex items-center justify-center gap-2">
                    <button class="btn btn-red" data-modal-close="deleteModal" @click="deleteHoliday()">Delete</button>
                    <button data-modal-close="deleteModal" class="btn link link-primary">Cancel</button>
                </div>
            </div>
        </div>
    </div><!--end-->
</div><!--end grid-->
</div>
{{> partials/footer }}
</div>


{{> partials/vendor-scripts }}

<script type="module" src="assets/js/apps/hospital/staff/holidays.init.js"></script>

<script type="module" src="assets/js/main.js"></script>

</body>
</html>