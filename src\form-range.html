{{> partials/main }}

<head>

    {{> partials/title-meta title="Range Slider" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}

{{> partials/page-heading title="Range Slider" sub-title="Forms" }}

<div class="grid grid-cols-12 gap-x-space">
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Default Example</h6>
        </div>
        <div class="flex flex-col gap-5 card-body">
            <input type="range" min="0" max="10" step="1" value="1" class="input-range">
            <input type="range" min="0" max="10" step="1" value="2" class="input-range range-purple">
            <input type="range" min="0" max="10" step="1" value="3" class="input-range range-green">
            <input type="range" min="0" max="10" step="1" value="4" class="input-range range-red">
            <input type="range" min="0" max="10" step="1" value="5" class="input-range range-yellow">
            <input type="range" min="0" max="10" step="1" value="6" class="input-range range-sky">
            <input type="range" min="0" max="10" step="1" value="7" class="input-range range-orange">
            <input type="range" min="0" max="10" step="1" value="8" class="input-range range-pink">
            <input type="range" min="0" max="10" step="1" value="9" class="input-range range-indigo">
            <input type="range" min="0" max="10" step="1" value="10" class="input-range range-dark">
        </div>
    </div><!--end col-->
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Basic</h6>
        </div>
        <div class="card-body">
            <div id="defaultSlider"></div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Using an array of values</h6>
        </div>
        <div class="card-body">
            <div class="flex items-center justify-center h-20">
                <div id="arrayValuesSlider" class="w-full"></div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Using arbitrary (string) values</h6>
        </div>
        <div class="card-body">
            <div class="flex items-center justify-center h-20">
                <div id="arbitrary-values-slider" class="w-full"></div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Slider Color</h6>
        </div>
        <div class="card-body">
            <div class="flex items-center justify-center h-20">
                <div id="slider-color" class="w-full"></div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Slider Toggle</h6>
        </div>
        <div class="card-body">
            <div class="flex items-center justify-center h-20">
                <div id="slider-toggle"></div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Soft Limit</h6>
        </div>
        <div class="card-body">
            <div class="flex items-center justify-center h-20">
                <div id="soft-limit" class="w-full"></div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Overlapping Tooltip</h6>
        </div>
        <div class="card-body">
            <div class="flex items-center justify-center h-20">
                <div id="overlapping-tooltip" class="w-full"></div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Colorpicker</h6>
        </div>
        <div class="card-body">
            <div class="flex items-center justify-center">
                <div id="colorpicker">
                    <div class="sliders" id="red"></div>
                    <div class="sliders" id="green"></div>
                    <div class="sliders" id="blue"></div>
                    <div id="result"></div>
                </div>
            </div>
        </div>
    </div><!--end col-->
</div><!--end grid-->

</div>
{{> partials/footer }}
</div>


{{> partials/vendor-scripts }}
<script type="module" src="assets/js/form/range.init.js"></script>
<script type="module" src="assets/js/main.js"></script>

</body>
</html>