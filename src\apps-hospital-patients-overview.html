{{> partials/main }}

<head>

    {{> partials/title-meta title="Overview" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}
{{> partials/page-heading title="Overview" sub-title="Patients" }}

<div class="grid grid-cols-12 gap-x-space">
    <div class="col-span-12 card">
        <div class="card-body">
            <div class="flex flex-wrap gap-5">
                <div class="shrink">
                    <img src="assets/images/avatar/user-5.png" alt="" class="rounded-md size-40">
                </div>
                <div class="grow">
                    <h6 class="mb-1 text-16"><PERSON></h6>
                    <div class="overflow-x-auto">
                        <div class="flex flex-wrap gap-3 item-center *:flex *:items-center">
                            <p><i class="ltr:mr-1 rtl:ml-1 ri-user-3-line"></i> <span class="text-gray-500 dark:text-dark-500">Female</span></p>
                            <p><i class="ltr:mr-1 rtl:ml-1 ri-briefcase-line"></i> <span class="text-gray-500 dark:text-dark-500">Accountant</span></p>
                            <p><i class="ltr:mr-1 rtl:ml-1 ri-map-pin-2-line"></i> <span class="text-gray-500 dark:text-dark-500">California</span></p>
                            <p><i class="ltr:mr-1 rtl:ml-1 ri-calendar-event-line"></i> <span class="text-gray-500 dark:text-dark-500">13 Dec, 1998 (25 Years)</span></p>
                        </div>
                    </div>
                    <div>
                        <div class="flex flex-wrap items-center mt-5 gap-space">
                            <div class="p-4 text-center border border-gray-200 border-dashed rounded-md dark:border-dark-800 w-36 shrink-0">
                                <h4 class="mb-1">26 <small class="text-sm font-normal text-gray-500 dark:text-dark-500">May, 2024</small></h4>
                                <p class="text-gray-500 dark:text-dark-500">Appo. Date</p>
                            </div>
                            <div class="p-4 text-center border border-gray-200 border-dashed rounded-md dark:border-dark-800 w-36 shrink-0">
                                <h4 class="mb-1">115/50</h4>
                                <p class="text-gray-500 dark:text-dark-500">Blood Pressure</p>
                            </div>
                            <div class="p-4 text-center border border-gray-200 border-dashed rounded-md dark:border-dark-800 w-36 shrink-0">
                                <h4 class="mb-1">B+</h4>
                                <p class="text-gray-500 dark:text-dark-500">Blood Group</p>
                            </div>
                            <div class="p-4 text-center border border-gray-200 border-dashed rounded-md dark:border-dark-800 w-36 shrink-0">
                                <h4 class="mb-1">86 <small class="text-sm font-normal text-gray-500 dark:text-dark-500">kg</small></h4>
                                <p class="text-gray-500 dark:text-dark-500">Weight</p>
                            </div>
                            <div class="p-4 text-center border border-gray-200 border-dashed rounded-md dark:border-dark-800 w-36 shrink-0">
                                <h4 class="mb-1">178 <small class="text-sm font-normal text-gray-500 dark:text-dark-500">cm</small></h4>
                                <p class="text-gray-500 dark:text-dark-500">Height</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="shrink-0">
                    <div class="flex items-center gap-2">
                        <button class="btn btn-sub-green btn-icon" title="phone"><i data-lucide="phone" class="size-4"></i></button>
                        <button class="btn btn-sub-purple btn-icon" title="messages-square"><i data-lucide="messages-square" class="size-4"></i></button>
                        <button type="button" class="btn btn-primary" title="add"><i data-lucide="pencil" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> Edit</button>
                    </div>
                </div>
            </div>

            <div class="mt-5">
                <div class="grid grid-cols-12 gap-6">
                    <div class="col-span-12 lg:col-span-6 xl:col-span-3">
                        <p class="mb-1 text-gray-500 dark:text-dark-500">Family Doctor</p>
                        <h6>Dr. Vernon Locklin</h6>
                    </div>
                    <div class="col-span-12 lg:col-span-6 xl:col-span-3">
                        <p class="mb-1 text-gray-500 dark:text-dark-500">Assigned Doctor</p>
                        <h6>Dr. Ellie Maggie</h6>
                    </div>
                    <div class="col-span-12 lg:col-span-6 xl:col-span-3">
                        <p class="mb-1 text-gray-500 dark:text-dark-500">Referring Doctor</p>
                        <h6>Dr. Jasper Liewald</h6>
                    </div>
                    <div class="col-span-12 lg:col-span-6 xl:col-span-3">
                        <p class="mb-1 text-gray-500 dark:text-dark-500">Pharmacy Name</p>
                        <h6>ABC Pharmacy</h6>
                    </div>
                    <div class="col-span-12 lg:col-span-6 xl:col-span-3">
                        <div class="whitespace-normal">
                            <p class="mb-1 text-gray-500 dark:text-dark-500">Full Address</p>
                            <h6>Apt. 757 866 Truman Ridge, Bashirianside, OH 34623-2200</h6>
                        </div>
                    </div>
                    <div class="col-span-12 lg:col-span-6 xl:col-span-3">
                        <p class="mb-1 text-gray-500 dark:text-dark-500">Phone Number</p>
                        <h6>+33 1 42 68 53 00</h6>
                    </div>
                    <div class="col-span-12 lg:col-span-6 xl:col-span-3">
                        <p class="mb-1 text-gray-500 dark:text-dark-500">Emergency Number</p>
                        <h6>+34 91 123 45 67</h6>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-span-12 row-span-2 md:col-span-6 xl:col-span-3 card">
        <div class="card-header">
            <h6 class="card-title">Timeline</h6>
        </div>
        <div class="card-body">
            <div data-simplebar class="h-[20.8rem] -mx-space px-space">
                <ul class="timeline">
                    <li class="timeline-primary">
                        <span class="badge badge-gray">19 June, 2024 - 11:15 AM</span>
                        <h6 class="mt-3 mb-1">Follow Up </h6>
                        <p class="text-sm text-gray-500 dark:text-dark-500">Follow-up for rash, prescribed antihistamine</p>
                    </li>
                    <li class="timeline-primary">
                        <span class="badge badge-gray">25 May, 2024 - 09:00 AM</span>
                        <h6 class="mt-3 mb-1">Appointment</h6>
                        <p class="text-sm text-gray-500 dark:text-dark-500">Annual physical check-up</p>
                    </li>
                    <li class="timeline-primary">
                        <span class="badge badge-gray">21 May, 2024 - 03:30 PM</span>
                        <h6 class="mt-3 mb-1">Lab Test</h6>
                        <p class="text-sm text-gray-500 dark:text-dark-500">Blood test: Complete Blood Count (CBC)</p>
                    </li>
                    <li class="timeline-primary">
                        <span class="badge badge-gray">05 May, 2024 - 10:00 AM</span>
                        <h6 class="mt-3 mb-1">Medication Start</h6>
                        <p class="text-sm text-gray-500 dark:text-dark-500">Prescribed Atorvastatin 20mg for high cholesterol</p>
                    </li>
                </ul>
            </div>
        </div>
    </div>
    <div class="col-span-12 md:col-span-6 xl:col-span-3">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title">Insurance Overview</h6>
            </div>
            <div class="card-body">
                <div class="flex items-center gap-3">
                    <div class="flex items-center justify-center rounded-full text-primary-500 bg-primary-500/10 size-12">
                        <i data-lucide="heart-handshake" class="size-5"></i>
                    </div>
                    <div class="grow">
                        <h6 class="mb-1">HealthCare Insurance</h6>
                        <p class="text-gray-500 dark:text-dark-500">#157-PE9871-541</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="bg-green-500/10 border-green-500/20 card">
            <div class="card-body">
                <div class="flex items-center gap-3">
                    <div class="grow">
                        <img src="assets/images/others/insurance.png" alt="" class="size-14">
                        <h6 class="mt-5 mb-1">Get peace of mind with the right insurance coverage.</h6>
                        <p class="mb-3 text-gray-500 dark:text-dark-500">Receive your personalized quote in just a few clicks!</p>
                        <button type="button" class="btn btn-green">Buy Insurance</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-span-12 overflow-hidden xl:col-span-6 xl:row-span-2 card" x-data="reportsTable()">
        <div class="flex items-center gap-3 card-header">
            <h6 class="card-title grow">Reports</h6>
            <a href="#!" data-modal-target="addReportsModal" class="font-medium shrink-0 text-primary-500 link hover:text-primary-600"><i data-lucide="plus" class="inline-block mb-1 align-middle size-4"></i> Add Reports</a>
        </div>
        <div class="pt-0 card-body">
            <div>
                <div data-simplebar class="table-box">
                    <table class="table flush odd-striped whitespace-nowrap">
                        <tbody>
                            <template x-for="(report, index) in displayedReports" :key="index">
                                <tr class="*:px-3 *:py-2.5">
                                    <td x-text="report.name"></td>
                                    <td x-text="report.age"></td>
                                    <td x-text="report.date"></td>
                                    <td x-text="report.doctor"></td>
                                    <td>
                                        <span x-text="report.status" :class="{
                                            'badge badge-green': report.status === 'Completed',
                                            'badge badge-purple': report.status === 'In Progress',
                                            'badge badge-yellow': report.status === 'Pending'
                                        }"></span>
                                    </td>
                                    <td>
                                        <div class="flex items-center gap-2">
                                            <button title="Download" class="btn btn-sub-purple btn-icon !size-8 rounded-full"><i class="ri-download-2-line"></i></button>
                                            <button title="delete" class="btn btn-sub-red btn-icon !size-8 rounded-full" @click="openModel( index)" data-modal-target="deleteReportsModal"><i class="ri-delete-bin-line"></i></button>
                                        </div>
                                    </td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                </div>
                <div class="grid grid-cols-12 gap-5 mt-5 items-center">
                    <div class="col-span-12 md:col-span-6 justify-center md:justify-start flex flex-wrap">
                        <p class="text-gray-500 dark:text-dark-500">Showing <b x-text="showingStart"></b> - <b x-text="showingEnd"></b> of <b x-text="reports.length"></b> Results</p>
                    </div>
                    <div class="col-span-12 md:col-span-6">
                        <div class="flex justify-center md:justify-end pagination pagination-primary">
                            <button @click="prevPage()" :disabled="currentPage === 1" class="pagination-pre">
                                <i data-lucide="chevron-left" class="mr-1 ltr:inline-block rtl:hidden size-4"></i>
                                <i data-lucide="chevron-right" class="ml-1 ltr:hidden rtl:inline-block size-4"></i>
                                Prev
                            </button>
                            <template x-for="page in totalPages" :key="page">
                                <button @click="gotoPage(page)" :class="{ 'active': currentPage === page }" class="pagination-item">
                                    <span x-text="page"></span>
                                </button>
                            </template>
                            <button @click="nextPage()" :disabled="currentPage === totalPages" class="pagination-pre">
                                Next
                                <i data-lucide="chevron-right" class="ml-1 ltr:inline-block rtl:hidden size-4"></i>
                                <i data-lucide="chevron-left" class="mr-1 ltr:hidden rtl:inline-block size-4"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--delete modal-->
        <div id="deleteReportsModal" class="!hidden modal show" x-show="deleteReportModal">
            <div class="modal-wrap modal-xs modal-center">
                <div class="text-center modal-content p-7">
                    <div class="flex items-center justify-center mx-auto mb-4 text-red-500 rounded-full bg-red-500/10 size-14 backdrop-blur-xl">
                        <i data-lucide="trash-2" class="size-6"></i>
                    </div>
                    <h5 class="mb-4">Are you sure you want to delete this Contact ?</h5>
                    <div class="flex items-center justify-center gap-2">
                        <button class="btn btn-red" @click="deleteReports()" data-modal-close="deleteMedicineModal">Delete</button>
                        <button data-modal-close="deleteReportsModal" class="btn link link-primary">Cancel</button>
                    </div>
                </div>
            </div>
        </div><!--end-->


        <!--Add Reports-->
        <div id="addReportsModal" class="!hidden modal show" x-show="showAddReportForm">
            <div class="modal-wrap modal-center">
                <div class="modal-header">
                    <h6 class="modal-title">Add Report</h6>
                    <button data-modal-close="addReportsModal" class="link link-red"><i data-lucide="x" class="size-5"></i></button>
                </div>
                <div class="modal-content">
                    <form action="#!">
                        <div class="grid grid-cols-12 gap-space">
                            <div class="col-span-12">
                                <label for="reportTypeSelect" class="form-label">Report Type <span class="text-red-500">*</span></label>
                                <div id="reportTypeSelect" placeholder="Select Report Type" x-model="reportForm.reportType" @change="validateField('reportType', document.getElementById('reportTypeSelect') , 'Report type is required.')"></div>
                                <span x-show="errors.reportType" class="text-red-500" x-text="errors.reportType"></span>
                            </div>
                            <div class="col-span-12">
                                <label for="dateInput" class="form-label">Date <span class="text-red-500">*</span></label>
                                <input type="text" id="dateInput" class="form-input" placeholder="Select date" x-model="reportForm.date" data-provider="flatpickr" data-date-format="d M, Y" @input="validateField('date', reportForm.date , 'Date is required.')">
                                <span x-show="errors.date" class="text-red-500" x-text="errors.date"></span>
                            </div>
                            <div class="col-span-12">
                                <label for="doctorNameInput" class="form-label">Doctor <span class="text-red-500">*</span></label>
                                <input type="text" id="doctorNameInput" class="form-input" placeholder="Enter doctor name" x-model="reportForm.doctor" @input="validateField('doctor', reportForm.doctor , 'Doctor name is required.')">
                                <span x-show="errors.doctor" class="text-red-500" x-text="errors.doctor"></span>
                            </div>
                            <div class="col-span-12">
                                <label for="textareaInput2" class="form-label">Clinical Details</label>
                                <textarea name="textareaInput2" id="textareaInput2" rows="3" class="h-auto form-input" placeholder="Enter your description" x-model="reportForm.clientDetails" @input="validateField('clientDetails', reportForm.clientDetails , 'Client details is required.')"></textarea>
                                <span x-show="errors.clientDetails" class="text-red-500" x-text="errors.clientDetails"></span>
                            </div>
                            <div class="col-span-6">
                                <label for="impressionsInput" class="form-label">Impressions</label>
                                <input type="text" id="impressionsInput" class="form-input" placeholder="Impressions" x-model="reportForm.impressions" @input="validateField('impressions', reportForm.impressions , 'Impressions is required.')">
                                <span x-show="errors.impressions" class="text-red-500" x-text="errors.impressions"></span>
                            </div>
                            <div class="col-span-6">
                                <label for="recommendationsSelect" class="form-label">Recommendations</label>
                                <div id="recommendationsSelect" placeholder="Select Recommendations" x-model="reportForm.recommendations" @change="validateField('recommendations', document.getElementById('recommendationsSelect') , 'Recommendations is required.')"></div>
                                <span x-show="errors.recommendations" class="text-red-500" x-text="errors.recommendations"></span>
                            </div>
                            <div class="col-span-12">
                                <label for="statusSelect" class="form-label">Status <span class="text-red-500">*</span></label>
                                <div id="statusSelect" placeholder="Select Status" x-model="reportForm.status" @change="validateField('status', document.getElementById('statusSelect') , 'Status is required.')"></div>
                                <span x-show="errors.status" class="text-red-500" x-text="errors.status"></span>
                            </div>
                            <div class="col-span-12">
                                <div class="flex items-center justify-end gap-2">
                                    <button type="button" class="btn btn-active-red" data-modal-close="addReportsModal"><i data-lucide="x" class="inline-block size-4"></i> <span class="align-baseline">Close</span></button>
                                    <a href="#!" class="btn btn-primary" @click="submitReport(); "><i data-lucide="plus" class="inline-block mr-1 size-4"></i> Add Report</a>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <div class="col-span-12 overflow-hidden card" x-data="medicineTable()">
        <div class="flex items-center gap-3 card-header">
            <h6 class="card-title grow">Medicine History</h6>
            <a href="#!" data-modal-target="addMedicineModal" @click="handleModal('showAddMedicineForm')" class="font-medium shrink-0 text-primary-500 link hover:text-primary-600"><i data-lucide="plus" class="inline-block mb-1 align-middle size-4"></i> Add Medicine</a>
        </div>
        <div class="pt-0 card-body">
            <div>
                <div data-simplebar class="table-box whitespace-nowrap">
                    <table class="table flush whitespace-nowrap">
                        <tbody>
                            <tr class="text-gray-500 bg-gray-100 dark:bg-dark-850 dark:text-dark-500">
                                <th x-on:click="sort('date')" class="!font-medium cursor-pointer">Date <span x-show="sortBy === 'date'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th x-on:click="sort('time')" class="!font-medium cursor-pointer">Time <span x-show="sortBy === 'time'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th x-on:click="sort('medicineName')" class="!font-medium cursor-pointer">Medicine Name <span x-show="sortBy === 'medicineName'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th x-on:click="sort('dosage')" class="!font-medium cursor-pointer">Dosage <span x-show="sortBy === 'dosage'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th x-on:click="sort('frequency')" class="!font-medium cursor-pointer">Frequency <span x-show="sortBy === 'frequency'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th x-on:click="sort('startDate')" class="!font-medium cursor-pointer">Start Date <span x-show="sortBy === 'startDate'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th x-on:click="sort('endDate')" class="!font-medium cursor-pointer">End Date <span x-show="sortBy === 'endDate'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th x-on:click="sort('prescribingDoctor')" class="!font-medium cursor-pointer">Prescribing Doctor <span x-show="sortBy === 'prescribingDoctor'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th x-on:click="sort('reasonCondition')" class="!font-medium cursor-pointer">Reason/Condition <span x-show="sortBy === 'reasonCondition'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th x-on:click="sort('notes')" class="!font-medium cursor-pointer">Notes <span x-show="sortBy === 'notes'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th class="!font-medium">Action</th>
                            </tr>
                            <template x-for="(medicine, index) in displayedMedicine" :key="index">
                                <tr class="*:px-3 *:py-2.5">
                                    <td x-text="medicine.date"></td>
                                    <td x-text="medicine.time"></td>
                                    <td x-text="medicine.medicineName"></td>
                                    <td x-text="medicine.dosage"></td>
                                    <td x-text="medicine.frequency"></td>
                                    <td x-text="medicine.startDate"></td>
                                    <td x-text="medicine.endDate"></td>
                                    <td x-text="medicine.prescribingDoctor"></td>
                                    <td x-text="medicine.reasonCondition"></td>
                                    <td x-text="medicine.notes"></td>
                                    <td>
                                        <div class="flex items-center gap-2">
                                            <button class="btn btn-sub-purple btn-icon !size-8 rounded-full" title="edit" @click="editMedicine(medicine.medicineID)" data-modal-target="addMedicineModal"><i class="ri-pencil-line"></i></button>
                                            <button class="btn btn-sub-red btn-icon !size-8 rounded-full" title="delete" @click="openMedicineDeleteModel(index)" data-modal-target="deleteMedicineModal"><i class="ri-delete-bin-line"></i></button>
                                        </div>
                                    </td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                </div>
                <div class="grid grid-cols-12 gap-5 mt-5 items-center">
                    <div class="col-span-12 md:col-span-6 justify-center md:justify-start flex flex-wrap">
                        <p class="text-gray-500 dark:text-dark-500">Showing <b x-text="showingStart"></b> - <b x-text="showingEnd"></b> of <b x-text="medicines.length"></b> Results</p>
                    </div>
                    <div class="col-span-12 md:col-span-6 items-center">
                        <div class="flex justify-center md:justify-end pagination pagination-primary">
                            <button @click="prevPage()" :disabled="currentPage === 1" class="pagination-pre">
                                <i data-lucide="chevron-left" class="mr-1 ltr:inline-block rtl:hidden size-4"></i>
                                <i data-lucide="chevron-right" class="ml-1 rtl:inline-block ltr:hidden size-4"></i>
                                Prev
                            </button>
                            <template x-for="page in totalPages" :key="page">
                                <button @click="gotoPage(page)" :class="{ 'active': currentPage === page }" class="pagination-item">
                                    <span x-text="page"></span>
                                </button>
                            </template>
                            <button @click="nextPage()" :disabled="currentPage === totalPages" class="pagination-next">
                                Next
                                <i data-lucide="chevron-right" class="ml-1 ltr:inline-block rtl:hidden size-4"></i>
                                <i data-lucide="chevron-left" class="mr-1 ltr:hidden rtl:inline-block size-4"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!--delete modal-->
        <div id="deleteMedicineModal" class="!hidden modal show" x-show="deleteMedicineModel">
            <div class="modal-wrap modal-xs modal-center">
                <div class="text-center modal-content p-7">
                    <div class="flex items-center justify-center mx-auto mb-4 text-red-500 rounded-full bg-red-500/10 size-14 backdrop-blur-xl">
                        <i data-lucide="trash-2" class="size-6"></i>
                    </div>
                    <h5 class="mb-4">Are you sure you want to delete this Contact ?</h5>
                    <div class="flex items-center justify-center gap-2">
                        <button class="btn btn-red" data-modal-close="deleteMedicineModal" @click="deleteMedicines()">Delete</button>
                        <button data-modal-close="deleteMedicineModal" class="btn link link-primary">Cancel</button>
                    </div>
                </div>
            </div>
        </div><!--end-modal-->
        <!--Add medicine-->
        <div id="addMedicineModal" class="!hidden modal show" :class="{'show d-block': showAddMedicineForm || showEditMedicineForm}" x-show="showAddMedicineForm || showEditMedicineForm">
            <div class="modal-wrap modal-center">
                <div class="modal-header">
                    <h6 class="modal-title" x-text="showAddMedicineForm ? 'Add Medicine' : 'Edit Medicine' ">Add Medicine</h6>
                    <button data-modal-close="addMedicineModal" class="link link-red"><i data-lucide="x" class="size-5"></i></button>
                </div>
                <div class="modal-content">
                    <form action="#!">
                        <div class="grid grid-cols-12 gap-space">
                            <div class="col-span-12">
                                <label for="medicineNameInput" class="form-label">Medicine Name <span class="text-red-500">*</span></label>
                                <input type="text" id="medicineNameInput" class="form-input" placeholder="Enter medicine name" x-model="medicineForm.medicineName" @input="validateField('medicineName', medicineForm.medicineName, 'Medicine name is required.')">
                                <span x-show="errors.medicineName" class="text-red-500" x-text="errors.medicineName"></span>
                            </div>
                            <div class="col-span-6">
                                <label for="dosageInput" class="form-label">Dosage <span class="text-red-500">*</span></label>
                                <input type="text" id="dosageInput" class="form-input" placeholder="0mg" x-model="medicineForm.dosage" @input="validateField('dosage', medicineForm.dosage, 'Dosage is required.')">
                                <span x-show="errors.dosage" class="text-red-500" x-text="errors.dosage"></span>
                            </div>
                            <div class="col-span-6">
                                <label for="frequencyInput" class="form-label">Frequency <span class="text-red-500">*</span></label>
                                <input type="text" id="frequencyInput" class="form-input" placeholder="Enter frequency" x-model="medicineForm.frequency" @input="validateField('frequency', medicineForm.frequency, 'Frequency is required.')">
                                <span x-show="errors.frequency" class="text-red-500" x-text="errors.frequency"></span>
                            </div>
                            <div class="col-span-6">
                                <label for="medicineStartDateInput" class="form-label">Start Date <span class="text-red-500">*</span></label>
                                <input id="medicineStartDateInput" class="form-input" placeholder="Select date" data-provider="flatpickr" data-date-format="Y-m-d" x-model="medicineForm.startDate" @input="validateField('startDate', medicineForm.startDate, 'Start date is required.')">
                                <span x-show="errors.startDate" class="text-red-500" x-text="errors.startDate"></span>
                            </div>
                            <div class="col-span-6">
                                <label for="medicineEndDateInput" class="form-label">End Date <span class="text-red-500">*</span></label>
                                <input type="text" id="medicineEndDateInput" class="form-input" placeholder="Select date" data-provider="flatpickr" data-date-format="Y-m-d" x-model="medicineForm.endDate" @input="validateField('endDate', medicineForm.endDate, 'End date is required.')">
                                <span x-show="errors.endDate" class="text-red-500" x-text="errors.endDate"></span>
                            </div>
                            <div class="col-span-12">
                                <label for="prescribingDoctorNameInput" class="form-label">Prescribing Doctor <span class="text-red-500">*</span></label>
                                <input type="text" id="prescribingDoctorNameInput" class="form-input" placeholder="Enter prescribing doctor" x-model="medicineForm.prescribingDoctor" @input="validateField('prescribingDoctor', medicineForm.prescribingDoctor, 'Priscribed doctor is required.')">
                                <span x-show="errors.prescribingDoctor" class="text-red-500" x-text="errors.prescribingDoctor"></span>
                            </div>
                            <div class="col-span-12">
                                <label for="reasonConditionInput" class="form-label">Reason/Condition <span class="text-red-500">*</span></label>
                                <input type="text" id="reasonConditionInput" class="form-input" placeholder="Reason/Condition" x-model="medicineForm.reasonCondition" @input="validateField('reasonCondition', medicineForm.reasonCondition, 'Reason is required.')">
                                <span x-show="errors.reasonCondition" class="text-red-500" x-text="errors.reasonCondition"></span>
                            </div>
                            <div class="col-span-12">
                                <label for="textareaInput2" class="form-label">Notes</label>
                                <textarea name="textareaInput2" id="textareaInput2" rows="2" class="h-auto form-input" placeholder="Enter notes" x-model="medicineForm.notes" @input="validateField('notes', medicineForm.notes, 'Notes is required.')"></textarea>
                                <span x-show="errors.notes" class="text-red-500" x-text="errors.notes"></span>
                            </div>
                            <div class="col-span-12">
                                <div class="flex items-center justify-end gap-2">
                                    <button type="button" class="btn btn-active-red" data-modal-close="addMedicineModal"><i data-lucide="x" class="inline-block size-4"></i> <span class="align-baseline">Close</span></button>
                                    <a href="#!" class="btn btn-primary" x-text="showAddMedicineForm ? 'Add Medicine' : 'Edit Medicine'" @click="submitForm($event)"><i data-lucide="plus" class="inline-block mr-1 size-4"></i> Add Medicine</a>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <div class="col-span-12 overflow-hidden card" x-data="appointmentsTable()">
        <div class="flex items-center gap-3 card-header">
            <h6 class="card-title grow">Appointments History</h6>
            <a href="#!" class="font-medium shrink-0 text-primary-500 link hover:text-primary-600"><i data-lucide="plus" class="inline-block mb-1 align-middle size-4"></i> New Appointment</a>
        </div>
        <div class="pt-0 card-body">
            <div>
                <div data-simplebar class="table-box">
                    <table class="table whitespace-nowrap">
                        <tbody>
                            <template x-for="(appointment, index) in displayedAppointments" :key="index">
                                <tr class="*:px-3 *:py-2.5">
                                    <td x-text="appointment.date"></td>
                                    <td x-text="appointment.treatmentType"></td>
                                    <td x-text="appointment.time"></td>
                                    <td x-text="appointment.reasonCondition"></td>
                                    <td x-text="appointment.notes"></td>
                                    <td x-text="appointment.doctor"></td>
                                    <td>
                                        <span x-text="appointment.status" :class="{
                                            'badge badge-green': appointment.status === 'Completed',
                                            'badge badge-red': appointment.status === 'Expired',
                                            'badge badge-sky': appointment.status === 'New',
                                            'badge badge-purple': appointment.status === 'Confirmed',
                                            'badge badge-yellow': appointment.status === 'Pending'
                                        }"></span>
                                    </td>
                                    <td>
                                        <div class="flex items-center gap-2">
                                            <button class="btn btn-sub-gray btn-icon !size-8" title="overview"><i class="ri-eye-line"></i></button>
                                            <button class="btn btn-sub-gray btn-icon !size-8" title="edit"><i class="ri-pencil-line"></i></button>
                                            <button class="btn btn-sub-red btn-icon !size-8" title="delete" @click="openAppointmentDeleteModel(index)" data-modal-target="deleteModalAppointment"><i class="ri-delete-bin-line"></i></button>
                                        </div>
                                    </td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                </div>
                <div class="grid grid-cols-12 gap-5 mt-5 items-center">
                    <div class="col-span-12 md:col-span-6 justify-center md:justify-start flex flex-wrap">
                        <p class="text-gray-500 dark:text-dark-500">Showing <b x-text="showingStart"></b> - <b x-text="showingEnd"></b> of <b x-text="appointments.length"></b> Results</p>
                    </div>
                    <div class="col-span-12 md:col-span-6">
                        <div class="flex justify-center md:justify-end pagination pagination-primary">
                            <button @click="prevPage()" :disabled="currentPage === 1" class="pagination-pre">
                                <i data-lucide="chevron-left" class="mr-1 ltr:inline-block rtl:hidden size-4"></i>
                                <i data-lucide="chevron-right" class="ml-1 ltr:hidden rtl:inline-block size-4"></i>
                                Prev
                            </button>
                            <template x-for="page in totalPages" :key="page">
                                <button @click="gotoPage(page)" :class="{ 'active': currentPage === page }" class="pagination-item">
                                    <span x-text="page"></span>
                                </button>
                            </template>
                            <button @click="nextPage()" :disabled="currentPage === totalPages" class="pagination-next">
                                Next
                                <i data-lucide="chevron-right" class="ml-1 ltr:inline-block rtl:hidden size-4"></i>
                                <i data-lucide="chevron-left" class="mr-1 ltr:hidden rtl:inline-block size-4"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--delete modal-->
        <div id="deleteModalAppointment" class="!hidden modal show" x-show="deleteAppointmentModel">
            <div class="modal-wrap modal-xs modal-center">
                <div class="text-center modal-content p-7">
                    <div class="flex items-center justify-center mx-auto mb-4 text-red-500 rounded-full bg-red-500/10 size-14 backdrop-blur-xl">
                        <i data-lucide="trash-2" class="size-6"></i>
                    </div>
                    <h5 class="mb-4">Are you sure you want to delete this Data ?</h5>
                    <div class="flex items-center justify-center gap-2">
                        <button class="btn btn-red" data-modal-close="deleteModalAppointment" @click="deleteAppointment()">Delete</button>
                        <button data-modal-close="deleteModalAppointment" class="btn link link-primary">Cancel</button>
                    </div>
                </div>
            </div>
        </div><!--end-modal-->
    </div>
</div>

</div>
{{> partials/footer }}
</div>

{{> partials/vendor-scripts }}

<script type="module" src="assets/js/apps/hospital/patients/overview.init.js"></script>

<script type="module" src="assets/js/main.js"></script>
</body>
</html>