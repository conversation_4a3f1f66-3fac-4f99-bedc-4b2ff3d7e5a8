{{> partials/main }}

<head>

    {{> partials/title-meta title="Store Configuration" }}

    <link rel="stylesheet" href="/assets/libs/virtual-select-plugin/virtual-select.min.css">

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}
{{> partials/page-heading title="Store Configuration" sub-title="POS" }}

<div class="grid grid-cols-12 gap-4 xl:gap-x-8">
    <div class="col-span-12 2xl:col-span-3">
        <nav class="card sticky top-24">
            <div class="card-body">
                <ul class="space-y-3 *:*:flex *:*:items-center *:*:px-3 *:*:py-2 *:*:text-sm *:*:font-medium *:*:rounded-md *:*:text-gray-500 *:*:dark:text-dark-500 *:*:hover:not-[&.active]:bg-gray-100 *:*:[&.active]:text-primary-500 *:*:[&.active]:bg-primary-500/10 *:*:link">
                    <li>
                        <a href="#store-info" class="active">
                            <i data-lucide="warehouse" class="size-4 ltr:mr-3 rtl:ml-3 shrink-0"></i>
                            <span class="grow">Store Information</span>
                        </a>
                    </li>
                    <li>
                        <a href="#tax-settings">
                            <i data-lucide="file-text" class="size-4 ltr:mr-3 rtl:ml-3 shrink-0"></i>
                            Tax Settings
                        </a>
                    </li>
                    <li>
                        <a href="#payment-methods">
                            <i data-lucide="credit-card" class="size-4 ltr:mr-3 rtl:ml-3 shrink-0"></i>
                            Payment Methods
                        </a>
                    </li>
                    <li>
                        <a href="#receipt-settings">
                            <i data-lucide="save" class="size-4 ltr:mr-3 rtl:ml-3 shrink-0"></i>
                            Receipt Settings
                        </a>
                    </li>
                    <li>
                        <a href="#users">
                            <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                            </svg>
                            User Management
                        </a>
                    </li>
                    <li>
                        <a href="#hardware">
                            <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                            </svg>
                            Hardware Setup
                        </a>
                    </li>
                </ul>
            </div>
        </nav>
    </div>
    <div class="col-span-12 2xl:col-span-9 space-y-8">
        <section id="store-info" class="card">
            <div class="card-header">
                <h6 class="card-title mb-1">Store Information</h6>
                <p class="text-gray-500 dark:text-dark-500 text-sm">Basic information about your store</p>
            </div>
            <div class="card-body">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="form-label">Store Name</label>
                        <input type="text" class="form-input" placeholder="Enter store name" value="Domiex POS">
                    </div>
                    <div>
                        <label class="form-label">Store ID</label>
                        <input type="text" class="form-input read-only:bg-gray-100 dark:read-only:bg-dark-850" placeholder="Auto-generated" value="QM001" readonly>
                    </div>
                    <div class="md:col-span-2">
                        <label class="form-label">Address</label>
                        <textarea class="form-input h-auto" rows="3" placeholder="Enter store address">123 Main Street, Downtown District, City 12345</textarea>
                    </div>
                    <div>
                        <label class="form-label">Phone Number</label>
                        <input type="tel" class="form-input" placeholder="+****************" value="+****************">
                    </div>
                    <div>
                        <label class="form-label">Email</label>
                        <input type="email" class="form-input" placeholder="<EMAIL>" value="<EMAIL>">
                    </div>
                </div>
            </div>
        </section>

        <!-- Tax Settings -->
        <section id="tax-settings" class="card">
            <div class="card-header">
                <h2 class="card-title mb-1">Tax Settings</h2>
                <p class="text-gray-500 dark:text-dark-500 text-sm">Configure tax rates and calculations</p>
            </div>
            <div class="card-body">
                <div class="space-y-5">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-14 mb-1">Enable Tax Calculation</h3>
                            <p class="text-sm text-gray-500 dark:text-dark-500">Automatically calculate taxes on sales</p>
                        </div>
                        <label for="togglePrimary" class="switch-group">
                            <div class="relative">
                                <input type="checkbox" id="togglePrimary" class="sr-only peer">
                                <div class="switch-wrapper"></div>
                                <div class="switch-dot peer-checked:translate-x-full rtl:peer-checked:-translate-x-full switch-primary"></div>
                            </div>
                        </label>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-5">
                        <div>
                            <label class="form-label">Default Tax Rate (%)</label>
                            <input type="number" step="0.01" class="form-input" placeholder="8.25" value="8.25">
                        </div>
                        <div>
                            <label class="form-label">Tax Display</label>
                            <select class="form-input form-select">
                                <option>Inclusive of tax</option>
                                <option selected>Exclusive of tax</option>
                            </select>
                        </div>
                    </div>
                    <div>
                        <h4 class="text-14 mb-3">Tax Categories</h4>
                        <div class="space-y-2">
                            <div class="flex items-center justify-between p-3 bg-gray-100 dark:bg-dark-850 rounded-md">
                                <div>
                                    <span class="text-sm font-medium text-gray-900">Food & Beverages</span>
                                    <span class="text-sm text-gray-500 dark:text-dark-500 ltr:ml-2 rtl:mr-2">5.00%</span>
                                </div>
                                <button class="link link-primary text-sm">Edit</button>
                            </div>
                            <div class="flex items-center justify-between p-3 bg-gray-100 dark:bg-dark-850 rounded-md">
                                <div>
                                    <span class="text-sm font-medium text-gray-900">General Merchandise</span>
                                    <span class="text-sm text-gray-500 dark:text-dark-500 ltr:ml-2 rtl:mr-2">8.25%</span>
                                </div>
                                <button class="link link-primary text-sm">Edit</button>
                            </div>
                        </div>
                        <div class="mt-4">
                            <button class="btn btn-sub-primary"><i data-lucide="plus" class="size-4 inline-block"></i> Add Tax Category</button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Payment Methods -->
        <section id="payment-methods" class="card">
            <div class="card-header">
                <h6 class="card-title mb-1">Payment Methods</h6>
                <p class="text-gray-500 dark:text-dark-500 text-sm">Configure accepted payment options</p>
            </div>
            <div class="card-body">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="space-y-3">
                        <h3 class="text-14">Accepted Payment Types</h3>
                        <div class="space-y-3">
                            <div class="input-check-group">
                                <input id="cashCheckbox" class="input-check input-check-primary" type="checkbox">
                                <label for="cashCheckbox" class="input-check-label">Cash</label>
                            </div>
                            <div class="input-check-group">
                                <input id="creditCardCheckbox" class="input-check input-check-primary" type="checkbox">
                                <label for="creditCardCheckbox" class="input-check-label">Credit Card</label>
                            </div>
                            <div class="input-check-group">
                                <input id="debitCardCheckbox" class="input-check input-check-primary" type="checkbox">
                                <label for="debitCardCheckbox" class="input-check-label">Debit Card</label>
                            </div>
                            <div class="input-check-group">
                                <input id="digitalWalletCheckbox" class="input-check input-check-primary" type="checkbox">
                                <label for="digitalWalletCheckbox" class="input-check-label">Digital Wallet</label>
                            </div>
                            <div class="input-check-group">
                                <input id="giftCardsCheckbox" class="input-check input-check-primary" type="checkbox">
                                <label for="giftCardsCheckbox" class="input-check-label">Gift Cards</label>
                            </div>
                        </div>
                    </div>
                    <div class="space-y-3">
                        <h3 class="text-14">Card Processing</h3>
                        <div>
                            <label class="form-label">Payment Processor</label>
                            <select class="form-input form-select">
                                <option>Square</option>
                                <option>Stripe</option>
                                <option>PayPal</option>
                                <option>Clover</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Merchant ID</label>
                            <input type="text" class="form-input" placeholder="Enter merchant ID">
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Receipt Settings -->
        <section id="receipt-settings" class="card">
            <div class="card-header">
                <h6 class="card-title mb-1">Receipt Settings</h6>
                <p class="text-gray-500 dark:text-dark-500 text-sm">Customize receipt appearance and content</p>
            </div>
            <div class="card-body">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="space-y-3">
                        <h3 class="text-14">Receipt Options</h3>
                        <div class="space-y-3">
                            <div class="input-check-group">
                                <input id="printReceiptsAutoCheckbox" class="input-check input-check-primary" type="checkbox">
                                <label for="printReceiptsAutoCheckbox" class="input-check-label">Print receipts automatically</label>
                            </div>
                            <div class="input-check-group">
                                <input id="emailReceiptsCheckbox" class="input-check input-check-primary" type="checkbox">
                                <label for="emailReceiptsCheckbox" class="input-check-label">Email receipts</label>
                            </div>
                            <div class="input-check-group">
                                <input id="showItemCodesCheckbox" class="input-check input-check-primary" type="checkbox">
                                <label for="showItemCodesCheckbox" class="input-check-label">Show item codes</label>
                            </div>
                            <div class="input-check-group">
                                <input id="showTaxBreakdownCheckbox" class="input-check input-check-primary" type="checkbox">
                                <label for="showTaxBreakdownCheckbox" class="input-check-label">Show tax breakdown</label>
                            </div>
                        </div>
                    </div>
                    <div class="space-y-3">
                        <h3 class="text-14">Receipt Content</h3>
                        <div>
                            <label class="form-label">Header Message</label>
                            <textarea class="form-input h-auto" rows="2" placeholder="Thank you for shopping with us!">Thank you for shopping with us!</textarea>
                        </div>
                        <div>
                            <label class="form-label">Footer Message</label>
                            <textarea class="form-input h-auto" rows="2" placeholder="Please come again!">Please come again!</textarea>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- User Management -->
        <section id="users" class="card">
            <div class="card-header flex items-center gap-3">
                <div class="grow">
                    <h6 class="card-title mb-1">User Management</h6>
                    <p class="text-gray-500 dark:text-dark-500 text-sm">Manage cashiers and admin users</p>
                </div>
                <button type="button" class="btn btn-primary shrink-0">
                    <i data-lucide="plus" class="size-4 inline-block"></i> Add User
                </button>
            </div>
            <div class="card-body">
                <div class="overflow-x-auto">
                    <table class="table bordered">
                        <thead class="bg-gray-100 dark:bg-dark-850">
                            <tr class="*:text-xs *:font-medium *:text-gray-500 *:uppercase *:tracking-wider">
                                <th>Name</th>
                                <th>Role</th>
                                <th>Status</th>
                                <th>Last Login</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    <h6 class="text-14">John Smith</h6>
                                    <p class="text-sm text-gray-500 dark:text-dark-500"><EMAIL></p>
                                </td>
                                <td>
                                    <span class="badge badge-sub-purple">Admin</span>
                                </td>
                                <td>
                                    <span class="badge badge-sub-green">Active</span>
                                </td>
                                <td class="text-sm text-gray-500 dark:text-dark-500">2 hours ago</td>
                                <td>
                                    <button class="btn btn-sm btn-sub-primary ltr:mr-1 rtl:ml-1">Edit</button>
                                    <button class="btn btn-sm btn-sub-red">Disable</button>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <h6 class="text-14">Sarah Johnson</h6>
                                    <p class="text-sm text-gray-500 dark:text-dark-500"><EMAIL></p>
                                </td>
                                <td>
                                    <span class="badge badge-sub-primary">Cashier</span>
                                </td>
                                <td>
                                    <span class="badge badge-sub-green">Active</span>
                                </td>
                                <td class="text-sm text-gray-500 dark:text-dark-500">1 day ago</td>
                                <td>
                                    <button class="btn btn-sm btn-sub-primary ltr:mr-1 rtl:ml-1">Edit</button>
                                    <button class="btn btn-sm btn-sub-red">Disable</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </section>

        <!-- Hardware Setup -->
        <section id="hardware" class="card">
            <div class="card-header">
                <h6 class="card-title mb-1">Hardware Setup</h6>
                <p class="text-sm text-gray-500 dark:text-dark-500">Configure POS hardware devices</p>
            </div>
            <div class="card-body">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-5">
                    <div class="space-y-4">
                        <h6 class="text-12">Receipt Printer</h6>
                        <div>
                            <label class="form-label">Printer Model</label>
                            <select class="form-input form-select">
                                <option>Epson TM-T88VI</option>
                                <option>Star TSP143III</option>
                                <option>Citizen CT-S310II</option>
                            </select>
                        </div>
                        <div>
                            <label class="form-label">Connection Type</label>
                            <select class="form-input form-select">
                                <option>USB</option>
                                <option>Ethernet</option>
                                <option>WiFi</option>
                                <option>Bluetooth</option>
                            </select>
                        </div>
                        <div class="flex items-center gap-2">
                            <div class="size-3 bg-green-500 rounded-full"></div>
                            <p class="text-sm">Connected</p>
                        </div>
                    </div>
                    <div class="space-y-4">
                        <h6 class="text-12">Barcode Scanner</h6>
                        <div>
                            <label class="form-label">Scanner Model</label>
                            <select class="form-input form-select">
                                <option>Honeywell Voyager 1200g</option>
                                <option>Symbol LS2208</option>
                                <option>Zebra DS2208</option>
                            </select>
                        </div>
                        <div>
                            <label class="form-label">Connection Type</label>
                            <select class="form-input form-select">
                                <option>USB</option>
                                <option>Serial</option>
                                <option>Wireless</option>
                            </select>
                        </div>
                        <div class="flex items-center gap-2">
                            <div class="size-3 bg-green-500 rounded-full"></div>
                            <p class="text-sm">Connected</p>
                        </div>
                    </div>
                    <div class="space-y-4">
                        <h6 class="text-14">Cash Drawer</h6>
                        <div>
                            <label class="form-label">Drawer Model</label>
                            <select class="form-input form-select">
                                <option>APG VPK-15BL-2-BX</option>
                                <option>Star SMD2-1317</option>
                                <option>M-S Cash Drawer EP-125NK</option>
                            </select>
                        </div>
                        <div class="input-check-group">
                            <input id="autoOpenOnSaleCheckbox" class="input-check input-check-primary" type="checkbox" checked>
                            <label for="autoOpenOnSaleCheckbox" class="input-check-label">Auto-open on sale</label>
                        </div>
                        <div class="flex items-center gap-2">
                            <div class="size-3 bg-green-500 rounded-full"></div>
                            <p class="text-sm">Connected</p>
                        </div>
                    </div>
                    <div class="space-y-4">
                        <h3 class="text-14">Customer Display</h3>
                        <div>
                            <label class="form-label">Display Type</label>
                            <select class="form-input form-select">
                                <option>Pole Display</option>
                                <option>LCD Monitor</option>
                                <option>Tablet</option>
                                <option>None</option>
                            </select>
                        </div>
                        <div class="input-check-group">
                            <input id="showPromotionalMessageCheckbox" class="input-check input-check-primary" type="checkbox">
                            <label for="showPromotionalMessageCheckbox" class="input-check-label">Show promotional messages</label>
                        </div>
                        <div class="flex items-center gap-2">
                            <div class="size-3 bg-gray-500 dark:bg-dark-500 rounded-full"></div>
                            <p class="text-sm">Not Connected</p>
                        </div>
                    </div>
                </div>
                <div class="mt-6 pt-6 border-t border-gray-200 dark:border-dark-800">
                    <button class="btn btn-primary">
                        Test All Devices
                    </button>
                    <button class="btn btn-red">
                        Refresh Connections
                    </button>
                </div>
            </div>
        </section>
    </div>
</div>

</div>
{{> partials/footer }}
</div>

{{> partials/vendor-scripts }}

<script type="module" src="assets/js/main.js"></script>

</body>

</html>