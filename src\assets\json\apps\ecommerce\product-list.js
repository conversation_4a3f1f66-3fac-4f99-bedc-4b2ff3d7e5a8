import product1 from "/assets/images/products/img-01.png";
import product2 from "/assets/images/products/img-02.png";
import product3 from "/assets/images/products/img-03.png";
import product4 from "/assets/images/products/img-04.png";
import product5 from "/assets/images/products/img-05.png";
import product6 from "/assets/images/products/img-06.png";
import product7 from "/assets/images/products/img-07.png";
import product8 from "/assets/images/products/img-08.png";
import product9 from "/assets/images/products/img-09.png";
import product10 from "/assets/images/products/img-10.png";
import product11 from "/assets/images/products/img-11.png";
import product12 from "/assets/images/products/img-12.png";
import product13 from "/assets/images/products/img-13.png";
const productListData = [
    {
        "productName": "Blouse Ruffle Tube top",
        "category": "Fashion",
        "price": "$14.99",
        "revenue": "$15,236",
        "qty": 154,
        "image": product1,
        "status": "Published"
    },
    {
        "productName": "Gold-colored locket watch",
        "category": "Watch",
        "price": "$59.99",
        "revenue": "$18,956",
        "qty": 187,
        "image": product2,
        "status": "Published"
    },
    {
        "productName": "Spar Men Black Running Shoes",
        "category": "Footwear",
        "price": "$35.78",
        "revenue": "$0",
        "qty": 487,
        "image": product3,
        "status": "Inactive"
    },
    {
        "productName": "Crop top Sweater Clothing",
        "category": "Fashion",
        "price": "$29.49",
        "revenue": "$4,265",
        "qty": 177,
        "image": product4,
        "status": "Inactive"
    },
    {
        "productName": "Sleeve Clothing Leggings",
        "category": "Fashion",
        "price": "$22.00",
        "revenue": "$7,465",
        "qty": 183,
        "image": product5,
        "status": "Published"
    },
    {
        "productName": "Bra Lace Crop top",
        "category": "Fashion",
        "price": "$29.99",
        "revenue": "$9,613",
        "qty": 326,
        "image": product6,
        "status": "Published"
    },
    {
        "productName": "Yellow women shoes",
        "category": "Footwear",
        "price": "$36.87",
        "revenue": "$11,074",
        "qty": 147,
        "image": product7,
        "status": "Inactive"
    },
    {
        "productName": "Tote bag Leather Handbag Dolce",
        "category": "Bags",
        "price": "$79.99",
        "revenue": "$19,803",
        "qty": 98,
        "image": product8,
        "status": "Published"
    },
    {
        "productName": "Hoodie Jacket Letterman Sleeve Coat",
        "category": "Fashion",
        "price": "$44.49",
        "revenue": "$9,961",
        "qty": 246,
        "image": product9,
        "status": "Published"
    },
    {
        "productName": "Straw hat Cap Cowboy hat Sun hat",
        "category": "Accessories",
        "price": "$24.99",
        "revenue": "$6,087",
        "qty": 213,
        "image": product10,
        "status": "Inactive"
    },
    {
        "productName": "Sneakers Shoe Nike Basketball",
        "category": "Footwear",
        "price": "$32.00",
        "revenue": "$14,872",
        "qty": 198,
        "image": product11,
        "status": "Published"
    },
    {
        "productName": "Bermuda shorts Denim Jeans Waist",
        "category": "Fashion",
        "price": "$24.87",
        "revenue": "$5,108",
        "qty": 54,
        "image": product12,
        "status": "Inactive"
    },
    {
        "productName": "Jean jacket Denim Levi Strauss & Co. Jeans",
        "category": "Fashion",
        "price": "$39.49",
        "revenue": "$9,780",
        "qty": 119,
        "image": product13,
        "status": "Published"
    }
]
export default productListData