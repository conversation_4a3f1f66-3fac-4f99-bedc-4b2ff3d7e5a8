{{> partials/main }}

<head>

    {{> partials/title-meta title="List Group" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}

{{> partials/page-heading title="List Group" sub-title="UI" }}

<div class="grid grid-cols-12 gap-x-space">
    <div class="col-span-12 sm:col-span-6 xl:col-span-4 card">
        <div class="card-header">
            <h6 class="card-title">Basic List</h6>
        </div>
        <div class="card-body">
            <ul class="flex flex-col gap-2">
                <li>Build functional APIs with zero coding.</li>
                <li>Resources with permissions.</li>
                <li>Built in user authentication.</li>
                <li>Easy Integration with existing apps and tools.</li>
            </ul>
        </div>
    </div><!--col-->
    <div class="col-span-12 sm:col-span-6 xl:col-span-4 card">
        <div class="card-header">
            <h6 class="card-title">Disc List</h6>
        </div>
        <div class="card-body">
            <ul class="flex flex-col gap-2 list-disc list-inside">
                <li>Build functional APIs with zero coding.</li>
                <li>Resources with permissions.</li>
                <li>Built in user authentication.</li>
                <li>Easy Integration with existing apps and tools.</li>
            </ul>
        </div>
    </div><!--col-->
    <div class="col-span-12 sm:col-span-6 xl:col-span-4 card">
        <div class="card-header">
            <h6 class="card-title">Number List</h6>
        </div>
        <div class="card-body">
            <ul class="flex flex-col gap-2 list-decimal list-inside">
                <li>Build functional APIs with zero coding.</li>
                <li>Resources with permissions.</li>
                <li>Built in user authentication.</li>
                <li>Easy Integration with existing apps and tools.</li>
            </ul>
        </div>
    </div><!--col-->
    <div class="col-span-12 sm:col-span-6 xl:col-span-4 card">
        <div class="card-header">
            <h6 class="card-title">Square List</h6>
        </div>
        <div class="card-body">
            <ul class="flex flex-col gap-2 list-inside list-square">
                <li>Build functional APIs with zero coding.</li>
                <li>Resources with permissions.</li>
                <li>Built in user authentication.</li>
                <li>Easy Integration with existing apps and tools.</li>
            </ul>
        </div>
    </div><!--col-->
    <div class="col-span-12 sm:col-span-6 xl:col-span-4 card">
        <div class="card-header">
            <h6 class="card-title">Roman Uper List</h6>
        </div>
        <div class="card-body">
            <ul class="flex flex-col gap-2 list-inside list-roman">
                <li>Build functional APIs with zero coding.</li>
                <li>Resources with permissions.</li>
                <li>Built in user authentication.</li>
                <li>Easy Integration with existing apps and tools.</li>
            </ul>
        </div>
    </div><!--col-->
    <div class="col-span-12 sm:col-span-6 xl:col-span-4 card">
        <div class="card-header">
            <h6 class="card-title">Circle List</h6>
        </div>
        <div class="card-body">
            <ul class="flex flex-col gap-2 list-inside list-circle">
                <li>Build functional APIs with zero coding.</li>
                <li>Resources with permissions.</li>
                <li>Built in user authentication.</li>
                <li>Easy Integration with existing apps and tools.</li>
            </ul>
        </div>
    </div><!--col-->
    <div class="col-span-12 sm:col-span-6 xl:col-span-4 card">
        <div class="card-header">
            <h6 class="card-title">Disc Color List</h6>
        </div>
        <div class="card-body">
            <ul class="flex flex-col gap-2 list-disc list-inside marker:text-primary-500">
                <li>Build functional APIs with zero coding.</li>
                <li>Resources with permissions.</li>
                <li>Built in user authentication.</li>
                <li>Easy Integration with existing apps and tools.</li>
            </ul>
        </div>
    </div><!--col-->
    <div class="col-span-12 sm:col-span-6 xl:col-span-4 card">
        <div class="card-header">
            <h6 class="card-title">Marker Color List</h6>
        </div>
        <div class="card-body">
            <ul class="flex flex-col gap-2 list-inside list-circle marker:text-green-500">
                <li>Build functional APIs with zero coding.</li>
                <li>Resources with permissions.</li>
                <li>Built in user authentication.</li>
                <li>Easy Integration with existing apps and tools.</li>
            </ul>
        </div>
    </div><!--col-->
    <div class="col-span-12 sm:col-span-6 xl:col-span-4 card">
        <div class="card-header">
            <h6 class="card-title">Number Color List</h6>
        </div>
        <div class="card-body">
            <ul class="flex flex-col gap-2 list-decimal list-inside marker:text-purple-500">
                <li>Build functional APIs with zero coding.</li>
                <li>Resources with permissions.</li>
                <li>Built in user authentication.</li>
                <li>Easy Integration with existing apps and tools.</li>
            </ul>
        </div>
    </div><!--col-->
    <div class="col-span-12 sm:col-span-6 xl:col-span-4 card">
        <div class="card-header">
            <h6 class="card-title">Images List</h6>
        </div>
        <div class="card-body">
            <ul class="flex flex-col gap-2 list-image-[url('../images/others/arrow-right.png')] list-inside">
                <li>Build functional APIs with zero coding.</li>
                <li>Resources with permissions.</li>
                <li>Built in user authentication.</li>
                <li>Easy Integration with existing apps and tools.</li>
            </ul>
        </div>
    </div><!--col-->
    <div class="col-span-12 sm:col-span-6 xl:col-span-4 card">
        <div class="card-header">
            <h6 class="card-title">Flush List</h6>
        </div>
        <div class="card-body">
            <ul class="flex flex-col *:border-b *:border-gray-200 dark:*:border-dark-800 *:p-2">
                <li>Build functional APIs with zero coding.</li>
                <li>Resources with permissions.</li>
                <li>Built in user authentication.</li>
                <li>Easy Integration with existing apps and tools.</li>
            </ul>
        </div>
    </div><!--col-->
    <div class="col-span-12 sm:col-span-6 xl:col-span-4 card">
        <div class="card-header">
            <h6 class="card-title">Bordered List</h6>
        </div>
        <div class="card-body">
            <ul class="flex flex-col *:border *:border-gray-200 dark:*:border-dark-800 *:border-b-0 *:p-2">
                <li class="last:border-b [&.active]:bg-primary-500 [&.active]:text-white [&.active]:border-primary-500">Build functional APIs with zero coding.</li>
                <li class="last:border-b [&.active]:bg-primary-500 [&.active]:text-white [&.active]:border-primary-500 active">Resources with permissions.</li>
                <li class="last:border-b [&.active]:bg-primary-500 [&.active]:text-white [&.active]:border-primary-500">Built in user authentication.</li>
                <li class="last:border-b [&.active]:bg-primary-500 [&.active]:text-white [&.active]:border-primary-500">Easy Integration with existing apps and tools.</li>
            </ul>
        </div>
    </div><!--col-->
    <div class="col-span-12 sm:col-span-6 xl:col-span-4 card">
        <div class="card-header">
            <h6 class="card-title">Hovered List</h6>
        </div>
        <div class="card-body">
            <ul class="flex flex-col *:border-b *:border-gray-200 dark:*:border-dark-800 *:p-2">
                <li class="transition duration-200 ease-linear hover:bg-gray-100 dark:hover:bg-dark-850">Build functional APIs with zero coding.</li>
                <li class="transition duration-200 ease-linear hover:bg-gray-100 dark:hover:bg-dark-850">Resources with permissions.</li>
                <li class="transition duration-200 ease-linear hover:bg-gray-100 dark:hover:bg-dark-850">Built in user authentication.</li>
                <li class="transition duration-200 ease-linear hover:bg-gray-100 dark:hover:bg-dark-850">Easy Integration with existing apps and tools.</li>
            </ul>
        </div>
    </div><!--col-->
    <div class="col-span-12 sm:col-span-6 xl:col-span-4 card">
        <div class="card-header">
            <h6 class="card-title">Link List</h6>
        </div>
        <div class="card-body">
            <ul class="flex flex-col *:border-b *:border-gray-200 dark:*:border-dark-800">
                <li class="transition duration-200 ease-linear hover:bg-gray-100 dark:hover:bg-dark-850"><a href="#!" class="block p-2">Build functional APIs with zero coding.</a></li>
                <li class="transition duration-200 ease-linear hover:bg-gray-100 dark:hover:bg-dark-850"><a href="#!" class="block p-2">Resources with permissions.</a></li>
                <li class="transition duration-200 ease-linear hover:bg-gray-100 dark:hover:bg-dark-850"><a href="#!" class="block p-2">Built in user authentication.</a></li>
                <li class="transition duration-200 ease-linear hover:bg-gray-100 dark:hover:bg-dark-850"><a href="#!" class="block p-2">Easy Integration with existing apps and tools.</a></li>
            </ul>
        </div>
    </div><!--col-->
    <div class="col-span-12 sm:col-span-6 xl:col-span-4 card">
        <div class="card-header">
            <h6 class="card-title">Checkbox List</h6>
        </div>
        <div class="card-body">
            <ul class="flex flex-col *:border-b *:border-gray-200 dark:*:border-dark-800 *:p-2">
                <li>
                    <div class="flex items-center gap-2">
                        <input type="checkbox" id="checkboxList1" name="checkboxListGroup" class="border rounded-sm appearance-none cursor-pointer shrink-0 size-5 checked:bg-primary-500 checked:border-primary-500">
                        <label for="checkboxList1" class="cursor-pointer select-none">Build functional APIs with zero coding.</label>
                    </div>
                </li>
                <li>
                    <div class="flex items-center gap-2">
                        <input type="checkbox" id="checkboxList2" name="checkboxListGroup" class="border rounded-sm appearance-none cursor-pointer shrink-0 size-5 checked:bg-primary-500 checked:border-primary-500">
                        <label for="checkboxList2" class="cursor-pointer select-none">Resources with permissions.</label>
                    </div>
                </li>
                <li>
                    <div class="flex items-center gap-2">
                        <input type="checkbox" id="checkboxList3" name="checkboxListGroup" class="border rounded-sm appearance-none cursor-pointer shrink-0 size-5 checked:bg-primary-500 checked:border-primary-500">
                        <label for="checkboxList3" class="cursor-pointer select-none">Built in user authentication.</label>
                    </div>
                </li>
                <li>
                    <div class="flex items-center gap-2">
                        <input type="checkbox" id="checkboxList4" name="checkboxListGroup" class="border rounded-sm appearance-none cursor-pointer shrink-0 size-5 checked:bg-primary-500 checked:border-primary-500">
                        <label for="checkboxList4" class="cursor-pointer select-none">Easy Integration with existing apps and tools.</label>
                    </div>
                </li>
            </ul>
        </div>
    </div><!--col-->
    <div class="col-span-12 sm:col-span-6 xl:col-span-4 card">
        <div class="card-header">
            <h6 class="card-title">Radio List</h6>
        </div>
        <div class="card-body">
            <ul class="flex flex-col *:border-b *:border-gray-200 dark:*:border-dark-800 *:p-2">
                <li>
                    <div class="flex items-center gap-2">
                        <input type="radio" id="radioList1" name="radioListGroup" class="border rounded-full appearance-none cursor-pointer shrink-0 size-5 checked:bg-primary-500 checked:border-primary-500">
                        <label for="radioList1" class="cursor-pointer select-none">Build functional APIs with zero coding.</label>
                    </div>
                </li>
                <li>
                    <div class="flex items-center gap-2">
                        <input type="radio" id="radioList2" name="radioListGroup" class="border rounded-full appearance-none cursor-pointer shrink-0 size-5 checked:bg-primary-500 checked:border-primary-500">
                        <label for="radioList2" class="cursor-pointer select-none">Resources with permissions.</label>
                    </div>
                </li>
                <li>
                    <div class="flex items-center gap-2">
                        <input type="radio" id="radioList3" name="radioListGroup" class="border rounded-full appearance-none cursor-pointer shrink-0 size-5 checked:bg-primary-500 checked:border-primary-500">
                        <label for="radioList3" class="cursor-pointer select-none">Built in user authentication.</label>
                    </div>
                </li>
                <li>
                    <div class="flex items-center gap-2">
                        <input type="radio" id="radioList4" name="radioListGroup" class="border rounded-full appearance-none cursor-pointer shrink-0 size-5 checked:bg-primary-500 checked:border-primary-500">
                        <label for="radioList4" class="cursor-pointer select-none">Easy Integration with existing apps and tools.</label>
                    </div>
                </li>
            </ul>
        </div>
    </div><!--col-->
    <div class="col-span-12 sm:col-span-6 xl:col-span-4 card">
        <div class="card-header">
            <h6 class="card-title">Boxed List</h6>
        </div>
        <div class="card-body">
            <ul class="flex flex-col gap-2 *:border *:rounded-md *:border-gray-200 dark:*:border-dark-800 *:p-2">
                <li>Build functional APIs with zero coding.</li>
                <li>Resources with permissions.</li>
                <li>Built in user authentication.</li>
                <li>Easy Integration with existing apps and tools.</li>
            </ul>
        </div>
    </div><!--col-->
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Content List</h6>
        </div>
        <div class="card-body">
            <div class="grid grid-cols-12 gap-5">
                <div class="col-span-12 sm:col-span-6 xl:col-span-4">
                    <ul class="overflow-hidden border border-gray-200 dark:border-dark-800 sm:rounded-md">
                        <li>
                            <div class="p-5">
                                <div class="flex items-center justify-between">
                                    <h6>Item 1</h6>
                                    <p class="max-w-2xl mt-1 text-sm text-gray-500 dark:text-dark-500">Description for Item 1</p>
                                </div>
                                <div class="flex items-center justify-between mt-3">
                                    <p class="text-sm font-medium text-gray-500 dark:text-dark-500">Status: <span class="text-green-500">Active</span></p>
                                    <a href="#" class="font-medium text-primary-500 hover:text-primary-500">Edit</a>
                                </div>
                            </div>
                        </li>
                        <li class="border-t border-gray-200 dark:border-dark-800">
                            <div class="p-5">
                                <div class="flex items-center justify-between">
                                    <h6>Item 2</h6>
                                    <p class="max-w-2xl mt-1 text-sm text-gray-500 dark:text-dark-500">Description for Item 2</p>
                                </div>
                                <div class="flex items-center justify-between mt-3">
                                    <p class="text-sm font-medium text-gray-500 dark:text-dark-500">Status: <span class="text-red-500">Inactive</span></p>
                                    <a href="#" class="font-medium text-primary-500 hover:text-primary-500">Edit</a>
                                </div>
                            </div>
                        </li>
                        <li class="border-t border-gray-200 dark:border-dark-800">
                            <div class="p-5">
                                <div class="flex items-center justify-between">
                                    <h6>Item 3</h6>
                                    <p class="max-w-2xl mt-1 text-sm text-gray-500 dark:text-dark-500">Description for Item 3</p>
                                </div>
                                <div class="flex items-center justify-between mt-3">
                                    <p class="text-sm font-medium text-gray-500 dark:text-dark-500">Status: <span class="text-yellow-500">Pending</span></p>
                                    <a href="#" class="font-medium text-primary-500 hover:text-primary-500">Edit</a>
                                </div>
                            </div>
                        </li>
                    </ul>
                </div><!--end col-->
                <div class="col-span-12 sm:col-span-6 xl:col-span-4">
                    <div class="overflow-hidden border border-gray-200 rounded-md dark:border-dark-800">
                        <div class="px-4 py-2 border-b border-gray-200 dark:border-dark-800">
                            <h6>Top Users</h6>
                        </div>
                        <ul class="divide-y divide-gray-200 dark:divide-dark-800">
                            <li class="flex items-center gap-3 px-6 py-4">
                                <img class="object-cover rounded-full size-10 shrink-0" src="assets/images/avatar/user-11.png" alt="user">
                                <div class="grow">
                                    <h6>Emily Jones</h6>
                                    <p class="text-gray-500 dark:text-dark-500">1234 points</p>
                                </div>
                            </li>
                            <li class="flex items-center gap-3 px-6 py-4">
                                <img class="object-cover rounded-full size-10 shrink-0" src="assets/images/avatar/user-12.png" alt="user">
                                <div class="grow">
                                    <h6>David Lee</h6>
                                    <p class="text-gray-500 dark:text-dark-500">987 points</p>
                                </div>
                            </li>
                            <li class="flex items-center gap-3 px-6 py-4">
                                <img class="object-cover rounded-full size-10 shrink-0" src="assets/images/avatar/user-13.png" alt="user">
                                <div class="grow">
                                    <h6>Sophia Williams</h6>
                                    <p class="text-gray-500 dark:text-dark-500">876 points</p>
                                </div>
                            </li>
                            <li class="flex items-center gap-3 px-6 py-4">
                                <img class="object-cover rounded-full size-10 shrink-0" src="assets/images/avatar/user-14.png" alt="user">
                                <div class="grow">
                                    <h6>Michael Chen</h6>
                                    <p class="text-gray-500 dark:text-dark-500">765 points</p>
                                </div>
                            </li>
                            <li class="flex items-center gap-3 px-6 py-4">
                                <img class="object-cover rounded-full size-10 shrink-0" src="assets/images/avatar/user-15.png" alt="user">
                                <div class="grow">
                                    <h6>Mia Davis</h6>
                                    <p class="text-gray-500 dark:text-dark-500">654 points</p>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div><!--end col-->
            </div><!--end grid-->
        </div>
    </div><!--col-->
</div>

</div>
{{> partials/footer }}
</div>


{{> partials/vendor-scripts }}
<script type="module" src="assets/js/main.js"></script>

</body>
</html>