{{> partials/main }}

<head>

    {{> partials/title-meta title="Basic Wizard" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}

{{> partials/page-heading title="Basic Wizard" sub-title="Forms Wizard" }}

<div class="grid grid-cols-12">
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Multi Step Form</h6>
        </div>
        <div class="card-body">

            <div>
                <div class="w-full lg:max-w-[600px] mx-auto">
                    <div x-data="app()" x-cloak class="card">
                        <div class="card-body">
                            <div>

                                <div x-show.transition="step === 'complete'">
                                    <div class="flex items-center justify-between text-center">
                                        <div>
                                            <i data-lucide="circle-check-big" class="mx-auto my-4 text-green-500 fill-green-500/10 size-8"></i>

                                            <h4 class="mb-2">Registration Success</h4>

                                            <div class="mb-8 text-gray-500 md:mx-10 dark:text-dark-500">
                                                Thank you. We have sent you an <NAME_EMAIL>. Please click the link in the message to activate your account.
                                            </div>

                                            <button @click="resetForm" class="btn btn-sub-gray"><i data-lucide="home" class="inline-block -mt-1 ltr:mr-1 rtl:ml-1 size-4"></i> Back to home</button>
                                        </div>
                                    </div>
                                </div>

                                <div x-show.transition="step != 'complete'">
                                    <!-- Top Navigation -->
                                    <div class="py-4 border-b border-gray-200 dark:border-dark-800">
                                        <h6 class="mb-1 text-xs text-gray-500 uppercase dark:text-dark-500" x-text="`Step: ${step} of 3`"></h6>
                                        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                                            <div class="flex-1">
                                                <div x-show="step === 1">
                                                    <h5>Your Profile</h5>
                                                </div>

                                                <div x-show="step === 2">
                                                    <h5>Your Password</h5>
                                                </div>

                                                <div x-show="step === 3">
                                                    <h5>Tell me about yourself</h5>
                                                </div>
                                            </div>

                                            <div class="flex items-center md:w-64">
                                                <div class="w-full bg-gray-200 rounded-full dark:bg-dark-800 rtl:ml-2 ltr:mr-2">
                                                    <div class="h-2 text-xs leading-none text-center text-white bg-green-500 rounded-full" :style="'width: '+ parseInt(step / 3 * 100) +'%'"></div>
                                                </div>
                                                <h6 class="w-10 text-xs text-gray-500 dark:text-dark-500" x-text="parseInt(step / 3 * 100) +'%'"></h6>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- /Top Navigation -->

                                    <!-- Step Content -->
                                    <div class="pt-10 pb-5">
                                        <div x-show.transition.in="step === 1">
                                            <div class="mb-5 text-center">
                                                <div class="relative w-32 h-32 mx-auto mb-4 bg-gray-500 border rounded-full shadow-inset dark:border-dark-800">
                                                    <img id="image" class="object-cover w-full h-32 rounded-full" :src="form.imageForm" />
                                                </div>

                                                <label for="fileInput" type="button" class="items-center justify-between inline-block px-4 py-2 font-medium text-left text-gray-500 border border-gray-200 rounded-lg shadow-xs cursor-pointer dark:border-dark-800 dark:text-dark-500 inine-flex focus:outline-hidden hover:bg-gray-100 dark:hover:bg-dark-850">
                                                    <i data-lucide="camera" class="inline-flex -mt-1 shrink-0 ltr:mr-1 rtl:ml-1 size-5"></i>
                                                    Browse Photo
                                                </label>

                                                <div class="mt-1 text-xs text-center text-gray-500 dark:text-dark-500">Click to add profile picture</div>
                                                <p x-text="errors.imageForm" class="mt-1 text-xs text-red-500"></p>
                                                <input name="photo" id="fileInput" accept="image/*" class="hidden" type="file" @change="handleImageChange($event)">
                                            </div>

                                            <div class="mb-5">
                                                <label for="firstName" class="form-label">Firstname</label>
                                                <input type="text" class="form-input" id="firstName" x-model="form.firstName" placeholder="Enter your firstName..." @input="clearError('firstName')">
                                                <p x-text="errors.firstName" class="mt-1 text-xs text-red-500"></p>
                                            </div>

                                            <div class="mb-5">
                                                <label for="email" class="form-label">Email</label>
                                                <input type="email" class="form-input" id="email" x-model="form.email" placeholder="Enter your email address..." @input="clearError('email')">
                                                <p x-text="errors.email" class="mt-1 text-xs text-red-500"></p>
                                            </div>
                                        </div>
                                        <div x-show.transition.in="step === 2">

                                            <div class="mb-5">
                                                <label for="password" class="font-bold form-label">Set up password</label>
                                                <div class="mt-2 mb-4 text-gray-500 dark:text-dark-500">
                                                    Please create a secure password including the following criteria below.

                                                    <ul class="mt-2 text-sm list-disc list-inside">
                                                        <li>lowercase letters</li>
                                                        <li>numbers</li>
                                                        <li>capital letters</li>
                                                        <li>special characters</li>
                                                    </ul>
                                                </div>

                                                <div class="relative">
                                                    <input :type="togglePassword ? 'text' : 'password'" id="password" @input="checkPasswordStrength()" x-model="form.passwords" class="form-input" placeholder="Your strong password..." @input="clearError('passwords')">
                                                    <p x-text="errors.passwords" class="mt-1 text-xs text-red-500"></p>
                                                    <div class="absolute top-0 bottom-0 px-3 py-2 cursor-pointer ltr:right-0 rtl:left-0" @click="togglePassword = !togglePassword">
                                                        <svg :class="{'hidden': !togglePassword, 'block': togglePassword }" xmlns="http://www.w3.org/2000/svg" class="text-gray-500 fill-current size-6 dark:text-dark-500" viewBox="0 0 24 24">
                                                            <path d="M12 19c.946 0 1.81-.103 2.598-.281l-1.757-1.757C12.568 16.983 12.291 17 12 17c-5.351 0-7.424-3.846-7.926-5 .204-.47.674-1.381 1.508-2.297L4.184 8.305c-1.538 1.667-2.121 3.346-2.132 3.379-.069.205-.069.428 0 .633C2.073 12.383 4.367 19 12 19zM12 5c-1.837 0-3.346.396-4.604.981L3.707 2.293 2.293 3.707l18 18 1.414-1.414-3.319-3.319c2.614-1.951 3.547-4.615 3.561-4.657.069-.205.069-.428 0-.633C21.927 11.617 19.633 5 12 5zM16.972 15.558l-2.28-2.28C14.882 12.888 15 12.459 15 12c0-1.641-1.359-3-3-3-.459 0-.888.118-1.277.309L8.915 7.501C9.796 7.193 10.814 7 12 7c5.351 0 7.424 3.846 7.926 5C19.624 12.692 18.76 14.342 16.972 15.558z" />
                                                        </svg>

                                                        <svg :class="{'hidden': togglePassword, 'block': !togglePassword }" xmlns="http://www.w3.org/2000/svg" class="text-gray-500 fill-current size-6 dark:text-dark-500" viewBox="0 0 24 24">
                                                            <path d="M12,9c-1.642,0-3,1.359-3,3c0,1.642,1.358,3,3,3c1.641,0,3-1.358,3-3C15,10.359,13.641,9,12,9z" />
                                                            <path d="M12,5c-7.633,0-9.927,6.617-9.948,6.684L1.946,12l0.105,0.316C2.073,12.383,4.367,19,12,19s9.927-6.617,9.948-6.684 L22.054,12l-0.105-0.316C21.927,11.617,19.633,5,12,5z M12,17c-5.351,0-7.424-3.846-7.926-5C4.578,10.842,6.652,7,12,7 c5.351,0,7.424,3.846,7.926,5C19.422,13.158,17.348,17,12,17z" />
                                                        </svg>
                                                    </div>
                                                </div>

                                                <div class="flex items-center h-3 mt-4">
                                                    <div class="flex justify-between w-2/3 h-2">
                                                        <div :class="{ 'bg-red-400 dark:bg-red-400': passwordStrengthText == 'Too weak' ||  passwordStrengthText == 'Could be stronger' || passwordStrengthText == 'Strong password' }" class="w-1/3 h-2 bg-gray-300 rounded-full ltr:mr-1 rtl:ml-1 dark:bg-dark-800"></div>
                                                        <div :class="{ 'bg-orange-400 dark:bg-orange-400': passwordStrengthText == 'Could be stronger' || passwordStrengthText == 'Strong password' }" class="w-1/3 h-2 bg-gray-300 rounded-full ltr:mr-1 rtl:ml-1 dark:bg-dark-800"></div>
                                                        <div :class="{ 'bg-green-400 dark:bg-green-400': passwordStrengthText == 'Strong password' }" class="w-1/3 h-2 bg-gray-300 rounded-full dark:bg-dark-800"></div>
                                                    </div>
                                                    <div x-text="passwordStrengthText" class="text-sm font-medium leading-none text-gray-500 ltr:ml-3 rtl:mr-3 dark:text-dark-500"></div>
                                                </div>
                                                <p class="mt-5 text-gray-500 dark:text-dark-500">Exploration for a password strength meter by <a href="#!" class="text-primary-500">SRBThemes</a>.</p>
                                            </div>

                                        </div>
                                        <div x-show.transition.in="step === 3">
                                            <div class="mb-5">
                                                <label for="email" class="block mb-2">Gender</label>

                                                <div class="flex gap-5">
                                                    <div class="input-radio-group">
                                                        <input id="genderMale" x-model="form.gender" value="Male" class="input-radio input-radio-primary" type="radio" @change="clearError('gender')" />
                                                        <label for="genderMale" class="input-radio-label">Male</label>
                                                    </div>
                                                    <div class="input-radio-group">
                                                        <input id="genderFemale" x-model="form.gender" value="Female" class="input-radio input-radio-primary" type="radio" @change="clearError('gender')" />
                                                        <label for="genderFemale" class="input-radio-label">Female</label>
                                                    </div>
                                                </div>
                                                <p x-text="errors.gender" class="mt-1 text-xs text-red-500"></p>
                                            </div>
                                            <div class="mb-5">
                                                <label for="profession" class="block mb-2 text-sm">Profession</label>
                                                <input type="profession" id="profession" class="form-input" x-model="form.proffesion" placeholder="eg. Web Developer" @input="clearError('proffesion')">
                                                <p x-text="errors.proffesion" class="mt-1 text-xs text-red-500"></p>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- / Step Content -->
                                </div>
                            </div>

                            <!-- Bottom Navigation -->
                            <div x-show="step != 'complete'">
                                <div class="max-w-3xl">
                                    <div class="flex justify-between">
                                        <div class="w-1/2">
                                            <button x-show="step > 1" @click="prev" class="btn btn-sub-gray">
                                                <i data-lucide="move-left" class="mr-1 ltr:inline-block rtl:hidden size-4"></i>
                                                <i data-lucide="move-right" class="ml-1 rtl:inline-block ltr:hidden size-4"></i>
                                                Previous
                                            </button>
                                        </div>

                                        <div class="w-1/2 ltr:text-right rtl:text-left">
                                            <button x-show="step < 3" @click="validateStep(step)" class="btn btn-primary">
                                                Next
                                                <i data-lucide="move-right" class="ml-1 ltr:inline-block rtl:hidden size-4"></i>
                                                <i data-lucide="move-left" class="mr-1 rtl:inline-block ltr:hidden size-4"></i>
                                            </button>

                                            <button @click="validateStep(3)" x-show="step === 3" class="btn btn-green">Complete</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div><!--end col-->
    </div>
    <!--end grid-->

</div>

{{> partials/footer }}
</div>


{{> partials/vendor-scripts }}

<script type="module" src="assets/js/form/wizards-base.init.js"></script>

<script type="module" src="assets/js/main.js"></script>

</body>
</html>