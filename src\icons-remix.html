{{> partials/main }}

<head>

    {{> partials/title-meta title="Remix" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}
        {{> partials/page-heading title="Remix" sub-title="Icons" }}

        <div class="grid grid-cols-12 gap-x-space">
            <div class="col-span-12 card">
                <div class="flex items-center card-header">
                    <h6 class="text-15 grow">Remix Icons</h6>
                    <a href="https://remixicon.com/" target="_blank" class="font-medium text-red-500 underline transition duration-200 ease-linear hover:text-red-600 shrink-0">View All Icons</a>
                </div>
                <div class="card-body">
                    <p class="mb-3 text-gray-500 dark:text-dark-500">Open-source neutral-style system symbols elaborately crafted for designers and developers. All of the icons are free for both personal and commercial use.</p>

                    <h6 class="mb-2 text-16">Installation</h6>
                    <p class="text-gray-500 dark:text-dark-500 mb-2">If you'd like to use Remix Icon with a CDN, you can skip this installation step.</p>

                    <pre><deckgo-highlight-code lang="js">
                        <code slot="code">npm install remixicon --save</code>
                    </deckgo-highlight-code></pre>

                    <p class="text-gray-500 dark:text-dark-500 my-2">import CSS to your <code class="text-pink-500">icons.scss</code></p>

                    <pre><deckgo-highlight-code lang="js">
                        <code slot="code">@import 'remixicon/fonts/remixicon.css';</code>
                    </deckgo-highlight-code></pre>

                    <h6 class="mb-1 mt-2">CDN</h6>
                    <p class="text-gray-500 dark:text-dark-500 mb-2">Copy the following code and add it to the

                        <head> tag of your HTML document.
                    </p>

                    <pre><deckgo-highlight-code lang="js">
                        <code slot="code">&lt;link href=&quot;https://cdn.jsdelivr.net/npm/remixicon@4.2.0/fonts/remixicon.css&quot; rel=&quot;stylesheet&quot; /&gt;</code>
                    </deckgo-highlight-code></pre>

                    <h6 class="mb-1 mt-2">Usage</h6>
                    <p class="text-gray-500 dark:text-dark-500 mb-2">Add icon with class name, class name rule: ri-{name}-{style}</p>

                    <pre><deckgo-highlight-code lang="js"><code slot="code">&lt;i class=&quot;ri-admin-line&quot;&gt;&lt;/i&gt;
&lt;i class=&quot;ri-admin-fill&quot;&gt;&lt;/i&gt;</code>
</deckgo-highlight-code></pre>
                    <p class="mb-0 text-gray-500 dark:text-dark-500 mt-2">For more details, see the <a href="#!" class="transition duration-200 ease-linear hover:text-primary-600 text-primary-500">documentation</a>.</p>
                </div>
            </div><!--end col-->
            <div class="col-span-12 md:col-span-6 card">
                <div class="card-header">
                    <h6 class="card-title">Color Icons</h6>
                </div>
                <div class="card-body">
                    <div class="*:size-10 *:flex *:items-center *:justify-center flex items-center *:border *:border-gray-200 dark:*:border-dark-800 gap-2 *:rounded-md flex-wrap">
                        <div><i class="ri-building-line"></i></div>
                        <div><i class="text-gray-500 dark:text-dark-500 ri-archive-line"></i></div>
                        <div><i class="ri-pencil-ruler-2-line text-primary-500"></i></div>
                        <div><i class="text-green-500 ri-briefcase-line"></i></div>
                        <div><i class="text-yellow-500 ri-customer-service-line"></i></div>
                        <div><i class="text-purple-500 ri-discuss-line"></i></div>
                        <div><i class="text-red-500 ri-brush-3-line"></i></div>
                    </div>
                </div>
            </div><!--end col-->
            <div class="col-span-12 md:col-span-6 card">
                <div class="card-header">
                    <h6 class="card-title">Using SVG Code</h6>
                </div>
                <div class="card-body">
                    <div class="flex flex-wrap items-center gap-5">
                        <div>
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="size-6 text-primary-500">
                                <path d="M5.88401 18.6533C5.58404 18.4526 5.32587 18.1975 5.0239 17.8369C4.91473 17.7065 4.47283 17.1524 4.55811 17.2583C4.09533 16.6833 3.80296 16.417 3.50156 16.3089C2.9817 16.1225 2.7114 15.5499 2.89784 15.0301C3.08428 14.5102 3.65685 14.2399 4.17672 14.4263C4.92936 14.6963 5.43847 15.1611 6.12425 16.0143C6.03025 15.8974 6.46364 16.441 6.55731 16.5529C6.74784 16.7804 6.88732 16.9182 6.99629 16.9911C7.20118 17.1283 7.58451 17.1874 8.14709 17.1311C8.17065 16.7489 8.24136 16.3783 8.34919 16.0358C5.38097 15.3104 3.70116 13.3952 3.70116 9.63971C3.70116 8.40085 4.0704 7.28393 4.75917 6.3478C4.5415 5.45392 4.57433 4.37284 5.06092 3.15636C5.1725 2.87739 5.40361 2.66338 5.69031 2.57352C5.77242 2.54973 5.81791 2.53915 5.89878 2.52673C6.70167 2.40343 7.83573 2.69705 9.31449 3.62336C10.181 3.41879 11.0885 3.315 12.0012 3.315C12.9129 3.315 13.8196 3.4186 14.6854 3.62277C16.1619 2.69 17.2986 2.39649 18.1072 2.52651C18.1919 2.54013 18.2645 2.55783 18.3249 2.57766C18.6059 2.66991 18.8316 2.88179 18.9414 3.15636C19.4279 4.37256 19.4608 5.45344 19.2433 6.3472C19.9342 7.28337 20.3012 8.39208 20.3012 9.63971C20.3012 13.3968 18.627 15.3048 15.6588 16.032C15.7837 16.447 15.8496 16.9105 15.8496 17.4121C15.8496 18.0765 15.8471 18.711 15.8424 19.4225C15.8412 19.6127 15.8397 19.8159 15.8375 20.1281C16.2129 20.2109 16.5229 20.5077 16.6031 20.9089C16.7114 21.4504 16.3602 21.9773 15.8186 22.0856C14.6794 22.3134 13.8353 21.5538 13.8353 20.5611C13.8353 20.4708 13.836 20.3417 13.8375 20.1145C13.8398 19.8015 13.8412 19.599 13.8425 19.4094C13.8471 18.7019 13.8496 18.0716 13.8496 17.4121C13.8496 16.7148 13.6664 16.2602 13.4237 16.051C12.7627 15.4812 13.0977 14.3973 13.965 14.2999C16.9314 13.9666 18.3012 12.8177 18.3012 9.63971C18.3012 8.68508 17.9893 7.89571 17.3881 7.23559C17.1301 6.95233 17.0567 6.54659 17.199 6.19087C17.3647 5.77663 17.4354 5.23384 17.2941 4.57702L17.2847 4.57968C16.7928 4.71886 16.1744 5.0198 15.4261 5.5285C15.182 5.69438 14.8772 5.74401 14.5932 5.66413C13.7729 5.43343 12.8913 5.315 12.0012 5.315C11.111 5.315 10.2294 5.43343 9.40916 5.66413C9.12662 5.74359 8.82344 5.69492 8.57997 5.53101C7.8274 5.02439 7.2056 4.72379 6.71079 4.58376C6.56735 5.23696 6.63814 5.77782 6.80336 6.19087C6.94565 6.54659 6.87219 6.95233 6.61423 7.23559C6.01715 7.8912 5.70116 8.69376 5.70116 9.63971C5.70116 12.8116 7.07225 13.9683 10.023 14.2999C10.8883 14.3971 11.2246 15.4769 10.5675 16.0482C10.3751 16.2156 10.1384 16.7802 10.1384 17.4121V20.5611C10.1384 21.5474 9.30356 22.2869 8.17878 22.09C7.63476 21.9948 7.27093 21.4766 7.36613 20.9326C7.43827 20.5204 7.75331 20.2116 8.13841 20.1276V19.1381C7.22829 19.1994 6.47656 19.0498 5.88401 18.6533Z"></path>
                            </svg>
                        </div>
                        <div>
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="text-pink-500 size-6">
                                <path d="M12.001 9C10.3436 9 9.00098 10.3431 9.00098 12C9.00098 13.6573 10.3441 15 12.001 15C13.6583 15 15.001 13.6569 15.001 12C15.001 10.3427 13.6579 9 12.001 9ZM12.001 7C14.7614 7 17.001 9.2371 17.001 12C17.001 14.7605 14.7639 17 12.001 17C9.24051 17 7.00098 14.7629 7.00098 12C7.00098 9.23953 9.23808 7 12.001 7ZM18.501 6.74915C18.501 7.43926 17.9402 7.99917 17.251 7.99917C16.5609 7.99917 16.001 7.4384 16.001 6.74915C16.001 6.0599 16.5617 5.5 17.251 5.5C17.9393 5.49913 18.501 6.0599 18.501 6.74915ZM12.001 4C9.5265 4 9.12318 4.00655 7.97227 4.0578C7.18815 4.09461 6.66253 4.20007 6.17416 4.38967C5.74016 4.55799 5.42709 4.75898 5.09352 5.09255C4.75867 5.4274 4.55804 5.73963 4.3904 6.17383C4.20036 6.66332 4.09493 7.18811 4.05878 7.97115C4.00703 9.0752 4.00098 9.46105 4.00098 12C4.00098 14.4745 4.00753 14.8778 4.05877 16.0286C4.0956 16.8124 4.2012 17.3388 4.39034 17.826C4.5591 18.2606 4.7605 18.5744 5.09246 18.9064C5.42863 19.2421 5.74179 19.4434 6.17187 19.6094C6.66619 19.8005 7.19148 19.9061 7.97212 19.9422C9.07618 19.9939 9.46203 20 12.001 20C14.4755 20 14.8788 19.9934 16.0296 19.9422C16.8117 19.9055 17.3385 19.7996 17.827 19.6106C18.2604 19.4423 18.5752 19.2402 18.9074 18.9085C19.2436 18.5718 19.4445 18.2594 19.6107 17.8283C19.8013 17.3358 19.9071 16.8098 19.9432 16.0289C19.9949 14.9248 20.001 14.5389 20.001 12C20.001 9.52552 19.9944 9.12221 19.9432 7.97137C19.9064 7.18906 19.8005 6.66149 19.6113 6.17318C19.4434 5.74038 19.2417 5.42635 18.9084 5.09255C18.573 4.75715 18.2616 4.55693 17.8271 4.38942C17.338 4.19954 16.8124 4.09396 16.0298 4.05781C14.9258 4.00605 14.5399 4 12.001 4ZM12.001 2C14.7176 2 15.0568 2.01 16.1235 2.06C17.1876 2.10917 17.9135 2.2775 18.551 2.525C19.2101 2.77917 19.7668 3.1225 20.3226 3.67833C20.8776 4.23417 21.221 4.7925 21.476 5.45C21.7226 6.08667 21.891 6.81333 21.941 7.8775C21.9885 8.94417 22.001 9.28333 22.001 12C22.001 14.7167 21.991 15.0558 21.941 16.1225C21.8918 17.1867 21.7226 17.9125 21.476 18.55C21.2218 19.2092 20.8776 19.7658 20.3226 20.3217C19.7668 20.8767 19.2076 21.22 18.551 21.475C17.9135 21.7217 17.1876 21.89 16.1235 21.94C15.0568 21.9875 14.7176 22 12.001 22C9.28431 22 8.94514 21.99 7.87848 21.94C6.81431 21.8908 6.08931 21.7217 5.45098 21.475C4.79264 21.2208 4.23514 20.8767 3.67931 20.3217C3.12348 19.7658 2.78098 19.2067 2.52598 18.55C2.27848 17.9125 2.11098 17.1867 2.06098 16.1225C2.01348 15.0558 2.00098 14.7167 2.00098 12C2.00098 9.28333 2.01098 8.94417 2.06098 7.8775C2.11014 6.8125 2.27848 6.0875 2.52598 5.45C2.78014 4.79167 3.12348 4.23417 3.67931 3.67833C4.23514 3.1225 4.79348 2.78 5.45098 2.525C6.08848 2.2775 6.81348 2.11 7.87848 2.06C8.94514 2.0125 9.28431 2 12.001 2Z"></path>
                            </svg>
                        </div>
                        <div>
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="text-sky-500 size-6">
                                <path d="M13.0052 18.4232C13.4288 18.3577 13.8623 18.43 14.2418 18.6295C14.7026 18.8719 15.2154 19 15.751 19C17.5459 19 19.001 17.5449 19.001 15.75C19.001 15.2144 18.8728 14.7016 18.6305 14.2408C18.431 13.8613 18.3586 13.4278 18.4242 13.0042C18.4752 12.6746 18.501 12.3393 18.501 12C18.501 8.41015 15.5908 5.5 12.001 5.5C11.6617 5.5 11.3264 5.52582 10.9968 5.57681C10.5731 5.64234 10.1396 5.57001 9.7602 5.37047C9.29939 5.12815 8.7866 5 8.25098 5C6.45605 5 5.00098 6.45507 5.00098 8.25C5.00098 8.78562 5.12912 9.29841 5.37145 9.75922C5.57098 10.1387 5.64332 10.5722 5.57778 10.9958C5.5268 11.3254 5.50098 11.6607 5.50098 12C5.50098 15.5899 8.41113 18.5 12.001 18.5C12.3403 18.5 12.6755 18.4742 13.0052 18.4232ZM12.001 20.5C7.30656 20.5 3.50098 16.6944 3.50098 12C3.50098 11.5545 3.53524 11.1171 3.60129 10.6901C3.21792 9.96108 3.00098 9.13087 3.00098 8.25C3.00098 5.35051 5.35148 3 8.25098 3C9.13185 3 9.96205 3.21694 10.6911 3.60031C11.118 3.53427 11.5555 3.5 12.001 3.5C16.6954 3.5 20.501 7.30558 20.501 12C20.501 12.4455 20.4667 12.8829 20.4007 13.3099C20.784 14.0389 21.001 14.8691 21.001 15.75C21.001 18.6495 18.6505 21 15.751 21C14.8701 21 14.0399 20.7831 13.3109 20.3997C12.8839 20.4657 12.4464 20.5 12.001 20.5ZM12.0539 16.9993C9.25237 16.9993 8.00098 15.6213 8.00098 14.5872C8.00098 14.0545 8.39165 13.6848 8.92925 13.6848C10.1291 13.6848 9.81574 15.4096 12.0539 15.4096C13.1967 15.4096 13.8297 14.7864 13.8297 14.1485C13.8297 13.7648 13.6423 13.3408 12.8868 13.1535L10.3965 12.5303C8.39095 12.0261 8.02674 10.9384 8.02674 9.91832C8.02674 7.7966 10.0191 7 11.8909 7C13.6117 7 15.6465 7.95606 15.6465 9.22756C15.6465 9.77348 15.1674 10.091 14.6347 10.091C13.6117 10.091 13.8004 8.67259 11.735 8.67259C10.712 8.67259 10.1389 9.13495 10.1389 9.79855C10.1389 10.4622 10.9418 10.6745 11.6409 10.834L13.4773 11.2427C15.4905 11.6947 16.001 12.8763 16.001 13.9891C16.001 15.7132 14.6765 17 12.0163 17L12.0539 16.9993Z"></path>
                            </svg>
                        </div>
                        <div>
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="text-yellow-500 size-6">
                                <path d="M5.54429 2.67305C5.81644 2.49995 6.13587 2.41612 6.45799 2.43329C6.78102 2.4505 7.09056 2.56841 7.34318 2.77049L7.34405 2.77119C7.59044 2.96879 7.76998 3.2372 7.85866 3.5399L9.30537 7.96754H14.6944L16.1411 3.5399C16.2298 3.23722 16.4093 2.96879 16.6557 2.77116L16.6604 2.76745C16.9128 2.56777 17.2209 2.45133 17.5424 2.43423C17.8638 2.41712 18.1826 2.50023 18.4547 2.67197L18.4571 2.67347C18.7307 2.84735 18.9427 3.10328 19.0624 3.40486L19.0664 3.41491L21.5393 9.86622C21.9619 10.9712 22.0136 12.1836 21.6865 13.3205C21.3594 14.4574 20.6715 15.457 19.7263 16.1685L12.9955 21.2331L12.9945 21.2338C12.7066 21.4513 12.3554 21.5692 11.9943 21.5692C11.6332 21.5692 11.2819 21.4513 10.9939 21.2337L4.26254 16.1683C3.32063 15.4562 2.63541 14.4574 2.30989 13.3224C1.98437 12.1873 2.03616 10.9772 2.45747 9.8741L4.93724 3.40497C5.0571 3.10297 5.26966 2.84673 5.54429 2.67305ZM6.35534 4.73567L4.16029 10.4639C3.87993 11.2013 3.82298 12.0676 4.04049 12.8261C4.25704 13.5811 4.71123 14.2461 5.33544 14.7225L11.9943 19.7329L18.6484 14.7265C19.2789 14.2502 19.7379 13.5822 19.9563 12.8227C20.1751 12.0624 20.1148 11.1847 19.8328 10.4455L17.6444 4.73558L16.0001 9.76791H7.9996L6.35534 4.73567Z"></path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div><!--end col-->
            <div class="col-span-12 card">
                <div class="card-header">
                    <h6 class="card-title">Sizes Icons</h6>
                </div>
                <div class="card-body">
                    <div class="flex flex-wrap items-center gap-3">
                        <div><i class="text-xs ri-github-line"></i></div>
                        <div><i class="ri-github-line text-14"></i></div>
                        <div><i class="ri-github-line"></i></div>
                        <div><i class="ri-github-line text-16"></i></div>
                        <div><i class="text-lg ri-github-line"></i></div>
                        <div><i class="text-xl ri-github-line"></i></div>
                        <div><i class="text-2xl ri-github-line"></i></div>
                        <div><i class="text-3xl ri-github-line"></i></div>
                        <div><i class="text-4xl ri-github-line"></i></div>
                        <div><i class="text-5xl ri-github-line"></i></div>
                        <div><i class="text-6xl ri-github-line"></i></div>
                    </div>
                </div>
            </div><!--end col-->
        </div><!--end grid-->



    </div>
    {{> partials/footer }}
</div>

{{> partials/vendor-scripts }}

<script type="module" src="assets/js/ui/advanced-highlight-code.init.js"></script>
<script type="module" src="assets/js/main.js"></script>

</body>
</html>