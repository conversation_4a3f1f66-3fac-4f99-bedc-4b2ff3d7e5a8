{{> partials/main }}

<head>

    {{> partials/title-meta title="Checkout" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}
        {{> partials/page-heading title="Checkout" sub-title="Ecommerce" }}

        <div class="grid grid-cols-12 gap-space">
            <div class="col-span-12 2xl:col-span-8">
                <div class="card">
                    <div class="card-header">
                        <h6 class="card-title">Personal Details</h6>
                    </div>
                    <div class="card-body" x-data="addressSelection()">

                        <div >
                            <template class="card" x-for="address in addresses" :key="index">
                            <div class="card">
                                <div class="card-body">
                                    <div class="flex items-center gap-2 float-end">
                                        <a href="#!" class="font-medium link link-primary" @click="editAddress(address)"><i data-lucide="pencil" class="inline-block size-4"></i> <span class="align-center">Edit</span></a>
                                        <a href="#!" class="font-medium link link-primary" @click="deleteAddress(address)"><i data-lucide="trash-2" class="inline-block size-4"></i> <span class="align-center">Delete</span></a>
                                    </div>
                                    <span class="badge badge-purple" x-text="address.type"></span>
                                    <h6 class="mt-2 mb-1" x-text="address.firstName + ' '+  address.lastName + ' - ' + address.phone"></h6>
                                    <p class="mb-3 text-gray-500 dark:text-dark-500" x-text="`${address.address}, ${address.city}, ${address.country} - ${address.zip}`"></p>
                                    <button x-on:click="selectAddress(address)" :class="selected === address ? 'btn btn-primary' : 'btn btn-sub-gray'">
                                        <span x-text="selected === address ? 'Selected Address' : 'Select Here'"></span>
                                    </button>
                                </div>
                            </div>
                            </template>
                        </div>
                        <div >
                            <div class="text-right">
                                <button @click="handleModal('showAddAddressForm')" class="btn btn-green">
                                    Add a New Address
                                </button>
                            </div>

                            <div class="mt-3" :class="{'show d-block': showAddAddressForm || showEditAddressForm}" x-show="showAddAddressForm || showEditAddressForm" x-transition>
                                <h6 class="mb-2" x-text=" showAddAddressForm ? 'Add a New Address' : 'Edit Address'">Add a New Address</h6>
                              
                                    <div class="grid grid-cols-12 gap-5">
                                        <div class="col-span-12 md:col-span-6">
                                            <label for="firstName" class="form-label">First Name</label>
                                            <input type="text" id="firstName" class="form-input" placeholder="Enter first name" x-model="addressForm.firstName" @input="validateField('firstName', addressForm.firstName, 'First name is required.')">
                                            <span x-show="errors.firstName" class="text-red-500" x-text="errors.firstName"></span>
                                        </div>
                                        <div class="col-span-12 md:col-span-6">
                                            <label for="lastName" class="form-label">Last Name</label>
                                            <input type="text" id="lastName" class="form-input" placeholder="Enter last name" x-model="addressForm.lastName" @input="validateField('lastName', addressForm.lastName, 'Last name is required.')">
                                            <span x-show="errors.lastName" class="text-red-500" x-text="errors.lastName"></span>
                                        </div>
                                        <div class="col-span-12 md:col-span-6">
                                            <label for="phoneNumber" class="form-label">Phone Number</label>
                                            <input type="number" id="phoneNumber" class="form-input" placeholder="************" x-model="addressForm.phone" @input="validatePhone()">
                                            <span x-show="errors.phone" class="text-red-500" x-text="errors.phone"></span>
                                        </div>
                                        <div class="col-span-12 md:col-span-6">
                                            <label for="alternatePhoneNumber" class="form-label">Alternate Phone Number (Optional)</label>
                                            <input type="number" id="alternatePhoneNumber" class="form-input" placeholder="************">
                                        </div>
                                        <div class="col-span-12">
                                            <label for="textareaInput2" class="form-label">Address (Area and Street)</label>
                                            <textarea  name="textareaInput2" id="textareaInput2" rows="3" class="h-auto form-input" placeholder="Area and Street" x-model="addressForm.address" @input="validateField('address', addressForm.address, 'Address is required.')"></textarea>
                                            <span x-show="errors.address" class="text-red-500" x-text="errors.address"></span>
                                        </div>
                                        <div class="col-span-12 md:col-span-4">
                                            <label for="cityDistrictTownInput" class="form-label">City/District/Town</label>
                                            <input type="text" id="cityDistrictTownInput" class="form-input" placeholder="Enter city name" x-model="addressForm.city" @input="validateField('city', addressForm.city, 'City is required.')">
                                            <span x-show="errors.city" class="text-red-500" x-text="errors.city"></span>
                                        </div>
                                        <div class="col-span-12 md:col-span-4">
                                            <label for="conutryNameInput" class="form-label">Country Name</label>
                                            <input type="text" id="conutryNameInput" class="form-input" placeholder="Enter country" x-model="addressForm.country" @input ="validateField('country', addressForm.country, 'Country is required.')">
                                            <span x-show="errors.country" class="text-red-500" x-text="errors.country"></span>
                                        </div>
                                        <div class="col-span-12 md:col-span-4">
                                            <label for="zipCodeInput" class="form-label">Zip Code</label>
                                            <input type="text" id="zipCodeInput" class="form-input" placeholder="Enter zip code" x-model="addressForm.zip" @input="validateField('zip', addressForm.zip, 'Zip code is required.')">
                                            <span x-show="errors.zip" class="text-red-500" x-text="errors.zip"></span>
                                        </div>
                                        <div class="col-span-12">
                                            <h6 class="mb-2">Address Type</h6>
                                            <div class="grid grid-cols-1 md:grid-cols-2 gap-space">
                                                <div>
                                                    <div class="input-radio-group">
                                                        <input id="homeRadio" class="input-radio input-radio-primary" name="addressType" type="radio" value="Home" x-model="addressForm.type" />
                                                        <label for="homeRadio" class="input-radio-label">Home (All day delivery)</label>
                                                    </div>
                                                </div>
                                                <div>
                                                    <div class="input-radio-group">
                                                        <input id="workRadio" class="input-radio input-radio-primary" name="addressType" type="radio" value="Work" x-model="addressForm.type"/>
                                                        <label for="workRadio" class="input-radio-label">Work (Delivery between 10 AM - 5 PM)</label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-span-12">
                                            <div class="flex flex-wrap justify-end gap-2">
                                                <button type="button" class="btn btn-active-red" @click="showAddAddressForm = false; showEditAddressForm = false">Cancel</button>
                                                <button @click="submitForm()" class="btn btn-primary">Save and Deliver Here</button>
                                            </div>
                                        </div>
                                    </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div><!--end col-->
            <div class="col-span-12 2xl:col-span-4">
                <div class="sticky mb-5 top-24">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="card-title">Order Summary</h6>
                        </div>
                        <div class="card-body"  x-data="orderSummary()" x-init="init()">
                            <template x-for="product in products" :key="product.id">
                                <div x-data="{ showCard: true }">
                                    <div class="mb-3" x-show="showCard">
                                        <button class="float-end"  @click="products.splice(products.indexOf(product), 1)">
                                            <i class="link size-4 link-red ri-close-line"></i>
                                        </button>
                                        <div class="flex flex-col gap-3 md:flex-row">
                                            <div class="flex items-center justify-center w-16 bg-gray-100 rounded-md dark:bg-dark-850">
                                                <img :src="product.image" alt="">
                                            </div>
                                            <div>
                                                <h6 class="mb-1" x-text="product.name"></h6>
                                                <p class="text-gray-500 dark:text-dark-500 divide-x divide-gray-200 dark:divide-dark-800 flex gap-2 items-center mb-2">
                                                    <span class="px-2 ltr:first:!pl-0 rtl:first:!pr-0" x-text='`${product.qty} Qty`'></span> 
                                                    <span class="px-2 ltr:first:!pl-0 rtl:first:!pr-0" x-text="product.color"></span> 
                                                    <span x-text="product.size"></span>
                                                </p>
                                                <h5 x-text="`${product.price}`"></h5>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </template>
                            <div class="mb-4">
                                <label for="discountCode" class="form-label">Discount Code</label>
                                <input type="text" id="discountCode" class="form-input" placeholder="Enter coupon code" x-model="discountCode">
                            </div>
                            <table class="table flush">
                                <tr>
                                    <td class="font-semibold">Sub Amount</td>
                                    <td>$<span x-text="subtotal.toFixed(2)"></span></td>
                                </tr>
                                <tr>
                                    <td class="font-semibold">Vat Amount (6%)</td>
                                    <td>$<span x-text="vat.toFixed(2)"></span></td>
                                </tr>
                                <tr>
                                    <td class="font-semibold">Discount (10%)</td>
                                    <td>-$<span x-text="discount.toFixed(2)"></span></td>
                                </tr>
                                <tr>
                                    <td class="font-semibold">Shipping Charge</td>
                                    <td>$<span x-text="shippingCharge.toFixed(2)"></span></td>
                                </tr>
                                <tr class="border-t border-gray-200 dark:border-dark-800">
                                    <td class="font-semibold">Total Amount</td>
                                    <td>$<span x-text="total.toFixed(2)"></span></td>
                                </tr>
                            </table>
                            <div class="my-4">
                                <a href="apps-ecommerce-payment.html" class="w-full btn btn-primary">Checkout Now</a>
                            </div>
                            <p class="text-center text-gray-500 dark:text-dark-500">By clicking the "checkout order" button, you agree to the terms of the public offers.</p>
                        </div>
                    </div>
                    <div class="flex gap-4 mb-5">
                        <div class="flex items-center justify-center bg-gray-100 rounded-md size-12 shrink-0 dark:bg-dark-850">
                            <i data-lucide="shield-check" class="text-gray-500 dark:text-dark-500 fill-gray-200 dark:fill-dark-850"></i>
                        </div>
                        <div>
                            <h6 class="mb-1">Safe and Secure</h6>
                            <p class="text-gray-500 dark:text-dark-500">Safe and Secure Payments. Easy returns. 100% Authentic products.</p>
                        </div>
                    </div>
                </div>
            </div><!--end col-->
        </div><!--end grid-->

    </div>
    {{> partials/footer }}
</div>

{{> partials/vendor-scripts }}

<script type="module" src="assets/js/apps/ecommerce/checkout.init.js"></script>

<script type="module" src="assets/js/main.js"></script>

</body>
</html>