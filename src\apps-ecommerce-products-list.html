{{> partials/main }}

<head>

    {{> partials/title-meta title="Products List" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

<div class="relative min-h-screen" x-data="productTable()" x-init="init(); initSlider()">
    <div class="page-wrapper pt-[calc(theme('spacing.topbar')_*_1.2)] group-data-[layout=horizontal]:pt-[calc(theme('spacing.topbar')_*_1.9)]">
        {{> partials/page-heading title="Products List" sub-title="Ecommerce" }}

        <div class="card">
            <div class="card-header">
                <div class="flex flex-wrap items-center gap-5">
                    <div class="grow">
                        <h6 class="mb-1 card-title">Products List</h6>
                        <p class="text-gray-500 dark:text-dark-500">Track your store's progress to boost your sales.</p>
                    </div>
                    <div class="flex flex-wrap gap-2 shrink-0">
                        <button class="btn btn-sub-gray" @click="exportTable"><i data-lucide="download" class="inline-block ltr:mr-1 rtl:ml-1 align-center size-4"></i> Export</button>
                        <a href="apps-ecommerce-create-products.html" class="btn btn-primary"><i data-lucide="plus" class="inline-block ltr:mr-1 rtl:ml-1 align-center size-4"></i> Add Product</a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="flex flex-wrap justify-between gap-2">
                    <div>
                        <div class="relative group/form">
                            <input type="text" class="ltr:pl-9 rtl:pr-9 form-input ltr:group-[&.right]/form:pr-9 rtl:group-[&.right]/form:pl-9 ltr:group-[&.right]/form:pl-4 rtl:group-[&.right]/form:pr-4" placeholder="Search for ..." @input="filterProjects" x-model="searchTerm">
                            <div class="absolute inset-y-0 flex items-center text-gray-500 dark:text-dark-500 ltr:left-3 rtl:right-3 ltr:group-[&.right]/form:right-3 rtl:group-[&.right]/form:left-3 ltr:group-[&.right]/form:left-auto rtl:group-[&.right]/form:right-auto focus:outline-hidden">
                                <i data-lucide="search" class="size-4"></i>
                            </div>
                        </div>
                    </div><!--end col-->
                    <div>
                        <div class="flex items-center gap-3">
                            <button class="btn btn-red btn-icon shrink-0" x-show="selectedItems.length > 0" @click="deleteSelectedItems()">
                                <i data-lucide="trash" class="inline-block size-4"></i>
                            </button>
                            <div id="sampleSelect" class="grow" @change="filterProjects()" ></div>
                            <div x-data="{
                                open: false,
                                toggle() {
                                    if (this.open) {
                                        return this.close();
                                    }
                            
                                    this.$refs.button.focus();
                            
                                    this.open = true;
                                },
                                close(focusAfter) {
                                    if (!this.open) return;
                            
                                    this.open = false;
                            
                                    focusAfter && focusAfter.focus();
                                }
                            }" x-on:keydown.escape.prevent.stop="close($refs.button)" x-on:focusin.window="!$refs.panel.contains($event.target) && close()" x-id="['dropdown-button']" class="dropdown shrink-0">
                                <button x-ref="button" x-on:click="toggle()" :aria-expanded="open" :aria-controls="$id('dropdown-button')" type="button" title="dropdown-button" class="btn btn-sub-gray">
                                    <i data-lucide="filter" class="inline-block ltr:mr-1 rtl:ml-1 align-center size-4"></i> Filters
                                </button>

                                <div x-ref="panel" x-show="open" x-transition.origin.top.right x-on:click.outside="close($refs.button)" :id="$id('dropdown-button')" style="display: none;" class="p-3 dropdown-menu dropdown-right !w-64">

                                    <h6 class="mb-4">Filter Options</h6>

                                    <form @submit.prevent="submitForm; close();">
                                        <h6 class="mb-2 text-sm">Status</h6>

                                        <div class="grid grid-cols-2 gap-4">
                                            <div class="input-check-group">
                                                <input id="publishedCheckboxFilter" class="input-check input-check-primary" type="checkbox" x-model="selectedStatus" value="Published"/>
                                                <label for="publishedCheckboxFilter" class="input-check-label">Published</label>
                                            </div>
                                            <div class="input-check-group">
                                                <input id="inactiveCheckboxFilter" class="input-check input-check-primary" type="checkbox" x-model="selectedStatus" value="Inactive" />
                                                <label for="inactiveCheckboxFilter" class="input-check-label">Inactive</label>
                                            </div>
                                            <div class="col-span-2">
                                                <label class="mb-3 form-label">Price Range</label>
                                                <div id="priceFilter"></div>
                                            </div>
                                        </div>

                                        <div class="flex items-center justify-end gap-2 pt-1 mt-5">
                                            <button type="reset" class="btn-sm btn btn-sub-gray">
                                                Reset
                                            </button>
                                            <button type="submit"  class="btn-sm btn btn-primary">
                                                Apply
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div><!--end col-->
                </div><!--end grid-->
            </div>
            <div class="pt-0 card-body">
                <div>
                    <div class="overflow-x-auto table-box">
                        <table class="table hovered">
                            <tbody>
                                <tr class="text-gray-500 bg-gray-100 dark:bg-dark-850 dark:text-dark-500">
                                    <th class="!font-medium">
                                        <div class="input-check-group">
                                            <label for="checkboxAll" class="hidden input-check-label"></label>
                                            <input id="checkboxAll" class="input-check input-check-primary" type="checkbox" x-model="selectAll" @click="toggleAll" />
                                        </div>
                                    </th>
                                    <th x-on:click="sort('productID')" class="!font-medium cursor-pointer">Product ID <span x-show="sortBy === 'productID'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                    <th x-on:click="sort('productName')" class="!font-medium cursor-pointer">Product <span x-show="sortBy === 'productName'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                    <th x-on:click="sort('category')" class="!font-medium cursor-pointer">Category <span x-show="sortBy === 'category'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                    <th class="!font-medium cursor-pointer">Stock</th>
                                    <th x-on:click="sort('price')" class="!font-medium cursor-pointer">Price <span x-show="sortBy === 'price'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                    <th x-on:click="sort('qty')" class="!font-medium cursor-pointer">QTY <span x-show="sortBy === 'qty'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                    <th x-on:click="sort('revenue')" class="!font-medium cursor-pointer">Revenue <span x-show="sortBy === 'revenue'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                    <th x-on:click="sort('status')" class="!font-medium cursor-pointer">status <span x-show="sortBy === 'status'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                    <th class="!font-medium">Action</th>
                                </tr>
                                <template x-if="displayedProducts.length > 0">
                                    <template x-for="(product, index) in displayedProducts" :key="index">
                                        <tr>
                                            <td class="whitespace-nowrap">
                                                <div class="input-check-group">
                                                    <label :for="`product${index}`" class="hidden input-check-label"></label>
                                                    <input :id="`product${index}`" class="input-check input-check-primary" type="checkbox" @click="toggleItem(product)" :checked="selectedItems.includes(product)" />
                                                </div>
                                            </td>
                                            <td class="whitespace-nowrap"><a href="#!" class="link link-primary" x-text="product.productID"></a></td>
                                            <td class="whitespace-nowrap">
                                                <div class="flex items-center gap-2">
                                                    <div class="flex items-center justify-center p-1 border border-gray-200 rounded-sm dark:border-dark-800 size-9">
                                                        <img :src="product.image" alt="" class="rounded-sm">
                                                    </div>
                                                    <h6><a href="apps-ecommerce-product-overview.html" x-text="product.productName"></a></h6>
                                                </div>
                                            </td>
                                            <td x-text="product.category"></td>
                                            <td class="whitespace-nowrap">
                                                <label :for="'toggleStatus_' + index" class="switch-group switch-soft">
                                                    <div class="relative">
                                                        <input type="checkbox" :id="'toggleStatus_' + index" class="sr-only peer" x-bind:checked="product.status === 'Published'" x-on:click="toggleStatus(product)" />
                                                        <div class="switch-wrapper peer-checked:!bg-purple-500/15"></div>
                                                        <div class="switch-dot peer-checked:translate-x-full rtl:peer-checked:-translate-x-full peer-checked:!bg-purple-500"></div>
                                                    </div>
                                                </label>
                                            </td>
                                            <td x-text="product.price"></td>
                                            <td x-text="product.qty"></td>
                                            <td x-text="product.revenue"></td>
                                            <td class="whitespace-nowrap">
                                                <span x-text="product.status" :class="{
                                                'badge badge-green': product.status === 'Published',
                                                'badge badge-gray': product.status === 'Inactive'
                                            }"></span>
                                            </td>
                                            <td class="whitespace-nowrap">
                                                <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown">
                                                    <button x-ref="button" x-on:click="toggle()" :aria-expanded="open.toString()" type="button" title="dropdown-button" class="flex items-center text-gray-500 dark:text-dark-500">
                                                        <i class="ri-more-2-fill"></i>
                                                    </button>
                                                    <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden">
                                                        <ul>
                                                            <li>
                                                                <a href="apps-ecommerce-product-overview.html" class="dropdown-item">
                                                                    <i class="align-middle ltr:mr-2 rtl:ml-2 ri-eye-line"></i> <span>Overview</span>
                                                                </a>
                                                            </li>
                                                            <li>
                                                                <a href="apps-ecommerce-create-products.html" class="dropdown-item">
                                                                    <i class="align-middle ltr:mr-2 rtl:ml-2 ri-pencil-line"></i> <span>Edit</span>
                                                                </a>
                                                            </li>
                                                            <li>
                                                                <a href="#!" data-modal-target="deleteModal" @click=" deleteItem = product.productID" class="dropdown-item hover:!text-red-500">
                                                                    <i class="align-middle ltr:mr-2 rtl:ml-2 ri-delete-bin-line"></i> <span>Delete</span>
                                                                </a>
                                                            </li>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    </template>
                                </template>
                                <tr>
                                    <template x-if="displayedProducts.length == 0">
                                        <td colspan="10" class="!p-8">
                                            <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" class="mx-auto size-12" viewBox="0 0 48 48">
                                                <linearGradient id="SVGID_1__h35ynqzIJzH4_gr1" x1="34.598" x2="15.982" y1="15.982" y2="34.598" gradientUnits="userSpaceOnUse">
                                                    <stop offset="0" stop-color="#60e8fe"></stop>
                                                    <stop offset=".033" stop-color="#6ae9fe"></stop>
                                                    <stop offset=".197" stop-color="#97f0fe"></stop>
                                                    <stop offset=".362" stop-color="#bdf5ff"></stop>
                                                    <stop offset=".525" stop-color="#dafaff"></stop>
                                                    <stop offset=".687" stop-color="#eefdff"></stop>
                                                    <stop offset=".846" stop-color="#fbfeff"></stop>
                                                    <stop offset="1" stop-color="#fff"></stop>
                                                </linearGradient>
                                                <path fill="url(#SVGID_1__h35ynqzIJzH4_gr1)" d="M40.036,33.826L31.68,25.6c0.847-1.739,1.335-3.684,1.335-5.748c0-7.27-5.894-13.164-13.164-13.164	S6.688,12.582,6.688,19.852c0,7.27,5.894,13.164,13.164,13.164c2.056,0,3.995-0.485,5.728-1.326l3.914,4.015l4.331,4.331	c1.715,1.715,4.496,1.715,6.211,0C41.751,38.321,41.751,35.541,40.036,33.826z"></path>
                                                <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M31.95,25.739l8.086,8.086c1.715,1.715,1.715,4.496,0,6.211l0,0c-1.715,1.715-4.496,1.715-6.211,0	l-4.331-4.331"></path>
                                                <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M7.525,24.511c-1.771-4.694-0.767-10.196,3.011-13.975c3.847-3.847,9.48-4.817,14.228-2.912"></path>
                                                <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M30.856,12.603c3.376,5.114,2.814,12.063-1.688,16.565c-4.858,4.858-12.565,5.129-17.741,0.814"></path>
                                            </svg>
                                            <p class="mt-2 text-center text-gray-500 dark:text-dark-500">No matching records found</p>
                                        </td>
                                    </template>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="grid grid-cols-12 gap-5 mt-5 items-center" x-show="displayedProducts.length !== 0">
                        <div class="col-span-12 md:col-span-6 justify-center md:justify-start flex flex-wrap">
                            <p class="text-gray-500 dark:text-dark-500 ltr:pr-1 rtl:pl-1">Showing <b x-text="showingStart"></b> - <b x-text="showingEnd"></b> of <b x-text="filteredProducts.length"></b> Results</p>
                            <span x-show="selectedItems.length > 0"><b x-text="selectedItems.length != 0 ? selectedItems.length : ''"> </b> Row Selected</span>
                        </div>
                        <div class="col-span-12 md:col-span-6">
                            <div class="flex justify-center md:justify-end pagination pagination-primary">
                                <button @click="prevPage()" :disabled="currentPage === 1" class="pagination-pre">
                                    <i data-lucide="chevron-left" class="mr-1 ltr:inline-block rtl:hidden size-4"></i>
                                    <i data-lucide="chevron-right" class="ml-1 ltr:hidden rtl:inline-block size-4"></i>
                                    Prev
                                </button>
                                <template x-for="page in totalPages" :key="page">
                                    <button @click="gotoPage(page)" :class="{ 'active': currentPage === page }" class="pagination-item">
                                        <span x-text="page"></span>
                                    </button>
                                </template>
                                <button @click="nextPage()" :disabled="currentPage === totalPages" class="pagination-next">
                                    Next
                                    <i data-lucide="chevron-right" class="ml-1 ltr:inline-block rtl:hidden size-4"></i>
                                    <i data-lucide="chevron-left" class="mr-1 ltr:hidden rtl:inline-block size-4"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    
                </div>
            </div>
        </div>
    </div>
    <div id="deleteModal" class="!hidden modal show">
        <div class="modal-wrap modal-xs modal-center">
            <div class="text-center modal-content p-7">
                <div class="flex items-center justify-center mx-auto mb-4 text-red-500 rounded-full bg-red-500/10 size-14 backdrop-blur-xl">
                    <i data-lucide="trash-2" class="size-6"></i>
                </div>
                <h5 class="mb-4">Are you sure you want to delete this Product ?</h5>
                <div class="flex items-center justify-center gap-2">
                    <button class="btn btn-red" @click="deleteProduct(deleteItem)" data-modal-close="deleteModal">Delete</button>
                    <button data-modal-close="deleteModal" class="btn link link-primary">Cancel</button>
                </div>
            </div>
        </div>
    </div><!--end-->
    {{> partials/footer }}
</div>


{{> partials/vendor-scripts }}

<script src="assets/libs/nouislider/nouislider.min.js"></script>
<script src="assets/libs/wnumb/wNumb.min.js"></script>

<script type="module" src="assets/js/apps/ecommerce/product-list.init.js"></script>

<script type="module" src="assets/js/main.js"></script>

</body>
</html>