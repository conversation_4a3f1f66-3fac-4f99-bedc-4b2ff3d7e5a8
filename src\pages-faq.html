{{> partials/main }}

<head>

    {{> partials/title-meta title="FAQ's" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}
{{> partials/page-heading title="FAQ's" sub-title="Pages" }}

<div class="grid grid-cols-12 gap-x-space">
    <div class="col-span-12">
        <div class="card">
            <div class="card-header">
                <h6>Frequently asked questions (FAQ)</h6>
            </div>
            <div class="card-body">
                <p class="mb-4 text-gray-500 dark:text-dark-500">They serve as a self-service tool for customers to easily find the information they need without contacting customer support.</p>
                <div class="grid items-center grid-cols-1 gap-3 xl:grid-cols-2" x-data="{selected:1}">
                    <div class="xl:hidden">
                        <div class="text-center">
                            <img src="assets/images/auth/faq.png" alt="" class="mx-auto">

                            <h5 class="mt-5 mb-1">Frequently asked questions (FAQ)</h5>
                            <p class="mb-5 text-gray-500 dark:text-dark-500">Cleaning up common queries about domiex.</p>
                            <div class="flex flex-wrap items-center justify-center gap-3">
                                <button class="btn btn-purple"><i data-lucide="phone" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> Contact US</button>
                                <button class="btn btn-gray"><i data-lucide="headset" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> Help Center</button>
                            </div>
                        </div>
                    </div>
                    <div class="space-y-3">
                        <div>
                            <div class="accordion-boxed">
                                <button type="button" class="accordion-button accordion-primary" @click="selected !== 1 ? selected = 1 : selected = null" x-bind:class="{ 'active': selected === 1 }">
                                    <div class="flex items-center justify-between">
                                        <span>Why do we use Tailwind CSS?</span>
                                        <span class="text-gray-500 ico-down" x-bind:class="{ 'text-primary-500': selected === 1 }" x-show="selected !== 1"><i data-lucide="chevron-down"></i></span>
                                        <span class="text-gray-500 ico-up" x-bind:class="{ 'text-primary-500': selected === 1 }" x-show="selected === 1"><i data-lucide="chevron-up"></i></span>
                                    </div>
                                </button>
                                <div class="accordion-main-content" x-ref="container1" x-bind:style="selected == 1 ? 'max-height: ' + $refs.container1.scrollHeight + 'px' : ''">
                                    <div class="px-3 py-2.5">
                                        <p class="text-gray-500 dark:text-dark-500">Tailwind CSS is used to design and style web pages fast and Responsive. Rapid Development, Highly Customizable, Reduced CSS File Size, Great Documentation, and Community Support are the main reasons for using Tailwind CSS.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="accordion-boxed">
                                <button type="button" class="accordion-button accordion-primary" @click="selected !== 2 ? selected = 2 : selected = null" x-bind:class="{ 'active': selected === 2 }">
                                    <div class="flex items-center justify-between">
                                        <span>Can we change the base font-family in Tailwind config?</span>
                                        <span class="text-gray-500 ico-down" x-bind:class="{ 'text-primary-500': selected === 2 }" x-show="selected !== 2"><i data-lucide="chevron-down"></i></span>
                                        <span class="text-gray-500 ico-up" x-bind:class="{ 'text-primary-500': selected === 2 }" x-show="selected === 2"><i data-lucide="chevron-up"></i></span>
                                    </div>
                                </button>
                                <div class="accordion-main-content" x-ref="container2" x-bind:style="selected == 2 ? 'max-height: ' + $refs.container2.scrollHeight + 'px' : ''">
                                    <div class="px-3 py-2.5">
                                        <p class="text-gray-500 dark:text-dark-500">Yes, we can change the base <code class="text-pink-500">font-family</code> in Tailwind <code class="text-pink-500">config.</code> To adjust the main font style in Tailwind CSS, you can modify it by making changes in the “theme” part of your configuration file (<code class="text-pink-500">tailwind.config.js</code>). Just open that file, find the theme section, and add or update the fontFamily setting. We can also Change the font-family in the Tailwind config with different techniques Changing base font-family, Adding a new font family, Removing font family.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="accordion-boxed">
                                <button type="button" class="accordion-button accordion-primary" @click="selected !== 3 ? selected = 3 : selected = null" x-bind:class="{ 'active': selected === 3 }">
                                    <div class="flex items-center justify-between">
                                        <span>How to create a form with Tailwind CSS?</span>
                                        <span class="text-gray-500 ico-down" x-bind:class="{ 'text-primary-500': selected === 3 }" x-show="selected !== 3"><i data-lucide="chevron-down"></i></span>
                                        <span class="text-gray-500 ico-up" x-bind:class="{ 'text-primary-500': selected === 3 }" x-show="selected === 3"><i data-lucide="chevron-up"></i></span>
                                    </div>
                                </button>
                                <div class="accordion-main-content" x-ref="container3" x-bind:style="selected == 3 ? 'max-height: ' + $refs.container3.scrollHeight + 'px' : ''">
                                    <div class="px-3 py-2.5">
                                        <p class="text-gray-500 dark:text-dark-500">Tailwind CSS, offers Tailwind forms as plugins that provide a foundational reset for form styles. Install TailwindCSS by writing the following command. We can also use utility classes to make a form with Tailwind CSS, use the easy-to-apply classes for backgrounds, borders, shadows, etc. Start by creating the form element and use the space-y-{n} class to add vertical spacing between the form controls.”</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="accordion-boxed">
                                <button type="button" class="accordion-button accordion-primary" @click="selected !== 4 ? selected = 4 : selected = null" x-bind:class="{ 'active': selected === 4 }">
                                    <div class="flex items-center justify-between">
                                        <span>What is Tailwind CSS, and what is Utility-First CSS?</span>
                                        <span class="text-gray-500 ico-down" x-bind:class="{ 'text-primary-500': selected === 4 }" x-show="selected !== 4"><i data-lucide="chevron-down"></i></span>
                                        <span class="text-gray-500 ico-up" x-bind:class="{ 'text-primary-500': selected === 4 }" x-show="selected === 4"><i data-lucide="chevron-up"></i></span>
                                    </div>
                                </button>
                                <div class="accordion-main-content" x-ref="container4" x-bind:style="selected == 4 ? 'max-height: ' + $refs.container4.scrollHeight + 'px' : ''">
                                    <div class="px-3 py-2.5">
                                        <p class="mb-2 text-gray-500">Tailwind CSS is a utility-first CSS framework designed for rapid UI development. Instead of providing pre-built components, it offers low-level utility classes that let you build custom designs without ever leaving your HTML.</p>
                                        <p class="text-gray-500 dark:text-dark-500">Utility-first CSS is an approach where you use small, single-purpose classes to build your user interface. These utility classes are composed to create complex designs directly in the HTML, rather than relying on custom CSS. This approach favors composition over inheritance, making it easier to maintain and scale your codebase.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="accordion-boxed">
                                <button type="button" class="accordion-button accordion-primary" @click="selected !== 5 ? selected = 5 : selected = null" x-bind:class="{ 'active': selected === 5 }">
                                    <div class="flex items-center justify-between">
                                        <span>Why do we use Tailwind CSS?</span>
                                        <span class="text-gray-500 ico-down" x-bind:class="{ 'text-primary-500': selected === 5 }" x-show="selected !== 5"><i data-lucide="chevron-down"></i></span>
                                        <span class="text-gray-500 ico-up" x-bind:class="{ 'text-primary-500': selected === 5 }" x-show="selected === 5"><i data-lucide="chevron-up"></i></span>
                                    </div>
                                </button>
                                <div class="accordion-main-content" x-ref="container5" x-bind:style="selected == 5 ? 'max-height: ' + $refs.container5.scrollHeight + 'px' : ''">
                                    <div class="px-3 py-2.5">
                                        <p class="text-gray-500 dark:text-dark-500">Tailwind CSS is used to design and style web pages fast and Responsive. Rapid Development, Highly Customizable, Reduced CSS File Size, Great Documentation, and Community Support are the main reasons for using Tailwind CSS.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="accordion-boxed">
                                <button type="button" class="accordion-button accordion-primary" @click="selected !== 6 ? selected = 6 : selected = null" x-bind:class="{ 'active': selected === 6 }">
                                    <div class="flex items-center justify-between">
                                        <span>Why do we use Tailwind CSS?</span>
                                        <span class="text-gray-500 ico-down" x-bind:class="{ 'text-primary-500': selected === 6 }" x-show="selected !== 6"><i data-lucide="chevron-down"></i></span>
                                        <span class="text-gray-500 ico-up" x-bind:class="{ 'text-primary-500': selected === 6 }" x-show="selected === 6"><i data-lucide="chevron-up"></i></span>
                                    </div>
                                </button>
                                <div class="accordion-main-content" x-ref="container6" x-bind:style="selected == 6 ? 'max-height: ' + $refs.container6.scrollHeight + 'px' : ''">
                                    <div class="px-3 py-2.5">
                                        <p class="text-gray-500 dark:text-dark-500">Tailwind CSS is used to design and style web pages fast and Responsive. Rapid Development, Highly Customizable, Reduced CSS File Size, Great Documentation, and Community Support are the main reasons for using Tailwind CSS.</p>
                                    </div>
                                </div>
                            </div>
                        </div><!--end col-->
                        <div>
                            <div class="accordion-boxed">
                                <button type="button" class="accordion-button accordion-primary" @click="selected !== 7 ? selected = 7 : selected = null" x-bind:class="{ 'active': selected === 7 }">
                                    <div class="flex items-center justify-between">
                                        <span>Is Tailwind CSS open-source (FREE to use)?</span>
                                        <span class="text-gray-500 ico-down" x-bind:class="{ 'text-primary-500': selected === 7 }" x-show="selected !== 7"><i data-lucide="chevron-down"></i></span>
                                        <span class="text-gray-500 ico-up" x-bind:class="{ 'text-primary-500': selected === 7 }" x-show="selected === 7"><i data-lucide="chevron-up"></i></span>
                                    </div>
                                </button>
                                <div class="accordion-main-content" x-ref="container7" x-bind:style="selected == 7 ? 'max-height: ' + $refs.container7.scrollHeight + 'px' : ''">
                                    <div class="px-3 py-2.5">
                                        <p class="text-gray-500 dark:text-dark-500">Tailwind CSS is an open-source project, available for free usage and utility-first CSS framework that provides responsiveness.</p>
                                    </div>
                                </div>
                            </div>
                        </div><!--end col-->
                        <div>
                            <div class="accordion-boxed">
                                <button type="button" class="accordion-button accordion-primary" @click="selected !== 8 ? selected = 8 : selected = null" x-bind:class="{ 'active': selected === 8 }">
                                    <div class="flex items-center justify-between">
                                        <span>How to integrate Tailwind CSS into the HTML file?</span>
                                        <span class="text-gray-500 ico-down" x-bind:class="{ 'text-primary-500': selected === 8 }" x-show="selected !== 8"><i data-lucide="chevron-down"></i></span>
                                        <span class="text-gray-500 ico-up" x-bind:class="{ 'text-primary-500': selected === 8 }" x-show="selected === 8"><i data-lucide="chevron-up"></i></span>
                                    </div>
                                </button>
                                <div class="accordion-main-content" x-ref="container8" x-bind:style="selected == 8 ? 'max-height: ' + $refs.container8.scrollHeight + 'px' : ''">
                                    <div class="px-3 py-2.5">
                                        <p class="text-gray-500 dark:text-dark-500">We can easily integrate the tailwind CSS to the project via CDN links and by installing it from npm or yarn.</p>
                                    </div>
                                </div>
                            </div>
                        </div><!--end col-->
                        <div>
                            <div class="accordion-boxed">
                                <button type="button" class="accordion-button accordion-primary" @click="selected !== 9 ? selected = 9 : selected = null" x-bind:class="{ 'active': selected === 9 }">
                                    <div class="flex items-center justify-between">
                                        <span>Do Tailwind CSS 3 Classes Override Previous Classes?</span>
                                        <span class="text-gray-500 ico-down" x-bind:class="{ 'text-primary-500': selected === 9 }" x-show="selected !== 9"><i data-lucide="chevron-down"></i></span>
                                        <span class="text-gray-500 ico-up" x-bind:class="{ 'text-primary-500': selected === 9 }" x-show="selected === 9"><i data-lucide="chevron-up"></i></span>
                                    </div>
                                </button>
                                <div class="accordion-main-content" x-ref="container9" x-bind:style="selected == 9 ? 'max-height: ' + $refs.container9.scrollHeight + 'px' : ''">
                                    <div class="px-3 py-2.5">
                                        <p class="text-gray-500 dark:text-dark-500">Tailwind CSS is designed to be a low-level utility-first framework, which means that classes are not automatically overridden by default. This make the styling process for form elements simple and allowing easy customization with utilities.</p>
                                    </div>
                                </div>
                            </div>
                        </div><!--end col-->
                        <div>
                            <div class="accordion-boxed">
                                <button type="button" class="accordion-button accordion-primary" @click="selected !== 10 ? selected = 10 : selected = null" x-bind:class="{ 'active': selected === 10 }">
                                    <div class="flex items-center justify-between">
                                        <span>How to make text bold in Tailwind CSS?</span>
                                        <span class="text-gray-500 ico-down" x-bind:class="{ 'text-primary-500': selected === 10 }" x-show="selected !== 10"><i data-lucide="chevron-down"></i></span>
                                        <span class="text-gray-500 ico-up" x-bind:class="{ 'text-primary-500': selected === 10 }" x-show="selected === 10"><i data-lucide="chevron-up"></i></span>
                                    </div>
                                </button>
                                <div class="accordion-main-content" x-ref="container10" x-bind:style="selected == 10 ? 'max-height: ' + $refs.container10.scrollHeight + 'px' : ''">
                                    <div class="px-3 py-2.5">
                                        <p class="text-gray-500 dark:text-dark-500">For achieving the bold text we can easily add utility class font-bold.</p>
                                    </div>
                                </div>
                            </div>
                        </div><!--end col-->
                        <div>
                            <div class="accordion-boxed">
                                <button type="button" class="accordion-button accordion-primary" @click="selected !== 11 ? selected = 11 : selected = null" x-bind:class="{ 'active': selected === 11 }">
                                    <div class="flex items-center justify-between">
                                        <span>How to center both horizontally and vertically?</span>
                                        <span class="text-gray-500 ico-down" x-bind:class="{ 'text-primary-500': selected === 11 }" x-show="selected !== 11"><i data-lucide="chevron-down"></i></span>
                                        <span class="text-gray-500 ico-up" x-bind:class="{ 'text-primary-500': selected === 11 }" x-show="selected === 11"><i data-lucide="chevron-up"></i></span>
                                    </div>
                                </button>
                                <div class="accordion-main-content" x-ref="container11" x-bind:style="selected == 11 ? 'max-height: ' + $refs.container11.scrollHeight + 'px' : ''">
                                    <div class="px-3 py-2.5">
                                        <p class="text-gray-500 dark:text-dark-500">We can easily add utility class self-center.</p>
                                    </div>
                                </div>
                            </div>
                        </div><!--end col-->
                        <div>
                            <div class="accordion-boxed">
                                <button type="button" class="accordion-button accordion-primary" @click="selected !== 12 ? selected = 12 : selected = null" x-bind:class="{ 'active': selected === 12 }">
                                    <div class="flex items-center justify-between">
                                        <span>What are the Empty elements in HTML?</span>
                                        <span class="text-gray-500 ico-down" x-bind:class="{ 'text-primary-500': selected === 12 }" x-show="selected !== 12"><i data-lucide="chevron-down"></i></span>
                                        <span class="text-gray-500 ico-up" x-bind:class="{ 'text-primary-500': selected === 12 }" x-show="selected === 12"><i data-lucide="chevron-up"></i></span>
                                    </div>
                                </button>
                                <div class="accordion-main-content" x-ref="container12" x-bind:style="selected == 12 ? 'max-height: ' + $refs.container12.scrollHeight + 'px' : ''">
                                    <div class="px-3 py-2.5">
                                        <p class="text-gray-500 dark:text-dark-500">The empty elements in HTML are the elements that don’t require and closing tag followed by the opening tag. These elements are also known as self-closing elements. Example: <code>&lt;img&gt;</code>, &lt;input&gt;, &lt;br&gt;, &lt;hr&gt; etc.</p>
                                    </div>
                                </div>
                            </div>
                        </div><!--end col-->
                    </div><!--end grid-->
                    <div class="hidden xl:block">
                        <div class="text-center">
                            <img src="assets/images/auth/faq.png" alt="" class="mx-auto">
                            <h5 class="mt-5 mb-1">Frequently asked questions (FAQ)</h5>
                            <p class="mb-5 text-gray-500 dark:text-dark-500">Cleaning up common queries about domiex.</p>
                            <div class="flex items-center justify-center gap-3">
                                <a href="pages-contact-us.html" class="btn btn-purple"><i data-lucide="phone" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> Contact US</a>
                                <a href="pages-help-center.html" class="btn btn-sub-gray"><i data-lucide="headset" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> Help Center</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12">
        <div class="card">
            <div class="card-header">
                <h6>Video Tutorial by Domiex</h6>
            </div>
            <div class="card-body">
                <div class="grid grid-cols-1 gap-5 md:grid-cols-2 xl:grid-cols-3">
                    <iframe class="w-full rounded-xl aspect-video" src="https://www.youtube.com/embed/DxcJbrs6rKk?si=r9xt6eHRj0kayf8d" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>
                    <iframe class="w-full rounded-xl aspect-video" src="https://www.youtube.com/embed/eSzNNYk7nVU?si=EHJjJ8BjAsp6yMgx" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>
                    <iframe class="w-full rounded-xl aspect-video" src="https://www.youtube.com/embed/MAtaT8BZEAo?si=iyOi2lREUWB35ct6" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>
                </div>
            </div>
        </div>
    </div><!--end col-->
</div><!--end grid-->
</div>
{{> partials/footer }}
</div>

{{> partials/vendor-scripts }}

<script type="module" src="assets/js/main.js"></script>

</body>
</html>