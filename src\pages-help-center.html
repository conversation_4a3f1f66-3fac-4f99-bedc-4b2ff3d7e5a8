{{> partials/main }}

<head>

    {{> partials/title-meta title="Help Center" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}
{{> partials/page-heading title="Help Center" sub-title="Pages" }}

<div class="grid grid-cols-12 gap-x-space" x-data="ticketApp()">
    <div class="col-span-12 xl:col-span-4 2xl:col-span-3">
        <div class="card">
            <div class="card-body">
                <h6>Search for a Question</h6>
                <p class="mb-3 text-gray-500 dark:text-dark-500">Type your question or search keyword</p>
                <div class="relative group/form">
                    <input type="text" id="iconWithInput" class="ltr:pl-9 rtl:pr-9 form-input ltr:group-[&.right]/form:pr-9 rtl:group-[&.right]/form:pl-9 ltr:group-[&.right]/form:pl-4 rtl:group-[&.right]/form:pr-4" placeholder="Start typing ..." x-model="searchQuery" @input="searchQuestions()">
                    <button class="absolute inset-y-0 flex items-center text-gray-500 dark:text-dark-500 ltr:left-3 rtl:right-3 ltr:group-[&.right]/form:right-3 rtl:group-[&.right]/form:left-3 ltr:group-[&.right]/form:left-auto rtl:group-[&.right]/form:right-auto focus:outline-hidden">
                        <i data-lucide="search" class="size-4"></i>
                    </button>
                </div>
                <div class="h-auto lg:h-[calc(100vh_-_28rem)]" data-simplebar>
                    <ul class="px-1 my-5 space-y-3">
                        <li><a href="#!" :class="activeTicket === 'Getting Started' ? 'active' : ''" class="block px-4 py-2 font-medium rounded-md text-gray-500 dark:text-dark-500 [&.active]:bg-primary-500/10 [&.active]:text-primary-500 transition ease-linear duration-200 hover:text-primary-500 dark:hover:text-primary-500 outline-1 text-sm outline-gray-200 dark:outline-dark-800 [&.active]:outline outline-offset-2 [&.active]:!outline-primary-500/20 " @click="flattenTickets('Getting Started')">Getting Started <span class="py-1 leading-none align-middle badge badge-primary ltr:ml-1 rtl:mr-1">5</span></a>
                        </li>
                        <li><a href="#!" :class="activeTicket === 'Account with Card' ? 'active' : ''" class="block px-4 py-2 font-medium rounded-md text-gray-500 dark:text-dark-500 [&.active]:bg-primary-500/10 [&.active]:text-primary-500 transition ease-linear duration-200 hover:text-primary-500 dark:hover:text-primary-500 outline-1 text-sm outline-gray-200 dark:outline-dark-800 [&.active]:outline outline-offset-2 [&.active]:!outline-primary-500/20" @click="flattenTickets('Account with Card')">Account with Card <span class="py-1 leading-none align-middle badge badge-primary ltr:ml-1 rtl:mr-1">3</span></a>
                        </li>
                        <li><a href="#!" :class="activeTicket === 'Licenses Policy' ? 'active' : ''" class="block px-4 py-2 font-medium rounded-md text-gray-500 dark:text-dark-500 [&.active]:bg-primary-500/10 [&.active]:text-primary-500 transition ease-linear duration-200 hover:text-primary-500 dark:hover:text-primary-500 outline-1 text-sm outline-gray-200 dark:outline-dark-800 [&.active]:outline outline-offset-2 [&.active]:!outline-primary-500/20" @click="flattenTickets('Licenses Policy')">Licenses Policy</a></li>
                        <li><a href="#!" :class="activeTicket === 'Customize Templates' ? 'active' : ''" class="block px-4 py-2 font-medium rounded-md text-gray-500 dark:text-dark-500 [&.active]:bg-primary-500/10 [&.active]:text-primary-500 transition ease-linear duration-200 hover:text-primary-500 dark:hover:text-primary-500 outline-1 text-sm outline-gray-200 dark:outline-dark-800 [&.active]:outline outline-offset-2 [&.active]:!outline-primary-500/20" @click="flattenTickets('Customize Templates')">Customize Templates</a></li>
                        <li><a href="#!" :class="activeTicket === 'Customize Layouts' ? 'active' : ''" class="block px-4 py-2 font-medium rounded-md text-gray-500 dark:text-dark-500 [&.active]:bg-primary-500/10 [&.active]:text-primary-500 transition ease-linear duration-200 hover:text-primary-500 dark:hover:text-primary-500 outline-1 text-sm outline-gray-200 dark:outline-dark-800 [&.active]:outline outline-offset-2 [&.active]:!outline-primary-500/20" @click="flattenTickets('Customize Layouts')">Customize Layouts</a></li>
                    </ul>
                </div>

                <div class="relative px-4 py-3 overflow-hidden rounded-md bg-primary-600">
                    <div class="absolute bottom-0 ltr:right-0 rtl:left-0">
                        <svg xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:svgjs="http://svgjs.dev/svgjs" width="300" height="160" preserveAspectRatio="none" viewBox="0 0 300 160">
                            <g mask="url(&quot;#SvgjsMask1016&quot;)" fill="none">
                                <path d="M161.32 191.44C190.36 168.14 173.39 56.82 222.5 55.64 271.61 54.46 310.21 119.21 344.86 121.24" class="stroke-primary-400/15" stroke-width="2"></path>
                                <path d="M103.48 160.94C139.34 155.85 149.02 78.76 217.61 71.68 286.2 64.6 298.91 8.36 331.74 6.08" class="stroke-primary-400/15" stroke-width="2"></path>
                                <path d="M118.76 180.58C146.95 179.59 162.2 139.63 222.03 135.17 281.86 130.71 293.38 62.42 325.3 58.37" class="stroke-primary-400/15" stroke-width="2"></path>
                                <path d="M96.26 168.19C127.98 167.05 145.97 121.03 212.32 116.71 278.68 112.39 293.56 41.87 328.39 38.31" class="stroke-primary-400/15" stroke-width="2"></path>
                                <path d="M50.02 170.02C76.2 169.48 99.14 134.5 148.57 134.49 198 134.48 197.84 154.49 247.12 154.49 296.39 154.49 320.53 134.59 345.67 134.49" class="stroke-primary-400/15" stroke-width="2"></path>
                            </g>
                            <defs>
                                <mask id="SvgjsMask1016">
                                    <rect width="300" height="160" fill="#ffffff"></rect>
                                </mask>
                            </defs>
                        </svg>
                    </div>
                    <h6 class="mb-4 text-primary-50">Do you still need our help ?</h6>
                    <button data-modal-target="contactModal" class="relative text-white group/effect bg-primary-500 border-primary-500 hover:bg-primary-600 hover:text-white hover:border-primary-600 focus:bg-primary-600 focus:text-white focus:border-primary-600 btn">
                        <span class="absolute inset-0 overflow-hidden rounded-xl">
                            <span class="absolute inset-0 rounded-xl bg-[image:radial-gradient(75%_100%_at_50%_0%,rgba(56,189,248,0.6)_0%,rgba(56,189,248,0)_75%)] opacity-0 transition-opacity duration-500 group-hover/effect:opacity-100">
                            </span>
                        </span>
                        <span>
                            Contact Us
                            <i data-lucide="move-right" class="ml-1 ltr:inline-block rtl:hidden size-4"></i>
                            <i data-lucide="move-left" class="mr-1 ltr:hidden rtl:inline-block size-4"></i>
                        </span>
                    </button>
                </div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 xl:col-span-8 2xl:col-span-9">
        <!-- its list -->
        <div class="list">
            <div class="flex flex-wrap items-center gap-3 mb-5">
                <ul class="overflow-x-auto tabs grow">
                    <li><a href="javascript:void(0)" @click="filterTickets('All Tickets')" :class="activeFilter === 'All Tickets' ? 'active' : ''" class="nav-item [&.active]:after:opacity-100 [&.active]:after:w-full [&.active]:text-primary-500">All Tickets</a></li>
                    <li><a href="javascript:void(0)" @click="filterTickets('Active')" :class="activeFilter === 'Active' ? 'active' : ''" class="nav-item [&.active]:after:opacity-100 [&.active]:after:w-full [&.active]:text-primary-500">Active</a>
                    </li>
                    <li><a href="javascript:void(0)" @click="filterTickets('Closed')" :class="activeFilter === 'Closed' ? 'active' : ''" class="nav-item [&.active]:after:opacity-100 [&.active]:after:w-full [&.active]:text-primary-500">Closed</a>
                    </li>
                    <li><a href="javascript:void(0)" @click="filterTickets('Deleted')" :class="activeFilter === 'Deleted' ? 'active' : ''" class="nav-item [&.active]:after:opacity-100 [&.active]:after:w-full [&.active]:text-primary-500">Deleted</a>
                    </li>
                </ul>
                <div class="shrink-0">
                    <button class="btn btn-sky btn-icon-overlay" @click="showNewTicketsModal = true" data-modal-target="newTicketsModal"><span class="icon"><i class="size-5 ri-pencil-line"></i></span>New Tickets</button>
                </div>
            </div>

            <div data-simplebar class="h-[calc(100vh_-_16.8rem)]">
                <div class="flex flex-col gap-3">
                    <template x-for="ticket in filteredTickets" :key="index">
                        <div class="card !mb-0">
                            <div class="card-body">
                                <div class="flex items-center gap-5 mb-4">
                                    <h6 class="underline grow"><a :href="'#!'" @click="showTicket(ticket)">Ticket #<span x-text="ticket.id"></span></a></h6>
                                    <div class="flex items-center gap-4 shrink-0">
                                        <p class="text-sm text-gray-500 dark:text-dark-500" x-text="ticket.time"></p>
                                        <div x-data="{ open: false }" class="dropdown">
                                            <button @click="open = !open" :aria-expanded="open" title="dropdown-button" type="button" class="flex items-center gap-2 p-0 btn">
                                                <i class="ri-more-2-fill"></i>
                                            </button>
                                            <div x-show="open" @click.outside="open = false" class="p-2 dropdown-menu dropdown-right">
                                                <a href="#" class="dropdown-item" @click="showTicket(ticket)">Reply
                                                    Task</a>
                                                <a href="#" class="dropdown-item">More
                                                    Details</a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <h6 class="mb-1"><a :href="'#!'" @click="showTicket(ticket)" x-text="ticket.title"></a>
                                </h6>
                                <p class="text-gray-500 dark:text-dark-500 line-clamp-2" x-text="ticket.description">
                                </p>
                                <div class="flex flex-wrap items-center gap-4 mt-5">
                                    <div class="flex items-center gap-2 grow">
                                        <img :src="ticket.avatar" alt="" class="rounded-full size-8 shrink-0">
                                        <h6 x-text="ticket.author"></h6>
                                    </div>
                                    <div class="shrink-0">
                                        <template x-for="tag in ticket.tags">
                                            <a href="#!" class="p-1 text-gray-500 transition duration-200 ease-linear dark:text-dark-500 hover:text-primary-500 dark:hover:text-primary-500"><span x-text="tag"></span></a><template x-if="!$last">, </template>
                                        </template>
                                    </div>
                                    <div class="shrink-0">
                                        <a href="#!" class="text-gray-500 transition duration-200 ease-linear dark:text-dark-500 hover:text-primary-500 dark:hover:text-primary-500"><i class="inline-block align-middle size-5 ri-chat-3-line"></i> <span x-text="ticket.comments"></span></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </template>
                    <template x-if="filteredTickets.length == 0">
                        <td colspan="10" class="!p-8">
                            <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" class="mx-auto size-12" viewBox="0 0 48 48">
                                <linearGradient id="SVGID_1__h35ynqzIJzH4_gr1" x1="34.598" x2="15.982" y1="15.982" y2="34.598" gradientUnits="userSpaceOnUse">
                                    <stop offset="0" stop-color="#60e8fe"></stop>
                                    <stop offset=".033" stop-color="#6ae9fe"></stop>
                                    <stop offset=".197" stop-color="#97f0fe"></stop>
                                    <stop offset=".362" stop-color="#bdf5ff"></stop>
                                    <stop offset=".525" stop-color="#dafaff"></stop>
                                    <stop offset=".687" stop-color="#eefdff"></stop>
                                    <stop offset=".846" stop-color="#fbfeff"></stop>
                                    <stop offset="1" stop-color="#fff"></stop>
                                </linearGradient>
                                <path fill="url(#SVGID_1__h35ynqzIJzH4_gr1)" d="M40.036,33.826L31.68,25.6c0.847-1.739,1.335-3.684,1.335-5.748c0-7.27-5.894-13.164-13.164-13.164	S6.688,12.582,6.688,19.852c0,7.27,5.894,13.164,13.164,13.164c2.056,0,3.995-0.485,5.728-1.326l3.914,4.015l4.331,4.331	c1.715,1.715,4.496,1.715,6.211,0C41.751,38.321,41.751,35.541,40.036,33.826z">
                                </path>
                                <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M31.95,25.739l8.086,8.086c1.715,1.715,1.715,4.496,0,6.211l0,0c-1.715,1.715-4.496,1.715-6.211,0	l-4.331-4.331">
                                </path>
                                <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M7.525,24.511c-1.771-4.694-0.767-10.196,3.011-13.975c3.847-3.847,9.48-4.817,14.228-2.912">
                                </path>
                                <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M30.856,12.603c3.376,5.114,2.814,12.063-1.688,16.565c-4.858,4.858-12.565,5.129-17.741,0.814">
                                </path>
                            </svg>
                            <p class="mt-2 text-center text-gray-500 dark:text-dark-500">No matching records found</p>
                        </td>
                    </template>
                </div>
            </div>
        </div>
        <!-- its ticket -->
        <div class="ticket" x-show="showTicketDetailsModal">
            <div class="flex items-center gap-5 mb-4">
                <div class="grow">
                    <h5 class="mb-1"><a :href="'#!'">Ticket #<span x-text="ticketDetails.id"></span></a></h5>
                    <div class="flex items-center gap-3">
                        <span class="text-gray-500 dark:text-dark-500"><i data-lucide="user-round" class="inline-block mr-1 size-4"></i> <a href="#!" x-text="ticketDetails.author">Mary
                                Smith</a></span>
                        <span class="text-gray-500 dark:text-dark-500"><i data-lucide="calendar" class="inline-block mr-1 size-4"></i> 19 Feb, 2024</span>
                        <span class="badge badge-sub-green" x-text="ticketDetails.status"></span>
                    </div>
                </div>
                <div>
                    <p class="text-gray-500 cursor-pointer dark:text-dark-500" @click="showTicketDetailsModal = false; document.querySelector('.list').classList.remove('hidden') ">
                        Close</p>
                </div>
            </div>
            <div class="card">
                <div data-simplebar class="h-[calc(100vh_-_17rem)]">
                    <div class="p-5 space-y-4">
                        <div>
                            <h5 class="mb-1" x-text="ticketDetails.title">How to enable dark mode in Tailwind CSS?</h5>
                            <p class="text-gray-500 dark:text-dark-500" x-text="ticketDetails.description">How can you
                                enable and use dark mode in Tailwind CSS?</p>
                        </div>

                        <div class="flex gap-2">
                            <img :src="ticketDetails.avatar" alt="" class="rounded-md size-10">
                            <div class="space-y-2">
                                <p class="text-gray-500 dark:text-dark-500">To enable dark mode in Tailwind CSS, update
                                    your <span class="text-pink-500">tailwind.config.js</span> file with the <span class="text-pink-500">darkMode</span> option. You can choose between two
                                    different dark mode strategies: <span class="text-pink-500">[data-mode="dark"]</span> or <span class="text-pink-500">class</span>.</p>
                                <p class="text-gray-500 dark:text-dark-500">Using <span class="text-pink-500">[data-mode="dark"]</span>, the dark mode is enabled based
                                    on the user's operating system preference:</p>
                                <pre><deckgo-highlight-code lang="js" class="!mb-0"><code slot="code">module.exports = {
    darkMode: ['[data-mode="dark"]'],
    // ...
}</code>
</deckgo-highlight-code>
</pre>

                                <p class="text-gray-500 dark:text-dark-500">Using <span class="text-pink-500">class</span>, the dark mode is enabled by adding a <span class="text-pink-500">.dark</span> class to an ancestor element of your
                                    components:</p>
                                <pre><deckgo-highlight-code lang="js" class="!mb-0"><code slot="code">module.exports = {
    darkMode: ['class'],
    // ...
}</code>
</deckgo-highlight-code>
</pre>
                                <p class="text-gray-500 dark:text-dark-500">To apply styles for dark mode, simply prefix
                                    your utility classes with <span class="text-pink-500">dark:</span> followed by the
                                    desired state variant, if any.</p>
                                <p class="text-gray-500 dark:text-dark-500">For example, if you want to change the
                                    background color of an element in dark mode, you can use the following code:</p>
                                <pre><deckgo-highlight-code lang="js" class="!mb-0"><code slot="code">&lt;div class=&quot;bg-white dark:bg-gray-800&quot;&gt;
    &lt;!-- Your content here --&gt;
&lt;/div&gt;</code>
</deckgo-highlight-code>
</pre>
                            </div>
                        </div>
                    </div>
                    <template x-for="reply in replyMessages" :key="index">
                        <div class="flex gap-2 p-5">
                            <img :src="user17" alt="" class="rounded-md size-10">
                            <div class="space-y-2">
                                <p x-text="reply" class="text-gray-500 dark:text-dark-500"></p>
                            </div>
                        </div>
                    </template>
                    <form action="javascript:void(0);" class="p-5 pt-0">
                        <h5 class="mb-2">Comment</h5>

                        <div class="grid grid-cols-12 gap-5">
                            <div class="col-span-12">
                                <div class="mb-5">
                                    <label for="textareaInput2" class="block mb-2 text-sm">Your Reply</label>
                                    <textarea name="textareaInput2" id="textareaInput2" rows="3" class="h-auto form-input" placeholder="Enter your description" x-model="newMessage"></textarea>
                                </div>
                                <div class="ltr:text-right rtl:text-left">
                                    <button class="btn btn-primary" @click="addMessage(newMessage) && (newMessage = '')">Reply Now <i data-lucide="move-right" class="inline-block ml-1 size-4"></i></button>
                                </div>
                            </div><!--end col-->
                        </div><!--end grid-->
                    </form>
                </div>
            </div>
        </div>

    </div><!--end col-->
    <!--start create tickets modal-->
    <div id="newTicketsModal" class="!hidden show modal" x-show="showNewTicketsModal">
        <div class="modal-wrap modal-center modal-2xl">
            <div class="modal-header">
                <h6>Create New Ticket</h6>
                <button data-modal-close="newTicketsModal" class="link link-red"><i data-lucide="x" class="size-5"></i></button>
            </div>
            <div class="modal-content ">
                <div class="grid grid-cols-12 gap-5">
                    <div class="col-span-6">
                        <label for="taskTitleInput" class="block mb-2 text-sm font-medium">Task Title</label>
                        <input type="text" id="taskTitleInput" class="form-input" x-model="ticketForm.title" @input="validateField('title', ticketForm.title, 'Title is required.')">
                        <span x-show="errors.title" class="text-red-500" x-text="errors.title"></span>
                    </div><!--end col-->
                    <div class="col-span-6">
                        <label for="projectNameInput" class="block mb-2 text-sm font-medium">Project Name</label>
                        <input type="text" id="projectNameInput" class="form-input" x-model="ticketForm.projectName" @input="validateField('projectName', ticketForm.projectName, 'Project Name is required.')">
                        <span x-show="errors.projectName" class="text-red-500" x-text="errors.projectName"></span>
                    </div><!--end col-->
                    <div class="col-span-12">
                        <label for="descriptionInput2" class="block mb-2 text-sm font-medium">Description</label>
                        <textarea name="descriptionInput2" id="descriptionInput2" rows="3" class="h-auto form-input" x-model="ticketForm.description" @input="validateField('description', ticketForm.description, 'description is required.')"></textarea>
                        <span x-show="errors.description" class="text-red-500" x-text="errors.description"></span>
                    </div><!--end col-->
                    <div class="col-span-6">
                        <label for="emailInput" class="block mb-2 text-sm font-medium">Keywords</label>
                        <div id="multipleSelect" x-model="ticketForm.keywords" @change="validateField('keywords', document.getElementById('multipleSelect') , 'Keywords is required.')">
                        </div>
                        <span x-show="errors.keywords" class="text-red-500" x-text="errors.keywords"></span>
                        <div class="flex flex-wrap items-center gap-2 pt-3">
                        </div>
                    </div><!--end col-->
                    <div class="col-span-6">
                        <label for="assignedToSelect" class="block mb-2 text-sm font-medium">Assigned To</label>
                        <div id="assignedToSelect" x-model="ticketForm.assignedTo" @change="validateField('assignedTo', document.getElementById('assignedToSelect') , 'Assignees is required.')">
                        </div>
                        <span x-show="errors.assignedTo" class="text-red-500" x-text="errors.assignedTo"></span>
                    </div><!--end col-->
                    <div class="col-span-12">
                        <label for="phomenoInput" class="block mb-2 text-sm font-medium">Phone No</label>
                        <input type="tel" id="phomenoInput" class="form-input" x-model="ticketForm.phone" @input="validateField('phone', ticketForm.phone, 'phone is required.')">
                        <span x-show="errors.phone" class="text-red-500" x-text="errors.phone"></span>
                    </div><!--end col-->
                    <div class="col-span-12">
                        <div class="text-right">
                            <button class="btn btn-primary" @click="submitForm()">Send Message</button>
                        </div>
                    </div>
                </div><!--end grid-->
            </div>
        </div>
    </div><!--end-->
</div><!--end grid-->
</div>
{{> partials/footer }}
</div>

<!--start contact modal-->
<div id="contactModal" class="!hidden show modal">
    <div class="modal-wrap modal-center modal-2xl">
        <div class="p-0 modal-content ">
            <div class="grid grid-cols-12">
                <div class="relative flex flex-col justify-end col-span-4 p-8 bg-gray-50 rounded-l-md">
                    <img src="assets/images/others/mail.png" alt="" class="absolute left-0 top-10">
                    <div>
                        <img src="assets/images/main-logo.png" alt="" class="h-8">
                        <p class="mt-3 text-gray-500">Whether you need help with customization, troubleshooting, or
                            general inquiries, don't hesitate to reach out to us.</p>
                    </div>
                </div>
                <div class="col-span-8 p-8">
                    <button data-modal-close="contactModal" class="link link-red float-end"><i data-lucide="x" class="size-5"></i></button>
                    <h5 class="mb-1">Feel free to connect with us.</h5>
                    <p class="text-gray-500">Our team is here to assist you with any questions or issues you may
                        encounter while using our admin template.</p>
                    <form>
                        <div class="grid grid-cols-12 gap-5 mt-5">
                            <div class="col-span-6">
                                <label for="firstNameInput" class="block mb-2 text-sm font-medium">First Name</label>
                                <input type="text" id="firstNameInput" class="form-input" required>
                            </div><!--end col-->
                            <div class="col-span-6">
                                <label for="lastNameInput" class="block mb-2 text-sm font-medium">Last Name</label>
                                <input type="text" id="lastNameInput" class="form-input" required>
                            </div><!--end col-->
                            <div class="col-span-12">
                                <label for="emailInput" class="block mb-2 text-sm font-medium">Email</label>
                                <input type="email" id="emailInput" class="form-input" required>
                            </div><!--end col-->
                            <div class="col-span-12">
                                <label for="phomenoInput" class="block mb-2 text-sm font-medium">Phone No</label>
                                <input type="tel" id="phomenoInput" class="form-input" required>
                            </div><!--end col-->
                            <div class="col-span-12">
                                <label for="descriptionInput" class="block mb-2 text-sm font-medium">Description</label>
                                <textarea name="descriptionInput" id="descriptionInput" rows="3" class="h-auto form-input" required></textarea>
                            </div><!--end col-->
                            <div class="col-span-12">
                                <div class="text-right">
                                    <button type="submit" class="btn btn-primary">Send Message</button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div><!--end grid-->
            </div>
        </div>
    </div>
</div>
</div><!--end-->

{{> partials/vendor-scripts }}

<script type="module" src="assets/js/ui/advanced-highlight-code.init.js"></script>

<script type="module" src="assets/js/pages/help-center.init.js"></script>

<script type="module" src="assets/js/main.js"></script>

</body>

</html>