import user2 from "/assets/images/avatar/user-2.png";
import user3 from "/assets/images/avatar/user-3.png";
import user4 from "/assets/images/avatar/user-4.png";   
import user5 from "/assets/images/avatar/user-5.png";
import user6 from "/assets/images/avatar/user-6.png";
import user7 from "/assets/images/avatar/user-7.png";
import user8 from "/assets/images/avatar/user-8.png";
import user9 from "/assets/images/avatar/user-9.png";
import user10 from "/assets/images/avatar/user-10.png";
import user11 from "/assets/images/avatar/user-11.png";
import user12 from "/assets/images/avatar/user-12.png";
import user13 from "/assets/images/avatar/user-13.png";
import user14 from "/assets/images/avatar/user-14.png";
import user15 from "/assets/images/avatar/user-15.png";
import user16 from "/assets/images/avatar/user-16.png";
import user17 from "/assets/images/avatar/user-17.png";
import user18 from "/assets/images/avatar/user-18.png";
import user19 from "/assets/images/avatar/user-19.png";
import user20 from "/assets/images/avatar/user-20.png";
import user21 from "/assets/images/avatar/user-21.png";
import user22 from "/assets/images/avatar/user-22.png";
import user23 from "/assets/images/avatar/user-23.png";
import user24 from "/assets/images/avatar/user-24.png";



const leadsData = [
    {
        "id": 1,
        "image": user2,
        "name": "Ashton Abigail",
        "date": "28 May, 2024",
        "time": "3:45 PM",
        "email": "<EMAIL>",
        "phoneNumber": "+(145) 0128 2301",
        "status": "Hot"
    },
    {
        "id": 2,
        "image": user3,
        "name": "Bethany Bennett",
        "date": "29 May, 2024",
        "time": "10:00 AM",
        "email": "<EMAIL>",
        "phoneNumber": "+(145) 0128 2302",
        "status": "Pending"
    },
    {
        "id": 3,
        "image": user4,
        "name": "Charles Carter",
        "date": "30 May, 2024",
        "time": "1:30 PM",
        "email": "<EMAIL>",
        "phoneNumber": "+(145) 0128 2303",
        "status": "New"
    },
    {
        "id": 4,
        "image": user5,
        "name": "Diana Dawson",
        "date": "31 May, 2024",
        "time": "9:00 AM",
        "email": "<EMAIL>",
        "phoneNumber": "+(145) 0128 2304",
        "status": "New"
    },
    {
        "id": 5,
        "image": user6,
        "name": "Ethan Evans",
        "date": "1 June, 2024",
        "time": "11:15 AM",
        "email": "<EMAIL>",
        "phoneNumber": "+(145) 0128 2305",
        "status": "lost"
    },
    {
        "id": 6,
        "image": user7,
        "name": "Fiona Foster",
        "date": "2 June, 2024",
        "time": "2:45 PM",
        "email": "<EMAIL>",
        "phoneNumber": "+(145) 0128 2306",
        "status": "New"
    },
    {
        "id": 7,
        "image": user8,
        "name": "George Green",
        "date": "3 June, 2024",
        "time": "4:30 PM",
        "email": "<EMAIL>",
        "phoneNumber": "+(145) 0128 2307",
        "status": "Pending"
    },
    {
        "id": 8,
        "image": user9,
        "name": "Hannah Harris",
        "date": "4 June, 2024",
        "time": "8:45 AM",
        "email": "<EMAIL>",
        "phoneNumber": "+(145) 0128 2308",
        "status": "Pending"
    },
    {
        "id": 9,
        "image": user10,
        "name": "Ian Irving",
        "date": "5 June, 2024",
        "time": "12:00 PM",
        "email": "<EMAIL>",
        "phoneNumber": "+(145) 0128 2309",
        "status": "New"
    },
    {
        "id": 10,
        "image": user11,
        "name": "Julia Jackson",
        "date": "6 June, 2024",
        "time": "3:00 PM",
        "email": "<EMAIL>",
        "phoneNumber": "+(145) 0128 2310",
        "status": "Hot"
    },
    {
        "id": 11,
        "image": user12,
        "name": "Kevin King",
        "date": "7 June, 2024",
        "time": "9:30 AM",
        "email": "<EMAIL>",
        "phoneNumber": "+(145) 0128 2311",
        "status": "Pending"
    },
    {
        "id": 12,
        "image": user13,
        "name": "Lily Lewis",
        "date": "8 June, 2024",
        "time": "1:45 PM",
        "email": "<EMAIL>",
        "phoneNumber": "+(145) 0128 2312",
        "status": "New"
    },
    {
        "id": 13,
        "image": user14,
        "name": "Mason Martin",
        "date": "9 June, 2024",
        "time": "5:00 PM",
        "email": "<EMAIL>",
        "phoneNumber": "+(145) 0128 2313",
        "status": "Hot"
    },
    {
        "id": 14,
        "image": user15,
        "name": "Nora Nelson",
        "date": "10 June, 2024",
        "time": "10:15 AM",
        "email": "<EMAIL>",
        "phoneNumber": "+(145) 0128 2314",
        "status": "Pending"
    },
    {
        "id": 15,
        "image": user16,
        "name": "Oliver Olson",
        "date": "11 June, 2024",
        "time": "2:30 PM",
        "email": "<EMAIL>",
        "phoneNumber": "+(145) 0128 2315",
        "status": "New"
    },
    {
        "id": 16,
        "image": user17,
        "name": "Penelope Parker",
        "date": "12 June, 2024",
        "time": "4:45 PM",
        "email": "<EMAIL>",
        "phoneNumber": "+(145) 0128 2316",
        "status": "Lost"
    },
    {
        "id": 17,
        "image": user18,
        "name": "Quinn Quinn",
        "date": "13 June, 2024",
        "time": "11:00 AM",
        "email": "<EMAIL>",
        "phoneNumber": "+(145) 0128 2317",
        "status": "New"
    },
    {
        "id": 18,
        "image": user19,
        "name": "Rachel Rice",
        "date": "14 June, 2024",
        "time": "3:15 PM",
        "email": "<EMAIL>",
        "phoneNumber": "+(145) 0128 2318",
        "status": "Hot"
    },
    {
        "id": 19,
        "image": user20,
        "name": "Samuel Smith",
        "date": "15 June, 2024",
        "time": "9:45 AM",
        "email": "<EMAIL>",
        "phoneNumber": "+(145) 0128 2319",
        "status": "New"
    },
    {
        "id": 20,
        "image": user21,
        "name": "Tiffany Turner",
        "date": "16 June, 2024",
        "time": "1:00 PM",
        "email": "<EMAIL>",
        "phoneNumber": "+(145) 0128 2320",
        "status": "New"
    },
    {
        "id": 21,
        "image": user22,
        "name": "Ulysses Underwood",
        "date": "17 June, 2024",
        "time": "5:15 PM",
        "email": "<EMAIL>",
        "phoneNumber": "+(145) 0128 2321",
        "status": "Hot"
    },
    {
        "id": 22,
        "image": user23,
        "name": "Victoria Vaughn",
        "date": "18 June, 2024",
        "time": "10:30 AM",
        "email": "<EMAIL>",
        "phoneNumber": "+(145) 0128 2322",
        "status": "Lost"
    },
    {
        "id": 23,
        "image": user24,
        "name": "William Walker",
        "date": "19 June, 2024",
        "time": "2:45 PM",
        "email": "<EMAIL>",
        "phoneNumber": "+(145) 0128 2323",
        "status": "New"
    }
]
export default leadsData