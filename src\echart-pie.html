{{> partials/main }}

<head>

    {{> partials/title-meta title="Pie Charts" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}

{{> partials/page-heading title="Pie Charts" sub-title="Echarts" }}

<div class="grid grid-cols-12 gap-x-space">
    <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Basic</h6>
        </div>
        <div class="card-body" x-data="basicPieApp">
            <div class="h-80" x-ref="basicPieChart" data-chart-colors="[bg-primary-500, bg-green-500, bg-yellow-400, bg-purple-400, bg-orange-400, bg-gray-200, bg-gray-800]" data-chart-dark-colors="[bg-primary-500, bg-green-500, bg-yellow-400, bg-purple-400, bg-orange-400, bg-dark-800, bg-dark-100]"></div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Doughnut Chart with Rounded Corner</h6>
        </div>
        <div class="card-body" x-data="doughnutRoundedPieApp">
            <div class="h-80" x-ref="doughnutRoundedPieChart" data-chart-colors="[bg-primary-300, bg-green-300, bg-pink-300, bg-purple-300, bg-orange-300, bg-gray-200, bg-gray-800, bg-white]" data-chart-dark-colors="[bg-primary-300, bg-green-300, bg-pink-400, bg-purple-300, bg-orange-400, bg-dark-800, bg-dark-100, bg-dark-900]"></div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Doughnut Chart</h6>
        </div>
        <div class="card-body" x-data="doughnutPieApp">
            <div class="h-80" x-ref="doughnutPieChart" data-chart-colors="[bg-primary-300, bg-green-300, bg-pink-300, bg-purple-300, bg-orange-300, bg-gray-200, bg-gray-800, bg-white]" data-chart-dark-colors="[bg-primary-300, bg-green-300, bg-pink-400, bg-purple-300, bg-orange-400, bg-dark-800, bg-dark-100, bg-dark-900]"></div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Doughnut Chart</h6>
        </div>
        <div class="card-body" x-data="halfDouglasNutApp">
            <div class="h-80" x-ref="halfDouglasNutChart" data-chart-colors="[bg-primary-300, bg-green-300, bg-pink-300, bg-purple-300, bg-orange-300, bg-gray-200, bg-gray-800, bg-white]" data-chart-dark-colors="[bg-primary-300, bg-green-300, bg-pink-400, bg-purple-300, bg-orange-400, bg-dark-800, bg-dark-100, bg-dark-900]"></div>
        </div>
    </div><!--end col-->
</div><!--end grid-->

<div class="mb-5 text-center">
    <a href="https://echarts.apache.org/examples/en/index.html#chart-type-pie" target="_blank" class="btn btn-primary">More Example <i data-lucide="move-right" class="inline-block ml-1 size-4"></i></a>
</div>
</div>
{{> partials/footer }}
</div>

{{> partials/vendor-scripts }}

<script src="assets/libs/echarts/echarts.js"></script>

<script type="module" src="assets/js/charts/echart-pie.init.js"></script>

<script type="module" src="assets/js/main.js"></script>

</body>
</html>