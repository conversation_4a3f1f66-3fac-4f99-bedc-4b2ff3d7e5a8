{{> partials/main }}

<head>

    {{> partials/title-meta title="Products Grid" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

<div class="relative min-h-screen">
    <div class="page-wrapper pt-[calc(theme('spacing.topbar')_*_1.2)] group-data-[layout=horizontal]:pt-[calc(theme('spacing.topbar')_*_1.9)]" x-data="productTable()">
        {{> partials/page-heading title="Products Grid" sub-title="Ecommerce" }}

        <div class="flex flex-wrap items-center gap-5 mb-5">
            <div class="grow">
                <h6 class="mb-1 card-title">Popular Products</h6>
                <p class="text-gray-500 dark:text-dark-500">Track your store's progress to boost your sales.</p>
            </div>
            <div class="flex gap-2 shrink-0">
                <button class="btn btn-sub-gray" @click="showFilterModal = true" data-drawer-target="filterAside" drawer-end><i data-lucide="sliders-horizontal" class="inline-block ltr:mr-1 rt:ml-1 align-center size-4"></i> Filters</button>
                <a href="apps-ecommerce-create-products.html" class="btn btn-primary"><i data-lucide="plus" class="inline-block ltr:mr-1 rt:ml-1 align-center size-4"></i> Add Product</a>
            </div>
        </div>
        <div>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-x-5">
                <template x-if="displayedProducts.length > 0">
                <template x-for="(product, index) in displayedProducts" :key="index">
                    <div class="card hovered">
                        <div class="p-2 card-body">
                            <div :class="`relative p-5 ${product.color}`">
                                <div x-data="{
                                    open: false,
                                    toggle() {
                                        if (this.open) {
                                            return this.close();
                                        }
                                
                                        this.$refs.button.focus();
                                
                                        this.open = true;
                                    },
                                    close(focusAfter) {
                                        if (!this.open) return;
                                
                                        this.open = false;
                                
                                        focusAfter && focusAfter.focus();
                                    }
                                }" x-on:keydown.escape.prevent.stop="close($refs.button)" x-on:focusin.window="!$refs.panel.contains($event.target) && close()" x-id="['dropdown-button']" class="absolute right-2 top-2 dropdown">
                                    <button x-ref="button" x-on:click="toggle()" :aria-expanded="open" :aria-controls="$id('dropdown-button')" type="button" class="flex items-center justify-center bg-white rounded-full size-10 link link-red dark:bg-dark-850">
                                        <i class="ri-more-2-fill"></i>
                                    </button>

                                    <div x-ref="panel" x-show="open" x-transition.origin.top.right x-on:click.outside="close($refs.button)" :id="$id('dropdown-button')" style="display: none;" class="p-2 dropdown-menu dropdown-right">
                                        <a href="apps-ecommerce-product-overview.html" class="dropdown-item">
                                            <i class="align-middle ltr:mr-1 rtl:ml-1 ri-eye-line"></i> Overview
                                        </a>
                                        <a href="#!" class="dropdown-item">
                                            <i class="align-middle ltr:mr-1 rtl:ml-1 ri-pencil-line"></i> Edit
                                        </a>
                                        <a href="#!" @click="deleteItem = product" data-modal-target="deleteModal" class="dropdown-item hover:!text-red-500">
                                            <i class="align-middle ltr:mr-1 rtl:ml-1 ri-delete-bin-6-line"></i> Delete
                                        </a>
                                    </div>
                                </div>
                                <img :src="product.image" alt="">
                            </div>
                            <div class="p-1 mt-2">
                                <h5 class="mb-2" x-text="product.price"></h5>
                                <h6 class="mb-1"><a href="apps-ecommerce-product-overview.html" x-text="product.productName"></a></h6>
                                <p class="text-gray-500 dark:text-dark-500" x-text="product.category"></p>
                                <div class="flex gap-2 mt-3">
                                    <button type="button" class="w-full btn btn-primary">Add to Cart</button>
                                    <a href="#!" class="btn btn-sub-gray btn-icon shrink-0"><i class="text-lg ri-heart-line"></i></a>
                                </div>
                            </div>
                        </div>
                    </div>
                </template>
            </template>
        </div>
        <template x-if="displayedProducts.length == 0">
            <div class="!p-8">
                <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" class="mx-auto size-12" viewBox="0 0 48 48">
                    <linearGradient id="SVGID_1__h35ynqzIJzH4_gr1" x1="34.598" x2="15.982" y1="15.982" y2="34.598" gradientUnits="userSpaceOnUse">
                        <stop offset="0" stop-color="#60e8fe"></stop>
                        <stop offset=".033" stop-color="#6ae9fe"></stop>
                        <stop offset=".197" stop-color="#97f0fe"></stop>
                        <stop offset=".362" stop-color="#bdf5ff"></stop>
                        <stop offset=".525" stop-color="#dafaff"></stop>
                        <stop offset=".687" stop-color="#eefdff"></stop>
                        <stop offset=".846" stop-color="#fbfeff"></stop>
                        <stop offset="1" stop-color="#fff"></stop>
                    </linearGradient>
                    <path fill="url(#SVGID_1__h35ynqzIJzH4_gr1)" d="M40.036,33.826L31.68,25.6c0.847-1.739,1.335-3.684,1.335-5.748c0-7.27-5.894-13.164-13.164-13.164	S6.688,12.582,6.688,19.852c0,7.27,5.894,13.164,13.164,13.164c2.056,0,3.995-0.485,5.728-1.326l3.914,4.015l4.331,4.331	c1.715,1.715,4.496,1.715,6.211,0C41.751,38.321,41.751,35.541,40.036,33.826z"></path>
                    <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M31.95,25.739l8.086,8.086c1.715,1.715,1.715,4.496,0,6.211l0,0c-1.715,1.715-4.496,1.715-6.211,0	l-4.331-4.331"></path>
                    <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M7.525,24.511c-1.771-4.694-0.767-10.196,3.011-13.975c3.847-3.847,9.48-4.817,14.228-2.912"></path>
                    <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M30.856,12.603c3.376,5.114,2.814,12.063-1.688,16.565c-4.858,4.858-12.565,5.129-17.741,0.814"></path>
                </svg>
                <p class="mt-2 text-center text-gray-500 dark:text-dark-500">No matching records found</p>
            </div>
        </template>
        <div class="grid grid-cols-12 gap-5 mb-5 items-center" x-show="displayedProducts.length !== 0">
            <div class="col-span-12 md:col-span-6 justify-center md:justify-start flex flex-wrap">
                <p class="text-gray-500 dark:text-dark-500">Showing <b x-text="showingStart"></b> - <b x-text="showingEnd"></b> of <b x-text="filteredProducts.length"></b> Results</p>
            </div>
            <div class="col-span-12 md:col-span-6">
                <div class="flex justify-center md:justify-end pagination pagination-primary">
                    <button @click="prevPage()" :disabled="currentPage === 1" class="pagination-pre">
                        <i data-lucide="chevron-left" class="mr-1 ltr:inline-block rtl:hidden size-4"></i>
                        <i data-lucide="chevron-right" class="ml-1 ltr:hidden rtl:inline-block size-4"></i>
                        Prev
                    </button>
                    <template x-for="page in totalPages" :key="page">
                        <button @click="gotoPage(page)" :class="{ 'active': currentPage === page }" class="pagination-item">
                            <span x-text="page"></span>
                        </button>
                    </template>
                    <button @click="nextPage()" :disabled="currentPage === totalPages" class="pagination-next">
                        Next
                        <i data-lucide="chevron-right" class="ml-1 ltr:inline-block rtl:hidden size-4"></i>
                        <i data-lucide="chevron-left" class="mr-1 ltr:hidden rtl:inline-block size-4"></i>
                    </button>
                </div>
            </div>
        </div>
            
            
            <!-- Delete Modal -->
            <div id="deleteModal" class="!hidden modal show">
                <div class="modal-wrap modal-xs modal-center">
                    <div class="text-center modal-content p-7">
                        <div class="flex items-center justify-center mx-auto mb-4 text-red-500 rounded-full bg-red-500/10 size-14 backdrop-blur-xl">
                            <i data-lucide="trash-2" class="size-6"></i>
                        </div>
                        <h5 class="mb-4">Are you sure you want to delete this Product ?</h5>
                        <div class="flex items-center justify-center gap-2">
                            <button class="btn btn-red" data-modal-close="deleteModal" @click="deleteProduct()">Delete</button>
                            <button data-modal-close="deleteModal" class="btn link link-primary">Cancel</button>
                        </div>
                    </div>
                </div>
            </div><!--end-->

            <!--start filter aside-->
            <div id="filterAside" drawer-end class="drawer show drawer-lg" x-show="showFilterModal">
                <div class="drawer-header">
                    <h6 class="text-15">Filter & Sorting</h6>
                    <button data-drawer-close="filterAside"><i data-lucide="x" class="link link-red"></i></button>
                </div>
                <div class="drawer-content">
                    <div class="relative group/form">
                        <input type="email" class="ltr:pl-9 rtl:pr-9 form-input ltr:group-[&.right]/form:pr-9 rtl:group-[&.right]/form:pl-9 ltr:group-[&.right]/form:pl-4 rtl:group-[&.right]/form:pr-4" placeholder="Search products, price etc..." x-model="searchTerm">
                        <button class="absolute inset-y-0 flex items-center text-gray-500 dark:text-dark-500 ltr:left-3 rtl:right-3 rtl ltr:group-[&.right]/form:right-3 rtl:group-[&.right]/form:left-3 ltr:group-[&.right]/form:left-auto rtl:group-[&.right]/form:right-auto focus:outline-hidden">
                            <i data-lucide="search" class="size-4"></i>
                        </button>
                    </div>
                    <div x-data="{ showAll: true }" class="mt-5">
                        <div class="flex items-center gap-4 mb-3">
                            <h6 class="grow">Selected Filters</h6>
                            <a href="#!" class="text-sm link link-primary" x-on:click="showAll = false">
                                Clear All <i data-lucide="x" class="inline-block size-4"></i>
                            </a>
                        </div>
                        <div class="flex flex-wrap items-center gap-2">
                            <template x-for="filter in selectedFilters" :key="index">
                              
                                <span class="badge badge-gray" x-data="{ isOpen: true }" x-show="showAll && isOpen">
                                    <span>Fashion</span>
                                    <a href="#!" x-on:click="isOpen = false"><i class="align-bottom ltr:ml-1 rtl:mr-1 ri-close-fill"></i></a>
                                </span>
                                
                            </template>
                            <span class="badge badge-gray" x-data="{ isOpen: true }" x-show="showAll && isOpen">
                                <span>M</span>
                                <a href="#!" x-on:click="isOpen = false"><i class="align-bottom ltr:ml-1 rtl:mr-1 ri-close-fill"></i></a>
                            </span>
                            <span class="badge badge-gray" x-data="{ isOpen: true }" x-show="showAll && isOpen">
                                <span>Green</span>
                                <a href="#!" x-on:click="isOpen = false"><i class="align-bottom ltr:ml-1 rtl:mr-1 ri-close-fill"></i></a>
                            </span>
                            <span class="badge badge-gray" x-data="{ isOpen: true }" x-show="showAll && isOpen">
                                <span>$200 - $600</span>
                                <a href="#!" x-on:click="isOpen = false"><i class="align-bottom ltr:ml-1 rtl:mr-1 ri-close-fill"></i></a>
                            </span>
                        </div>
                    </div>

                    <div class="mt-5" x-data="productCategory">
                        <h6 class="mb-3">Product Category (<span x-text="selectedCount"></span>)</h6>

                        <div x-data="{ showMore: false }" class="flex flex-col gap-2">
                            <!-- Checkbox items -->
                            <div class="input-check-group">
                                <input id="productCategory1" class="input-check input-check-primary" type="checkbox" value="Fashion" @change="updateCountCategory">
                                <label for="productCategory1" class="input-check-label">Fashion</label>
                            </div>
                            <div class="input-check-group">
                                <input id="productCategory2" class="input-check input-check-primary" type="checkbox" value="Footwear" @change="updateCountCategory">
                                <label for="productCategory2" class="input-check-label">Footwear</label>
                            </div>
                            <div class="input-check-group">
                                <input id="productCategory3" class="input-check input-check-primary" type="checkbox" value="Bags" @change="updateCountCategory">
                                <label for="productCategory3" class="input-check-label">Bags</label>
                            </div>
                            <div class="input-check-group">
                                <input id="productCategory4" class="input-check input-check-primary" type="checkbox" value="Watch" @change="updateCountCategory">
                                <label for="productCategory4" class="input-check-label">Watch</label>
                            </div>
                            <div class="input-check-group">
                                <input id="productCategory5" class="input-check input-check-primary" type="checkbox" value="Accessories" @change="updateCountCategory">
                                <label for="productCategory5" class="input-check-label">Accessories</label>
                            </div>

                            <!-- Additional Categories -->
                            <div x-show="showMore" class="flex flex-col gap-2">
                                <div class="input-check-group">
                                    <input id="productCategory6" class="input-check input-check-primary" type="checkbox" value="Jewelry" @change="updateCountCategory">
                                    <label for="productCategory6" class="input-check-label">Jewelry</label>
                                </div>
                                <div class="input-check-group">
                                    <input id="productCategory7" class="input-check input-check-primary" type="checkbox" value="Sunglasses" @change="updateCountCategory">
                                    <label for="productCategory7" class="input-check-label">Sunglasses</label>
                                </div>
                                <div class="input-check-group">
                                    <input id="productCategory8" class="input-check input-check-primary" type="checkbox" value="Belts" @change="updateCountCategory">
                                    <label for="productCategory8" class="input-check-label">Belts</label>
                                </div>
                            </div>
                            <!-- Show more button -->
                            <a href="#!" class="block mt-3 link link-primary" x-on:click="toggleShowMore">
                                <span x-show="!showMore">Show More <i data-lucide="chevron-down" class="inline-block size-4"></i></span>
                                <span x-show="showMore">Show Less <i data-lucide="chevron-up" class="inline-block size-4"></i></span>
                            </a>
                        </div>
                    </div>
                    <div class="mt-5" x-data="colorFilter">
                        <h6 class="mb-3">Colors (<span x-text="selectedCount"></span>)</h6>

                        <div class="flex flex-col gap-2">
                            <div class="input-check-group">
                                <input id="productColor1" class="input-check input-check-primary" type="checkbox" value="bg-primary-50" @change="updateCountColor">
                                <label for="productColor1" class="input-check-label">Blue</label>
                            </div>
                            <div class="input-check-group">
                                <input id="productColor2" class="input-check input-check-primary" type="checkbox" value="bg-green-50" @change="updateCountColor">
                                <label for="productColor2" class="input-check-label">Green</label>
                            </div>
                            <div class="input-check-group">
                                <input id="productColor3" class="input-check input-check-primary" type="checkbox" value="bg-red-50" @change="updateCountColor">
                                <label for="productColor3" class="input-check-label">Red</label>
                            </div>
                            <div class="input-check-group">
                                <input id="productColor4" class="input-check input-check-primary" type="checkbox" value="bg-yellow-50" @change="updateCountColor">
                                <label for="productColor4" class="input-check-label">Yellow</label>
                            </div>
                            <div class="input-check-group">
                                <input id="productColor5" class="input-check input-check-primary" type="checkbox" value="bg-sky-50" @change="updateCountColor">
                                <label for="productColor5" class="input-check-label">Sky</label>
                            </div>

                            <!-- Additional Categories -->
                            <div x-show="showMore" class="space-y-2">
                                <div class="input-check-group">
                                    <input id="productColor6" class="input-check input-check-primary" type="checkbox" value="bg-pink-50" @change="updateCountColor">
                                    <label for="productColor6" class="input-check-label">Pink</label>
                                </div>
                                <div class="input-check-group">
                                    <input id="productColor7" class="input-check input-check-primary" type="checkbox" value="bg-indigo-50" @change="updateCountColor">
                                    <label for="productColor7" class="input-check-label">Black</label>
                                </div>
                                <div class="input-check-group">
                                    <input id="productColor8" class="input-check input-check-primary" type="checkbox" value="bg-gray-50" @change="updateCountColor">
                                    <label for="productColor8" class="input-check-label">Gray</label>
                                </div>
                            </div>

                            <a href="#!" class="block mt-3 link link-primary" x-on:click="toggleShowMore">
                                <span x-show="!showMore">Show More <i data-lucide="chevron-down" class="inline-block size-4"></i></span>
                                <span x-show="showMore">Show Less <i data-lucide="chevron-up" class="inline-block size-4"></i></span>
                            </a>
                        </div>
                    </div>
                    <div class="mt-5">
                        <h6 class="mb-4">Price Range</h6>
                        <div class="flex items-center justify-center h-20 px-5">
                            <div id="arbitrary-values-slider" class="w-full"></div>
                        </div>
                    </div>

                    <div class="mt-5">
                        <h6 class="mb-3">Sort By</h6>

                        <div class="space-y-2">
                            <div class="input-radio-group">
                                <input id="bestSellerByRadio" class="input-radio input-radio-primary" type="radio" name="sortBy">
                                <label for="bestSellerByRadio" class="input-radio-label">Best Sellers</label>
                            </div>
                            <div class="input-radio-group">
                                <input id="newArrivalsSortBy" class="input-radio input-radio-primary" type="radio" name="sortBy">
                                <label for="newArrivalsSortBy" class="input-radio-label">New Arrivals</label>
                            </div>
                            <div class="input-radio-group">
                                <input id="tradingSortBy" class="input-radio input-radio-primary" type="radio" name="sortBy">
                                <label for="tradingSortBy" class="input-radio-label">Trading</label>
                            </div>
                            <div class="input-radio-group">
                                <input id="lowToHighSortBy" class="input-radio input-radio-primary" type="radio" name="sortBy">
                                <label for="lowToHighSortBy" class="input-radio-label">Price (Low to High)</label>
                            </div>
                            <div class="input-radio-group">
                                <input id="highToLowSortBy" class="input-radio input-radio-primary" type="radio" name="sortBy">
                                <label for="highToLowSortBy" class="input-radio-label">Price (High to Low)</label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex items-center justify-end gap-2 p-4 border-t border-slate-200">
                    <button class="btn btn-sub-gray" @click="clearFilters" data-drawer-close="filterAside"><i data-lucide="x" class="inline-block ltr:mr-1 rtl:ml-1 align-center size-4"></i>Reset</button>
                    <button class="btn btn-primary" @click="applyFilters($event)"  data-drawer-close="filterAside"><i data-lucide="sliders-horizontal" class="inline-block ltr:mr-1 rtl:ml-1 align-center size-4"></i> <span class="align-middle">Filter</span></button>
                </div>
            </div>
        </div>
    </div>
    {{> partials/footer }}
</div>



{{> partials/vendor-scripts }}

<script src="assets/libs/nouislider/nouislider.min.js"></script>
<script src="assets/libs/wnumb/wNumb.min.js"></script>

<script type="module" src="assets/js/apps/ecommerce/product-grid.init.js"></script>

<script type="module" src="assets/js/main.js"></script>


</body>
</html>