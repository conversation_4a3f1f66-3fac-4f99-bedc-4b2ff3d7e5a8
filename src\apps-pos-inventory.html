{{> partials/main }}

<head>

    {{> partials/title-meta title="Product & Catalog" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}
{{> partials/page-heading title="Product & Catalog" sub-title="POS" }}

<div x-data="inventory">
    <div x-data="{ isOpen: true }" x-show="isOpen" class="alert alert-red mb-5">
        <div class="flex items-center justify-between">
            <div class="flex items-center gap-2">
                <i data-lucide="alert-triangle" class="size-5"></i>
                <span x-text="'Low Stock Alert: ' + lowStockCount + ' items need attention'"></span>
            </div>
            <a href="javascript:void(0);" x-on:click="isOpen = false" class="text-red-400 hover:text-red-200 btn-close !top-3.5">
                <i data-lucide="x" class="size-4"></i>
            </a>
        </div>
    </div>
    <div class="card">
        <div class="card-header flex items-center gap-3">
            <h6 class="card-title grow">Inventory List</h6>
            <div class="relative group/form shrink-0">
                <input type="text" x-model="searchTerm" class="ltr:pl-9 rtl:pr-9 form-input ltr:group-[&.right]/form:pr-9 rtl:group-[&.right]/form:pl-9 ltr:group-[&.right]/form:pl-4 rtl:group-[&.right]/form:pr-4" placeholder="Search for ...">
                <button title="search-button" class="absolute inset-y-0 flex items-center text-gray-500 dark:text-dark-500 ltr:left-3 rtl:right-3 ltr:group-[&.right]/form:right-3 rtl:group-[&.right]/form:left-3 ltr:group-[&.right]/form:left-auto rtl:group-[&.right]/form:right-auto focus:outline-hidden">
                    <i data-lucide="search" class="size-4"></i>
                </button>
            </div>
            <div class="shrink-0">
                <button type="button" class="btn btn-primary" data-modal-target="addInventoryModal"><i data-lucide="plus" class="size-4 inline-block ltr:mr-1 rtl:ml-1"></i> Add Product</button>
            </div>
        </div>
        <div class="card-body pt-0">
            <div class="table-box overflow-x-auto">
                <table class="table whitespace-nowrap">
                    <thead class="bg-gray-100 dark:bg-dark-850">
                        <tr class="*:text-gray-500 *:text-dark-500">
                            <th x-on:click="sort('name')" class="text-left text-xs font-medium uppercase tracking-wider cursor-pointer">Product <span x-show="sortBy === 'name'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                            <th x-on:click="sort('sku')" class="text-left text-xs font-medium uppercase tracking-wider cursor-pointer">SKU <span x-show="sortBy === 'sku'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                            <th x-on:click="sort('category')" class="text-left text-xs font-medium uppercase tracking-wider cursor-pointer">Category <span x-show="sortBy === 'category'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                            <th x-on:click="sort('stock')" class="text-left text-xs font-medium uppercase tracking-wider cursor-pointer">Stock <span x-show="sortBy === 'stock'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                            <th x-on:click="sort('price')" class="text-left text-xs font-medium uppercase tracking-wider cursor-pointer">Price <span x-show="sortBy === 'price'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                            <th x-on:click="sort('cost')" class="text-left text-xs font-medium uppercase tracking-wider cursor-pointer">Cost <span x-show="sortBy === 'cost'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                            <th x-on:click="sort('margin')" class="text-left text-xs font-medium uppercase tracking-wider cursor-pointer">Margin <span x-show="sortBy === 'margin'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                            <th x-on:click="sort('status')" class="text-left text-xs font-medium uppercase tracking-wider cursor-pointer">Status <span x-show="sortBy === 'status'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                            <th class="text-left text-xs font-medium uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <template x-for="product in displayedProducts" :key="product.id">
                            <tr>
                                <td>
                                    <h6 class="text-sm" x-text="product.name"></h6>
                                    <div class="text-sm text-gray-500" x-text="product.description"></div>
                                </td>
                                <td x-text="product.sku"></td>
                                <td x-text="product.category"></td>
                                <td>
                                    <div class="flex flex-col">
                                        <span :class="'badge ' + getStockBadgeClass(getStockStatus(product.stock, product.minStock, product.maxStock))" x-text="product.stock + ' ' + product.unit + ' in stock'"></span>
                                        <span class="text-xs text-gray-500 mt-1" x-text="'Min: ' + product.minStock + ' | Max: ' + product.maxStock"></span>
                                    </div>
                                </td>
                                <td x-text="'$' + product.price + '/' + product.unit"></td>
                                <td x-text="'$' + product.cost + '/' + product.unit"></td>
                                <td x-text="product.margin + '%'"></td>
                                <td>
                                    <span :class="'badge ' + getStockBadgeClass(getStockStatus(product.stock, product.minStock, product.maxStock))" x-text="getStatusText(getStockStatus(product.stock, product.minStock, product.maxStock))"></span>
                                </td>
                                <td>
                                    <div class="flex gap-2">
                                        <button class="link link-primary" aria-label="View Details">
                                            <i class="ri-eye-line"></i>
                                        </button>
                                        <button class="link link-green" data-modal-target="addInventoryModal" aria-label="Edit">
                                            <i class="ri-pencil-line"></i>
                                        </button>
                                        <button class="link link-red" data-modal-target="deleteModal" aria-label="Delete">
                                            <i class="ri-delete-bin-line"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </template>
                        <tr>
                            <template x-if="displayedProducts.length == 0">
                                <td colspan="10" class="!p-8 border-0">
                                    <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" class="mx-auto size-12" viewBox="0 0 48 48">
                                        <linearGradient id="SVGID_1__h35ynqzIJzH4_gr1" x1="34.598" x2="15.982" y1="15.982" y2="34.598" gradientUnits="userSpaceOnUse">
                                            <stop offset="0" stop-color="#60e8fe"></stop>
                                            <stop offset=".033" stop-color="#6ae9fe"></stop>
                                            <stop offset=".197" stop-color="#97f0fe"></stop>
                                            <stop offset=".362" stop-color="#bdf5ff"></stop>
                                            <stop offset=".525" stop-color="#dafaff"></stop>
                                            <stop offset=".687" stop-color="#eefdff"></stop>
                                            <stop offset=".846" stop-color="#fbfeff"></stop>
                                            <stop offset="1" stop-color="#fff"></stop>
                                        </linearGradient>
                                        <path fill="url(#SVGID_1__h35ynqzIJzH4_gr1)" d="M40.036,33.826L31.68,25.6c0.847-1.739,1.335-3.684,1.335-5.748c0-7.27-5.894-13.164-13.164-13.164	S6.688,12.582,6.688,19.852c0,7.27,5.894,13.164,13.164,13.164c2.056,0,3.995-0.485,5.728-1.326l3.914,4.015l4.331,4.331	c1.715,1.715,4.496,1.715,6.211,0C41.751,38.321,41.751,35.541,40.036,33.826z"></path>
                                        <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M31.95,25.739l8.086,8.086c1.715,1.715,1.715,4.496,0,6.211l0,0c-1.715,1.715-4.496,1.715-6.211,0	l-4.331-4.331"></path>
                                        <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M7.525,24.511c-1.771-4.694-0.767-10.196,3.011-13.975c3.847-3.847,9.48-4.817,14.228-2.912"></path>
                                        <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M30.856,12.603c3.376,5.114,2.814,12.063-1.688,16.565c-4.858,4.858-12.565,5.129-17.741,0.814"></path>
                                    </svg>
                                    <p class="mt-2 text-center text-gray-500 dark:text-dark-500">No matching records found</p>
                                </td>
                            </template>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="grid grid-cols-12 gap-5 mt-5 items-center" x-show="filteredProducts.length !== 0">
                <div class="col-span-12 md:col-span-6 justify-center md:justify-start flex flex-wrap">
                    <p class="text-gray-500 dark:text-dark-500 ltr:pr-1 rtl:pl-1">Showing <b x-text="showingStart"></b> - <b x-text="showingEnd"></b> of <b x-text="filteredProducts.length"></b> Results</p>
                    <span x-show="selectedItems.length > 0"><b x-text="selectedItems.length != 0 ? selectedItems.length : ''"> </b> Row Selected</span>
                </div>
                <div class="col-span-12 md:col-span-6">
                    <div class="flex justify-center md:justify-end pagination pagination-primary">
                        <button @click="prevPage()" :disabled="currentPage === 1" class="pagination-pre">
                            <i data-lucide="chevron-left" class="mr-1 ltr:inline-block rtl:hidden size-4"></i>
                            <i data-lucide="chevron-right" class="ml-1 ltr:hidden rtl:inline-block size-4"></i>
                            Prev
                        </button>
                        <template x-for="page in totalPages" :key="page">
                            <button @click="gotoPage(page)" :class="{ 'active': currentPage === page }" class="pagination-item">
                                <span x-text="page"></span>
                            </button>
                        </template>
                        <button @click="nextPage()" :disabled="currentPage === totalPages" class="pagination-next">
                            Next
                            <i data-lucide="chevron-right" class="ml-1 ltr:inline-block rtl:hidden size-4"></i>
                            <i data-lucide="chevron-left" class="mr-1 ltr:hidden rtl:inline-block size-4"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div id="addInventoryModal" class="!hidden modal show">
        <div class="modal-wrap modal-center">
            <div class="modal-header">
                <h6 class="modal-title">Add Inventory</h6>
                <button data-modal-close="addInventoryModal" class="link link-red"><i data-lucide="x" class="size-5"></i></button>
            </div>
            <div class="modal-content">
                <div class="grid grid-cols-12 gap-4">
                    <div class="col-span-12">
                        <label for="productNameInput" class="form-label">Product Name</label>
                        <input type="text" id="productNameInput" class="form-input" placeholder="Product name">
                        <!-- <span x-show="errors.productNameInput" class="text-red-500" x-text="errors.productNameInput"></span> -->
                    </div>
                    <div class="col-span-12">
                        <label for="categoryIngredientsName" class="form-label">Category Ingredients Name</label>
                        <input list="browsers" type="text" id="categoryIngredientsName" class="form-input" placeholder="Category ingredients name">
                        <datalist id="browsers">
                            <option value="Drinks Ingredients">
                            <option value="Cooking Essentials">
                            <option value="Dairy Products">
                            <option value="Meat Products">
                            <option value="Desserts">
                            <option value="Beverages">
                        </datalist>
                        <!-- <span x-show="errors.categoryIngredientsName" class="text-red-500" x-text="errors.categoryIngredientsName"></span> -->
                    </div>
                    <div class="col-span-12">
                        <label for="SKUInput" class="form-label">SKU</label>
                        <input type="text" id="SKUInput" class="form-input" placeholder="SRB0748">
                        <!-- <span x-show="errors.SKUInput" class="text-red-500" x-text="errors.SKUInput"></span> -->
                    </div>
                    <div class="col-span-12">
                        <label for="categorySelect" class="form-label">Category</label>
                        <div id="categorySelect" placeholder="Select Category"></div>
                        <!-- <span x-show="errors.categorySelect" class="text-red-500" x-text="errors.categorySelect"></span> -->
                    </div>
                    <div class="col-span-12">
                        <label for="availableStockInput" class="form-label">Available Stock</label>
                        <input type="text" id="availableStockInput" class="form-input" placeholder="0/kg/pieces/cans/liters">
                        <!-- <span x-show="errors.availableStockInput" class="text-red-500" x-text="errors.availableStockInput"></span> -->
                    </div>
                    <div class="col-span-6">
                        <label for="minStockInput" class="form-label">Min Stock</label>
                        <input type="text" id="minStockInput" class="form-input" placeholder="0">
                        <!-- <span x-show="errors.minStockInput" class="text-red-500" x-text="errors.minStockInput"></span> -->
                    </div>
                    <div class="col-span-6">
                        <label for="maxStockInput" class="form-label">Max Stock</label>
                        <input type="text" id="maxStockInput" class="form-input" placeholder="000">
                        <!-- <span x-show="errors.maxStockInput" class="text-red-500" x-text="errors.maxStockInput"></span> -->
                    </div>
                    <div class="col-span-6">
                        <label for="costInput" class="form-label">Cost</label>
                        <input type="text" id="costInput" class="form-input" placeholder="$0.00">
                        <!-- <span x-show="errors.costInput" class="text-red-500" x-text="errors.costInput"></span> -->
                    </div>
                    <div class="col-span-6">
                        <label for="priceInput" class="form-label">Selling Price</label>
                        <input type="text" id="priceInput" class="form-input" placeholder="$0.00">
                        <!-- <span x-show="errors.priceInput" class="text-red-500" x-text="errors.priceInput"></span> -->
                    </div>
                </div>
                <div class="flex items-center justify-end gap-2 mt-5">
                    <button type="button" class="btn btn-active-red" data-modal-close="addInventoryModal">
                        <i data-lucide="x" class="inline-block size-4"></i>
                        <span class="align-baseline">Close</span>
                    </button>
                    <button type="button" class="btn btn-primary">Add Inventory</button>
                </div>
            </div>
        </div>
    </div>

    <!--delete modal-->
    <div id="deleteModal" class="!hidden modal show">
        <div class="modal-wrap modal-xs modal-center">
            <div class="text-center modal-content p-7">
                <div class="flex items-center justify-center mx-auto mb-4 text-red-500 rounded-full bg-red-500/10 size-14 backdrop-blur-xl">
                    <i data-lucide="trash-2" class="size-6"></i>
                </div>
                <h5 class="mb-4">Are you sure you want to delete this project ?</h5>
                <div class="flex items-center justify-center gap-2">
                    <button class="btn btn-red" data-modal-close="deleteModal">Delete</button>
                    <button data-modal-close="deleteModal" class="btn link link-primary">Cancel</button>
                </div>
            </div>
        </div>
    </div>
</div>

</div>
{{> partials/footer }}
</div>

{{> partials/vendor-scripts }}

<script type="module" src="assets/js/apps/pos/inventory.init.js"></script>

<script type="module" src="assets/js/main.js"></script>

</body>

</html>