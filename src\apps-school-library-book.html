{{> partials/main }}

<head>

    {{> partials/title-meta title="Book Library" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}
{{> partials/page-heading title="Book Library" sub-title="School" }}
<div x-data="bookTable()">
    <div class="grid grid-cols-12 gap-space mb-space">
        <div class="col-span-12 md:col-span-4 xl:col-span-3">
            <div class="relative flex items-center">
                <input type="text" class="ltr:rounded-r-none rtl:rounded-l-none ltr:border-r-0 rtl:border-l-0 form-input grow" placeholder="Search for ..." x-model="searchTerm" @input="filteredBooks" />
                <button type="button" title="search-button" class="border-gray-200 ltr:rounded-l-none rtl:rounded-r-none btn btn-sub-gray btn-icon shrink-0"><i data-lucide="search" class="size-5"></i></button>
            </div>
        </div><!--end col-->
        <div class="col-span-12 md:col-span-3 2xl:col-span-2 2xl:col-start-9">
            <div id="sortBySelect" placeholder="Sort By" @change="filteredBooks()"></div>
        </div><!--end col-->
        <div class="col-span-12 md:col-span-3 2xl:col-span-2">
            <button type="button" class="w-full btn btn-primary" @click="handleModalOpen()" data-modal-target="addBookModal"><i data-lucide="circle-plus" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> Add Book</button>
        </div><!--end col-->
    </div><!--end grid-->
    <div class="grid grid-cols-12 gap-x-space">
        <template x-if="filteredBooks" x-for="book in displayedBooks">
            <div class="col-span-12 md:col-span-6 xl:col-span-4 2xl:col-span-3 card">
                <div class="flex items-center gap-3 card-body">
                    <img :src="book.image" alt="" class="h-40 rounded-md cursor-pointer shrink-0" @click="inViewBook = book" data-modal-target="bookOverviewModal">
                    <div class="overflow-hidden grow">
                        <h6 class="mb-1 truncate"><a href="#!" @click="inViewBook = book" data-modal-target="bookOverviewModal" class="text-current link link-primary" x-text="book.title"></a>
                        </h6>
                        <p class="mb-2 text-gray-500 dark:text-dark-500">By <a href="#!" class="underline link link-green" x-text="book.author"></a></p>
                        <div class="text-yellow-500 flex gap-1.5 mb-2">
                            <template x-for="i in fullStars(book.rating)">
                                <i class="ri-star-fill"></i>
                            </template>
                            <template x-if="halfStars(book.rating)">
                                <i class="ri-star-half-fill"></i>
                            </template>
                            <template x-for="i in emptyStars(book.rating)">
                                <i class="ri-star-line"></i>
                            </template>
                            <span class="text-gray-800 dark:text-dark-50" x-text="`(${book.reviewCount})`"></span>
                        </div>
                        <h5 class="mb-2" x-text="`$${book.price}`">$47.99</h5>
                        <button type="button" class="w-full btn btn-sub-red">Buy Now</button>
                    </div>
                </div>
            </div>
        </template>
    
    </div>
    <template x-if="filterBooks.length == 0">
        <div colspan="10" class="!p-8">
            <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" class="mx-auto size-12" viewBox="0 0 48 48">
                <linearGradient id="SVGID_1__h35ynqzIJzH4_gr1" x1="34.598" x2="15.982" y1="15.982" y2="34.598" gradientUnits="userSpaceOnUse">
                    <stop offset="0" stop-color="#60e8fe"></stop>
                    <stop offset=".033" stop-color="#6ae9fe"></stop>
                    <stop offset=".197" stop-color="#97f0fe"></stop>
                    <stop offset=".362" stop-color="#bdf5ff"></stop>
                    <stop offset=".525" stop-color="#dafaff"></stop>
                    <stop offset=".687" stop-color="#eefdff"></stop>
                    <stop offset=".846" stop-color="#fbfeff"></stop>
                    <stop offset="1" stop-color="#fff"></stop>
                </linearGradient>
                <path fill="url(#SVGID_1__h35ynqzIJzH4_gr1)" d="M40.036,33.826L31.68,25.6c0.847-1.739,1.335-3.684,1.335-5.748c0-7.27-5.894-13.164-13.164-13.164	S6.688,12.582,6.688,19.852c0,7.27,5.894,13.164,13.164,13.164c2.056,0,3.995-0.485,5.728-1.326l3.914,4.015l4.331,4.331	c1.715,1.715,4.496,1.715,6.211,0C41.751,38.321,41.751,35.541,40.036,33.826z"></path>
                <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M31.95,25.739l8.086,8.086c1.715,1.715,1.715,4.496,0,6.211l0,0c-1.715,1.715-4.496,1.715-6.211,0	l-4.331-4.331"></path>
                <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M7.525,24.511c-1.771-4.694-0.767-10.196,3.011-13.975c3.847-3.847,9.48-4.817,14.228-2.912"></path>
                <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M30.856,12.603c3.376,5.114,2.814,12.063-1.688,16.565c-4.858,4.858-12.565,5.129-17.741,0.814"></path>
            </svg>
            <p class="mt-2 text-center text-gray-500 dark:text-dark-500">No matching records found</p>
        </div>
    </template>
    <div class="grid grid-cols-12 gap-5 mb-5 items-center" x-show="displayedBooks.length !== 0">
        <div class="col-span-12 text-center md:col-span-6 ltr:md:text-left rtl:md:text-right">
            <p class="text-gray-500 dark:text-dark-500">Showing <b x-text="showingStart"></b> - <b x-text="showingEnd"></b> of <b x-text="filterBooks.length"></b> Results</p>
        </div>
        <div class="col-span-12 md:col-span-6">
            <div class="flex justify-center md:justify-end pagination pagination-primary">
                <button @click="prevPage()" :disabled="currentPage === 1" class="pagination-pre">
                    <i data-lucide="chevron-left" class="mr-1 ltr:inline-block rtl:hidden size-4"></i>
                    <i data-lucide="chevron-right" class="ml-1 ltr:hidden rtl:inline-block size-4"></i>
                    Prev
                </button>
                <template x-for="page in totalPages" :key="page">
                    <button @click="gotoPage(page)" :class="{ 'active': currentPage === page }" class="pagination-item">
                        <span x-text="page"></span>
                    </button>
                </template>
                <button @click="nextPage()" :disabled="currentPage === totalPages" class="pagination-next">
                    Next
                    <i data-lucide="chevron-right" class="ml-1 ltr:inline-block rtl:hidden size-4"></i>
                    <i data-lucide="chevron-left" class="mr-1 ltr:hidden rtl:inline-block size-4"></i>
                </button>
            </div>
        </div>
    </div>

    <!--Add Book Modals-->
    <div id="addBookModal" class="!hidden modal show" x-show="addBookModal">
        <div class="modal-wrap modal-center">
            <div class="modal-header">
                <h6>Add Book</h6>
                <button data-modal-close="addBookModal" class="link link-red"><i data-lucide="x" class="size-5"></i></button>
            </div>
            <div class="modal-content">
                <form action="#!">
                    <div class="grid grid-cols-12 gap-space">
                        <div class="col-span-12">
                            <div class="text-center">
                                <label for="logo">
                                    <div class="inline-flex items-center justify-center mx-auto overflow-hidden bg-gray-100 border border-gray-200 rounded-md cursor-pointer dark:bg-dark-850 dark:border-dark-800 h-44">
                                        <img x-show="imageUrl" :src="imageUrl" class="object-cover w-full h-full">
                                        <div x-show="!imageUrl" class="flex flex-col items-center mx-8 text-gray-500 dark:text-dark-500">
                                            <i data-lucide="upload"></i>
                                            <div class="mt-2">Book Poster</div>
                                        </div>
                                    </div>
                                </label>
                                <label class="hidden">
                                    <input type="file" name="logo" id="logo" @change="fileChosen" class="block w-full text-sm file:rounded-md focus:outline-0 text-slate-500 file:mr-4 file:py-2 file:px-4 file:border-0 file:text-sm file:font-semibold file:bg-violet-50 file:text-violet-700 hover:file:bg-violet-100 " />
                                </label>
                            </div>
                            <div class="text-center">
                            <span x-show="errors.image" x-text="errors.image" class="text-red-500 "></span>
                        </div>
                        </div><!--end col-->
                        <div class="col-span-12">
                            <label for="bookTitleInput" class="form-label">Book Title</label>
                            <input type="text" id="bookTitleInput" class="form-input" placeholder="Enter book title" x-model="bookForm.title" @input="validateField('title', bookForm.title, 'Title is required.')">
                            <span x-show="errors.title" x-text="errors.title" class="text-red-500"></span>
                        </div>
                        <div class="col-span-12">
                            <label for="writerNameInput" class="form-label">Writer Name</label>
                            <input type="text" id="writerNameInput" class="form-input" placeholder="Enter writer name" x-model="bookForm.author" @input="validateField('author', bookForm.author, 'Author is required.')">
                            <span x-show="errors.author" x-text="errors.author" class="text-red-500"></span>
                        </div>
                        <div class="col-span-12">
                            <label for="priceInput" class="form-label">Price</label>
                            <input type="number" id="priceInput" class="form-input" placeholder="$00.00" x-model="bookForm.price" @input="validateField('price', bookForm.price, 'Price is required.')">
                            <span x-show="errors.price" x-text="errors.price" class="text-red-500"></span>
                        </div>
                        <div class="col-span-12">
                            <div class="flex justify-end gap-2">
                                <button type="button" class="btn btn-active-red" data-modal-close="addBookModal">
                                    <i data-lucide="x" class="inline-block size-4"></i>
                                    <span class="align-baseline">Close</span>
                                </button>
                                <button type="button" class="btn btn-primary" @click="AddBook()">Add Book</button>
                            </div>
                        </div>
                    </div><!--end grid-->
                </form>
            </div>
        </div>
    </div><!--end modals-->

    <!--Book Overview Modals-->
    <div id="bookOverviewModal" class="!hidden modal show">
        <div class="modal-wrap modal-center">
            <div class="modal-header">
                <h6>Book Overview</h6>
                <button data-modal-close="bookOverviewModal" class="link link-red"><i data-lucide="x" class="size-5"></i></button>
            </div>
            <div class="modal-content">
                <img :src="inViewBook.image" alt="" class="mx-auto rounded-md h-44">
                <div class="mt-5">
                    <div class="text-yellow-500 flex gap-1.5 mb-2">

                        <template x-for="i in fullStars(inViewBook.rating)">
                            <i class="ri-star-fill"></i>
                        </template>
                        <template x-if="halfStars(inViewBook.rating)">
                            <i class="ri-star-half-fill"></i>
                        </template>
                        <template x-for="i in emptyStars(inViewBook.rating)">
                            <i class="ri-star-line"></i>
                        </template>
                        <span class="text-gray-800 dark:text-dark-50" x-text="`(${inViewBook.reviewCount})`"></span>
                    </div>
                    <h6 class="mb-1 truncate"><a href="#!" class="text-current link link-primary" x-text="inViewBook.title">The Wager: A Tale of Shipwreck, Mutiny and Murder</a></h6>
                    <p class="mb-2 text-gray-500 dark:text-dark-500">By <a href="#!" class="underline link link-green" x-text="inViewBook.author">Elin Hilderbrand</a></p>
                    <h5 class="mb-2" x-text="`${inViewBook.price}`">$29.99</h5>
                    <button type="button" class="w-full btn btn-sub-red">Buy Now</button>
                </div>
            </div>
        </div>
    </div><!--end modals-->
</div> <!-- end table responsive -->
</div>
{{> partials/footer }}
</div>

{{> partials/vendor-scripts }}

<script type="module" src="assets/js/apps/school/library/book-list.init.js"></script>

<script type="module" src="assets/js/main.js"></script>

</body>
</html>