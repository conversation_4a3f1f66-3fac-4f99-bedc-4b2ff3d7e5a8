const orderList = [
    {
        "orderNumber": "POS-2024-002",
        "productName": "Classic Beef Burger",
        "orderDate": "Mar 15, 2025",
        "customersName": "<PERSON>",
        "price": "$12.99",
        "quantity": 1,
        "totalAmount": "$12.99",
        "paymentMethod": "Cash",
        "status": "Takeaway",
        "pickupTime": "15:30"
    },
    {
        "orderNumber": "POS-2024-003",
        "productName": "Chicken Biryani",
        "orderDate": "Mar 15, 2025",
        "customersName": "<PERSON>",
        "price": "$14.99",
        "quantity": 3,
        "totalAmount": "$44.97",
        "paymentMethod": "Debit Card",
        "status": "Delivered",
        "deliveryAddress": "123 Main St, City"
    },
    {
        "orderNumber": "POS-2024-004",
        "productName": "Espresso",
        "orderDate": "Mar 15, 2025",
        "customersName": "<PERSON> Lee",
        "price": "$3.99",
        "quantity": 1,
        "totalAmount": "$3.99",
        "paymentMethod": "Debit Card",
        "status": "Dine In",
        "tableNumber": "B05"
    },
    {
        "orderNumber": "POS-2024-005",
        "productName": "Supreme Pizza",
        "orderDate": "Mar 15, 2025",
        "customersName": "Emma Thompson",
        "price": "$18.99",
        "quantity": 1,
        "totalAmount": "$18.99",
        "paymentMethod": "Cash",
        "status": "Takeaway",
        "pickupTime": "16:45"
    },
    {
        "orderNumber": "POS-2024-006",
        "productName": "Fresh Lemonade",
        "orderDate": "Mar 15, 2025",
        "customersName": "James Wilson",
        "price": "$4.99",
        "quantity": 2,
        "totalAmount": "$9.98",
        "paymentMethod": "Cash",
        "status": "Dine In",
        "tableNumber": "C08"
    },
    {
        "orderNumber": "POS-2024-007",
        "productName": "Fettuccine Alfredo",
        "orderDate": "Mar 15, 2025",
        "customersName": "Lisa Anderson",
        "price": "$15.99",
        "quantity": 1,
        "totalAmount": "$15.99",
        "paymentMethod": "Credit Card",
        "status": "Delivered",
        "deliveryAddress": "456 Oak Ave, Town"
    },
    {
        "orderNumber": "POS-2024-008",
        "productName": "Double Cheese Burger",
        "orderDate": "Mar 15, 2025",
        "customersName": "Robert Chen",
        "price": "$13.99",
        "quantity": 1,
        "totalAmount": "$13.99",
        "paymentMethod": "Credit Card",
        "status": "Takeaway",
        "pickupTime": "17:15"
    },
    {
        "orderNumber": "POS-2024-009",
        "productName": "Vegetable Fried Rice",
        "orderDate": "Mar 15, 2025",
        "customersName": "Maria Garcia",
        "price": "$12.99",
        "quantity": 1,
        "totalAmount": "$12.99",
        "paymentMethod": "Cash",
        "status": "Dine In",
        "tableNumber": "D03"
    },
    {
        "orderNumber": "POS-2024-010",
        "productName": "Iced Americano",
        "orderDate": "Mar 15, 2025",
        "customersName": "Thomas White",
        "price": "$4.49",
        "quantity": 2,
        "totalAmount": "$8.98",
        "paymentMethod": "Debit Card",
        "status": "Delivered",
        "deliveryAddress": "789 Pine Rd, Village"
    },
    {
        "orderNumber": "POS-2024-011",
        "productName": "Pepperoni Pizza",
        "orderDate": "Mar 15, 2025",
        "customersName": "Jennifer Kim",
        "price": "$16.99",
        "quantity": 1,
        "totalAmount": "$16.99",
        "paymentMethod": "Credit Card",
        "status": "Takeaway",
        "pickupTime": "18:00"
    },
    {
        "orderNumber": "POS-2024-012",
        "productName": "Chicken Pasta",
        "orderDate": "Mar 15, 2025",
        "customersName": "Daniel Martinez",
        "price": "$14.99",
        "quantity": 1,
        "totalAmount": "$14.99",
        "paymentMethod": "Bank Transfer",
        "status": "Dine In",
        "tableNumber": "E07"
    },
    {
        "orderNumber": "POS-2024-013",
        "productName": "Chicken Burger",
        "orderDate": "Mar 15, 2025",
        "customersName": "Sophie Taylor",
        "price": "$11.99",
        "quantity": 1,
        "totalAmount": "$11.99",
        "paymentMethod": "Bank Transfer",
        "status": "Delivered",
        "deliveryAddress": "321 Elm St, County"
    },
    {
        "orderNumber": "POS-2024-014",
        "productName": "Green Tea",
        "orderDate": "Mar 15, 2025",
        "customersName": "William Johnson",
        "price": "$3.99",
        "quantity": 2,
        "totalAmount": "$7.98",
        "paymentMethod": "Bank Transfer",
        "status": "Takeaway",
        "pickupTime": "18:30"
    },
    {
        "orderNumber": "POS-2024-015",
        "productName": "Vegetable Biryani",
        "orderDate": "Mar 15, 2025",
        "customersName": "Olivia Davis",
        "price": "$13.99",
        "quantity": 1,
        "totalAmount": "$13.99",
        "paymentMethod": "Cash",
        "status": "Dine In",
        "tableNumber": "F02"
    },
    {
        "orderNumber": "POS-2024-016",
        "productName": "Margherita Pizza",
        "orderDate": "Mar 15, 2025",
        "customersName": "John Smith",
        "price": "$15.99",
        "quantity": 2,
        "totalAmount": "$31.98",
        "paymentMethod": "Cash",
        "status": "Dine In",
        "tableNumber": "A03"
    },
    {
        "orderNumber": "POS-2024-017",
        "productName": "Spaghetti Bolognese",
        "orderDate": "Mar 15, 2025",
        "customersName": "Emily Johnson",
        "price": "$14.99",
        "quantity": 1,
        "totalAmount": "$14.99",
        "paymentMethod": "Bank Transfer",
        "status": "Takeaway",
        "pickupTime": "19:15"
    },
    {
        "orderNumber": "POS-2024-018",
        "productName": "Chicken Rice Bowl",
        "orderDate": "Mar 15, 2025",
        "customersName": "Robert Wilson",
        "price": "$12.99",
        "quantity": 3,
        "totalAmount": "$38.97",
        "paymentMethod": "Credit Card",
        "status": "Delivered",
        "deliveryAddress": "789 Pine Street, Apt 4B"
    },
    {
        "orderNumber": "POS-2024-019",
        "productName": "Bacon Cheeseburger",
        "orderDate": "Mar 15, 2025",
        "customersName": "Sarah Davis",
        "price": "$13.99",
        "quantity": 2,
        "totalAmount": "$27.98",
        "paymentMethod": "Cash",
        "status": "Dine In",
        "tableNumber": "C12"
    },
    {
        "orderNumber": "POS-2024-020",
        "productName": "Cappuccino",
        "orderDate": "Mar 15, 2025",
        "customersName": "Michael Brown",
        "price": "$4.49",
        "quantity": 1,
        "totalAmount": "$4.49",
        "paymentMethod": "Cash",
        "status": "Takeaway",
        "pickupTime": "20:00"
    },
    {
        "orderNumber": "POS-2024-021",
        "productName": "Seafood Pasta",
        "orderDate": "Mar 15, 2025",
        "customersName": "Lisa Anderson",
        "price": "$16.99",
        "quantity": 1,
        "totalAmount": "$16.99",
        "paymentMethod": "Bank Transfer",
        "status": "Delivered",
        "deliveryAddress": "456 Oak Avenue, Suite 7"
    },
    {
        "orderNumber": "POS-2024-022",
        "productName": "Veggie Burger",
        "orderDate": "Mar 15, 2025",
        "customersName": "David Miller",
        "price": "$12.99",
        "quantity": 2,
        "totalAmount": "$25.98",
        "paymentMethod": "Debit Card",
        "status": "Dine In",
        "tableNumber": "B08"
    },
    {
        "orderNumber": "POS-2024-023",
        "productName": "Fresh Orange Juice",
        "orderDate": "Mar 15, 2025",
        "customersName": "Jennifer White",
        "price": "$5.99",
        "quantity": 1,
        "totalAmount": "$5.99",
        "paymentMethod": "Credit Card",
        "status": "Takeaway",
        "pickupTime": "20:30"
    },
    {
        "orderNumber": "POS-2024-024",
        "productName": "Chicken Fried Rice",
        "orderDate": "Mar 15, 2025",
        "customersName": "Thomas Lee",
        "price": "$13.99",
        "quantity": 1,
        "totalAmount": "$13.99",
        "paymentMethod": "Bank Transfer",
        "status": "Delivered",
        "deliveryAddress": "123 Maple Drive, Unit 3"
    },
    {
        "orderNumber": "POS-2024-025",
        "productName": "Hawaiian Pizza",
        "orderDate": "Mar 15, 2025",
        "customersName": "Emma Wilson",
        "price": "$17.99",
        "quantity": 2,
        "totalAmount": "$35.98",
        "paymentMethod": "Cash",
        "status": "Dine In",
        "tableNumber": "D05"
    },
    {
        "orderNumber": "POS-2024-026",
        "productName": "Caramel Latte",
        "orderDate": "Mar 15, 2025",
        "customersName": "James Taylor",
        "price": "$4.99",
        "quantity": 1,
        "totalAmount": "$4.99",
        "paymentMethod": "Cash",
        "status": "Takeaway",
        "pickupTime": "21:00"
    },
    {
        "orderNumber": "POS-2024-027",
        "productName": "Penne Arrabbiata",
        "orderDate": "Mar 15, 2025",
        "customersName": "Maria Garcia",
        "price": "$14.99",
        "quantity": 1,
        "totalAmount": "$14.99",
        "paymentMethod": "Debit Card",
        "status": "Delivered",
        "deliveryAddress": "789 Cedar Lane, Apt 12"
    },
    {
        "orderNumber": "POS-2024-028",
        "productName": "BBQ Chicken Pizza",
        "orderDate": "Mar 15, 2025",
        "customersName": "Daniel Kim",
        "price": "$16.99",
        "quantity": 2,
        "totalAmount": "$33.98",
        "paymentMethod": "Credit Card",
        "status": "Dine In",
        "tableNumber": "A07"
    },
    {
        "orderNumber": "POS-2024-029",
        "productName": "Mango Smoothie",
        "orderDate": "Mar 15, 2025",
        "customersName": "Sophie Chen",
        "price": "$6.99",
        "quantity": 1,
        "totalAmount": "$6.99",
        "paymentMethod": "Cash",
        "status": "Takeaway",
        "pickupTime": "21:15"
    },
    {
        "orderNumber": "POS-2024-030",
        "productName": "Vegetable Rice Bowl",
        "orderDate": "Mar 15, 2025",
        "customersName": "William Johnson",
        "price": "$12.99",
        "quantity": 1,
        "totalAmount": "$12.99",
        "paymentMethod": "Debit Card",
        "status": "Delivered",
        "deliveryAddress": "321 Elm Street, Unit 5"
    },
    {
        "orderNumber": "POS-2024-031",
        "productName": "Classic Cheeseburger",
        "orderDate": "Mar 15, 2025",
        "customersName": "Olivia Davis",
        "price": "$11.99",
        "quantity": 2,
        "totalAmount": "$23.98",
        "paymentMethod": "Credit Card",
        "status": "Dine In",
        "tableNumber": "C03"
    },
    {
        "orderNumber": "POS-2024-032",
        "productName": "Iced Tea",
        "orderDate": "Mar 15, 2025",
        "customersName": "Robert Wilson",
        "price": "$3.99",
        "quantity": 1,
        "totalAmount": "$3.99",
        "paymentMethod": "Cash",
        "status": "Takeaway",
        "pickupTime": "21:30"
    },
    {
        "orderNumber": "POS-2024-033",
        "productName": "Lasagna",
        "orderDate": "Mar 15, 2025",
        "customersName": "Lisa Anderson",
        "price": "$15.99",
        "quantity": 1,
        "totalAmount": "$15.99",
        "paymentMethod": "Debit Card",
        "status": "Delivered",
        "deliveryAddress": "456 Oak Avenue, Suite 3"
    },
    {
        "orderNumber": "POS-2024-034",
        "productName": "Chicken Mushroom Rice",
        "orderDate": "Mar 15, 2025",
        "customersName": "David Miller",
        "price": "$13.99",
        "quantity": 2,
        "totalAmount": "$27.98",
        "paymentMethod": "Credit Card",
        "status": "Dine In",
        "tableNumber": "B12"
    },
    {
        "orderNumber": "POS-2024-035",
        "productName": "Hot Chocolate",
        "orderDate": "Mar 15, 2025",
        "customersName": "Jennifer White",
        "price": "$4.99",
        "quantity": 1,
        "totalAmount": "$4.99",
        "paymentMethod": "Cash",
        "status": "Takeaway",
        "pickupTime": "21:45"
    },
    {
        "orderNumber": "POS-2024-036",
        "productName": "Four Cheese Pizza",
        "orderDate": "Mar 15, 2025",
        "customersName": "Christopher Lee",
        "price": "$17.99",
        "quantity": 1,
        "totalAmount": "$17.99",
        "paymentMethod": "Credit Card",
        "status": "Dine In",
        "tableNumber": "A12"
    },
    {
        "orderNumber": "POS-2024-037",
        "productName": "Chicken Pasta Primavera",
        "orderDate": "Mar 15, 2025",
        "customersName": "Patricia Martinez",
        "price": "$15.99",
        "quantity": 2,
        "totalAmount": "$31.98",
        "paymentMethod": "Debit Card",
        "status": "Takeaway",
        "pickupTime": "22:00"
    },
    {
        "orderNumber": "POS-2024-038",
        "productName": "Strawberry Smoothie",
        "orderDate": "Mar 15, 2025",
        "customersName": "Richard Thompson",
        "price": "$6.99",
        "quantity": 1,
        "totalAmount": "$6.99",
        "paymentMethod": "Cash",
        "status": "Delivered",
        "deliveryAddress": "567 Willow Street, Apt 8C"
    },
    {
        "orderNumber": "POS-2024-039",
        "productName": "Chicken Burger Deluxe",
        "orderDate": "Mar 15, 2025",
        "customersName": "Elizabeth Chen",
        "price": "$13.99",
        "quantity": 1,
        "totalAmount": "$13.99",
        "paymentMethod": "Credit Card",
        "status": "Dine In",
        "tableNumber": "B15"
    },
    {
        "orderNumber": "POS-2024-040",
        "productName": "Vegetable Rice Noodles",
        "orderDate": "Mar 15, 2025",
        "customersName": "Michael Rodriguez",
        "price": "$12.99",
        "quantity": 2,
        "totalAmount": "$25.98",
        "paymentMethod": "Debit Card",
        "status": "Takeaway",
        "pickupTime": "22:15"
    },
    {
        "orderNumber": "POS-2024-041",
        "productName": "Mac and Cheese",
        "orderDate": "Mar 15, 2025",
        "customersName": "Sarah Anderson",
        "price": "$11.99",
        "quantity": 1,
        "totalAmount": "$11.99",
        "paymentMethod": "Credit Card",
        "status": "Delivered",
        "deliveryAddress": "890 Oak Lane, Suite 12"
    },
    {
        "orderNumber": "POS-2024-042",
        "productName": "Mushroom Pizza",
        "orderDate": "Mar 15, 2025",
        "customersName": "David Wilson",
        "price": "$15.99",
        "quantity": 1,
        "totalAmount": "$15.99",
        "paymentMethod": "Cash",
        "status": "Dine In",
        "tableNumber": "C10"
    },
    {
        "orderNumber": "POS-2024-043",
        "productName": "Vanilla Latte",
        "orderDate": "Mar 15, 2025",
        "customersName": "Jennifer Brown",
        "price": "$4.99",
        "quantity": 2,
        "totalAmount": "$9.98",
        "paymentMethod": "Debit Card",
        "status": "Takeaway",
        "pickupTime": "22:30"
    },
    {
        "orderNumber": "POS-2024-044",
        "productName": "Chicken Rice Combo",
        "orderDate": "Mar 15, 2025",
        "customersName": "Robert Garcia",
        "price": "$14.99",
        "quantity": 1,
        "totalAmount": "$14.99",
        "paymentMethod": "Credit Card",
        "status": "Delivered",
        "deliveryAddress": "123 Pine Street, Unit 5"
    },
    {
        "orderNumber": "POS-2024-045",
        "productName": "Spaghetti Marinara",
        "orderDate": "Mar 15, 2025",
        "customersName": "Lisa Thompson",
        "price": "$13.99",
        "quantity": 1,
        "totalAmount": "$13.99",
        "paymentMethod": "Cash",
        "status": "Dine In",
        "tableNumber": "D08"
    },
    {
        "orderNumber": "POS-2024-046",
        "productName": "Double Bacon Burger",
        "orderDate": "Mar 15, 2025",
        "customersName": "James Wilson",
        "price": "$14.99",
        "quantity": 2,
        "totalAmount": "$29.98",
        "paymentMethod": "Credit Card",
        "status": "Dine In",
        "tableNumber": "E05"
    },
    {
        "orderNumber": "POS-2024-047",
        "productName": "Chicken Biryani",
        "orderDate": "Mar 15, 2025",
        "customersName": "Emma Davis",
        "price": "$14.99",
        "quantity": 1,
        "totalAmount": "$14.99",
        "paymentMethod": "Debit Card",
        "status": "Takeaway",
        "pickupTime": "22:45"
    },
    {
        "orderNumber": "POS-2024-048",
        "productName": "Caramel Macchiato",
        "orderDate": "Mar 15, 2025",
        "customersName": "Robert Chen",
        "price": "$4.99",
        "quantity": 3,
        "totalAmount": "$14.97",
        "paymentMethod": "Cash",
        "status": "Delivered",
        "deliveryAddress": "456 Maple Avenue, Suite 15"
    },
    {
        "orderNumber": "POS-2024-049",
        "productName": "Supreme Pizza",
        "orderDate": "Mar 15, 2025",
        "customersName": "Sarah Johnson",
        "price": "$18.99",
        "quantity": 1,
        "totalAmount": "$18.99",
        "paymentMethod": "Credit Card",
        "status": "Dine In",
        "tableNumber": "F03"
    },
    {
        "orderNumber": "POS-2024-050",
        "productName": "Berry Smoothie",
        "orderDate": "Mar 15, 2025",
        "customersName": "Michael Brown",
        "price": "$6.99",
        "quantity": 2,
        "totalAmount": "$13.98",
        "paymentMethod": "Debit Card",
        "status": "Takeaway",
        "pickupTime": "23:00"
    },
    {
        "orderNumber": "POS-2024-051",
        "productName": "Fettuccine Carbonara",
        "orderDate": "Mar 15, 2025",
        "customersName": "Patricia Lee",
        "price": "$15.99",
        "quantity": 1,
        "totalAmount": "$15.99",
        "paymentMethod": "Cash",
        "status": "Delivered",
        "deliveryAddress": "789 Cedar Road, Unit 7"
    },
    {
        "orderNumber": "POS-2024-052",
        "productName": "Classic Cheeseburger",
        "orderDate": "Mar 15, 2025",
        "customersName": "David Kim",
        "price": "$12.99",
        "quantity": 1,
        "totalAmount": "$12.99",
        "paymentMethod": "Credit Card",
        "status": "Dine In",
        "tableNumber": "A15"
    },
    {
        "orderNumber": "POS-2024-053",
        "productName": "Green Tea",
        "orderDate": "Mar 15, 2025",
        "customersName": "Jennifer White",
        "price": "$3.99",
        "quantity": 2,
        "totalAmount": "$7.98",
        "paymentMethod": "Debit Card",
        "status": "Takeaway",
        "pickupTime": "23:15"
    },
    {
        "orderNumber": "POS-2024-054",
        "productName": "Vegetable Biryani",
        "orderDate": "Mar 15, 2025",
        "customersName": "Thomas Anderson",
        "price": "$13.99",
        "quantity": 1,
        "totalAmount": "$13.99",
        "paymentMethod": "Cash",
        "status": "Delivered",
        "deliveryAddress": "321 Pine Street, Apt 9"
    },
    {
        "orderNumber": "POS-2024-055",
        "productName": "Pepperoni Pizza",
        "orderDate": "Mar 15, 2025",
        "customersName": "Elizabeth Wilson",
        "price": "$16.99",
        "quantity": 1,
        "totalAmount": "$16.99",
        "paymentMethod": "Credit Card",
        "status": "Dine In",
        "tableNumber": "B12"
    },
    {
        "orderNumber": "POS-2024-056",
        "productName": "Chicken Pasta Alfredo",
        "orderDate": "Mar 15, 2025",
        "customersName": "Robert Garcia",
        "price": "$15.99",
        "quantity": 1,
        "totalAmount": "$15.99",
        "paymentMethod": "Debit Card",
        "status": "Takeaway",
        "pickupTime": "23:30"
    },
    {
        "orderNumber": "POS-2024-057",
        "productName": "Mango Smoothie",
        "orderDate": "Mar 15, 2025",
        "customersName": "Lisa Martinez",
        "price": "$6.99",
        "quantity": 2,
        "totalAmount": "$13.98",
        "paymentMethod": "Cash",
        "status": "Delivered",
        "deliveryAddress": "567 Oak Lane, Apt 4"
    },
    {
        "orderNumber": "POS-2024-058",
        "productName": "Veggie Burger",
        "orderDate": "Mar 15, 2025",
        "customersName": "Daniel Lee",
        "price": "$12.99",
        "quantity": 1,
        "totalAmount": "$12.99",
        "paymentMethod": "Credit Card",
        "status": "Dine In",
        "tableNumber": "C07"
    },
    {
        "orderNumber": "POS-2024-059",
        "productName": "Cappuccino",
        "orderDate": "Mar 15, 2025",
        "customersName": "Maria Brown",
        "price": "$4.49",
        "quantity": 1,
        "totalAmount": "$4.49",
        "paymentMethod": "Debit Card",
        "status": "Takeaway",
        "pickupTime": "23:45"
    },
    {
        "orderNumber": "POS-2024-060",
        "productName": "Chicken Rice Bowl",
        "orderDate": "Mar 15, 2025",
        "customersName": "William Chen",
        "price": "$13.99",
        "quantity": 1,
        "totalAmount": "$13.99",
        "paymentMethod": "Cash",
        "status": "Delivered",
        "deliveryAddress": "890 Pine Street, Unit 12"
    }
]

export default orderList