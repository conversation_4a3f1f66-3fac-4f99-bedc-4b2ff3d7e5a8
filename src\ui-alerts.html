{{> partials/main }}

<head>

    {{> partials/title-meta title="Alerts" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

    {{> partials/topbar }}
    {{> partials/sidebar }}

    {{> partials/page-wrapper }}

            {{> partials/page-heading title="Alerts" sub-title="UI" }}

            <div class="grid grid-cols-1 xl:grid-cols-2 gap-x-space">
                <div class="card">
                    <div class="card-header">
                        <h6 class="card-title">Basic Alerts</h6>
                    </div>
                    <div class="card-body">
                        <div x-data="{ 
                                    alerts: [
                                        { text: 'Primary Alerts',   color: 'alert-primary',    closeBtn: 'text-primary-400 hover:text-primary-500' }, 
                                        { text: 'Purple Alerts',    color: 'alert-purple',       closeBtn: 'text-purple-400 hover:text-purple-500' },
                                        { text: 'Green Alerts',     color: 'alert-green',          closeBtn: 'text-green-400 hover:text-green-500' }, 
                                        { text: 'Red Alerts',   color: 'alert-red',                closeBtn: 'text-red-400 hover:text-red-500' }, 
                                        { text: 'Yellow Alerts',   color: 'alert-yellow',       closeBtn: 'text-yellow-400 hover:text-yellow-500' }, 
                                        { text: 'Sky Alerts',       color: 'alert-sky',                closeBtn: 'text-sky-400 hover:text-sky-500' },
                                        { text: 'Pink Alerts',       color: 'alert-pink',                closeBtn: 'text-pink-400 hover:text-pink-500' },
                                        { text: 'Indigo Alerts',    color: 'alert-indigo',       closeBtn: 'text-indigo-400 hover:text-indigo-500' },
                                        { text: 'Orange Alerts',    color: 'alert-orange',       closeBtn: 'text-orange-400 hover:text-orange-500' }, 
                                        { text: 'Dark Alerts',    color: 'alert-gray',       closeBtn: 'text-gray-400 hover:text-gray-500' }
                                    ] 
                                }" class="flex flex-col gap-2">
                            <template x-for="(alert, index) in alerts" :key="index">
                                <div :class="alert.color + ' alert'">
                                    <span x-text="alert.text"></span>
                                    <a href="javascript:void(0);" @click="alerts.splice(index, 1)" class="btn-close"><i class="ri-close-fill"></i></a>
                                </div>
                            </template>
                        </div>
                    </div>
                </div><!--Basic alerts-->

                <div class="card">
                    <div class="card-header">
                        <h6 class="card-title">Soft Alerts</h6>
                    </div>
                    <div class="card-body">
                        <div x-data="{ 
                                    alerts: [
                                        { text: 'Primary Alerts',   color: 'alert-sub-primary',    closeBtn: 'text-primary-400 hover:text-primary-500' }, 
                                        { text: 'Purple Alerts',    color: 'alert-sub-purple',       closeBtn: 'text-purple-400 hover:text-purple-500' },
                                        { text: 'Green Alerts',     color: 'alert-sub-green',          closeBtn: 'text-green-400 hover:text-green-500' }, 
                                        { text: 'Red Alerts',   color: 'alert-sub-red',                closeBtn: 'text-red-400 hover:text-red-500' }, 
                                        { text: 'Yellow Alerts',   color: 'alert-sub-yellow',       closeBtn: 'text-yellow-400 hover:text-yellow-500' }, 
                                        { text: 'Sky Alerts',       color: 'alert-sub-sky',                closeBtn: 'text-sky-400 hover:text-sky-500' },
                                        { text: 'Pink Alerts',       color: 'alert-sub-pink',                closeBtn: 'text-pink-400 hover:text-pink-500' },
                                        { text: 'Indigo Alerts',       color: 'alert-sub-indigo',                closeBtn: 'text-indigo-400 hover:text-indigo-500' },
                                        { text: 'Orange Alerts',    color: 'alert-sub-orange',       closeBtn: 'text-orange-400 hover:text-orange-500' }, 
                                        { text: 'Dark Alerts',    color: 'alert-sub-gray',       closeBtn: 'text-gray-400 hover:text-gray-500' }, 
                                    ] 
                                }" class="flex flex-col gap-2">
                            <template x-for="(alert, index) in alerts" :key="index">
                                <div :class="alert.color + ' alert'">
                                    <span x-text="alert.text"></span>
                                    <a href="javascript:void(0);" @click="alerts.splice(index, 1)" :class="alert.closeBtn + ' btn-close '"><i class="ri-close-fill"></i></a>
                                </div>
                            </template>
                        </div>
                    </div>
                </div><!--soft alerts-->

                <div class="card">
                    <div class="card-header">
                        <h6 class="card-title">Outline Alerts</h6>
                    </div>
                    <div class="card-body">
                        <div x-data="{ 
                                    alerts: [
                                        { text: 'Primary Alerts',   color: 'alert-outline-primary',    closeBtn: 'text-primary-400 hover:text-primary-500' }, 
                                        { text: 'Purple Alerts',    color: 'alert-outline-purple',       closeBtn: 'text-purple-400 hover:text-purple-500' },
                                        { text: 'Green Alerts',     color: 'alert-outline-green',          closeBtn: 'text-green-400 hover:text-green-500' }, 
                                        { text: 'Red Alerts',   color: 'alert-outline-red',                closeBtn: 'text-red-400 hover:text-red-500' }, 
                                        { text: 'Yellow Alerts',   color: 'alert-outline-yellow',       closeBtn: 'text-yellow-400 hover:text-yellow-500' }, 
                                        { text: 'Sky Alerts',       color: 'alert-outline-sky',                closeBtn: 'text-sky-400 hover:text-sky-500' },
                                        { text: 'Pink Alerts',    color: 'alert-outline-pink',       closeBtn: 'text-pink-400 hover:text-pink-500' },
                                        { text: 'Indigo Alerts',    color: 'alert-outline-indigo',       closeBtn: 'text-indigo-400 hover:text-indigo-500' },
                                        { text: 'Orange Alerts',    color: 'alert-outline-orange',       closeBtn: 'text-orange-400 hover:text-orange-500' }, 
                                        { text: 'Dark Alerts',    color: 'alert-outline-gray',       closeBtn: 'text-gray-400 hover:text-gray-500' },
                                    ] 
                                }" class="flex flex-col gap-2">
                            <template x-for="(alert, index) in alerts" :key="index">
                                <div :class="alert.color + ' alert'">
                                    <span x-text="alert.text"></span>
                                    <a href="javascript:void(0);" @click="alerts.splice(index, 1)" :class="alert.closeBtn + ' btn-close '"><i class="ri-close-fill"></i></a>
                                </div>
                            </template>
                        </div>
                    </div>
                </div><!--outline alerts-->

                <div class="card">
                    <div class="card-header">
                        <h6 class="card-title">Solid Alerts</h6>
                    </div>
                    <div class="card-body">
                        <div x-data="{ 
                                    alerts: [
                                        { text: 'Primary Alerts',   color: 'alert-solid-primary' }, 
                                        { text: 'Purple Alerts',    color: 'alert-solid-purple' },
                                        { text: 'Green Alerts',     color: 'alert-solid-green' }, 
                                        { text: 'Red Alerts',   color: 'alert-solid-red' }, 
                                        { text: 'Yellow Alerts',   color: 'alert-solid-yellow' }, 
                                        { text: 'Sky Alerts',       color: 'alert-solid-sky' },
                                        { text: 'Pink Alerts',    color: 'alert-solid-pink' },
                                        { text: 'Indigo Alerts',    color: 'alert-solid-indigo' },
                                        { text: 'Orange Alerts',    color: 'alert-solid-orange' }, 
                                        { text: 'Dark Alerts',    color: 'alert-solid-gray' }, 
                                    ] 
                                }" class="flex flex-col gap-2">
                            <template x-for="(alert, index) in alerts" :key="index">
                                <div :class="alert.color + ' alert'">
                                    <span x-text="alert.text"></span>
                                    <a href="javascript:void(0);" @click="alerts.splice(index, 1)" class="btn-close"><i class="ri-close-fill"></i></a>
                                </div>
                            </template>
                        </div>
                    </div>
                </div><!--solid alerts-->
            </div>

            <div class="card">
                <div class="card-header">
                    <h6 class="card-title">Icons with Alerts</h6>
                </div>
                <div class="card-body">
                    <div class="grid grid-cols-1 gap-2 xl:grid-cols-2">
                        <div x-data="{ isOpen: true }" x-show="isOpen" class="alert alert-primary alert-icon">
                            <div class="icon border-primary-200 dark:border-primary-500/30">
                                <i data-lucide="alert-triangle" class="size-4"></i>
                            </div>
                            <span>Primary Alerts</span>
                            <a href="javascript:void(0);" x-on:click="isOpen = false" class="btn-close text-primary-400 hover:text-primary-500"><i class="ri-close-fill"></i></a>
                        </div>
                        <div x-data="{ isOpen: true }" x-show="isOpen" class="alert alert-green alert-icon">
                            <div class="border-green-200 icon dark:border-green-500/30">
                                <i data-lucide="check-square" class="size-4"></i>
                            </div>
                            <span>Green Alerts</span>
                            <a href="javascript:void(0);" x-on:click="isOpen = false" class="text-green-400 btn-close hover:text-green-500"><i class="ri-close-fill"></i></a>
                        </div>
                        <div x-data="{ isOpen: true }" x-show="isOpen" class="alert-icon alert alert-solid-purple">
                            <div class="icon border-purple-400/50">
                                <i data-lucide="check-square" class="size-4"></i>
                            </div>
                            <span>Purple Alerts</span>
                            <a href="javascript:void(0);" x-on:click="isOpen = false" class="text-purple-400 btn-close hover:text-purple-200"><i class="ri-close-fill"></i></a>
                        </div>
                        <div x-data="{ isOpen: true }" x-show="isOpen" class="alert-icon alert alert-sub-yellow">
                            <div class="border-yellow-200 icon dark:border-yellow-500/30">
                                <i data-lucide="check-square" class="size-4"></i>
                            </div>
                            <span>Yellow Alerts</span>
                            <a href="javascript:void(0);" x-on:click="isOpen = false" class="text-yellow-400 hover:text-yellow-500 btn-close"><i class="ri-close-fill"></i></a>
                        </div>
                    </div>
                </div>
            </div><!--end -->

            <div class="card">
                <div class="card-header">
                    <h6 class="card-title">Gradient Alerts</h6>
                </div>
                <div class="card-body">
                    <div class="grid grid-cols-1 gap-2 xl:grid-cols-2">
                        <div x-data="{ isOpen: true }" x-show="isOpen" class="text-white border-none bg-gradient-to-r from-primary-500 to-primary-700 alert">
                            <span>Primary Gradient Alerts</span>
                            <a href="javascript:void(0);" x-on:click="isOpen = false" class="text-primary-400 hover:text-primary-200 btn-close"><i class="ri-close-fill"></i></a>
                        </div>

                        <div x-data="{ isOpen: true }" x-show="isOpen" class="text-white border-none bg-gradient-to-r from-purple-500 to-purple-700 alert">
                            <span>Purple Gradient Alerts</span>
                            <a href="javascript:void(0);" x-on:click="isOpen = false" class="text-purple-400 hover:text-purple-200 btn-close"><i class="ri-close-fill"></i></a>
                        </div>

                        <div x-data="{ isOpen: true }" x-show="isOpen" class="text-white border-none bg-gradient-to-r from-primary-500 to-green-500 alert">
                            <span>Gradient Alerts</span>
                            <a href="javascript:void(0);" x-on:click="isOpen = false" class="text-green-300 hover:text-green-100 btn-close"><i class="ri-close-fill"></i></a>
                        </div>

                        <div x-data="{ isOpen: true }" x-show="isOpen" class="text-white border-none bg-gradient-to-r from-primary-950 to-red-950 alert">
                            <span>Gradient Alerts</span>
                            <a href="javascript:void(0);" x-on:click="isOpen = false" class="text-red-400 hover:text-red-200 btn-close"><i class="ri-close-fill"></i></a>
                        </div>
                    </div>
                </div>
            </div><!--end -->

            <div class="card">
                <div class="card-header">
                    <h6 class="card-title">Live Alerts</h6>
                </div>
                <div class="card-body">
                    <div>
                        <div x-data="{ isOpen: false }">
                            <button @click="isOpen = true; setTimeout(() => isOpen = false, 7000)" class="text-white bg-primary-500 border-primary-500 btn hover:bg-primary-600 hover:text-white hover:border-primary-600 focus:bg-primary-600 focus:text-white focus:border-primary-600">
                                Live Alert
                            </button>
                            <div x-show="isOpen" x-init="setTimeout(() => isOpen = false, 7000)" class="fixed -translate-x-1/2 alert alert-primary top-5 z-[1055] left-1/2">
                                <span>You have successfully completed this thing!</span>
                                <a href="javascript:void(0);" @click="isOpen = false" class="text-primary-400 hover:text-primary-500 btn-close"><i class="ri-close-fill"></i></a>
                            </div>
                        </div>
                    </div>
                </div>
            </div><!--end -->

            <div class="card">
                <div class="card-header">
                    <h6 class="card-title">Icons Alerts</h6>
                </div>
                <div class="card-body">
                    <div class="grid grid-cols-1 gap-2 xl:grid-cols-2">
                        <div x-data="{ isOpen: true }" x-show="isOpen" class="border-gray-200 alert alert-border dark:border-dark-800 !border-t-sky-500 dark:!border-t-sky-500">
                            <button x-on:click="isOpen = false" class="btn-close link link-sky"><i class="ri-close-fill"></i></button>
                            <div class="flex items-center justify-center border rounded-full size-11 shrink-0 border-sky-500/20">
                                <i data-lucide="alert-triangle" class="size-5 text-sky-500 fill-sky-100 dark:fill-sky-900"></i>
                            </div>
                            <div class="mt-3 grow sm:mt-0">
                                <h6 class="mb-1">New update available!</h6>
                                <p class="text-gray-500 dark:text-dark-500">Some new updates are available. Refresh this page to get the new updates now. It is recommended to always have the latest version available.</p>
                                <div class="flex items-center justify-end gap-3 mt-3">
                                    <button x-on:click="isOpen = false" class="btn btn-active-gray">
                                        Close
                                    </button>
                                    <button class="btn btn-primary">
                                        Refresh
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div x-data="{ isOpen: true }" x-show="isOpen" class="border-gray-200 alert alert-border dark:border-dark-800 !border-t-green-500 dark:!border-t-green-500">
                            <button x-on:click="isOpen = false" class="btn-close link link-green"><i class="ri-close-fill"></i></button>
                            <div class="flex items-center justify-center border rounded-full border-green-500/20 size-11 shrink-0">
                                <i data-lucide="alert-triangle" class="text-green-500 size-5 fill-green-100 dark:fill-green-900"></i>
                            </div>
                            <div class="mt-3 grow sm:mt-0">
                                <h6 class="mb-1">Ready to publish?</h6>
                                <p class="text-gray-500 dark:text-dark-500">Publishing this project will make it publicy visible. you cam reverse this action anytime here.</p>
                                <div class="flex items-center justify-end gap-3 mt-3">
                                    <button x-on:click="isOpen = false" class="btn-active-gray btn">
                                        Close
                                    </button>
                                    <button class="btn btn-green">
                                        Publish Now
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div x-data="{ isOpen: true }" x-show="isOpen" class="border-gray-200 alert alert-border dark:border-dark-800 !border-t-yellow-500 dark:!border-t-yellow-500">
                            <button x-on:click="isOpen = false" class="btn-close link link-yellow"><i class="ri-close-fill"></i></button>
                            <div class="flex items-center justify-center border rounded-full border-yellow-500/20 size-11 shrink-0">
                                <i data-lucide="alert-triangle" class="text-yellow-500 size-5 fill-yellow-100 dark:fill-yellow-900"></i>
                            </div>
                            <div class="mt-3 grow sm:mt-0">
                                <h6 class="mb-1">Are you sure?</h6>
                                <p class="text-gray-500 dark:text-dark-500">This will bulk update all the names of the tasks selected. You can reverse this action in the activity log.</p>
                                <div class="flex items-center justify-end gap-3 mt-3">
                                    <button x-on:click="isOpen = false" class="btn-active-gray btn">
                                        Close
                                    </button>
                                    <button class="btn btn-yellow">
                                        Yes, Update
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div x-data="{ isOpen: true }" x-show="isOpen" class="border-gray-200 alert alert-border dark:border-dark-800 !border-t-red-500 dark:!border-t-red-500">
                            <button x-on:click="isOpen = false" class="btn-close link link-red"><i class="ri-close-fill"></i></button>
                            <div class="flex items-center justify-center border rounded-full border-red-500/20 size-11 shrink-0">
                                <i data-lucide="alert-triangle" class="text-red-500 size-5 fill-red-100 dark:fill-red-950"></i>
                            </div>
                            <div class="mt-3 grow sm:mt-0">
                                <h6 class="mb-1">Are you Sure?</h6>
                                <p class="text-gray-500 dark:text-dark-500">This will delete all your latest tasks and other important information. If you don't want this you can always archive this.</p>
                                <div class="flex items-center justify-end gap-3 mt-3">
                                    <button x-on:click="isOpen = false" class="btn-active-gray btn">
                                        Close
                                    </button>
                                    <button class="btn btn-red">
                                        Yes, Delete
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div><!--end -->

            <div class="card">
                <div class="card-header">
                    <h6 class="card-title">Basic Alerts</h6>
                </div>
                <div class="card-body">

                    <div class="grid grid-cols-1 gap-2 md:grid-cols-2 xl:grid-cols-4">
                        <div class="relative overflow-hidden text-sm border border-gray-200 rounded-md dark:border-dark-800">
                            <div class="h-9 bg-sky-500"></div>
                            <div class="relative p-5 text-center">
                                <div class="absolute flex items-center justify-center w-10 h-10 text-lg text-white -translate-x-1/2 border-4 border-white rounded-full dark:border-dark-900 bg-sky-500 -top-5 left-1/2"><i class="ri-spam-2-line"></i></div>
                                <h6 class="mt-4 mb-1">Info</h6>
                                <p class="mb-4 text-gray-500 dark:text-dark-500">The pdf is getting ready for you, just sit back and relax.</p>
                                <div class="flex items-center justify-center gap-3">
                                    <button class="btn btn-active-gray">
                                        Close
                                    </button>
                                    <button class="btn btn-sky">
                                        Great
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="relative overflow-hidden text-sm border border-gray-200 rounded-md dark:border-dark-800">
                            <div class="bg-red-500 h-9"></div>
                            <div class="relative p-5 text-center">
                                <div class="absolute flex items-center justify-center w-10 h-10 text-lg text-white -translate-x-1/2 bg-red-500 border-4 border-white rounded-full dark:border-dark-900 -top-5 left-1/2"><i class="ri-spam-2-line"></i></div>
                                <h6 class="mt-4 mb-1">Attention</h6>
                                <p class="mb-4 text-gray-500 dark:text-dark-500">Your are about to delete 40 Posts are you sure ?</p>
                                <div class="flex items-center justify-center gap-3">
                                    <button class="btn btn-active-gray">
                                        Close
                                    </button>
                                    <button class="btn btn-red">
                                        Delete Now
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="relative overflow-hidden text-sm border border-gray-200 rounded-md dark:border-dark-800">
                            <div class="bg-green-500 h-9"></div>
                            <div class="relative p-5 text-center">
                                <div class="absolute flex items-center justify-center w-10 h-10 text-lg text-white -translate-x-1/2 bg-green-500 border-4 border-white rounded-full dark:border-dark-900 -top-5 left-1/2"><i class="ri-alert-line"></i></div>
                                <h6 class="mt-4 mb-1">Success</h6>
                                <p class="mb-4 text-gray-500 dark:text-dark-500">The action that you done was a success! great success.</p>
                                <div class="flex items-center justify-center gap-3">
                                    <button class="btn btn-active-gray">
                                        Close
                                    </button>
                                    <button class="btn btn-green">
                                        Successful Login
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="relative overflow-hidden text-sm border border-gray-200 rounded-md dark:border-dark-800">
                            <div class="bg-yellow-500 h-9"></div>
                            <div class="relative p-5 text-center">
                                <div class="absolute flex items-center justify-center w-10 h-10 text-lg text-white -translate-x-1/2 bg-yellow-500 border-4 border-white rounded-full dark:border-dark-900 -top-5 left-1/2"><i class="ri-alert-line"></i></div>
                                <h6 class="mt-4 mb-1">Warning</h6>
                                <p class="mb-4 text-gray-500 dark:text-dark-500">The action you are doing will change the proccess in some ways.</p>
                                <div class="flex items-center justify-center gap-3">
                                    <button class="btn btn-active-gray">
                                        Close
                                    </button>
                                    <button class="btn btn-yellow">
                                        Warning
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div><!--end -->

        </div>
        {{> partials/footer }}
    </div>


{{> partials/vendor-scripts }}

<!--Main JS-->
<script type="module" src="assets/js/main.js"></script>

</body>
</html>