const leavesData = [
    {
        "leaveType": "Vacation",
        "startDate": "15 June, 2024",
        "endDate": "21 June, 2024",
        "reason": "Family vacation",
        "approvedBy": "",
        "dateRequested": "12 June, 2024",
        "dateApproved": "14 June, 2024",
        "status": "Pending"
    },
    {
        "leaveType": "Sick Leave",
        "startDate": "10 July, 2024",
        "endDate": "14 July, 2024",
        "reason": "Medical",
        "approvedBy": "HR",
        "dateRequested": "8 July, 2024",
        "dateApproved": "9 July, 2024",
        "status": "Approved"
    },
    {
        "leaveType": "Personal",
        "startDate": "1 Aug, 2024",
        "endDate": "3 Aug, 2024",
        "reason": "Personal reasons",
        "approvedBy": "Manager",
        "dateRequested": "28 July, 2024",
        "dateApproved": "30 July, 2024",
        "status": "Approved"
    },
    {
        "leaveType": "Vacation",
        "startDate": "20 Aug, 2024",
        "endDate": "27 Aug, 2024",
        "reason": "Travel",
        "approvedBy": "",
        "dateRequested": "15 Aug, 2024",
        "dateApproved": "18 Aug, 2024",
        "status": "Pending"
    },
    {
        "leaveType": "Maternity Leave",
        "startDate": "5 Sep, 2024",
        "endDate": "5 Sep, 2024",
        "reason": "Mom Birthday",
        "approvedBy": "HR",
        "dateRequested": "1 Sep, 2024",
        "dateApproved": "3 Sep, 2024",
        "status": "Approved"
    },
    {
        "leaveType": "Sick Leave",
        "startDate": "12 Sep, 2024",
        "endDate": "16 Sep, 2024",
        "reason": "Surgery recovery",
        "approvedBy": "",
        "dateRequested": "10 Sep, 2024",
        "dateApproved": "11 Sep, 2024",
        "status": "Pending"
    },
    {
        "leaveType": "Vacation",
        "startDate": "1 Oct, 2024",
        "endDate": "10 Oct, 2024",
        "reason": "Honeymoon",
        "approvedBy": "Admin",
        "dateRequested": "25 Sep, 2024",
        "dateApproved": "28 Sep, 2024",
        "status": "Approved"
    },
    {
        "leaveType": "Personal",
        "startDate": "20 Oct, 2024",
        "endDate": "22 Oct, 2024",
        "reason": "Personal reasons",
        "approvedBy": "Manager",
        "dateRequested": "18 Oct, 2024",
        "dateApproved": "19 Oct, 2024",
        "status": "Rejected"
    },
    {
        "leaveType": "Sick Leave",
        "startDate": "5 Nov, 2024",
        "endDate": "7 Nov, 2024",
        "reason": "Flu",
        "approvedBy": "HR",
        "dateRequested": "3 Nov, 2024",
        "dateApproved": "4 Nov, 2024",
        "status": "Approved"
    },
    {
        "leaveType": "Vacation",
        "startDate": "15 Nov, 2024",
        "endDate": "22 Nov, 2024",
        "reason": "Holiday",
        "approvedBy": "",
        "dateRequested": "10 Nov, 2024",
        "dateApproved": "12 Nov, 2024",
        "status": "Pending"
    },
    {
        "leaveType": "Maternity Leave",
        "startDate": "1 Dec, 2024",
        "endDate": "1 Dec, 2024",
        "reason": "Childbirth",
        "approvedBy": "HR",
        "dateRequested": "28 Nov, 2024",
        "dateApproved": "30 Nov, 2024",
        "status": "Approved"
    },
    {
        "leaveType": "Personal",
        "startDate": "10 Dec, 2024",
        "endDate": "12 Dec, 2024",
        "reason": "Family event",
        "approvedBy": "",
        "dateRequested": "7 Dec, 2024",
        "dateApproved": "9 Dec, 2024",
        "status": "Pending"
    },
    {
        "leaveType": "Sick Leave",
        "startDate": "20 Dec, 2024",
        "endDate": "22 Dec, 2024",
        "reason": "Medical",
        "approvedBy": "HR",
        "dateRequested": "18 Dec, 2024",
        "dateApproved": "19 Dec, 2024",
        "status": "Approved"
    },
    {
        "leaveType": "Vacation",
        "startDate": "1 Jan, 2025",
        "endDate": "7 Jan, 2025",
        "reason": "New Year holiday",
        "approvedBy": "",
        "dateRequested": "27 Dec, 2024",
        "dateApproved": "30 Dec, 2024",
        "status": "Pending"
    },
    {
        "leaveType": "Personal",
        "startDate": "15 Jan, 2025",
        "endDate": "17 Jan, 2025",
        "reason": "Personal reasons",
        "approvedBy": "",
        "dateRequested": "12 Jan, 2025",
        "dateApproved": "14 Jan, 2025",
        "status": "Pending"
    }
]
export default leavesData