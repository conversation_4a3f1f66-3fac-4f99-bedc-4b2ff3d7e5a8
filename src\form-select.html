{{> partials/main }}

<head>

    {{> partials/title-meta title="Select" }}

    <link rel="stylesheet" href="/assets/libs/virtual-select-plugin/virtual-select.min.css">
    <link rel="stylesheet" href="https://sa-si-dev.github.io/virtual-select/assets/external/flags.css">

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}
        {{> partials/page-heading title="Select" sub-title="Forms" }}

        <div class="grid grid-cols-12 gap-x-space">
            <div class="col-span-12 md:col-span-6 card">
                <div class="card-header">
                    <h6 class="card-title">Default Select</h6>
                </div>
                <div class="card-body">
                    <div id="sampleSelect"></div>
                </div>
            </div><!--end col-->
            <div class="col-span-12 md:col-span-6 card">
                <div class="card-header">
                    <h6 class="card-title">With search box</h6>
                </div>
                <div class="card-body">
                    <div id="searchBoxSelect"></div>
                </div>
            </div><!--end col-->
            <div class="col-span-12 md:col-span-6 card">
                <div class="card-header">
                    <h6 class="card-title">Multiple Select</h6>
                </div>
                <div class="card-body">
                    <div id="multipleSelect"></div>
                </div>
            </div><!--end col-->
            <div class="col-span-12 md:col-span-6 card">
                <div class="card-header">
                    <h6 class="card-title">Multiple Select without Search</h6>
                </div>
                <div class="card-body">
                    <div id="multipleWithoutSearchSelect"></div>
                </div>
            </div><!--end col-->
            <div class="col-span-12 md:col-span-6 card">
                <div class="card-header">
                    <h6 class="card-title">Disabled options</h6>
                </div>
                <div class="card-body">
                    <div id="disabledOptionSelect"></div>
                </div>
            </div><!--end col-->
            <div class="col-span-12 md:col-span-6 card">
                <div class="card-header">
                    <h6 class="card-title">Option Group</h6>
                </div>
                <div class="card-body">
                    <div id="optionGroupSelect"></div>
                </div>
            </div><!--end col-->
            <div class="col-span-12 md:col-span-6 card">
                <div class="card-header">
                    <h6 class="card-title">Preselect value</h6>
                </div>
                <div class="card-body">
                    <div id="preselectValue"></div>
                </div>
            </div><!--end col-->
            <div class="col-span-12 md:col-span-6 card">
                <div class="card-header">
                    <h6 class="card-title">Preselect multiple values</h6>
                </div>
                <div class="card-body">
                    <div id="preselectMultipleValue"></div>
                </div>
            </div><!--end col-->
            <div class="col-span-12 md:col-span-6 card">
                <div class="card-header">
                    <h6 class="card-title">Hide Clear Button</h6>
                </div>
                <div class="card-body">
                    <div id="hideClearButton"></div>
                </div>
            </div><!--end col-->
            <div class="col-span-12 md:col-span-6 card">
                <div class="card-header">
                    <h6 class="card-title">Custom width for dropbox</h6>
                </div>
                <div class="card-body">
                    <div id="customWidthDropbox"></div>
                </div>
            </div><!--end col-->
            <div class="col-span-12 md:col-span-6 card">
                <div class="card-header">
                    <h6 class="card-title">Allow to add new option</h6>
                </div>
                <div class="card-body">
                    <div id="allowNewOption"></div>
                </div>
            </div><!--end col-->
            <div class="col-span-12 md:col-span-6 card">
                <div class="card-header">
                    <h6 class="card-title">Mark matched term in label</h6>
                </div>
                <div class="card-body">
                    <div id="markMatchedLabel"></div>
                </div>
            </div><!--end col-->
            <div class="col-span-12 md:col-span-6 card">
                <div class="card-header">
                    <h6 class="card-title">Showing selected options first</h6>
                </div>
                <div class="card-body">
                    <div id="showingSelectedOption"></div>
                </div>
            </div><!--end col-->
            <div class="col-span-12 md:col-span-6 card">
                <div class="card-header">
                    <h6 class="card-title">Using alias for searching</h6>
                </div>
                <div class="card-body">
                    <div id="aliasForSearching"></div>
                </div>
            </div><!--end col-->
            <div class="col-span-12 md:col-span-6 card">
                <div class="card-header">
                    <h6 class="card-title">Maximum Values</h6>
                </div>
                <div class="card-body">
                    <div id="maximumValues"></div>
                </div>
            </div><!--end col-->
            <div class="col-span-12 md:col-span-6 card">
                <div class="card-header">
                    <h6 class="card-title">Label with description</h6>
                </div>
                <div class="card-body">
                    <div id="labelDescription"></div>
                </div>
            </div><!--end col-->
            <div class="col-span-12 md:col-span-6 card">
                <div class="card-header">
                    <h6 class="card-title">Show options only on search</h6>
                </div>
                <div class="card-body">
                    <div id="showOptionOnlySearch"></div>
                </div>
            </div><!--end col-->
            <div class="col-span-12 md:col-span-6 card">
                <div class="card-header">
                    <h6 class="card-title">Initialize from native select element (not recommended)</h6>
                </div>
                <div class="card-body">
                    <select name="native-select" placeholder="Native Select" data-search="false" data-silent-initial-value-set="true">
                        <option value="1" disabled>Option 1</option>
                        <option value="2">Option 2</option>
                        <option value="3" selected>Option 3</option>
                        <option value="4" selected>Option 4</option>
                        <option value="5">Option 5</option>
                        <option value="6">Option 6</option>
                    </select>
                </div>
            </div><!--end col-->

            <div class="col-span-12 md:col-span-6 card">
                <div class="card-header">
                    <h6 class="card-title">Image/Icon</h6>
                </div>
                <div class="card-body">
                    <div id="sample-image"></div>
                </div>
            </div><!--end col-->

            <div class="col-span-12 md:col-span-6 card">
                <div class="card-header">
                    <h6 class="card-title">Popup with Multi Select</h6>
                </div>
                <div class="card-body">
                    <div id="popup-multi-select"></div>
                </div>
            </div><!--end col-->

            <div class="col-span-12 md:col-span-6 card">
                <div class="card-header">
                    <h6 class="card-title">Popup with Single Select</h6>
                </div>
                <div class="card-body">
                    <div id="popup-single-select"></div>
                </div>
            </div><!--end col-->

            <div class="col-span-12 md:col-span-6 card">
                <div class="card-header">
                    <h6 class="card-title">Show values as tags</h6>
                </div>
                <div class="card-body">
                    <div id="value-tag"></div>
                </div>
            </div><!--end col-->
        </div><!--end grid-->

    </div>
    {{> partials/footer }}
</div>

{{> partials/vendor-scripts }}

<script type="module" src="assets/js/form/select.init.js"></script>

<script type="module" src="assets/js/main.js"></script>

</body>
</html>