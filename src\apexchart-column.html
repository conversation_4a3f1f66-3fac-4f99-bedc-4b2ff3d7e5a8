{{> partials/main }}

<head>

    {{> partials/title-meta title="Column Charts" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}

{{> partials/page-heading title="Column Charts" sub-title="Apexcharts" }}

<div class="grid grid-cols-12 gap-x-space">
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Basic</h6>
        </div>
        <div class="card-body">
            <div x-data="basicColumnApp" dir="ltr">
                <div class="!min-h-full" data-chart-colors="[bg-primary-500, bg-green-500, bg-yellow-500]" x-ref="basicColumnChart"></div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Column with Data Labels</h6>
        </div>
        <div class="card-body">
            <div x-data="labelColumnApp" dir="ltr">
                <div class="!min-h-full" data-chart-colors="[bg-primary-500]" x-ref="labelColumnChart"></div>
            </div>
        </div>
    </div><!--end col-->
     <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Stacked Columns</h6>
        </div>
        <div class="card-body">
            <div x-data="stackedColumnApp" dir="ltr">
                <div class="!min-h-full" data-chart-colors="[bg-primary-500, bg-green-500, bg-red-500, bg-yellow-500]" x-ref="stackedColumnChart"></div>
            </div>
        </div>
    </div><!--end col-->
     <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Stacked Columns 100</h6>
        </div>
        <div class="card-body">
            <div x-data="stackedColumn100App" dir="ltr">
                <div class="!min-h-full" data-chart-colors="[bg-primary-500, bg-green-500, bg-yellow-500]" x-ref="stackedColumn100Chart"></div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Grouped Stacked Columns</h6>
        </div>
        <div class="card-body">
            <div x-data="groupStackedColumnApp" dir="ltr">
                <div class="!min-h-full" data-chart-colors="[bg-primary-500, bg-green-500, bg-primary-200, bg-green-300]" data-chart-dark-colors="[bg-primary-500, bg-green-500, bg-primary-800, bg-green-800]" x-ref="groupStackedColumnChart"></div>
            </div>
        </div>
    </div><!--end col-->
     <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Dumbbell Chart</h6>
        </div>
        <div class="card-body">
            <div x-data="dumbbellColumnApp" dir="ltr">
                <div class="!min-h-full" data-chart-colors="[bg-primary-500, bg-pink-500]" x-ref="dumbbellColumnChart"></div>
            </div>
        </div>
    </div><!--end col-->
     <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Column with Markers</h6>
        </div>
        <div class="card-body">
            <div x-data="markersColumnApp" dir="ltr">
                <div class="!min-h-full" data-chart-colors="[bg-primary-500]" x-ref="markersColumnChart"></div>
            </div>
        </div>
    </div><!--end col-->
     <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Column with Group Label</h6>
        </div>
        <div class="card-body">
            <div x-data="groupLabelColumnApp" dir="ltr">
                <div class="!min-h-full" data-chart-colors="[bg-primary-500]" x-ref="groupLabelColumnChart"></div>
            </div>
        </div>
    </div><!--end col-->
     <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Column with Rotated Labels</h6>
        </div>
        <div class="card-body">
            <div x-data="rotatedLabelColumnApp" dir="ltr">
                <div class="!min-h-full" data-chart-colors="[bg-primary-500]" x-ref="rotatedLabelColumnChart"></div>
            </div>
        </div>
    </div><!--end col-->
     <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Column with Negative Values</h6>
        </div>
        <div class="card-body">
            <div x-data="negativeLabelColumnApp" dir="ltr">
                <div class="!min-h-full" data-chart-colors="[bg-primary-500, bg-yellow-500, bg-red-500]" x-ref="negativeLabelColumnChart"></div>
            </div>
        </div>
    </div><!--end col-->
     <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Distributed Columns</h6>
        </div>
        <div class="card-body">
            <div x-data="distributedColumnApp" dir="ltr">
                <div class="!min-h-full" data-chart-colors="[bg-primary-500, bg-pink-500, bg-sky-500, bg-green-300, bg-yellow-500, bg-purple-500, bg-red-500, bg-sky-500]" x-ref="distributedColumnChart"></div>
            </div>
        </div>
    </div><!--end col-->
</div><!--end grid-->

</div>
{{> partials/footer }}
</div>


{{> partials/vendor-scripts }}

<script src="assets/libs/dayjs/dayjs.min.js"></script>
<script src="assets/libs/dayjs/plugin/quarterOfYear.js"></script>

<script type="module" src="assets/js/charts/column-chart.init.js"></script>

<script type="module" src="assets/js/main.js"></script>

</body>
</html>