import product1 from "/assets/images/products/img-01.png";
import product2 from "/assets/images/products/img-02.png";
import product3 from "/assets/images/products/img-03.png";
import product4 from "/assets/images/products/img-04.png";
import product5 from "/assets/images/products/img-05.png";
import product6 from "/assets/images/products/img-06.png";
import product7 from "/assets/images/products/img-07.png";
import product8 from "/assets/images/products/img-08.png";
import product9 from "/assets/images/products/img-09.png";
import product11 from "/assets/images/products/img-11.png";

const orderData = [
    {
        "ordersDate": "15 Mar, 2022",
        "deliveredDate": "21 Mar, 2022",
        "customersName": "<PERSON>",
        "productName": "Blouse Ruffle Tube top",
        "image": product1,
        "status": "Delivered"
    },
    {
        "ordersDate": "02 Apr, 2022",
        "deliveredDate": "09 Apr, 2022",
        "customersName": "<PERSON> Ng<PERSON>",
        "productName": "Gold-colored locket watch",
        "image": product2,
        "status": "Pending"
    },
    {
        "ordersDate": "18 Jun, 2022",
        "deliveredDate": "26 Jun, 2022",
        "customersName": "Isabella Thomas",
        "productName": "Spar Men Black Running Shoes",
        "image": product3,
        "status": "New"
    },
    {
        "ordersDate": "30 Jul, 2022",
        "deliveredDate": "07 Aug, 2022",
        "customersName": "Mason Wilson",
        "productName": "Crop top Sweater Clothing",
        "image": product4,
        "status": "Delivered"
    },
    {
        "ordersDate": "12 Sep, 2022",
        "deliveredDate": "19 Sep, 2022",
        "customersName": "Olivia Brown",
        "productName": "Sleeve Clothing Leggings",
        "image": product5,
        "status": "Shipping"
    },
    {
        "ordersDate": "24 Oct, 2022",
        "deliveredDate": "31 Oct, 2022",
        "customersName": "William Garcia",
        "productName": "Bra Lace Crop top",
        "image": product6,
        "status": "Delivered"
    },
    {
        "ordersDate": "05 Nov, 2022",
        "deliveredDate": "12 Nov, 2022",
        "customersName": "Ava Martinez",
        "productName": "Yellow women shoes",
        "image": product7,
        "status": "Shipping"
    },
    {
        "ordersDate": "14 Dec, 2022",
        "deliveredDate": "22 Dec, 2022",
        "customersName": "Liam Clark",
        "productName": "Tote bag Leather Handbag Dolce",
        "image": product8,
        "status": "New"
    },
    {
        "ordersDate": "01 Jan, 2023",
        "deliveredDate": "09 Jan, 2023",
        "customersName": "Charlotte Lewis",
        "productName": "Hoodie Jacket Letterman Sleeve Coat",
        "image": product9,
        "status": "Pending"
    },
    {
        "ordersDate": "17 Dec, 2023",
        "deliveredDate": "24 Dec, 2023",
        "customersName": "Noah King",
        "productName": "Sneakers Shoe Nike Basketball",
        "image": product11,
        "status": "Pending"
    }
]
export default orderData