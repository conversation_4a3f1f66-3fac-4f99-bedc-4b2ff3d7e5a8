{{> partials/main }}

<head>

    {{> partials/title-meta title="Contact US" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}


<div class="grid grid-cols-12 gap-0 from-sky-500/10 ltr:bg-gradient-to-l rtl:bg-gradient-to-r via-green-500/5 to-pink-500/5">
    <div class="col-span-12 xl:col-span-7">
        <div class="flex items-center py-10 border-gray-200 xl:py-0 xl:h-screen ltr:border-r rtl:border-l dark:border-dark-800">
            <div class="px-10 md:px-20 grow">
                <h1 class="mb-8 leading-normal xl:w-2/3">Let's now concentrate on the fundamental aspects of your projects.</h1>
                <form action="#!" class="mb-6 xl:w-2/3">
                    <div class="grid grid-cols-12 gap-5">
                        <div class="col-span-12">
                            <label for="projectInput" class="form-label">Project Name</label>
                            <input type="text" id="projectInput" class="bg-transparent form-input dark:bg-transparent">
                        </div>
                        <div class="col-span-12">
                            <label for="textareaInput2" class="form-label">Description</label>
                            <textarea name="textareaInput2" id="textareaInput2" rows="3" class="h-auto bg-transparent form-input dark:bg-transparent"></textarea>
                        </div>
                        <div class="col-span-12">
                            <label for="filesInput" class="form-label">Please send us any additional files you want to send us below</label>
                            <input type="file" id="filesInput" class="form-file" multiple>
                        </div>
                        <div class="col-span-12">
                            <label for="linksInput" class="form-label">You can also upload links to your content, such as Google Docs or Dropbox</label>
                            <input type="url" id="linksInput" class="bg-transparent form-input dark:bg-transparent">
                        </div>
                    </div>
                </form>
                <div class="flex flex-wrap gap-2">
                    <a href="pages-contact-us-three.html" class="btn btn-outline-gray">
                        <i data-lucide="move-left" class="mr-1 ltr:inline-block rtl:hidden size-4"></i> 
                        <i data-lucide="move-right" class="ml-1 ltr:hidden rtl:inline-block size-4"></i> 
                        Back
                    </a>
                    <a href="pages-contact-us-five.html" class="btn btn-primary">
                        Go Forward 
                        <i data-lucide="move-right" class="ml-1 ltr:inline-block rtl:hidden size-4"></i>
                        <i data-lucide="move-left" class="mr-1 ltr:hidden rtl:inline-block size-4"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>
    <div class="col-span-12 xl:col-span-5">
        <div class="flex flex-col justify-center w-full px-10 py-10 md:px-20 xl:py-0 xl:h-screen">
            <div>
                <h5 class="mb-3">Send Additional Files</h5>
                <p class="mb-5 text-gray-500 dark:text-dark-500">Please provide any supplementary materials (files or links) that will assist us in formulating the estimate.</p>
                <ul class="space-y-2 mb-7">
                    <li>
                        <i data-lucide="corner-up-right" class="mr-1 text-green-500 ltr:inline-block rtl:hidden size-4"></i>
                        <i data-lucide="corner-up-left" class="ml-1 text-green-500 ltr:hidden rtl:inline-block size-4"></i>
                        Description of your project
                    </li>
                    <li>
                        <i data-lucide="corner-up-right" class="mr-1 text-green-500 ltr:inline-block rtl:hidden size-4"></i>
                        <i data-lucide="corner-up-left" class="ml-1 text-green-500 ltr:hidden rtl:inline-block size-4"></i>
                        Wireframes or functional sketches
                    </li>
                    <li>
                        <i data-lucide="corner-up-right" class="mr-1 text-green-500 ltr:inline-block rtl:hidden size-4"></i>
                        <i data-lucide="corner-up-left" class="ml-1 text-green-500 ltr:hidden rtl:inline-block size-4"></i>
                        Visual identification materials
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div><!--end grid-->

{{> partials/vendor-scripts }}

</body>
</html>