{{> partials/main }}

<head>

    {{> partials/title-meta title="Default Chat" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

<div class="relative min-h-screen">
    <div class="page-wrapper pt-[calc(theme('spacing.topbar')_*_1.2)] group-data-[layout=horizontal]:pt-[calc(theme('spacing.topbar')_*_1.9)]" x-data="callModal">
        {{> partials/page-heading title="Default Chat" sub-title="Chats" }}

        <div class="grid grid-cols-12 gap-x-space" x-data="contactListComponent">
            <div class="col-span-12 2xl:col-span-1 card">
                <div data-simplebar class="max-h-[calc(100vh_-_13rem)]">
                    <div class="flex gap-4 2xl:flex-col *:shrink-0 card-body">
                        <a href="#!" title="link" class="relative flex items-center justify-center font-semibold transition duration-200 ease-linear bg-gray-100 rounded-full dark:bg-dark-850 size-14 hover:ring-2 [&.active]:ring-2 hover:ring-offset-2 dark:hover:ring-offset-dark-900 [&.active]:ring-offset-2 dark:[&.active]:ring-offset-dark-900 hover:ring-primary-500 [&.active]:ring-primary-500 active">
                            <img src="assets/images/brands/img-02.png" alt="" class="rounded-full size-9">
                            <span class="absolute bottom-0 bg-green-500 border-2 border-white rounded-full dark:border-dark-900 right-1 size-3"></span>
                        </a>
                        <a href="#!" title="link" class="relative flex items-center justify-center font-semibold transition duration-200 ease-linear bg-gray-100 rounded-full dark:bg-dark-850 size-14 hover:ring-2 [&.active]:ring-2 hover:ring-offset-2 dark:hover:ring-offset-dark-900 [&.active]:ring-offset-2 dark:[&.active]:ring-offset-dark-900 hover:ring-primary-500 [&.active]:ring-primary-500">
                            <span>PE</span>
                            <span class="absolute bottom-0 bg-green-500 border-2 border-white rounded-full dark:border-dark-900 right-1 size-3"></span>
                        </a>
                        <a href="#!" title="link" class="relative flex items-center justify-center font-semibold transition duration-200 ease-linear bg-gray-100 rounded-full dark:bg-dark-850 size-14 hover:ring-2 [&.active]:ring-2 hover:ring-offset-2 dark:hover:ring-offset-dark-900 [&.active]:ring-offset-2 dark:[&.active]:ring-offset-dark-900 hover:ring-primary-500 [&.active]:ring-primary-500">
                            <img src="assets/images/brands/img-06.png" alt="" class="rounded-full size-9">
                            <span class="absolute bottom-0 bg-green-500 border-2 border-white rounded-full dark:border-dark-900 right-1 size-3"></span>
                        </a>
                        <a href="#!" title="link" class="relative flex items-center justify-center font-semibold transition duration-200 ease-linear bg-gray-100 rounded-full dark:bg-dark-850 size-14 hover:ring-2 [&.active]:ring-2 hover:ring-offset-2 dark:hover:ring-offset-dark-900 [&.active]:ring-offset-2 dark:[&.active]:ring-offset-dark-900 hover:ring-primary-500 [&.active]:ring-primary-500">
                            <img src="assets/images/brands/img-05.png" alt="" class="rounded-full size-9">
                            <span class="absolute bottom-0 bg-green-500 border-2 border-white rounded-full dark:border-dark-900 right-1 size-3"></span>
                        </a>
                        <a href="#!" title="link" class="relative flex items-center justify-center font-semibold transition duration-200 ease-linear bg-gray-100 rounded-full dark:bg-dark-850 size-14 hover:ring-2 [&.active]:ring-2 hover:ring-offset-2 dark:hover:ring-offset-dark-900 [&.active]:ring-offset-2 dark:[&.active]:ring-offset-dark-900 hover:ring-primary-500 [&.active]:ring-primary-500">
                            <img src="assets/images/brands/img-01.png" alt="" class="rounded-full size-9">
                            <span class="absolute bottom-0 bg-green-500 border-2 border-white rounded-full dark:border-dark-900 right-1 size-3"></span>
                        </a>
                        <a href="#!" title="link" class="relative flex items-center justify-center font-semibold transition duration-200 ease-linear bg-gray-100 rounded-full dark:bg-dark-850 size-14 hover:ring-2 [&.active]:ring-2 hover:ring-offset-2 dark:hover:ring-offset-dark-900 [&.active]:ring-offset-2 dark:[&.active]:ring-offset-dark-900 hover:ring-primary-500 [&.active]:ring-primary-500">
                            <img src="assets/images/brands/img-07.png" alt="" class="rounded-full size-9">
                            <span class="absolute bottom-0 bg-green-500 border-2 border-white rounded-full dark:border-dark-900 right-1 size-3"></span>
                        </a>
                        <a href="#!" title="link" class="relative flex items-center justify-center font-semibold transition duration-200 ease-linear bg-gray-100 rounded-full dark:bg-dark-850 size-14 hover:ring-2 [&.active]:ring-2 hover:ring-offset-2 dark:hover:ring-offset-dark-900 [&.active]:ring-offset-2 dark:[&.active]:ring-offset-dark-900 hover:ring-primary-500 [&.active]:ring-primary-500">
                            <img src="assets/images/brands/img-08.png" alt="" class="rounded-full size-9">
                            <span class="absolute bottom-0 bg-green-500 border-2 border-white rounded-full dark:border-dark-900 right-1 size-3"></span>
                        </a>
                        <a href="#!" title="link" class="relative flex items-center justify-center font-semibold transition duration-200 ease-linear bg-gray-100 rounded-full dark:bg-dark-850 size-14 hover:ring-2 [&.active]:ring-2 hover:ring-offset-2 dark:hover:ring-offset-dark-900 [&.active]:ring-offset-2 dark:[&.active]:ring-offset-dark-900 hover:ring-primary-500 [&.active]:ring-primary-500">
                            <span>AI</span>
                            <span class="absolute bottom-0 bg-green-500 border-2 border-white rounded-full dark:border-dark-900 right-1 size-3"></span>
                        </a>
                        <a href="#!" title="link" class="relative flex items-center justify-center font-semibold transition duration-200 ease-linear bg-gray-100 rounded-full dark:bg-dark-850 size-14 hover:ring-2 [&.active]:ring-2 hover:ring-offset-2 dark:hover:ring-offset-dark-900 [&.active]:ring-offset-2 dark:[&.active]:ring-offset-dark-900 hover:ring-primary-500 [&.active]:ring-primary-500">
                            <img src="assets/images/brands/img-09.png" alt="" class="rounded-full size-9">
                            <span class="absolute bottom-0 bg-green-500 border-2 border-white rounded-full dark:border-dark-900 right-1 size-3"></span>
                        </a>
                        <a href="#!" title="link" class="relative flex items-center justify-center font-semibold transition duration-200 ease-linear bg-gray-100 rounded-full dark:bg-dark-850 size-14 hover:ring-2 [&.active]:ring-2 hover:ring-offset-2 dark:hover:ring-offset-dark-900 [&.active]:ring-offset-2 dark:[&.active]:ring-offset-dark-900 hover:ring-primary-500 [&.active]:ring-primary-500">
                            <img src="assets/images/brands/img-10.png" alt="" class="rounded-full size-9">
                            <span class="absolute bottom-0 bg-green-500 border-2 border-white rounded-full dark:border-dark-900 right-1 size-3"></span>
                        </a>
                        <a href="#!" title="link" class="relative flex items-center justify-center font-semibold transition duration-200 ease-linear bg-gray-100 rounded-full dark:bg-dark-850 size-14 hover:ring-2 [&.active]:ring-2 hover:ring-offset-2 dark:hover:ring-offset-dark-900 [&.active]:ring-offset-2 dark:[&.active]:ring-offset-dark-900 hover:ring-primary-500 [&.active]:ring-primary-500">
                            <img src="assets/images/brands/img-12.png" alt="" class="rounded-full size-9">
                            <span class="absolute bottom-0 bg-green-500 border-2 border-white rounded-full dark:border-dark-900 right-1 size-3"></span>
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-span-12 xl:col-span-4 2xl:col-span-3 card" id="chat-list">
                <div class="card-body">
                    <div class="relative group/form">
                        <input type="text" class="ltr:pl-9 rtl:pr-9 form-input ltr:group-[&.right]/form:pr-9 rtl:group-[&.right]/form:pl-9 ltr:group-[&.right]/form:pl-4 rtl:group-[&.right]/form:pr-4" placeholder="Search for ..." x-model="searchChat">
                        <button title="search btn" class="absolute inset-y-0 flex items-center text-gray-500 ltr:left-3 rtl:right-3 ltr:group-[&.right]/form:right-3 rtl:group-[&.right]/form:left-3 ltr:group-[&.right]/form:left-auto rtl:group-[&.right]/form:right-auto focus:outline-hidden">
                            <i data-lucide="search" class="size-4"></i>
                        </button>
                    </div>
                    <div class="py-4">
                        <button type="button" @click="showAddChatModal = true" data-modal-target="addNewChatModals" class="w-full btn btn-primary">Start New Chat</button>
                    </div>
                    <div class="max-h-[calc(100vh_-_22.5rem)] -mx-space" data-simplebar>
                        <ul class="flex flex-col gap-3">
                            <template x-if="displayChatlist.length > 0">
                                <template x-for="(item, index) in displayChatlist" :key="item.name">
                                    <li>
                                        <a href="#!" class="flex items-center gap-2 px-space py-2.5 hover:bg-gray-50 dark:hover:bg-dark-850 [&.active]:bg-primary-500/10 transition ease-linear duration-300 group/item unread" @click="setActiveChat(index)" :class="{'active': isActiveChat(index)} ">
                                            <div class="relative flex items-center justify-center font-semibold transition duration-200 ease-linear bg-gray-100 rounded-full dark:bg-dark-850 size-10 shrink-0">
                                                <img :src="item.avatar" alt="" class="rounded-full">
                                                <span x-show="!item.avatar" x-text="item.avatarText"></span>
                                                <span class="absolute bottom-0 bg-green-500 border-2 border-white dark:border-dark-900 rounded-full ltr:right-0.5 rtl:left-0.5 size-2.5"></span>
                                            </div>
                                            <div class="overflow-hidden grow">
                                                <h6 class="mb-0.5" x-text="item.name"></h6>
                                                <p class="text-sm group-[&.unread]/item:font-medium truncate group-[&.unread]/item:text-gray-950 text-gray-500 dark:text-dark-500 dark:group-[&.unread]/item:text-gray-50 unread" x-text="item.lastMessage">Hello, How are you?</p>
                                            </div>
                                            <div class="ltr:text-right rtl:text-left shrink-0">
                                                <p class="mb-1 text-xs text-gray-500 dark:text-dark-500" x-text="item.time"></p>
                                                <span class="badge-sub-red badge-square size-5" x-show="item.unread > 0" x-text="item.unread">2</span>
                                            </div>
                                        </a>
                                    </li>
                                </template>
                            </template>
                            <template x-if="displayChatlist.length == 0">
                                    <li>
                                    <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" class="mx-auto size-12" viewBox="0 0 48 48">
                                        <linearGradient id="SVGID_1__h35ynqzIJzH4_gr1" x1="34.598" x2="15.982" y1="15.982" y2="34.598" gradientUnits="userSpaceOnUse">
                                            <stop offset="0" stop-color="#60e8fe"></stop>
                                            <stop offset=".033" stop-color="#6ae9fe"></stop>
                                            <stop offset=".197" stop-color="#97f0fe"></stop>
                                            <stop offset=".362" stop-color="#bdf5ff"></stop>
                                            <stop offset=".525" stop-color="#dafaff"></stop>
                                            <stop offset=".687" stop-color="#eefdff"></stop>
                                            <stop offset=".846" stop-color="#fbfeff"></stop>
                                            <stop offset="1" stop-color="#fff"></stop>
                                        </linearGradient>
                                        <path fill="url(#SVGID_1__h35ynqzIJzH4_gr1)" d="M40.036,33.826L31.68,25.6c0.847-1.739,1.335-3.684,1.335-5.748c0-7.27-5.894-13.164-13.164-13.164	S6.688,12.582,6.688,19.852c0,7.27,5.894,13.164,13.164,13.164c2.056,0,3.995-0.485,5.728-1.326l3.914,4.015l4.331,4.331	c1.715,1.715,4.496,1.715,6.211,0C41.751,38.321,41.751,35.541,40.036,33.826z"></path>
                                        <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M31.95,25.739l8.086,8.086c1.715,1.715,1.715,4.496,0,6.211l0,0c-1.715,1.715-4.496,1.715-6.211,0	l-4.331-4.331"></path>
                                        <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M7.525,24.511c-1.771-4.694-0.767-10.196,3.011-13.975c3.847-3.847,9.48-4.817,14.228-2.912"></path>
                                        <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M30.856,12.603c3.376,5.114,2.814,12.063-1.688,16.565c-4.858,4.858-12.565,5.129-17.741,0.814"></path>
                                    </svg>
                                    <p class="mt-2 text-center text-gray-500 dark:text-dark-500">No matching records found</p>
                                </li>
                            </template>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="col-span-12 overflow-hidden xl:col-span-8 card" id="chat-wrapper">
                <div class="max-h-[calc(100vh_-_19rem)] min-h-[calc(100vh_-_19rem)] relative chat-body" tabindex="1" data-simplebar x-ref="chatContainer">
                    <div class="sticky inset-x-0 top-0 z-50 flex items-center gap-3 border-b border-gray-200 card-body bg-white/30 dark:bg-dark-900/90 dark:border-dark-800 backdrop-blur-lg">
                        <div class="xl:hidden shrink-0">
                            <button class="btn btn-sub-gray btn-icon" title="back btn" @click="backToChatList()">
                                <i data-lucide="chevrons-left" class="size-5"></i>
                            </button>
                        </div>
                        <div class="relative flex items-center justify-center font-semibold transition duration-200 ease-linear bg-gray-100 rounded-full dark:bg-dark-850 size-10 shrink-0">
                            <img :src="chatAvtar" alt="" class="rounded-full">
                            <span x-show="!chatAvtar" x-text="avatarText"></span>
                        </div>
                        <div class="grow">
                            <h6 class="mb-0.5"><a href="#!" class="" x-text="chatName">David Johnson</a></h6>
                            <p class="text-gray-500 dark:text-dark-500">Last seen 2 hr</p>
                        </div>
                        <button title="call btn" class="btn btn-active-gray btn-icon shrink-0" @click="callModalOpen = true" data-modal-target="callModal">
                            <i data-lucide="phone" class="size-5"></i>
                        </button>
                        <button title="video call btn" class="btn btn-active-gray btn-icon shrink-0" data-modal-target="videoCallModal">
                            <i data-lucide="video" class="size-5"></i>
                        </button>
                    </div>
                    <div class="pb-0 card-body">
                        <div class="flex flex-col justify-end min-h-[calc(100vh_-_24rem)] gap-5 " id="chat-messages">

                            <template x-for="(msg , index ) of messages" :key="index">
                                <div class="messages flex items-end max-w-xl gap-3 ltr:[&.right]:ml-auto rtl:[&.right]:mr-auto group/chat" :class="msg.type == 'sent'? 'right' : ''">
                                    <div class="relative flex items-center justify-center font-semibold transition duration-200 ease-linear bg-gray-100 dark:bg-dark-850 rounded-full size-8 shrink-0 group-[&.right]/chat:order-2">
                                        <img :src="msg.avatar" alt="" class="rounded-full">
                                        <span x-show="!msg.avatar" x-text="msg.avatarText"></span>
                                        <span class="absolute bottom-0 bg-green-500 border-2 border-white dark:border-dark-900 rounded-full ltr:right-0 rtl:left-0 size-2.5"></span>
                                    </div>
                                    <div class="grow *:mb-3">
                                        <div class="flex items-end gap-2 last:mb-0">
                                            <div class="grow">
                                                <p class="ltr:group-[&.right]/chat:text-right rtl:group-[&.right]/chat:text-left text-gray-500 dark:text-dark-500 mb-1 text-xs" x-text="msg.time">Today, 09:59 AM</p>
                                                <div class="px-4 py-2.5 last:mb-0 bg-gray-100 dark:bg-dark-850 rounded-xl ltr:rounded-bl-none rtl:rounded-br-none group-[&.right]/chat:order-1 ltr:group-[&.right]/chat:rounded-bl-lg rtl:group-[&.right]/chat:rounded-br-lg ltr:group-[&.right]/chat:rounded-br-none rtl:group-[&.right]/chat:rounded-bl-none" x-html="msg.text">
                                                    We need a new website that allows users to create accounts, browse products, and make purchases. Can you provide a rough timeline and cost estimate?
                                                </div>
                                            </div>
                                            <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown shrink-0">
                                                <button x-ref="button" x-on:click="toggle()" title="dropdown btn" :aria-expanded="open.toString()" type="button" class="flex items-center text-gray-500 dark:text-dark-500">
                                                    <i class="ri-more-2-fill"></i>
                                                </button>
                                                <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden" dropdown-position="right">
                                                    <ul>
                                                        <li>
                                                            <a href="#!" x-on:click="close()" class="dropdown-item">
                                                                <i class="align-middle ltr:mr-2 rtl:ml-2 ri-reply-line"></i> <span>Reply</span>
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a href="#!" @click="DeleteMessage(index); close()" class="dropdown-item">
                                                                <i class="align-middle ltr:mr-2 rtl:ml-2 ri-delete-bin-line"></i> <span>Delete</span>
                                                            </a>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="last:mb-0" x-show="msg.images ">
                                            <div class="grid grid-cols-12 gap-4">
                                                <template x-for="img of msg.images">
                                                    <div class="col-span-3">
                                                        <a href="#!" title="Gallery Images"><img :src="img" alt="" class="rounded-md"></a>
                                                    </div>
                                                </template>
                                                <a href="#!" class="flex items-center justify-center col-span-3 p-3 bg-gray-100 rounded-md dark:bg-dark-850">
                                                    <h6 x-text="msg.extraImagesCount"></h6>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </template>

                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="flex items-center gap-2 p-2 border border-gray-200 rounded-md dark:border-dark-800">
                        <button class="btn btn-active-gray btn-icon shrink-0">
                            <i data-lucide="audio-lines" class="size-5"></i>
                        </button>
                        <input type="text" class="border-0 form-input grow" placeholder="Type something ..." x-model="message" @keydown.enter.prevent="sendMessage(message)">
                        <button type="submit" class="btn btn-active-primary btn-icon shrink-0" @click="sendMessage(message)">
                            <i data-lucide="send" class="size-5"></i>
                        </button>
                        <div class="hidden shrink-0 md:flex">
                            <label for="sendImages" class="btn btn-active-gray btn-icon">
                                <i data-lucide="image" class="size-5"></i>
                            </label>
                            <input type="file" id="sendImages" class="hidden">
                        </div>
                        <button class="text-lg btn btn-active-gray btn-icon shrink-0">
                            😊
                        </button>
                        <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown">
                            <button x-ref="button" x-on:click="toggle()" :aria-expanded="open.toString()" type="button" class="text-lg btn btn-active-gray btn-icon shrink-0">
                                <i data-lucide="ellipsis" class="size-5"></i>
                            </button>
                            <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden">
                                <ul>
                                    <li>
                                        <a href="#!" class="dropdown-item">
                                            <i class="align-middle ltr:mr-2 rtl:ml-2 ri-chat-4-line"></i> <span>Clear Chat</span>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="#!" class="dropdown-item">
                                            <i class="align-middle ltr:mr-2 rtl:ml-2 ri-delete-bin-line"></i> <span>Delete</span>
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- add newChat modal -->
            <div id="addNewChatModals" class="!hidden modal show" x-show="showAddChatModal">
                <div class="modal-wrap modal-center">
                    <div class="modal-header">
                        <h6>Add New Chat</h6>
                        <button data-modal-close="addNewChatModals" class="link link-red"><i data-lucide="x" class="size-5"></i></button>
                    </div>
                    <div class="modal-content">
                        <div class="relative mb-4 group/form">
                            <input type="text" x-model="searchQuery" class="ltr:pl-9 rtl:pr-9 form-input ltr:group-[&.right]/form:pr-9 rtl:group-[&.right]/form:pl-9 ltr:group-[&.right]/form:pl-4 rtl:group-[&.right]/form:pr-4" placeholder="Search for ...">
                            <button class="absolute inset-y-0 flex items-center text-gray-500 dark:text-dark-500 ltr:left-3 rtl:right-3 ltr:group-[&.right]/form:right-3 rtl:group-[&.right]/form:left-3 ltr:group-[&.right]/form:left-auto rtl:group-[&.right]/form:right-auto focus:outline-hidden">
                                <i data-lucide="search" class="size-4"></i>
                            </button>
                        </div>
                        <div class="max-h-72" data-simplebar>
                            <div class="flex flex-col gap-4">
                                <template x-for="contact in filteredContacts" :key="contact.id">
                                    <div class="flex items-center gap-2">
                                        <div class="flex items-center justify-center font-semibold transition duration-200 ease-linear bg-gray-100 rounded-full dark:bg-dark-850 shrink-0 size-6">
                                            <img :src="contact.avatar" alt="" class="rounded-full">
                                        </div>
                                        <h6 class="grow" x-text="contact.name"></h6>
                                        <a href="#!" class="btn-xs btn btn-sub-gray shrink-0" x-on:click="openChat(contact); filteredChatList">Send <i class="align-middle ri-send-plane-2-line ltr:ml-1 rtl:mr-1"></i></a>
                                    </div>
                                </template>
                            </div>
                        </div>
                        <template x-if="filteredContacts.length === 0">
                            <div class="text-center text-gray-500 dark:text-dark-500">
                                <i class="text-lg ri-search-2-line"></i>
                                <p class="mt-2">No contact available</p>
                            </div>
                        </template>
                    </div>
                </div>
            </div>
            <div id="videoCallModal" class="!hidden modal show">
                <div class="modal-wrap modal-lg modal-center">
                    <div class="modal-content">
                        <div x-data="videoCallSwap()" class="relative overflow-hidden rounded-md">
                            <img :src="mainImage" alt="" class="main-image">
                            <a href="#!" @click="swapVideo" class="absolute inline-block right-5 bottom-5">
                                <img :src="swapVideoSrc" alt="" class="h-24 rounded-md">
                            </a>
                        </div>
                        <div class="flex items-center justify-center gap-2 pt-5">
                            <button class="btn btn-sub-gray btn-icon"><i data-lucide="mic-off" class="size-5"></i></button>
                            <button class="btn btn-red btn-icon" data-modal-close="videoCallModal"><i data-lucide="phone-missed" class="size-5"></i></button>
                            <button class="btn btn-sub-gray btn-icon"><i data-lucide="video-off" class="size-5"></i></button>
                        </div>
                    </div>
                </div>
            </div><!--end modal-->

            <!--call modal-->
            <div id="callModal" class="!hidden modal show" x-show="callModalOpen">
                <div class="modal-wrap modal-xs modal-br" @click.outside="stopCall()">
                    <template x-if="callModalOpen? startCall() : stopCall()"></template>
                    <div class="modal-content">
                        <div>
                            <div class="flex items-center gap-2">
                                <div class="rounded-full size-10 shrink-0">
                                    <img alt="" :src="chatAvtar" class="rounded-full">
                                    <span x-show="!chatAvtar" x-text="avatarText"></span>
                                </div>
                                <div>
                                    <h6 x-text="chatName">David Johnson</h6>
                                    <p class="text-sm text-gray-500 dark:text-dark-500" x-text="isCalling ? 'Calling ...' : formatDuration(callDuration)"></p>
                                </div>
                            </div>
                            <div class="flex items-center gap-3 mt-5">
                                <button type="button" class="btn btn-active-gray shrink-0 btn-icon-text btn-icon">
                                    <i data-lucide="mic" class="size-5"></i>
                                </button>
                                <button type="button" class="btn btn-active-gray shrink-0 btn-icon-text btn-icon">
                                    <i data-lucide="pause" class="size-5"></i>
                                </button>
                                <button type="button" class="btn btn-active-gray shrink-0 btn-icon-text btn-icon">
                                    <i data-lucide="disc" class="size-5"></i>
                                </button>
                                <button type="button" data-modal-close="callModal" @click="stopCall()" class="btn btn-active-red shrink-0 btn-icon-text btn-icon">
                                    <i data-lucide="phone" class="size-5"></i>
                                </button>
                                <button type="button" class="btn btn-active-gray shrink-0 btn-icon-text btn-icon">
                                    <i data-lucide="settings" class="size-5"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {{> partials/footer }}
</div>


{{> partials/vendor-scripts }}

<script type="module" src="assets/js/apps/chat/default.init.js"></script>

<script type="module" src="assets/js/main.js"></script>

</body>
</html>