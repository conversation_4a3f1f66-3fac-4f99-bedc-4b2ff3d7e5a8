{{> partials/main }}

<head>

    {{> partials/title-meta title="Employee Salary" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}
{{> partials/page-heading title="Employee Salary" sub-title="Payroll" }}

<div class="card">
    <div class="flex items-center gap-3 card-header">
        <h6 class="card-title grow">Employee</h6>
        <button type="button" class="btn btn-primary shrink-0">Download</button>
    </div>
    <div class="pt-0 card-body">
        <div x-data="employeesTable()">
            <div class="overflow-x-auto table-box">
                <table class="table flush">
                    <tbody>
                        <tr class="text-gray-500 bg-gray-100 dark:bg-dark-850 dark:text-dark-500">
                            <th x-on:click="sort('employeeName')" class="!font-medium cursor-pointer">Employee Name <span x-show="sortBy === 'employeeName'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                            <th x-on:click="sort('email')" class="!font-medium cursor-pointer">Email <span x-show="sortBy === 'email'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                            <th x-on:click="sort('phoneNumber')" class="!font-medium cursor-pointer">Phone Number <span x-show="sortBy === 'phoneNumber'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                            <th x-on:click="sort('department')" class="!font-medium cursor-pointer">Department <span x-show="sortBy === 'department'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                            <th x-on:click="sort('monthlySalary')" class="!font-medium cursor-pointer">Monthly Salary <span x-show="sortBy === 'monthlySalary'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                            <th x-on:click="sort('status')" class="!font-medium cursor-pointer">Status <span x-show="sortBy === 'status'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                            <th class="!font-medium">Action</th>
                        </tr>
                        <template x-for="(employee, index) in displayedEmployees" :key="index">
                            <tr class="*:px-3 *:py-2.5">
                                <td x-text="employee.employeeName"></td>
                                <td x-text="employee.email"></td>
                                <td x-text="employee.phoneNumber"></td>
                                <td x-text="employee.department"></td>
                                <td x-text="employee.monthlySalary"></td>
                                <td>
                                    <span x-text="employee.status" :class="{
                                                'badge badge-green': employee.status === 'Successful',
                                                'badge badge-yellow': employee.status === 'Pending',
                                                'badge badge-red': employee.status === 'Failed',
                                            }"></span>
                                </td>
                                <td>
                                    <div class="flex items-center gap-2">
                                        <button class="btn btn-sub-primary btn-icon !size-8" title="edit"><i class="ri-pencil-line"></i></button>
                                        <button class="btn btn-sub-red btn-icon !size-8" title="delete" @click="deleteEmployee = employee.employeeName" data-modal-target="deleteModal"><i class="ri-delete-bin-line"></i></button>
                                    </div>
                                </td>
                            </tr>
                        </template>
                    </tbody>
                </table>
            </div>
            <div class="grid grid-cols-12 gap-5 mt-5 items-center">
                <div class="col-span-12 md:col-span-6 justify-center md:justify-start flex flex-wrap">
                    <p class="text-gray-500 dark:text-dark-500">Showing <b x-text="showingStart"></b> - <b x-text="showingEnd"></b> of <b x-text="employees.length"></b> Results</p>
                </div>
                <div class="col-span-12 md:col-span-6">
                    <div class="flex justify-center md:justify-end pagination pagination-primary">
                        <button @click="prevPage()" :disabled="currentPage === 1" class="pagination-pre">
                            <i data-lucide="chevron-left" class="mr-1 ltr:inline-block rtl:hidden size-4"></i>
                            <i data-lucide="chevron-right" class="ml-1 ltr:hidden rtl:inline-block size-4"></i>
                            Prev
                        </button>
                        <template x-for="page in totalPages" :key="page">
                            <button @click="gotoPage(page)" :class="{ 'active': currentPage === page }" class="pagination-item">
                                <span x-text="page"></span>
                            </button>
                        </template>
                        <button @click="nextPage()" :disabled="currentPage === totalPages" class="pagination-next">
                            Next
                            <i data-lucide="chevron-right" class="ml-1 ltr:inline-block rtl:hidden size-4"></i>
                            <i data-lucide="chevron-left" class="mr-1 ltr:hidden rtl:inline-block size-4"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!--delete modal-->
            <div id="deleteModal" class="!hidden modal show">
                <div class="modal-wrap modal-xs modal-center">
                    <div class="text-center modal-content p-7">
                        <div class="flex items-center justify-center mx-auto mb-4 text-red-500 rounded-full bg-red-500/10 size-14 backdrop-blur-xl">
                            <i data-lucide="trash-2" class="size-6"></i>
                        </div>
                        <h5 class="mb-4">Are you sure you want to delete this employee ?</h5>
                        <div class="flex items-center justify-center gap-2">
                            <button class="btn btn-red" @click="deleteSalary()" data-modal-close="deleteModal">Delete</button>
                            <button data-modal-close="deleteModal" class="btn link link-primary">Cancel</button>
                        </div>
                    </div>
                </div>
            </div><!--end-->

        </div>
    </div>
</div>

</div>
{{> partials/footer }}
</div>


{{> partials/vendor-scripts }}

<script type="module" src="assets/js/apps/hospital/payroll/employee-salary.init.js"></script>

<script type="module" src="assets/js/main.js"></script>

</body>
</html>