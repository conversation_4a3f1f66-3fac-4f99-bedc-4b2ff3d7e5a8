{{> partials/main }}

<head>

    {{> partials/title-meta title="Schedule" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}
{{> partials/page-heading title="Schedule" sub-title="Exam" }}

<div x-data="{ isOpen: true }" x-show="isOpen" class="font-medium text-center ltr:pr-10 rtl:pl-10 alert alert-primary mb-space">
    <span>Today Test: 2 Test and online 1 Test</span>
    <a href="javascript:void(0);" x-on:click="isOpen = false" class="absolute text-lg transition duration-200 ease-linear text-primary-400 hover:text-primary-500 ltr:right-5 rtl:left-5 top-2"><i class="ri-close-fill"></i></a>
</div><!--end Alerts-->
<div x-data="scheduleTable()">
<div class="grid grid-cols-12 gap-x-space">
    <div class="col-span-12 row-span-2 sm:col-span-6 xl:col-span-3 card">
        <div class="card-body">
            <h6 class="mb-5">Total Exam (This month)</h6>
            <div x-data="circleProgress(34)" x-init="animateProgress" class="relative mx-auto size-36" dir="ltr">
                <svg class="size-full" width="36" height="36" viewBox="0 0 36 36" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="18" cy="18" r="16" fill="none" class="stroke-current text-sky-500/10" stroke-width="3">
                    </circle>
                    <g class="origin-center transform -rotate-90">
                        <circle cx="18" cy="18" r="16" fill="none" class="stroke-current text-sky-500" stroke-width="3" stroke-dasharray="100" :stroke-dashoffset="progress" style="transition: stroke-dashoffset 1s ease-out;"></circle>
                    </g>
                </svg>
                <!-- Percentage Text -->
                <div class="absolute transform -translate-x-1/2 -translate-y-1/2 top-1/2 start-1/2">
                    <span class="text-lg font-bold text-center text-gray-800 dark:text-dark-50" x-text="`${percent}%`"></span>
                </div>
            </div>
        </div>
    </div><!--end col-->
    <div class="relative col-span-12 sm:col-span-6 xl:col-span-3 card">
        <div class="card-body">
            <i data-lucide="book-open-text" class="absolute top-5 ltr:right-5 rtl:left-5 text-primary-500 fill-primary-500/10 size-6"></i>
            <p class="mb-3 text-gray-500 dark:text-dark-500">Total Test</p>
            <h5><span x-text="examTypeFilter('All')"></span>
            </h5>
        </div>
    </div><!--end col-->
    <div class="relative col-span-12 sm:col-span-6 xl:col-span-3 card">
        <div class="card-body">
            <i data-lucide="library-big" class="absolute text-purple-500 top-5 ltr:right-5 rtl:left-5 fill-purple-500/10 size-6"></i>
            <p class="mb-3 text-gray-500 dark:text-dark-500">General Test</p>
            <h5><span x-text="examTypeFilter('General')"></span>
            </h5>
        </div>
    </div><!--end col-->
    <div class="relative col-span-12 sm:col-span-6 xl:col-span-3 card">
        <div class="card-body">
            <i data-lucide="notepad-text-dashed" class="absolute text-sky-500 top-5 ltr:right-5 rtl:left-5 fill-sky-500/10 size-6"></i>
            <p class="mb-3 text-gray-500 dark:text-dark-500">Formative Test</p>
            <h5><span x-text="examTypeFilter('Formative')"></span>
            </h5>
        </div>
    </div><!--end col-->
    <div class="relative col-span-12 sm:col-span-6 xl:col-span-3 card">
        <div class="card-body">
            <i data-lucide="notebook-text" class="absolute text-green-500 top-5 ltr:right-5 rtl:left-5 fill-green-500/10 size-6"></i>
            <p class="mb-3 text-gray-500 dark:text-dark-500">Summative Test</p>
            <h5><span x-text="examTypeFilter('Summative')"></span>
            </h5>
        </div>
    </div><!--end col-->
    <div class="relative col-span-12 sm:col-span-6 xl:col-span-3 card">
        <div class="card-body">
            <i data-lucide="video" class="absolute text-red-500 top-5 ltr:right-5 rtl:left-5 fill-red-500/10 size-6"></i>
            <p class="mb-3 text-gray-500 dark:text-dark-500">Online Test (MCQ)</p>
            <h5><span x-text="examTypeFilter('Online')"></span>
            </h5>
        </div>
    </div><!--end col-->
    <div class="relative col-span-12 sm:col-span-6 xl:col-span-3 card">
        <div class="card-body">
            <i data-lucide="blend" class="absolute text-yellow-500 top-5 ltr:right-5 rtl:left-5 fill-yellow-500/10 size-6"></i>
            <p class="mb-3 text-gray-500 dark:text-dark-500">Rejoining Test</p>
            <h5><span x-text="examTypeFilter('Rejoining')"></span>
            </h5>
        </div>
    </div><!--end col-->
</div><!--end grid-->

<!--Exam List-->
<div class="card" >
    <div class="justify-between md:flex card-header gap-5 items-center">
        <h6 class="card-title">Exam Schedule List</h6>
        <div class="flex flex-wrap gap-2 mt-3 md:mt-0">
            <div>
                <div id="stdFilterSelect" placeholder="Select STD" @change="filteredSchedules()"></div>
            </div><!--end col-->
            <div>
                <div id="dateFilterSelect" placeholder="Select Filters" @change="filteredSchedules()"></div>
            </div><!--end col-->
            <div>
                <button type="button" class="w-full btn btn-primary" data-modal-target="addExamModal" @click="handleModal('showAddScheduleForm')"><i data-lucide="circle-plus" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> Add Exam
                    Schedule</button>
            </div><!--end col-->
        </div>
    </div><!--end card-header/grid-->
    <div class="pt-0 card-body">
        <div>
            <div class="overflow-x-auto table-box">
                <table class="table flush whitespace-nowrap">
                    <tbody>
                        <tr class="text-gray-500 bg-gray-100 dark:bg-dark-850 dark:text-dark-500">
                            <th x-on:click="sort('scheduleID')" class="!font-medium cursor-pointer">ID
                                <span x-show="sortBy === 'studentID'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span>
                            </th>
                            <th x-on:click="sort('testName')" class="!font-medium cursor-pointer">Test Name <span x-show="sortBy === 'testName'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                            <th x-on:click="sort('testCategory')" class="!font-medium cursor-pointer">Test Category <span x-show="sortBy === 'testCategory'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                            <th x-on:click="sort('testType')" class="!font-medium cursor-pointer">Test Type <span x-show="sortBy === 'testType'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                            <th x-on:click="sort('class')" class="!font-medium cursor-pointer">Class <span x-show="sortBy === 'class'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                            <th x-on:click="sort('startDate')" class="!font-medium cursor-pointer">Start Date <span x-show="sortBy === 'startDate'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                            <th x-on:click="sort('endDate')" class="!font-medium cursor-pointer">End Date <span x-show="sortBy === 'endDate'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                            <th x-on:click="sort('status')" class="!font-medium cursor-pointer">Status <span x-show="sortBy === 'status'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                            <th class="!font-medium">Action</th>
                        </tr>
                        <template x-for="(schedule, index) in displayedSchedules" :key="index">
                            <tr class="*:px-3 *:py-2.5">
                                <td x-text="schedule.scheduleID"></td>
                                <td x-text="schedule.testName"></td>
                                <td x-text="schedule.testCategory"></td>
                                <td x-text="schedule.testType"></td>
                                <td x-text="schedule.class"></td>
                                <td x-text="schedule.startDate"></td>
                                <td x-text="schedule.endDate"></td>
                                <td>
                                    <span x-text="schedule.status" :class="{
                                            'badge badge-sky': schedule.status === 'New',
                                            'badge badge-yellow': schedule.status === 'Scheduled',
                                            'badge badge-green': schedule.status === 'Completed'
                                        }"></span>
                                <td>
                                    <div class="flex items-center gap-2">
                                        <button class="btn btn-sub-gray btn-icon !size-8 rounded-md" @click="editSchedule(schedule.scheduleID)" data-modal-target="addExamModal"><i class="ri-pencil-line"></i></button>
                                        <button class="btn btn-sub-red btn-icon !size-8 rounded-md" @click="deleteItem = schedule.scheduleID" data-modal-target="deleteModal"><i class="ri-delete-bin-line"></i></button>
                                    </div>
                                </td>
                            </tr>
                        </template>
                        <tr>
                            <template x-if="displayedSchedules.length == 0">
                                <td colspan="10" class="!p-8">
                                    <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" class="mx-auto size-12" viewBox="0 0 48 48">
                                        <linearGradient id="SVGID_1__h35ynqzIJzH4_gr1" x1="34.598" x2="15.982" y1="15.982" y2="34.598" gradientUnits="userSpaceOnUse">
                                            <stop offset="0" stop-color="#60e8fe"></stop>
                                            <stop offset=".033" stop-color="#6ae9fe"></stop>
                                            <stop offset=".197" stop-color="#97f0fe"></stop>
                                            <stop offset=".362" stop-color="#bdf5ff"></stop>
                                            <stop offset=".525" stop-color="#dafaff"></stop>
                                            <stop offset=".687" stop-color="#eefdff"></stop>
                                            <stop offset=".846" stop-color="#fbfeff"></stop>
                                            <stop offset="1" stop-color="#fff"></stop>
                                        </linearGradient>
                                        <path fill="url(#SVGID_1__h35ynqzIJzH4_gr1)" d="M40.036,33.826L31.68,25.6c0.847-1.739,1.335-3.684,1.335-5.748c0-7.27-5.894-13.164-13.164-13.164	S6.688,12.582,6.688,19.852c0,7.27,5.894,13.164,13.164,13.164c2.056,0,3.995-0.485,5.728-1.326l3.914,4.015l4.331,4.331	c1.715,1.715,4.496,1.715,6.211,0C41.751,38.321,41.751,35.541,40.036,33.826z"></path>
                                        <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M31.95,25.739l8.086,8.086c1.715,1.715,1.715,4.496,0,6.211l0,0c-1.715,1.715-4.496,1.715-6.211,0	l-4.331-4.331"></path>
                                        <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M7.525,24.511c-1.771-4.694-0.767-10.196,3.011-13.975c3.847-3.847,9.48-4.817,14.228-2.912"></path>
                                        <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M30.856,12.603c3.376,5.114,2.814,12.063-1.688,16.565c-4.858,4.858-12.565,5.129-17.741,0.814"></path>
                                    </svg>
                                    <p class="mt-2 text-center text-gray-500 dark:text-dark-500">No matching records found</p>
                                </td>
                            </template>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="grid grid-cols-12 gap-5 mt-5 items-center" x-show="displayedSchedules.length > 0">
                <div class="col-span-12 md:col-span-6 justify-center md:justify-start flex flex-wrap">
                    <p class="text-gray-500 dark:text-dark-500">Showing <b x-text="showingStart"></b> - <b x-text="showingEnd"></b> of <b x-text="schedules.length"></b> Results</p>
                </div>
                <div class="col-span-12 md:col-span-6">
                    <div class="flex justify-center md:justify-end pagination pagination-primary">
                        <button @click="prevPage()" :disabled="currentPage === 1" class="pagination-pre">
                            <i data-lucide="chevron-left" class="mr-1 ltr:inline-block rtl:hidden size-4"></i>
                            <i data-lucide="chevron-right" class="ml-1 ltr:hidden rtl:inline-block size-4"></i>
                            Prev
                        </button>
                        <template x-for="page in totalPages" :key="page">
                            <button @click="gotoPage(page)" :class="{ 'active': currentPage === page }" class="pagination-item">
                                <span x-text="page"></span>
                            </button>
                        </template>
                        <button @click="nextPage()" :disabled="currentPage === totalPages" class="pagination-next">
                            Next
                            <i data-lucide="chevron-right" class="ml-1 ltr:inline-block rtl:hidden size-4"></i>
                            <i data-lucide="chevron-left" class="mr-1 ltr:hidden rtl:inline-block size-4"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!--Create exam-->
    <div id="addExamModal" class="!hidden modal show" :class="{'show d-block': showAddScheduleForm || showEditScheduleForm}" x-show="showAddScheduleForm || showEditScheduleForm">
        <div class="modal-wrap modal-center">
            <div class="modal-header">
                <h6 class="modal-title" x-text="showEditScheduleForm ? 'Edit Exam' : 'Add Exam' ">Add Exam</h6>
                <button data-modal-close="addExamModal" class="link link-red"><i data-lucide="x" class="size-5"></i></button>
            </div>
            <div class="modal-content">
                <div class="grid grid-cols-12 gap-4">
                    <div class="col-span-12">
                        <label for="testNameInput" class="form-label">Test Name</label>
                        <input type="text" id="testNameInput" class="form-input" placeholder="Test name" x-model="scheduleForm.testName" @input="validateField('testName' , scheduleForm.testName , 'Test NAme is required') ">
                        <span x-show="errors.testName" class="text-red-500" x-text="errors.testName"></span>
                    </div>
                    <div class="col-span-12">
                        <label for="testCategorySelect" class="form-label">Test Category</label>
                        <div id="testCategorySelect" placeholder="Select Category" x-model="scheduleForm.testCategory" @change="validateField('testCategory' , document.querySelector('#testCategorySelect') , 'Test Category is required') "></div>
                        <span x-show="errors.testCategory" class="text-red-500" x-text="errors.testCategory"></span>
                    </div>
                    <div class="col-span-12">
                        <label for="testTypeSelect" class="form-label">Test Type</label>
                        <div id="testTypeSelect" placeholder="Select Type" x-model="scheduleForm.testType" @change="validateField('testType' , document.querySelector('#testTypeSelect') , 'Test Type is required') "></div>
                        <span x-show="errors.testType" class="text-red-500" x-text="errors.testType"></span>
                    </div>
                    <div class="col-span-12">
                        <label for="classSelect" class="form-label">Class</label>
                        <div id="classSelect" placeholder="Select Class" x-model="scheduleForm.class" @change="validateField('class' , document.querySelector('#classSelect') , 'Class is required') "></div>
                        <span x-show="errors.class" class="text-red-500" x-text="errors.class"></span>
                    </div>
                    <div class="col-span-6">
                        <label for="startDateSelect" class="form-label">Start Date</label>
                        <input id="startDateSelect" data-provider="flatpickr" data-date-format="d M, Y" type="text" placeholder="DD-MM-YYYY" class="form-input" x-model="scheduleForm.startDate" @input="validateField('startDate' , scheduleForm.startDate , 'Start Date is required') " />
                        <span x-show="errors.startDate" class="text-red-500" x-text="errors.startDate"></span>
                    </div>
                    <div class="col-span-6">
                        <label for="endDateSelect" class="form-label">End Date</label>
                        <input id="endDateSelect" data-provider="flatpickr" data-date-format="d M, Y" type="text" placeholder="DD-MM-YYYY" class="form-input" x-model="scheduleForm.endDate" @input="validateField('endDate' , scheduleForm.endDate , 'End Date is required') " />
                        <span x-show="errors.endDate" class="text-red-500" x-text="errors.endDate"></span>
                    </div>
                    <div class="col-span-12">
                        <label for="statusSelect" class="form-label">Status</label>
                        <div id="statusSelect" placeholder="Select Status" x-model="scheduleForm.status" @change="validateField('status' , document.querySelector('#statusSelect') , 'Status is required') "></div>
                        <span x-show="errors.status" class="text-red-500" x-text="errors.status"></span>
                    </div>
                </div>
                <div class="flex items-center justify-end gap-2 mt-5">
                    <button type="button" class="btn btn-active-red" data-modal-close="addExamModal">
                        <i data-lucide="x" class="inline-block size-4"></i>
                        <span class="align-baseline">Close</span>
                    </button>
                    <button type="button" class="btn btn-primary" x-text="showEditScheduleForm ? 'Update Exam Schedule' : 'Add Exam Schedule'" @click="submitForm()">Add Exam Schedule</button>
                </div>
            </div>
        </div>
    </div>

    <!--delete modal-->
    <div id="deleteModal" class="!hidden modal show">
        <div class="modal-wrap modal-xs modal-center">
            <div class="text-center modal-content p-7">
                <div class="flex items-center justify-center mx-auto mb-4 text-red-500 rounded-full bg-red-500/10 size-14 backdrop-blur-xl">
                    <i data-lucide="trash-2" class="size-6"></i>
                </div>
                <h5 class="mb-4">Are you sure you want to delete this exam ?</h5>
                <div class="flex items-center justify-center gap-2">
                    <button class="btn btn-red" @click="deleteSchedule()" data-modal-close="deleteModal">Delete</button>
                    <button data-modal-close="deleteModal" class="btn link link-primary">Cancel</button>
                </div>
            </div>
        </div>
    </div><!--end-->
</div><!--end card-->
</div>
</div>
{{> partials/footer }}
</div>



{{> partials/vendor-scripts }}

<script type="module" src="assets/js/apps/school/exam/schedule.init.js"></script>

<script type="module" src="assets/js/main.js"></script>

</body>
</html>