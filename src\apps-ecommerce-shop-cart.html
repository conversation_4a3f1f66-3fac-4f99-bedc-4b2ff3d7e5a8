{{> partials/main }}

<head>

    {{> partials/title-meta title="Shop Cart" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

<div class="relative min-h-screen">
    <div class="page-wrapper pt-[calc(theme('spacing.topbar')_*_1.2)] group-data-[layout=horizontal]:pt-[calc(theme('spacing.topbar')_*_1.9)]" x-data="counter()" x-init="init()">
        {{> partials/page-heading title="Shop Cart" sub-title="Ecommerce" }}

        <div class="grid grid-cols-12 gap-5">
            <div class="col-span-12 xl:col-span-8">
                <div class="flex flex-col md:items-center md:flex-row gap-space mb-space">
                    <h6 class="card-title grow">Shopping Cart</h6>
                    <div x-data="countdownTimer(300)" x-init="startTimer()" class="shrink-0">
                        <p class="text-gray-500 dark:text-dark-500 shrink-0">
                            <span class="font-semibold text-red-500" x-text="minutes + ':' + (seconds < 10 ? '0' + seconds : seconds)"></span>
                            left until the end of the process
                        </p>
                    </div>
                </div>

                <template x-for="(product, index) in products" :key="product.id">
                    <div x-data="{ showCard: true, count: product.count, price: product.price, discount: product.discount, activeColor: product.colors[0], activeSize: product.sizes[0] }" x-show="showCard">
                        <div class="card">
                            <div class="card-body">
                                <button class="float-end" @click="products.splice(index, 1)">
                                    <i class="link size-4 link-red ri-close-line"></i>
                                </button>
                                <div class="gap-5 sm:flex">
                                    <div class="w-full sm:!w-[300px] shrink-0 bg-gray-100 dark:bg-dark-850 rounded-md">
                                        <img :src="product.image" alt="">
                                    </div>
                                    <div class="mt-5 sm:mt-0">
                                        <span class="badge badge-gray" x-text="product.category"></span>
                                        <h6 class="mt-2 mb-3"><a href="#!" x-text="product.name"></a></h6>
                                        <div class="grid grid-cols-2 gap-space">
                                            <div>
                                                <h6>Select Colors</h6>
                                                <div class="flex items-center gap-2 mt-2 grow">
                                                    <template x-for="color in product.colors" :key="color">
                                                        <a href="#!" class="flex items-center justify-center text-white border-2 border-white rounded-full dark:border-dark-900 outline-1 outline size-6 group/item" :class="{
                                                               'bg-blue-500 outline-blue-500/20': color === 'blue',
                                                               'bg-gray-500 outline-gray-200 dark:outline-gray-800': color === 'gray',
                                                               'bg-pink-500 outline-pink-500/20': color === 'pink',
                                                               'bg-green-500 outline-green-500/20': color === 'green',
                                                               'bg-red-500 outline-red-500/20': color === 'red'
                                                           }" :class="{ 'active': activeColor === color }" @click.prevent="activeColor = color">
                                                            <i data-lucide="check" class="size-4 hidden group-[&.active]/item:block"></i>
                                                        </a>
                                                    </template>
                                                </div>
                                            </div>
                                            <div>
                                                <h6>Select Size</h6>
                                                <div class="flex items-center gap-2 mt-3 font-medium shrink-0">
                                                    <template x-for="size in product.sizes" :key="size">
                                                        <a href="#!" class="text-gray-500 dark:text-dark-500 [&.active]:text-green-500" :class="{ 'active': activeSize === size }" @click.prevent="activeSize = size" x-text="size"></a>
                                                    </template>
                                                </div>
                                            </div>
                                        </div>
                                        <h5 class="flex items-center gap-2 mt-4">
                                            <span>$<span x-text="(price * count * (1 - discount)).toFixed(2)"></span></span>
                                            <small class="font-normal text-gray-500 line-through dark:text-dark-500" x-show="discount > 0">$<span x-text="(price * count).toFixed(2)"></span></small>
                                            <span class="text-xs badge badge-red shrink-0" x-show="discount > 0" x-text="(discount * 100).toFixed(0) + '%'"></span>
                                        </h5>
                                        <div class="mt-5">
                                            <div class="flex items-center w-32 p-1 text-center border border-gray-200 rounded-md dark:border-dark-800">
                                                <button @click="if (count > 0 && count--) { count--; product.count = count; }" class="flex items-center justify-center transition duration-200 ease-linear rounded-md text-primary-500 minus size-8 shrink-0 bg-primary-500/20 hover:text-primary-700"><i class="size-4 ri-subtract-line"></i></button>
                                                <input type="text" x-model="count" class="h-8 p-0 text-center border-0 rounded-none form-input" readonly>
                                                <button @click="if (count < 5) { count++; product.count = count; }" class="flex items-center justify-center transition duration-200 ease-linear rounded-md text-primary-500 plus size-8 shrink-0 bg-primary-500/20 hover:text-primary-700"><i class="size-4 ri-add-line"></i></button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </template>

            </div>

            <div class="col-span-12 xl:col-span-4">
                <div class="sticky mb-5 top-24">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="card-title">Order Summary</h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-4">
                                <label for="discountCode" class="form-label">Discount Code</label>
                                <input type="text" id="discountCode" class="form-input" placeholder="Enter coupon code" x-model="discountCode">
                            </div>
                            <table class="table flush">
                                <tr>
                                    <td class="font-semibold">Sub Amount</td>
                                    <td>$<span x-text="subtotal.toFixed(2)"></span></td>
                                </tr>
                                <tr>
                                    <td class="font-semibold">Vat Amount (6%)</td>
                                    <td>$<span x-text="vat.toFixed(2)"></span></td>
                                </tr>
                                <tr>
                                    <td class="font-semibold">Discount (10%)</td>
                                    <td>-$<span x-text="discount.toFixed(2)"></span></td>
                                </tr>
                                <tr>
                                    <td class="font-semibold">Shipping Charge</td>
                                    <td>$<span x-text="shippingCharge.toFixed(2)"></span></td>
                                </tr>
                                <tr class="border-t border-gray-200 dark:border-dark-800">
                                    <td class="font-semibold">Total Amount</td>
                                    <td>$<span x-text="total.toFixed(2)"></span></td>
                                </tr>
                            </table>
                            <div class="my-4">
                                <a href="apps-ecommerce-checkout.html" class="w-full btn btn-primary">Checkout Now</a>
                            </div>
                            <p class="text-center text-gray-500 dark:text-dark-500">By clicking the "checkout order" button, you agree to the terms of the public offers.</p>
                        </div>
                    </div>
                    <div class="flex gap-4 mb-5">
                        <div class="flex items-center justify-center bg-gray-100 rounded-md size-12 dark:bg-dark-850 shrink-0">
                            <i data-lucide="truck" class="text-gray-500 fill-gray-200 dark:text-dark-500 dark:fill-dark-850"></i>
                        </div>
                        <div>
                            <h6 class="mb-1">Free delivery on May 24 </h6>
                            <p class="text-gray-500 dark:text-dark-500">To the address, by courier - with fitting, free of charge for purchases over $500.</p>
                        </div>
                    </div>
                    <div class="flex gap-4 mb-5">
                        <div class="flex items-center justify-center bg-gray-100 rounded-md size-12 dark:bg-dark-850 shrink-0">
                            <i data-lucide="shield-check" class="text-gray-500 dark:text-dark-500 fill-gray-200 dark:fill-dark-850"></i>
                        </div>
                        <div>
                            <h6 class="mb-1">Safety</h6>
                            <p class="text-gray-500 dark:text-dark-500">The security of payments is guaranteed through the use of the SSL protocol. Your bank card details are securely protected during online transactions.</p>
                        </div>
                    </div>
                    <div class="flex gap-4">
                        <div class="flex items-center justify-center bg-gray-100 rounded-md size-12 dark:bg-dark-850 shrink-0">
                            <i data-lucide="percent" class="text-gray-500 dark:text-dark-500 fill-gray-200 dark:fill-dark-850"></i>
                        </div>
                        <div>
                            <h6 class="mb-1">5% discount</h6>
                            <p class="text-gray-500 dark:text-dark-500">When paying online, you receive a 5% discount on your next purchase.</p>
                        </div>
                    </div>
                </div>
            </div>

        </div>

    </div>
    {{> partials/footer }}
</div>


{{> partials/vendor-scripts }}

<script type="module" src="assets/js/apps/ecommerce/shop-cart.init.js"></script>

<script type="module" src="assets/js/main.js"></script>

</body>
</html>