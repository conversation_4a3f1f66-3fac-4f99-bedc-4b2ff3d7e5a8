{{> partials/main }}

<head>

    {{> partials/title-meta title="POS (Admin)" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}
{{> partials/page-heading title="POS (Admin)" sub-title="POS" }}

<div class="grid grid-cols-12 gap-x-space">
    <div class="col-span-12 xl:col-span-8 order-1 card pos-widget-card bg-primary-500 border-none text-white relative">
        <div class="card-body p-8">
            <div class="max-w-[450px]">
                <h3 class="mb-2 leading-normal capitalize">Advanced reporting and real-time analytics</h3>
                <p class="text-white/75 mb-6">Remove all limits on transactions, users, and reporting features</p>
                <a href="#!" class="btn bg-white text-primary-500">
                    Unlock Premium ($29/month)
                    <i data-lucide="move-right" class="inline-block rtl:hidden size-4 ml-1"></i>
                    <i data-lucide="move-left" class="ltr:hidden rtl:inline-block size-4 mr-1"></i></a>
            </div>
            <img src="assets/images/others/dashboard.png" alt="" class="md:absolute md:top-1/2 md:-translate-y-1/2 ltr:md:right-8 rtl:md:left-8 size-44 animate-spin mt-5 md:m-0 mx-auto">
        </div>
    </div>
    <div class="col-span-12 xl:col-span-4 order-2 card">
        <div class="card-body">
            <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown float-end">
                <button x-ref="button" x-on:click="toggle()" title="dropdown-button" :aria-expanded="open.toString()" type="button" class="flex items-center text-gray-500 dark:text-dark-500">
                    <i data-lucide="ellipsis" class="size-5"></i>
                </button>
                <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden" dropdown-position="right">
                    <ul>
                        <li>
                            <a href="#!" class="dropdown-item">
                                <span>Weekly</span>
                            </a>
                        </li>
                        <li>
                            <a href="#!" class="dropdown-item">
                                <span>Monthly</span>
                            </a>
                        </li>
                        <li>
                            <a href="#!" class="dropdown-item">
                                <span>Yearly</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
            <h6 class="card-title mb-1">Point of Sale</h6>
            <p class="text-gray-500 dark:text-dark-500 mb-4">Sales Dashboard</p>
            <div class="h-[124px] -mx-5 px-5" data-simplebar>
                <div class="space-y-3">
                    <a href="#!" class="p-3 bg-green-500/10 text-green-500 flex items-center gap-3 rounded-md">
                        <i data-lucide="receipt" class="size-5"></i>
                        <p class="text-sm grow">New Transaction</p>
                        <span class="badge bg-white border-white dark:bg-dark-900 dark:border-dark-900 shrink-0">POS</span>
                    </a>
                    <a href="#!" class="p-3 bg-indigo-500/10 text-indigo-500 flex items-center gap-3 rounded-md">
                        <i data-lucide="bar-chart-2" class="size-5"></i>
                        <p class="text-sm grow">Daily Sales Report</p>
                        <span class="badge bg-white border-white dark:bg-dark-900 dark:border-dark-900 shrink-0">Reports</span>
                    </a>
                    <a href="#!" class="p-3 bg-yellow-500/10 text-yellow-500 flex items-center gap-3 rounded-md">
                        <i data-lucide="users" class="size-5"></i>
                        <p class="text-sm grow">Customer Management</p>
                        <span class="badge bg-white border-white dark:bg-dark-900 dark:border-dark-900 shrink-0">CRM</span>
                    </a>
                </div>
            </div>
        </div>
    </div>
    <div class="col-span-12 order-3">
        <div class="card">
            <div class="card-body">
                <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-space">
                    <div class="card mb-0 bg-gradient-to-r from-primary-500/15 via-primary-500/5 to-primary-500/10 backdrop-lg border-none shadow-none relative">
                        <div class="pos-widget-curve">
                            <div class="bg-primary-500 text-white rounded-xl size-10 flex items-center justify-center">
                                <i data-lucide="badge-dollar-sign" class="size-5"></i>
                            </div>
                        </div>
                        <div class="card-body">
                            <p class="mb-3 text-gray-500 dark:text-dark-500">Total Sales</p>
                            <h3 class="font-normal grow mb-3"><span x-data="animatedCounter(3497, 500, 0)" x-init="updateCounter" x-text="Math.round(current)"></span></h3>
                            <p class="text-gray-500 dark:text-dark-500"><span class="text-green-500"><i data-lucide="trending-up" class="size-4 inline-block mr-1"></i> 10%</span> than yesterday</p>
                        </div>
                    </div>
                    <div class="card mb-0 bg-gradient-to-r from-pink-500/15 via-pink-500/5 to-pink-500/10 backdrop-lg border-none shadow-none relative">
                        <div class="pos-widget-curve size-12 border-[5px] flex items-center justify-center border-white dark:border-dark-900 absolute -top-0 -right-0 rounded-bl-xl bg-white">
                            <div class="bg-pink-500 text-white rounded-xl size-10 flex items-center justify-center">
                                <i data-lucide="badge-dollar-sign" class="size-5"></i>
                            </div>
                        </div>
                        <div class="card-body">
                            <p class="mb-3 text-gray-500 dark:text-dark-500">New Orders</p>
                            <h3 class="font-normal grow mb-3"><span x-data="animatedCounter(1205, 500, 0)" x-init="updateCounter" x-text="Math.round(current)"></span></h3>
                            <p class="text-gray-500 dark:text-dark-500"><span class="text-green-500"><i data-lucide="trending-up" class="size-4 inline-block mr-1"></i> 10%</span> than yesterday</p>
                        </div>
                    </div>
                    <div class="card mb-0 bg-gradient-to-r from-yellow-500/15 via-yellow-500/5 to-yellow-500/10 backdrop-lg border-none shadow-none relative">
                        <div class="pos-widget-curve size-12 border-[5px] flex items-center justify-center border-white dark:border-dark-900 absolute -top-0 -right-0 rounded-bl-xl bg-white">
                            <div class="bg-yellow-500 text-white rounded-xl size-10 flex items-center justify-center">
                                <i data-lucide="badge-dollar-sign" class="size-5"></i>
                            </div>
                        </div>
                        <div class="card-body">
                            <p class="mb-3 text-gray-500 dark:text-dark-500">Pending Orders</p>
                            <h3 class="font-normal grow mb-3"><span x-data="animatedCounter(937, 500, 0)" x-init="updateCounter" x-text="Math.round(current)"></span></h3>
                            <p class="text-gray-500 dark:text-dark-500"><span class="text-green-500"><i data-lucide="trending-up" class="size-4 inline-block mr-1"></i> 10%</span> than yesterday</p>
                        </div>
                    </div>
                    <div class="card mb-0 bg-gradient-to-r from-purple-500/15 via-purple-500/5 to-purple-500/10 backdrop-lg border-none shadow-none relative">
                        <div class="pos-widget-curve size-12 border-[5px] flex items-center justify-center border-white dark:border-dark-900 absolute -top-0 -right-0 rounded-bl-xl bg-white">
                            <div class="bg-purple-500 text-white rounded-xl size-10 flex items-center justify-center">
                                <i data-lucide="badge-dollar-sign" class="size-5"></i>
                            </div>
                        </div>
                        <div class="card-body">
                            <p class="mb-3 text-gray-500 dark:text-dark-500">Net Profit</p>
                            <h3 class="font-normal grow mb-3">$<span x-data="animatedCounter(389, 500, 0)" x-init="updateCounter" x-text="Math.round(current)"></span>K</h3>
                            <p class="text-gray-500 dark:text-dark-500"><span class="text-green-500"><i data-lucide="trending-up" class="size-4 inline-block mr-1"></i> 10%</span> than yesterday</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-span-12 2xl:col-span-8 order-4">
        <div class="mb-3 flex items-center gap-3">
            <h6 class="grow">Recently Payment</h6>
            <a href="#!" class="link link-primary">View All <i data-lucide="move-right" class="inline-block size-4 ltr:ml-1 rtl:mr-1"></i></a>
        </div>
        <div class="swiper mySwiper">
            <div class="swiper-wrapper">
                <div class="swiper-slide">
                    <div class="card">
                        <div class="card-body flex items-center gap-3 p-3">
                            <img src="assets/images/avatar/user-14.png" alt="" class="size-12 rounded-md shrink-0">
                            <div class="grow">
                                <h6 class="mb-0.5">Alexander Brown</h6>
                                <p class="text-gray-500 fs-sm">20 May, 2025</p>
                            </div>
                            <h6 class="grow">$154.87</h6>
                            <span class="shrink-0 badge badge-green">Done</span>
                        </div>
                    </div>
                </div>
                <div class="swiper-slide">
                    <div class="card">
                        <div class="card-body flex items-center gap-3 p-3">
                            <img src="assets/images/avatar/user-18.png" alt="" class="size-12 rounded-md shrink-0">
                            <div class="grow">
                                <h6 class="mb-0.5">Sophia Martinez</h6>
                                <p class="text-gray-500 fs-sm">10 May, 2025</p>
                            </div>
                            <h6 class="grow">$291.49</h6>
                            <span class="shrink-0 badge badge-yellow">Pending</span>
                        </div>
                    </div>
                </div>
                <div class="swiper-slide">
                    <div class="card">
                        <div class="card-body flex items-center gap-3 p-3">
                            <img src="assets/images/avatar/user-11.png" alt="" class="size-12 rounded-md shrink-0">
                            <div class="grow">
                                <h6 class="mb-0.5">Benjamin Brown</h6>
                                <p class="text-gray-500 fs-sm">10 May, 2025</p>
                            </div>
                            <h6 class="grow">$114.99</h6>
                            <span class="shrink-0 badge badge-green">Done</span>
                        </div>
                    </div>
                </div>
                <div class="swiper-slide">
                    <div class="card">
                        <div class="card-body flex items-center gap-3 p-3">
                            <img src="assets/images/avatar/user-5.png" alt="" class="size-12 rounded-md shrink-0">
                            <div class="grow">
                                <h6 class="mb-0.5">Elizabeth Martinez</h6>
                                <p class="text-gray-500 fs-sm">28 Feb, 2025</p>
                            </div>
                            <h6 class="grow">$548.69</h6>
                            <span class="shrink-0 badge badge-green">Done</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-span-12 2xl:col-span-4 2xl:row-span-2 order-6 2xl:order-5 card">
        <div class="card-header flex items-center gap-3">
            <h6 class="card-title grow">Top Selling Products</h6>
            <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown shrink-0">
                <button x-ref="button" x-on:click="toggle()" title="dropdown-button" :aria-expanded="open.toString()" type="button" class="flex items-center text-gray-500 dark:text-dark-500">
                    <i data-lucide="ellipsis" class="size-5"></i>
                </button>
                <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden" dropdown-position="right">
                    <ul>
                        <li>
                            <a href="#!" class="dropdown-item">
                                <span>Revenue</span>
                            </a>
                        </li>
                        <li>
                            <a href="#!" class="dropdown-item">
                                <span>Sales</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="card-body pt-0">
            <div class="overflow-x-auto table-box">
                <table class="table text-whitespace">
                    <tbody class="divide-y">
                        <tr class="hover:bg-gray-50 dark:hover:bg-dark-850">
                            <td>
                                <div class="flex items-center gap-3">
                                    <div class="size-10 bg-gray-100 dark:bg-dark-850 rounded-lg flex items-center justify-center">
                                        <img src="assets/images/pos/img-06.png" alt="" class="h-8">
                                    </div>
                                    <div>
                                        <a href="#!">
                                            <h6>Triple Cheese Bacon Supreme</h6>
                                        </a>
                                        <p class="text-sm text-gray-500 dark:text-dark-500">SKU: SRB-041</p>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <p class="font-medium">1,234</p>
                                <p class="text-sm text-gray-500 dark:text-dark-500">Sales</p>
                            </td>
                            <td>
                                <p class="font-medium">$12,340</p>
                                <p class="text-sm text-green-600">+12.5%</p>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50 dark:hover:bg-dark-850">
                            <td>
                                <div class="flex items-center gap-3">
                                    <div class="size-10 bg-gray-100 dark:bg-dark-850 rounded-lg flex items-center justify-center">
                                        <img src="assets/images/pos/img-14.png" alt="" class="h-8">
                                    </div>
                                    <div>
                                        <a href="#!">
                                            <h6>Prosciutto Fig Balsamic Glaze</h6>
                                        </a>
                                        <p class="text-sm text-gray-500 dark:text-dark-500">SKU: SRB-029</p>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <p class="font-medium">856</p>
                                <p class="text-sm text-gray-500 dark:text-dark-500">units</p>
                            </td>
                            <td>
                                <p class="font-medium">$8,560</p>
                                <p class="text-sm text-green-600">+8.3%</p>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50 dark:hover:bg-dark-850">
                            <td>
                                <div class="flex items-center gap-3">
                                    <div class="size-10 bg-gray-100 dark:bg-dark-850 rounded-lg flex items-center justify-center">
                                        <img src="assets/images/pos/img-21.png" alt="" class="h-8">
                                    </div>
                                    <div>
                                        <a href="#!">
                                            <h6>Honey Ginger Spice Mojito</h6>
                                        </a>
                                        <p class="text-sm text-gray-500 dark:text-dark-500">SKU: SRB-086</p>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <p class="font-medium">654</p>
                                <p class="text-sm text-gray-500 dark:text-dark-500">units</p>
                            </td>
                            <td>
                                <p class="font-medium">$3,270</p>
                                <p class="text-sm text-red-600">-2.1%</p>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50 dark:hover:bg-dark-850">
                            <td>
                                <div class="flex items-center gap-3">
                                    <div class="size-10 bg-gray-100 dark:bg-dark-850 rounded-lg flex items-center justify-center">
                                        <img src="assets/images/pos/img-11.png" alt="" class="h-8">
                                    </div>
                                    <div>
                                        <a href="#!">
                                            <h6>Smoky Chipotle Cilantro Rice</h6>
                                        </a>
                                        <p class="text-sm text-gray-500 dark:text-dark-500">SKU: SRB-093</p>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <p class="font-medium">573</p>
                                <p class="text-sm text-gray-500 dark:text-dark-500">units</p>
                            </td>
                            <td>
                                <p class="font-medium">$3,270</p>
                                <p class="text-sm text-red-600">-2.1%</p>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50 dark:hover:bg-dark-850 *:!border-b-none">
                            <td>
                                <div class="flex items-center gap-3">
                                    <div class="size-10 bg-gray-100 dark:bg-dark-850 rounded-lg flex items-center justify-center">
                                        <img src="assets/images/pos/img-18.png" alt="" class="h-8">
                                    </div>
                                    <div>
                                        <a href="#!">
                                            <h6>Black Bean Southwest Fiesta</h6>
                                        </a>
                                        <p class="text-sm text-gray-500 dark:text-dark-500">SKU: SRB-001</p>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <p class="font-medium">415</p>
                                <p class="text-sm text-gray-500 dark:text-dark-500">units</p>
                            </td>
                            <td>
                                <p class="font-medium">$3,270</p>
                                <p class="text-sm text-red-600">-2.1%</p>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50 dark:hover:bg-dark-850">
                            <td>
                                <div class="flex items-center gap-3">
                                    <div class="size-10 bg-gray-100 dark:bg-dark-850 rounded-lg flex items-center justify-center">
                                        <img src="assets/images/pos/img-11.png" alt="" class="h-8">
                                    </div>
                                    <div>
                                        <a href="#!">
                                            <h6>Smoky Chipotle Cilantro Rice</h6>
                                        </a>
                                        <p class="text-sm text-gray-500 dark:text-dark-500">SKU: SRB-093</p>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <p class="font-medium">573</p>
                                <p class="text-sm text-gray-500 dark:text-dark-500">units</p>
                            </td>
                            <td>
                                <p class="font-medium">$3,270</p>
                                <p class="text-sm text-red-600">-2.1%</p>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="text-center pt-4">
                <a href="#!" class="link link-primary">Show More Products <i data-lucide="move-right" class="inline-block size-4 ltr:ml-1 rtl:mr-1"></i></a>
            </div>
        </div>
    </div>
    <div class="col-span-12 2xl:col-span-8 order-5 2xl:order-6 card">
        <div class="card-header flex items-center gap-3" x-data="stackedColumnApp" dir="ltr">
            <h6 class="card-title grow">Sales Trade</h6>
            <div class="flex gap-2 shrink-0">
                <button type="button" class="btn btn-sm" :class="isActive('lastMonth') ? 'btn-primary' : 'btn-sub-primary'" @click="switchPeriod('lastMonth')">
                    Last Month
                </button>
                <button type="button" class="btn btn-sm" :class="isActive('thisMonth') ? 'btn-primary' : 'btn-sub-primary'" @click="switchPeriod('thisMonth')">
                    This Month
                </button>
            </div>
        </div>
        <div class="card-body">
            <div class="!min-h-full" data-chart-colors="[bg-primary-500, bg-green-500, bg-primary-100, bg-green-100]" x-ref="stackedColumnChart"></div>
        </div>
    </div>
    <div class="col-span-12 md:col-span-6 2xl:col-span-4 order-7 card">
        <div class="card-header flex items-center gap-3">
            <h6 class="card-title grow">Customers</h6>
            <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown shrink-0">
                <button x-ref="button" x-on:click="toggle()" :aria-expanded="open.toString()" type="button" class="flex px-3 py-1.5 text-xs border-gray-200 font-medium dark:border-dark-800 link link-primary btn">
                    <span x-text="$store.customers.period.charAt(0).toUpperCase() + $store.customers.period.slice(1)">Recent</span>
                    <svg :class="{ 'transform rotate-180': open }" class="transition-transform duration-300 ltr:ml-1 rtl:mr-1 size-4" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                    </svg>
                </button>
                <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden" dropdown-position="right">
                    <ul>
                        <li>
                            <a href="#!" class="dropdown-item" @click="$store.customers.setPeriod('recent'); close()">
                                <span>Recent</span>
                            </a>
                        </li>
                        <li>
                            <a href="#!" class="dropdown-item" @click="$store.customers.setPeriod('weekly'); close()">
                                <span>Weekly</span>
                            </a>
                        </li>
                        <li>
                            <a href="#!" class="dropdown-item" @click="$store.customers.setPeriod('monthly'); close()">
                                <span>Monthly</span>
                            </a>
                        </li>
                        <li>
                            <a href="#!" class="dropdown-item" @click="$store.customers.setPeriod('yearly'); close()">
                                <span>Yearly</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div x-data="customersApp" dir="ltr">
                <div class="!min-h-full" data-chart-colors="[bg-purple-500]" x-ref="customersCharts"></div>
            </div>
            <div class="space-y-4 mt-6">
                <div class="flex items-center gap-2">
                    <div class="size-10 flex items-center justify-center bg-primary-500/10 text-primary-500 rounded-full shrink-0">
                        <i data-lucide="users" class="size-5"></i>
                    </div>
                    <div class="grow">
                        <h6>New Customers</h6>
                        <p class="text-sm text-gray-500 dark:text-dark-500">2,450 total</p>
                    </div>
                    <p class="text-green-500">+12%</p>
                </div>
                <div class="flex items-center gap-2">
                    <div class="size-10 flex items-center justify-center bg-green-500/10 text-green-500 rounded-full shrink-0">
                        <i data-lucide="user-check" class="size-5"></i>
                    </div>
                    <div class="grow">
                        <h6>Active Customers</h6>
                        <p class="text-sm text-gray-500 dark:text-dark-500">1,280 total</p>
                    </div>
                    <p class="text-green-500">65%</p>
                </div>
                <div class="flex items-center gap-2">
                    <div class="size-10 flex items-center justify-center bg-yellow-500/10 text-yellow-500 rounded-full shrink-0">
                        <i data-lucide="user-plus" class="size-5"></i>
                    </div>
                    <div class="grow">
                        <h6>Returning Customers</h6>
                        <p class="text-sm text-gray-500 dark:text-dark-500">856 total</p>
                    </div>
                    <p class="text-yellow-500">35%</p>
                </div>
                <div class="flex items-center gap-2">
                    <div class="size-10 flex items-center justify-center bg-red-500/10 text-red-500 rounded-full shrink-0">
                        <i data-lucide="user-minus" class="size-5"></i>
                    </div>
                    <div class="grow">
                        <h6>Inactive Customers</h6>
                        <p class="text-sm text-gray-500 dark:text-dark-500">320 total</p>
                    </div>
                    <p class="text-red-500">15%</p>
                </div>
            </div>
        </div>
    </div>
    <div class="col-span-12 2xl:col-span-4 order-9 2xl:order-8 card">
        <div class="card-header flex items-center gap-3">
            <h6 class="card-title grow">Products Stock</h6>
            <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown shrink-0">
                <button x-ref="button" x-on:click="toggle()" title="dropdown-button" :aria-expanded="open.toString()" type="button" class="flex items-center text-gray-500 dark:text-dark-500">
                    <i data-lucide="ellipsis" class="size-5"></i>
                </button>
                <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden" dropdown-position="right">
                    <ul>
                        <li>
                            <a href="#!" class="dropdown-item">
                                <span>Low to High</span>
                            </a>
                        </li>
                        <li>
                            <a href="#!" class="dropdown-item">
                                <span>High to Low</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="space-y-4">
                <div class="flex items-center gap-3">
                    <div class="size-14 bg-gray-100 dark:bg-dark-850 rounded-lg flex items-center justify-center shrink-0">
                        <img src="assets/images/pos/img-02.png" alt="" class="h-10">
                    </div>
                    <div class="grow">
                        <div class="flex items-center justify-between mb-1">
                            <h6 class="mb-0">Coffee milk Latte Tea Cappuccino</h6>
                            <span class="text-sm font-medium">85%</span>
                        </div>
                        <div class="progress-bar progress-1">
                            <div class="w-[80%] text-white progress-bar-wrap bg-green-500"></div>
                        </div>
                        <div class="flex items-center justify-between mt-1 *:text-sm *:text-gray-500 *:text-dark-500">
                            <p>In Stock: 425</p>
                            <p>Total: 500</p>
                        </div>
                    </div>
                </div>
                <div class="flex items-center gap-3">
                    <div class="size-14 bg-gray-100 dark:bg-dark-850 rounded-lg flex items-center justify-center shrink-0">
                        <img src="assets/images/pos/img-20.png" alt="" class="h-10">
                    </div>
                    <div class="grow">
                        <div class="flex items-center justify-between mb-1">
                            <h6 class="mb-0">Classic Lime Mint Refresher</h6>
                            <span class="text-sm font-medium">45%</span>
                        </div>
                        <div class="progress-bar progress-1">
                            <div class="w-[45%] text-white progress-bar-wrap bg-yellow-500"></div>
                        </div>
                        <div class="flex items-center justify-between mt-1 *:text-sm *:text-gray-500 *:text-dark-500">
                            <p>In Stock: 225</p>
                            <p>Total: 500</p>
                        </div>
                    </div>
                </div>
                <div class="flex items-center gap-3">
                    <div class="size-14 bg-gray-100 dark:bg-dark-850 rounded-lg flex items-center justify-center shrink-0">
                        <img src="assets/images/pos/img-22.png" alt="" class="h-10">
                    </div>
                    <div class="grow">
                        <div class="flex items-center justify-between mb-1">
                            <h6 class="mb-0">Grapefruit Rosemary Citrus Fizz</h6>
                            <span class="text-sm font-medium">15%</span>
                        </div>
                        <div class="progress-bar progress-1">
                            <div class="w-[15%] text-white progress-bar-wrap bg-red-500"></div>
                        </div>
                        <div class="flex items-center justify-between mt-1 *:text-sm *:text-gray-500 *:text-dark-500">
                            <p>In Stock: 75</p>
                            <p>Total: 500</p>
                        </div>
                    </div>
                </div>
                <div class="flex items-center gap-3">
                    <div class="size-14 bg-gray-100 dark:bg-dark-850 rounded-lg flex items-center justify-center shrink-0">
                        <img src="assets/images/pos/img-17.png" alt="" class="h-10">
                    </div>
                    <div class="grow">
                        <div class="flex items-center justify-between mb-1">
                            <h6 class="mb-0">Gourmet Truffle Umami Explosion</h6>
                            <span class="text-sm font-medium">60%</span>
                        </div>
                        <div class="progress-bar progress-1">
                            <div class="w-[60%] text-white progress-bar-wrap bg-green-500"></div>
                        </div>
                        <div class="flex items-center justify-between mt-1 *:text-sm *:text-gray-500 *:text-dark-500">
                            <p>In Stock: 300</p>
                            <p>Total: 500</p>
                        </div>
                    </div>
                </div>
                <div class="flex items-center gap-3">
                    <div class="size-14 bg-gray-100 dark:bg-dark-850 rounded-lg flex items-center justify-center shrink-0">
                        <img src="assets/images/pos/img-18.png" alt="" class="h-10">
                    </div>
                    <div class="grow">
                        <div class="flex items-center justify-between mb-1">
                            <h6 class="mb-0">Black Bean Southwest</h6>
                            <span class="text-sm font-medium">92%</span>
                        </div>
                        <div class="progress-bar progress-1">
                            <div class="w-[92%] text-white progress-bar-wrap bg-green-500"></div>
                        </div>
                        <div class="flex items-center justify-between mt-1 *:text-sm *:text-gray-500 *:text-dark-500">
                            <p>In Stock: 460</p>
                            <p>Total: 500</p>
                        </div>
                    </div>
                </div>
                <div class="flex items-center gap-3">
                    <div class="size-14 bg-gray-100 dark:bg-dark-850 rounded-lg flex items-center justify-center shrink-0">
                        <img src="assets/images/pos/img-04.png" alt="" class="h-10">
                    </div>
                    <div class="grow">
                        <div class="flex items-center justify-between mb-1">
                            <h6 class="mb-0">Peppermint Mocha Holiday Cheer</h6>
                            <span class="text-sm font-medium">28%</span>
                        </div>
                        <div class="w-full bg-gray-100 dark:bg-dark-850 rounded-full h-2">
                            <div class="bg-yellow-500 h-2 rounded-full" style="width: 28%"></div>
                        </div>
                        <div class="flex items-center justify-between mt-1 *:text-sm *:text-gray-500 *:text-dark-500">
                            <p>In Stock: 140</p>
                            <p>Total: 500</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-span-12 md:col-span-6 2xl:col-span-4 order-8 2xl:order-9 card">
        <div class="card-header flex items-center gap-3">
            <h6 class="card-title grow">Payment Types</h6>
            <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown shrink-0">
                <button x-ref="button" x-on:click="toggle()" title="dropdown-button" :aria-expanded="open.toString()" type="button" class="flex items-center text-gray-500 dark:text-dark-500">
                    <i data-lucide="ellipsis" class="size-5"></i>
                </button>
                <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden" dropdown-position="right">
                    <ul>
                        <li>
                            <a href="#!" class="dropdown-item">
                                <span>Weekly</span>
                            </a>
                        </li>
                        <li>
                            <a href="#!" class="dropdown-item">
                                <span>Monthly</span>
                            </a>
                        </li>
                        <li>
                            <a href="#!" class="dropdown-item">
                                <span>Yearly</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div x-data="paymentTypesApp" dir="ltr">
                <div class="!min-h-full" data-chart-colors="[bg-primary-500, bg-yellow-500, bg-sky-500, bg-red-500, bg-green-500]" x-ref="paymentTypesChart"></div>
            </div>

            <div class="grid grid-cols-2 mt-5 *:flex *:items-center *:gap-2 gap-4">
                <a href="#!" @click="paymentTypesChart.toggleSeries('Cash')" class="payment-legend-item" data-label="Cash">
                    <i data-lucide="squares-unite" class="size-5 stroke-1 fill-primary-500/20 stroke-primary-500"></i>
                    <span>Cash</span>
                    <span class="text-sm text-gray-500 dark:text-dark-500">$974</span>
                </a>
                <a href="#!" @click="paymentTypesChart.toggleSeries('Credit Card')" class="payment-legend-item" data-label="Credit Card">
                    <i data-lucide="squares-unite" class="size-5 stroke-1 fill-yellow-500/20 stroke-yellow-500"></i>
                    <span>Card</span>
                    <span class="text-sm text-gray-500 dark:text-dark-500">$750</span>
                </a>
                <a href="#!" @click="paymentTypesChart.toggleSeries('PayPal')" class="payment-legend-item" data-label="PayPal">
                    <i data-lucide="squares-unite" class="size-5 stroke-1 fill-sky-500/20 stroke-sky-500"></i>
                    <span>PayPal</span>
                    <span class="text-sm text-gray-500 dark:text-dark-500">$510</span>
                </a>
                <a href="#!" @click="paymentTypesChart.toggleSeries('Bank Transfer')" class="payment-legend-item" data-label="Bank Transfer">
                    <i data-lucide="squares-unite" class="size-5 stroke-1 fill-red-500/20 stroke-red-500"></i>
                    <span>Bank Transfer</span>
                    <span class="text-sm text-gray-500 dark:text-dark-500">$370</span>
                </a>
                <a href="#!" @click="paymentTypesChart.toggleSeries('Other')" class="payment-legend-item" data-label="Other">
                    <i data-lucide="squares-unite" class="size-5 stroke-1 fill-green-500/20 stroke-green-500"></i>
                    <span>Other</span>
                    <span class="text-sm text-gray-500 dark:text-dark-500">$250</span>
                </a>
            </div>
        </div>
    </div>
    <div class="col-span-12 2xl:col-span-3 order-10 card">
        <div class="card-body p-3">
            <div class="p-4 bg-gradient-to-br from-primary-500 to-purple-500 text-white rounded-md relative">
                <i data-lucide="coins" class="size-8 fill-white/20 absolute right-4 top-4"></i>
                <p class="mb-2">Total Balance</p>
                <h3>$<span x-data="animatedCounter(9475, 500, 0)" x-init="updateCounter" x-text="Math.round(current)"></span></h3>
            </div>
            <div class="my-3 flex items-center gap-2">
                <p class="text-gray-500 dark:text-dark-500 grow">Recent</p>
                <a href="#!" class="link link-primary shrink-0 text-sm">View More</a>
            </div>
            <div class="h-[205px] -mx-3 px-3" data-simplebar>
                <div class="space-y-2">
                    <div class="flex items-center gap-2">
                        <div class="size-10 bg-gray-100 rounded flex items-center justify-center text-gray-500 dark:bg-dark-850 dark:text-dark-500 shrink-0">
                            <i data-lucide="credit-card" class="size-5"></i>
                        </div>
                        <div class="grow">
                            <h6 class="line-clamp-1">Credit Card Payment</h6>
                            <p class="text-11 text-gray-500 dark:text-dark-500">Today, 02:45 PM</p>
                        </div>
                        <h6 class="shrink-0 text-green-500">+$128.75</h6>
                    </div>
                    <div class="flex items-center gap-2">
                        <div class="size-10 bg-gray-100 rounded flex items-center justify-center text-gray-500 dark:bg-dark-850 dark:text-dark-500 shrink-0">
                            <i data-lucide="wallet" class="size-5"></i>
                        </div>
                        <div class="grow">
                            <h6 class="line-clamp-1">Cash Payment</h6>
                            <p class="text-11 text-gray-500 dark:text-dark-500">Today, 01:30 PM</p>
                        </div>
                        <h6 class="shrink-0 text-green-500">+$85.50</h6>
                    </div>
                    <div class="flex items-center gap-2">
                        <div class="size-10 bg-gray-100 rounded flex items-center justify-center text-gray-500 dark:bg-dark-850 dark:text-dark-500 shrink-0">
                            <i data-lucide="smartphone" class="size-5"></i>
                        </div>
                        <div class="grow">
                            <h6 class="line-clamp-1">Mobile Payment</h6>
                            <p class="text-11 text-gray-500 dark:text-dark-500">Today, 12:15 PM</p>
                        </div>
                        <h6 class="shrink-0 text-red-500">-$45.99</h6>
                    </div>
                    <div class="flex items-center gap-2">
                        <div class="size-10 bg-gray-100 rounded flex items-center justify-center text-gray-500 dark:bg-dark-850 dark:text-dark-500 shrink-0">
                            <i data-lucide="banknote" class="size-5"></i>
                        </div>
                        <div class="grow">
                            <h6 class="line-clamp-1">Bank Transfer</h6>
                            <p class="text-11 text-gray-500 dark:text-dark-500">Today, 11:00 AM</p>
                        </div>
                        <h6 class="shrink-0 text-red-500">-$250.00</h6>
                    </div>
                    <div class="flex items-center gap-2">
                        <div class="size-10 bg-gray-100 rounded flex items-center justify-center text-gray-500 dark:bg-dark-850 dark:text-dark-500 shrink-0">
                            <i data-lucide="wallet" class="size-5"></i>
                        </div>
                        <div class="grow">
                            <h6 class="line-clamp-1">Cash Payment</h6>
                            <p class="text-11 text-gray-500 dark:text-dark-500">Today, 10:49 AM</p>
                        </div>
                        <h6 class="shrink-0 text-green-500">+$178.14</h6>
                    </div>
                    <div class="flex items-center gap-2">
                        <div class="size-10 bg-gray-100 rounded flex items-center justify-center text-gray-500 dark:bg-dark-850 dark:text-dark-500 shrink-0">
                            <i data-lucide="wallet" class="size-5"></i>
                        </div>
                        <div class="grow">
                            <h6 class="line-clamp-1">Cash Payment</h6>
                            <p class="text-11 text-gray-500 dark:text-dark-500">Today, 10:30 AM</p>
                        </div>
                        <h6 class="shrink-0 text-green-500">+$224.93</h6>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-span-12 2xl:col-span-9 order-11">
        <div class="card">
            <div class="card-header flex items-center gap-3">
                <h6 class="card-title grow">All Revenue</h6>
                <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown shrink-0">
                    <button x-ref="button" x-on:click="toggle()" :aria-expanded="open.toString()" type="button" class="flex px-3 py-1.5 text-xs border-gray-200 font-medium dark:border-dark-800 link link-primary btn">
                        Recent
                        <svg :class="{ 'transform rotate-180': open }" class="transition-transform duration-300 ltr:ml-1 rtl:mr-1 size-4" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                        </svg>
                    </button>
                    <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden" dropdown-position="right">
                        <ul>
                            <li>
                                <a href="#!" class="dropdown-item">
                                    <span>Recent</span>
                                </a>
                            </li>
                            <li>
                                <a href="#!" class="dropdown-item">
                                    <span>Weekly</span>
                                </a>
                            </li>
                            <li>
                                <a href="#!" class="dropdown-item">
                                    <span>Monthly</span>
                                </a>
                            </li>
                            <li>
                                <a href="#!" class="dropdown-item">
                                    <span>Yearly</span>
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="grid grid-cols-12 gap-0 items-center">
                    <div class="col-span-12 md:col-span-6 lg:col-span-4 p-5 md:last:pr-0 md:first:pl-0">
                        <div class="flex items-center gap-2 mb-3">
                            <i data-lucide="dollar-sign" class="size-5 text-primary-500"></i>
                            <p class="text-gray-500 dark:text-dark-500">Total Revenue</p>
                        </div>
                        <h4>$1,245,750</h4>
                        <div class="progress-bar progress-2 mt-2">
                            <div class="w-[74%] text-white progress-bar-wrap bg-primary-500"></div>
                        </div>
                        <div class="flex items-center gap-2 text-sm mt-1 text-gray-500 dark:text-dark-500">
                            <div class="flex items-center gap-1 grow">
                                <i data-lucide="target" class="size-4"></i>
                                <p>Target Progress</p>
                            </div>
                            <span class="shrink-0">74%</span>
                        </div>
                        <div class="flex items-center gap-2 mt-3">
                            <i data-lucide="shopping-cart" class="size-4 text-gray-500"></i>
                            <p class="text-gray-500 dark:text-dark-500">1,845 Orders</p>
                        </div>
                    </div>
                    <div class="col-span-12 md:col-span-6 lg:col-span-4 p-5 md:last:pr-0 md:first:pl-0 border-y md:border-y-0 md:border-l lg:border-r border-gray-200 dark:border-dark-800">
                        <div class="flex items-center gap-2 mb-3">
                            <i data-lucide="utensils" class="size-5 text-primary-500"></i>
                            <p class="text-gray-500 dark:text-dark-500">Dine In</p>
                        </div>
                        <h4>$785,320</h4>
                        <div class="progress-bar progress-2 mt-2">
                            <div class="w-[63%] text-white progress-bar-wrap bg-primary-500"></div>
                        </div>
                        <div class="flex items-center gap-2 text-sm mt-1 text-gray-500 dark:text-dark-500">
                            <div class="flex items-center gap-1 grow">
                                <i data-lucide="target" class="size-4"></i>
                                <p>Target Progress</p>
                            </div>
                            <span class="shrink-0">63%</span>
                        </div>
                        <div class="flex items-center gap-2 mt-3">
                            <i data-lucide="shopping-cart" class="size-4 text-gray-500"></i>
                            <p class="text-gray-500 dark:text-dark-500">1,152 Orders</p>
                        </div>
                    </div>
                    <div class="col-span-12 md:col-span-12 lg:col-span-4 p-5 lg:last:pr-0 md:first:pl-0">
                        <div class="flex items-center gap-2 mb-3">
                            <i data-lucide="package" class="size-5 text-primary-500"></i>
                            <p class="text-gray-500 dark:text-dark-500">Takeaway</p>
                        </div>
                        <h4>$460,430</h4>
                        <div class="progress-bar progress-2 mt-2">
                            <div class="w-[37%] text-white progress-bar-wrap bg-primary-500"></div>
                        </div>
                        <div class="flex items-center gap-2 text-sm mt-1 text-gray-500 dark:text-dark-500">
                            <div class="flex items-center gap-1 grow">
                                <i data-lucide="target" class="size-4"></i>
                                <p>Target Progress</p>
                            </div>
                            <span class="shrink-0">37%</span>
                        </div>
                        <div class="flex items-center gap-2 mt-3">
                            <i data-lucide="shopping-cart" class="size-4 text-gray-500"></i>
                            <p class="text-gray-500 dark:text-dark-500">693 Orders</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="alert flex gap-2 border-none bg-gradient-to-r from-red-500/20 to-yellow-500/20 mb-5">
            <i data-lucide="circle-alert" class="size-4 shrink-0 mt-0.5"></i>
            <div class="grow">
                <p class="mb-1">Low stock alert: Product #SRB-041 (Triple Cheese Bacon Supreme) is running low with only 5 units remaining.</p>
                <a href="#!" class="link text-red-500">Restock inventory now</a>
            </div>
        </div>
    </div>
    <div class="col-span-12 order-12 card" x-data="productsTable()">
        <div class="card-header flex md:items-center gap-3 flex-col md:flex-row">
            <h6 class="card-title grow">Recent Orders</h6>
            <div class="relative group/form shrink-0">
                <input type="text" x-model="searchTerm" @input="filteredProducts()" class="ltr:pl-9 rtl:pr-9 form-input ltr:group-[&.right]/form:pr-9 rtl:group-[&.right]/form:pl-9 ltr:group-[&.right]/form:pl-4 rtl:group-[&.right]/form:pr-4" placeholder="Search for ...">
                <button title="search-button" class="absolute inset-y-0 flex items-center text-gray-500 dark:text-dark-500 ltr:left-3 rtl:right-3 ltr:group-[&.right]/form:right-3 rtl:group-[&.right]/form:left-3 ltr:group-[&.right]/form:left-auto rtl:group-[&.right]/form:right-auto focus:outline-hidden">
                    <i data-lucide="search" class="size-4"></i>
                </button>
            </div>
            <div class="shrink-0">
                <a href="#!" class="btn btn-primary"><i data-lucide="circle-plus" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> Add Orders</a>
            </div>
        </div>
        <div class="card-header flex items-center gap-3 flex-wrap *:py-2 *:px-4 *:rounded-md">
            <button @click="changeTab('Dine In')" :class="{'bg-primary-500 text-white': activeTab === 'Dine In', 'bg-gray-100 dark:bg-dark-850 text-gray-500 dark:text-dark-500 hover:text-primary-500': activeTab !== 'Dine In'}">Dine In</button>
            <button @click="changeTab('Takeaway')" :class="{'bg-primary-500 text-white': activeTab === 'Takeaway', 'bg-gray-100 dark:bg-dark-850 text-gray-500 dark:text-dark-500 hover:text-primary-500': activeTab !== 'Takeaway'}">Takeaway</button>
            <button @click="changeTab('Delivered')" :class="{'bg-primary-500 text-white': activeTab === 'Delivered', 'bg-gray-100 dark:bg-dark-850 text-gray-500 dark:text-dark-500 hover:text-primary-500': activeTab !== 'Delivered'}">Delivered</button>
        </div>
        <div class="card-body pt-0">
            <div class="overflow-x-auto table-box">
                <table class="table whitespace-nowrap">
                    <tbody>
                        <tr class="text-gray-500 bg-gray-100 dark:bg-dark-850 dark:text-dark-500">
                            <th x-on:click="sort('ordersID')" class="cursor-pointer !font-medium">Order ID <span x-show="sortBy === 'ordersID'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                            <th x-on:click="sort('orderDate')" class="cursor-pointer !font-medium">Order Date <span x-show="sortBy === 'orderDate'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                            <th x-on:click="sort('productName')" class="cursor-pointer !font-medium">Product Name <span x-show="sortBy === 'productName'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                            <th x-on:click="sort('customersName')" class="cursor-pointer !font-medium">Customers <span x-show="sortBy === 'customersName'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                            <th x-on:click="sort('quantity')" class="cursor-pointer !font-medium">QTY <span x-show="sortBy === 'quantity'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                            <th x-on:click="sort('price')" class="cursor-pointer !font-medium">Amount <span x-show="sortBy === 'price'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                            <th x-on:click="sort('paymentMethod')" class="cursor-pointer !font-medium">Payment Method <span x-show="sortBy === 'paymentMethod'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                            <th x-show="activeTab === 'Dine In'" x-on:click="sort('tableNumber')" class="cursor-pointer !font-medium">Table Number <span x-show="sortBy === 'tableNumber'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                            <th x-show="activeTab === 'Takeaway'" x-on:click="sort('pickupTime')" class="cursor-pointer !font-medium">Pickup Time <span x-show="sortBy === 'pickupTime'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                            <th x-show="activeTab === 'Delivered'" x-on:click="sort('deliveryAddress')" class="cursor-pointer !font-medium">Delivery Address <span x-show="sortBy === 'deliveryAddress'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                        </tr>
                        <template x-if="displayedProducts.length > 0">
                            <template x-for="(products, index) in displayedProducts" :key="index">
                                <tr>
                                    <td x-text="products.ordersID"></td>
                                    <td x-text="products.orderDate"></td>
                                    <td><a href="apps-ecommerce-product-overview.html" x-text="products.productName"></a></td>
                                    <td x-text="products.customersName"></td>
                                    <td x-text="products.quantity"></td>
                                    <td x-text="products.price"></td>
                                    <td>
                                        <span x-text="products.paymentMethod" :class="{
                                            'badge badge-purple': products.paymentMethod === 'Cash',
                                            'badge badge-yellow': products.paymentMethod === 'Debit Card',
                                            'badge badge-orange': products.paymentMethod === 'Credit Card',
                                            'badge badge-green': products.paymentMethod === 'Bank Transfer'
                                        }"></span>
                                    </td>
                                    <td x-show="activeTab === 'Dine In'" x-text="products.tableNumber"></td>
                                    <td x-show="activeTab === 'Takeaway'" x-text="products.pickupTime"></td>
                                    <td x-show="activeTab === 'Delivered'" x-text="products.deliveryAddress"></td>
                                </tr>
                            </template>
                        </template>
                        <tr>
                            <template x-if="displayedProducts.length == 0">
                                <td colspan="10" class="!p-8">
                                    <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" class="mx-auto size-12" viewBox="0 0 48 48">
                                        <linearGradient id="SVGID_1__h35ynqzIJzH4_gr1" x1="34.598" x2="15.982" y1="15.982" y2="34.598" gradientUnits="userSpaceOnUse">
                                            <stop offset="0" stop-color="#60e8fe"></stop>
                                            <stop offset=".033" stop-color="#6ae9fe"></stop>
                                            <stop offset=".197" stop-color="#97f0fe"></stop>
                                            <stop offset=".362" stop-color="#bdf5ff"></stop>
                                            <stop offset=".525" stop-color="#dafaff"></stop>
                                            <stop offset=".687" stop-color="#eefdff"></stop>
                                            <stop offset=".846" stop-color="#fbfeff"></stop>
                                            <stop offset="1" stop-color="#fff"></stop>
                                        </linearGradient>
                                        <path fill="url(#SVGID_1__h35ynqzIJzH4_gr1)" d="M40.036,33.826L31.68,25.6c0.847-1.739,1.335-3.684,1.335-5.748c0-7.27-5.894-13.164-13.164-13.164	S6.688,12.582,6.688,19.852c0,7.27,5.894,13.164,13.164,13.164c2.056,0,3.995-0.485,5.728-1.326l3.914,4.015l4.331,4.331	c1.715,1.715,4.496,1.715,6.211,0C41.751,38.321,41.751,35.541,40.036,33.826z"></path>
                                        <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M31.95,25.739l8.086,8.086c1.715,1.715,1.715,4.496,0,6.211l0,0c-1.715,1.715-4.496,1.715-6.211,0	l-4.331-4.331"></path>
                                        <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M7.525,24.511c-1.771-4.694-0.767-10.196,3.011-13.975c3.847-3.847,9.48-4.817,14.228-2.912"></path>
                                        <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M30.856,12.603c3.376,5.114,2.814,12.063-1.688,16.565c-4.858,4.858-12.565,5.129-17.741,0.814"></path>
                                    </svg>
                                    <p class="mt-2 text-center text-gray-500 dark:text-dark-500">No matching records found</p>
                                </td>
                            </template>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="grid items-center grid-cols-12 gap-space mt-space" x-show="displayedProducts.length > 0">
                <div class="col-span-12 text-center lg:col-span-6 lg:ltr:text-left lg:rtl:text-right">
                    <p class="text-gray-500 dark:text-dark-500">Showing <b x-text="showingStart"></b> - <b x-text="showingEnd"></b> of <b x-text="filterProducts.length"></b> Results</p>
                </div>
                <div class="col-span-12 lg:col-span-6">
                    <div class="flex justify-center gap-2 lg:justify-end pagination pagination-primary">
                        <button @click="prevPage()" :disabled="currentPage === 1" class="pagination-pre">
                            <i data-lucide="chevron-left" class="mr-1 ltr:inline-block rtl:hidden size-4"></i>
                            <i data-lucide="chevron-right" class="ml-1 rtl:inline-block ltr:hidden size-4"></i>
                            Prev
                        </button>
                        <template x-for="page in totalPages" :key="page">
                            <button @click="gotoPage(page)" :class="{ 'active': currentPage === page }" class="pagination-item">
                                <span x-text="page"></span>
                            </button>
                        </template>
                        <button @click="nextPage()" :disabled="currentPage === totalPages" class="pagination-next">
                            Next
                            <i data-lucide="chevron-right" class="ml-1 ltr:inline-block rtl:hidden size-4"></i>
                            <i data-lucide="chevron-left" class="mr-1 rtl:inline-block ltr:hidden size-4"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

</div>
{{> partials/footer }}
</div>

{{> partials/vendor-scripts }}

<script type="module" src="assets/js/dashboards/pos-admin.init.js"></script>

<script type="module" src="assets/js/main.js"></script>
</body>

</html>