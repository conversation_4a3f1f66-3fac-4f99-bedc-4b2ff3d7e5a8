import user1 from '/assets/images/avatar/user-1.png'
import user2 from '/assets/images/avatar/user-2.png'
import user3 from '/assets/images/avatar/user-3.png'
import user4 from '/assets/images/avatar/user-4.png'
import user5 from '/assets/images/avatar/user-5.png'
import user6 from '/assets/images/avatar/user-6.png'
import user7 from '/assets/images/avatar/user-7.png'
import user8 from '/assets/images/avatar/user-8.png'
import user9 from '/assets/images/avatar/user-9.png'
import user10 from '/assets/images/avatar/user-10.png'
import user11 from '/assets/images/avatar/user-11.png'
import user12 from '/assets/images/avatar/user-12.png'
import user13 from '/assets/images/avatar/user-13.png'
import user14 from '/assets/images/avatar/user-14.png'
import user15 from '/assets/images/avatar/user-15.png'
import user18 from '/assets/images/avatar/user-18.png'
import user19 from '/assets/images/avatar/user-19.png'
import user20 from '/assets/images/avatar/user-20.png'
import user21 from '/assets/images/avatar/user-21.png'
import user22 from '/assets/images/avatar/user-22.png'
import user23 from '/assets/images/avatar/user-23.png'
import user24 from '/assets/images/avatar/user-24.png'
import user25 from '/assets/images/avatar/user-25.png'
import user26 from '/assets/images/avatar/user-26.png'
import user27 from '/assets/images/avatar/user-27.png'
import user28 from '/assets/images/avatar/user-28.png'
import user29 from '/assets/images/avatar/user-29.png'
import user30 from '/assets/images/avatar/user-30.png'
import user31 from '/assets/images/avatar/user-31.png'
import user32 from '/assets/images/avatar/user-32.png'
import user33 from '/assets/images/avatar/user-33.png'
import user34 from '/assets/images/avatar/user-34.png'
import user35 from '/assets/images/avatar/user-35.png'
import user36 from '/assets/images/avatar/user-36.png'
import user37 from '/assets/images/avatar/user-37.png'
import user38 from '/assets/images/avatar/user-38.png'
import user39 from '/assets/images/avatar/user-39.png'
import user40 from '/assets/images/avatar/user-40.png'
import user41 from '/assets/images/avatar/user-41.png'
import user42 from '/assets/images/avatar/user-42.png'
import user43 from '/assets/images/avatar/user-43.png'
import user44 from '/assets/images/avatar/user-44.png'
import user45 from '/assets/images/avatar/user-45.png'
import user46 from '/assets/images/avatar/user-46.png'
import user47 from '/assets/images/avatar/user-47.png'

const invoicesListData = [
    {
        "image": user1,
        "clientName": "John Doe",
        "content": "Front End Development",
        "country": "New York",
        "invoiceDate": "18 May, 2024",
        "dueDate": "23 May, 2024",
        "amount": "$154.98",
        "status": "Paid"
    },
    {
        "image": user2,
        "clientName": "Jane Smith",
        "content": "Laravel Development",
        "country": "California",
        "invoiceDate": "19 May, 2024",
        "dueDate": "24 May, 2024",
        "amount": "$200.00",
        "status": "Unpaid"
    },
    {
        "image": user3,
        "clientName": "Alice Johnson",
        "content": "Web Designing",
        "country": "Texas",
        "invoiceDate": "20 May, 2024",
        "dueDate": "25 May, 2024",
        "amount": "$350.75",
        "status": "Paid"
    },
    {
        "image": user4,
        "clientName": "Bob Brown",
        "content": "Crop top Sweater Clothing",
        "country": "Florida",
        "invoiceDate": "21 May, 2024",
        "dueDate": "26 May, 2024",
        "amount": "$410.00",
        "status": "Overdue"
    },
    {
        "image": user5,
        "clientName": "Charlie Davis",
        "content": "Bra Lace Crop top",
        "country": "Illinois",
        "invoiceDate": "22 May, 2024",
        "dueDate": "27 May, 2024",
        "amount": "$100.25",
        "status": "Unpaid"
    },
    {
        "image": user6,
        "clientName": "Emily Clark",
        "content": "Asp.Net Services",
        "country": "Ohio",
        "invoiceDate": "23 May, 2024",
        "dueDate": "28 May, 2024",
        "amount": "$250.50",
        "status": "Pending"
    },
    {
        "image": user7,
        "clientName": "David Martinez",
        "content": "Yellow women shoes",
        "country": "Michigan",
        "invoiceDate": "24 May, 2024",
        "dueDate": "29 May, 2024",
        "amount": "$300.00",
        "status": "Paid"
    },
    {
        "image": user8,
        "clientName": "Sophia Garcia",
        "content": "Tote bag Leather Handbag Dolce",
        "country": "Georgia",
        "invoiceDate": "25 May, 2024",
        "dueDate": "30 May, 2024",
        "amount": "$120.75",
        "status": "Pending"
    },
    {
        "image": user9,
        "clientName": "Liam Miller",
        "content": "Chat Application",
        "country": "North Carolina",
        "invoiceDate": "26 May, 2024",
        "dueDate": "31 May, 2024",
        "amount": "$500.50",
        "status": "Paid"
    },
    {
        "image": user10,
        "clientName": "Olivia Wilson",
        "content": "Laravel Development",
        "country": "New Jersey",
        "invoiceDate": "27 May, 2024",
        "dueDate": "01 June, 2024",
        "amount": "$450.00",
        "status": "Unpaid"
    },
    {
        "image": user11,
        "clientName": "Mason Anderson",
        "content": "Straw hat Cap Cowboy hat Sun hat",
        "country": "Virginia",
        "invoiceDate": "28 May, 2024",
        "dueDate": "02 June, 2024",
        "amount": "$330.25",
        "status": "Pending"
    },
    {
        "image": user12,
        "clientName": "Ella Thomas",
        "content": "Blouse Ruffle Tube top",
        "country": "Arizona",
        "invoiceDate": "29 May, 2024",
        "dueDate": "03 June, 2024",
        "amount": "$270.00",
        "status": "Overdue"
    },
    {
        "image": user13,
        "clientName": "James Lee",
        "content": "Summer Dress",
        "country": "Massachusetts",
        "invoiceDate": "30 May, 2024",
        "dueDate": "04 June, 2024",
        "amount": "$600.75",
        "status": "Paid"
    },
    {
        "image": user14,
        "clientName": "Isabella Harris",
        "content": "Wireless Headphones",
        "country": "Washington",
        "invoiceDate": "31 May, 2024",
        "dueDate": "05 June, 2024",
        "amount": "$420.50",
        "status": "Pending"
    },
    {
        "image": user15,
        "clientName": "Michael Walker",
        "content": "Sunglasses",
        "country": "Colorado",
        "invoiceDate": "01 June, 2024",
        "dueDate": "06 June, 2024",
        "amount": "$275.00",
        "status": "Unpaid"
    },
    {
        "image": user18,
        "clientName": "Benjamin Lopez",
        "content": "Backpack",
        "country": "California",
        "invoiceDate": "03 June, 2024",
        "dueDate": "08 June, 2024",
        "amount": "$300.25",
        "status": "Overdue"
    },
    {
        "image": user19,
        "clientName": "Emma Hernandez",
        "content": "Winter Coat",
        "country": "Illinois",
        "invoiceDate": "04 June, 2024",
        "dueDate": "09 June, 2024",
        "amount": "$400.00",
        "status": "Paid"
    },
    {
        "image": user20,
        "clientName": "William Clark",
        "content": "Smartphone Case",
        "country": "Ohio",
        "invoiceDate": "05 June, 2024",
        "dueDate": "10 June, 2024",
        "amount": "$200.50",
        "status": "Pending"
    },
    {
        "image": user21,
        "clientName": "Sophia Garcia",
        "content": "Fitness Tracker",
        "country": "Georgia",
        "invoiceDate": "06 June, 2024",
        "dueDate": "11 June, 2024",
        "amount": "$180.75",
        "status": "Paid"
    },
    {
        "image": user22,
        "clientName": "Liam Miller",
        "content": "Sneakers",
        "country": "North Carolina",
        "invoiceDate": "07 June, 2024",
        "dueDate": "12 June, 2024",
        "amount": "$500.50",
        "status": "Overdue"
    },
    {
        "image": user23,
        "clientName": "Oliver Smith",
        "content": "Handbag",
        "country": "Washington",
        "invoiceDate": "08 June, 2024",
        "dueDate": "13 June, 2024",
        "amount": "$350.00",
        "status": "Paid"
    },
    {
        "image": user24,
        "clientName": "Sophie Garcia",
        "content": "Earrings",
        "country": "Texas",
        "invoiceDate": "09 June, 2024",
        "dueDate": "14 June, 2024",
        "amount": "$175.50",
        "status": "Pending"
    },
    {
        "image": user25,
        "clientName": "Noah Wilson",
        "content": "Sweater",
        "country": "New Jersey",
        "invoiceDate": "10 June, 2024",
        "dueDate": "15 June, 2024",
        "amount": "$225.75",
        "status": "Unpaid"
    },
    {
        "image": user26,
        "clientName": "Emma Johnson",
        "content": "Cufflinks",
        "country": "California",
        "invoiceDate": "11 June, 2024",
        "dueDate": "16 June, 2024",
        "amount": "$275.00",
        "status": "Paid"
    },
    {
        "image": user27,
        "clientName": "William Anderson",
        "content": "Winter Boots",
        "country": "Virginia",
        "invoiceDate": "12 June, 2024",
        "dueDate": "17 June, 2024",
        "amount": "$200.25",
        "status": "Pending"
    },
    {
        "image": user28,
        "clientName": "Ava Brown",
        "content": "Beanie",
        "country": "Florida",
        "invoiceDate": "13 June, 2024",
        "dueDate": "18 June, 2024",
        "amount": "$180.00",
        "status": "Pending"
    },
    {
        "image": user29,
        "clientName": "Liam Miller",
        "content": "Necklace",
        "country": "Texas",
        "invoiceDate": "14 June, 2024",
        "dueDate": "19 June, 2024",
        "amount": "$400.50",
        "status": "Overdue"
    },
    {
        "image": user30,
        "clientName": "Isabella Wilson",
        "content": "Sunglasses",
        "country": "California",
        "invoiceDate": "15 June, 2024",
        "dueDate": "20 June, 2024",
        "amount": "$300.75",
        "status": "Paid"
    },
    {
        "image": user31,
        "clientName": "Mason Taylor",
        "content": "Watch",
        "country": "Virginia",
        "invoiceDate": "16 June, 2024",
        "dueDate": "21 June, 2024",
        "amount": "$250.00",
        "status": "Unpaid"
    },
    {
        "image": user32,
        "clientName": "Sophia Garcia",
        "content": "T-shirt",
        "country": "Texas",
        "invoiceDate": "17 June, 2024",
        "dueDate": "22 June, 2024",
        "amount": "$350.25",
        "status": "Paid"
    },
    {
        "image": user33,
        "clientName": "Olivia Martinez",
        "content": "Smartwatch",
        "country": "Michigan",
        "invoiceDate": "18 June, 2024",
        "dueDate": "23 June, 2024",
        "amount": "$280.50",
        "status": "Paid"
    },
    {
        "image": user34,
        "clientName": "William Thompson",
        "content": "Web Development",
        "country": "Ohio",
        "invoiceDate": "19 June, 2024",
        "dueDate": "24 June, 2024",
        "amount": "$190.75",
        "status": "Pending"
    },
    {
        "image": user35,
        "clientName": "Sophie Hernandez",
        "content": "Web Flow Chart",
        "country": "California",
        "invoiceDate": "20 June, 2024",
        "dueDate": "25 June, 2024",
        "amount": "$410.00",
        "status": "Overdue"
    },
    {
        "image": user36,
        "clientName": "Ethan Jackson",
        "content": "Hoodie",
        "country": "Texas",
        "invoiceDate": "21 June, 2024",
        "dueDate": "26 June, 2024",
        "amount": "$350.25",
        "status": "Paid"
    },
    {
        "image": user37,
        "clientName": "Ava Wilson",
        "content": "Jeans",
        "country": "New Jersey",
        "invoiceDate": "22 June, 2024",
        "dueDate": "27 June, 2024",
        "amount": "$220.00",
        "status": "Unpaid"
    },
    {
        "image": user38,
        "clientName": "Charlotte Garcia",
        "content": "Sweatpants",
        "country": "Florida",
        "invoiceDate": "23 June, 2024",
        "dueDate": "28 June, 2024",
        "amount": "$180.00",
        "status": "Pending"
    },
    {
        "image": user39,
        "clientName": "Elijah Brown",
        "content": "Gloves",
        "country": "Illinois",
        "invoiceDate": "24 June, 2024",
        "dueDate": "29 June, 2024",
        "amount": "$400.50",
        "status": "Overdue"
    },
    {
        "image": user40,
        "clientName": "Ava Johnson",
        "content": "Bracelet",
        "country": "California",
        "invoiceDate": "25 June, 2024",
        "dueDate": "30 June, 2024",
        "amount": "$300.75",
        "status": "Paid"
    },
    {
        "image": user41,
        "clientName": "Henry Wilson",
        "content": "Socks",
        "country": "Virginia",
        "invoiceDate": "26 June, 2024",
        "dueDate": "01 July, 2024",
        "amount": "$250.00",
        "status": "Pending"
    },
    {
        "image": user42,
        "clientName": "Emma Thompson",
        "content": "Hat",
        "country": "Texas",
        "invoiceDate": "27 June, 2024",
        "dueDate": "02 July, 2024",
        "amount": "$280.50",
        "status": "Paid"
    },
    {
        "image": user43,
        "clientName": "William Hernandez",
        "content": "Wallet",
        "country": "Michigan",
        "invoiceDate": "28 June, 2024",
        "dueDate": "03 July, 2024",
        "amount": "$190.75",
        "status": "Unpaid"
    },
    {
        "image": user44,
        "clientName": "Sophia Jackson",
        "content": "Web Designing",
        "country": "Ohio",
        "invoiceDate": "29 June, 2024",
        "dueDate": "04 July, 2024",
        "amount": "$410.00",
        "status": "Overdue"
    },
    {
        "image": user45,
        "clientName": "James Garcia",
        "content": "Back End Development",
        "country": "New Jersey",
        "invoiceDate": "30 June, 2024",
        "dueDate": "05 July, 2024",
        "amount": "$350.25",
        "status": "Paid"
    },
    {
        "image": user46,
        "clientName": "Amelia Lopez",
        "content": "Jacket",
        "country": "Florida",
        "invoiceDate": "01 July, 2024",
        "dueDate": "06 July, 2024",
        "amount": "$220.00",
        "status": "Pending"
    },
    {
        "image": user47,
        "clientName": "Oliver Martinez",
        "content": "Purse",
        "country": "Texas",
        "invoiceDate": "02 July, 2024",
        "dueDate": "07 July, 2024",
        "amount": "$300.25",
        "status": "Overdue"
    }
]
export default invoicesListData