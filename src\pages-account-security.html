{{> partials/main }}

<head>

    {{> partials/title-meta title="Account Security" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}

<div class="relative mb-6">
    <div class="relative overflow-hidden rounded-md h-44 bg-primary-500/10">
        <div class="border-[60px] border-t-primary-500 border-l-primary-500 absolute opacity-10 -top-2 left-0 rotate-45 size-96"></div>
        <div class="border-[60px] border-green-500 absolute opacity-10 top-20 left-8 rotate-45 size-80"></div>
        <div class="border-[60px] border-pink-500 absolute opacity-10 top-36 left-28 rotate-45 size-40"></div>
    </div>
    <div class="text-center">
        <div class="relative inline-block mx-auto">
            <div class="relative p-1 rounded-full bg-gradient-to-tr from-primary-300 via-red-300 to-green-300 -mt-14">
                <img src="assets/images/avatar/user-17.png" alt="" class="mx-auto border-4 border-white rounded-full dark:border-dark-900 size-28">
            </div>
            <div class="absolute border-2 border-white dark:border-dark-900 rounded-full size-4 bg-green-500 bottom-2.5 ltr:right-2.5 rtl:left-2.5"></div>
        </div>
        <h5 class="mt-2 mb-1">Sophia Mia <i data-lucide="badge-check" class="inline-block text-primary-500 fill-primary-500/20 size-5"></i></h5>
        <ul class="flex flex-wrap items-center justify-center gap-2 text-gray-500 dark:text-dark-500 text-14">
            <li><i data-lucide="building-2" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-middle">UI / UX Designer</span></li>
            <li><i data-lucide="map-pin" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-middle">Argentina</span></li>
            <li><i data-lucide="calendar-days" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-middle">24 April, 2024</span></li>
        </ul>
    </div>
</div>

<ul class="pb-2 overflow-x-auto tabs-pills lg:pb-0">
    <li>
        <a href="pages-account-settings.html" class="nav-item text-gray-500 dark:text-dark-500 [&.active]:bg-primary-500 [&.active]:text-primary-50">
            <i data-lucide="user-round" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-middle whitespace-nowrap">Account</span>
        </a>
    </li>
    <li>
        <a href="pages-account-security.html" class="nav-item text-gray-500 dark:text-dark-500 [&.active]:bg-primary-500 [&.active]:text-primary-50 active">
            <i data-lucide="shield-check" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-middle whitespace-nowrap">Security</span>
        </a>
    </li>
    <li>
        <a href="pages-account-billing-plan.html" class="nav-item text-gray-500 dark:text-dark-500 [&.active]:bg-primary-500 [&.active]:text-primary-50">
            <i data-lucide="gem" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-middle whitespace-nowrap">Billing & Plans</span>
        </a>
    </li>
    <li>
        <a href="pages-account-notification.html" class="nav-item text-gray-500 dark:text-dark-500 [&.active]:bg-primary-500 [&.active]:text-primary-50">
            <i data-lucide="bell" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-middle whitespace-nowrap">Notification</span>
        </a>
    </li>
    <li>
        <a href="pages-account-statements.html" class="nav-item text-gray-500 dark:text-dark-500 [&.active]:bg-primary-500 [&.active]:text-primary-50">
            <i data-lucide="list-tree" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-middle whitespace-nowrap">Statements</span>
        </a>
    </li>
    <li>
        <a href="pages-account-logs.html" class="nav-item text-gray-500 dark:text-dark-500 [&.active]:bg-primary-500 [&.active]:text-primary-50">
            <i data-lucide="log-out" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-middle whitespace-nowrap">Logs</span>
        </a>
    </li>
</ul>

<h6 class="mt-5 mb-1 text-16">Security</h6>
<p class="mb-5 text-gray-500 dark:text-dark-500"><a href="#!" class="underline transition duration-300 ease-linear text-primary-500 hover:text-primary-600">Learn More</a> about securing your account from external and unknown intrusion.</p>

<div class="card">
    <div class="grid grid-cols-12 card-body">
        <div class="col-span-12 md:col-span-9">
            <h6 class="mb-1">Account Security</h6>
            <p class="mb-2 text-gray-500 dark:text-dark-500">Secured Account means any account for which the related obligor has pledged assets or made a cash collateral deposit as security for payment of receivables that arise in such an account.</p>
            <a href="#!" class="underline link text-primary-500 dark:text-primary-500 hover:text-primary-600">
                Learn More 
                <i data-lucide="move-right" class="ml-1 ltr:inline-block rtl:hidden size-4"></i>
                <i data-lucide="move-left" class="ml-1 ltr:hidden rtl:inline-block size-4"></i>
            </a>
        </div>
        <div class="col-span-12 md:col-span-3">
            <img src="assets/images/others/security.png" alt="" class="block h-24 mx-auto mt-5 md:mt-0 ltr:md:ml-auto rtl:md:mr-auto">
        </div>
    </div>
</div>
<div class="card">
    <div class="gap-3 md:flex card-body">
        <div class="shrink-0">
            <div class="flex items-center gap-2">
                <img src="assets/images/others/google.png" alt="" class="size-5">
                <h6>Google Authentication</h6>
            </div>
        </div>
        <div class="my-3 grow md:my-0">
            <p class="mb-3 text-gray-500 dark:text-dark-500">If you set up 2-Step Verification, you can use the Google Authenticator app to generate codes. You can still generate codes without internet connection or mobile service. Learn more about <a href="#!" class="transition duration-300 ease-linear text-primary-500 hover:text-primary-600">2-Step Verification</a>.</p>
            <span class="badge badge-green"><i data-lucide="circle-check-big" class="inline-block ltr:mr-1 rtl:ml-1 size-3"></i> <span class="align-middle">Connected</span></span>
        </div>
        <div class="shrink-0">
            <button type="button" data-modal-target="googleAuth1Modal" class="btn btn-sub-gray">Enable</button>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h6 class="card-title">Update Password</h6>
    </div>
    <div class="card-body">
        <p class="mb-3 text-gray-500 dark:text-dark-500">To change your password, please enter your current password.</p>
        <form action="#!">
            <div x-data="{ show: false }" class="mb-5">
                <label for="currentPasswordInput" class="form-label">Current Password</label>
                <div class="relative">
                    <input type="password" id="currentPasswordInput" x-bind:type="show ? 'text' : 'password'" class="ltr:pr-8 rtl:pl-8 form-input" autocomplete="off" placeholder="Enter current password">
                    <button @click="show = !show" title="password" class="absolute inset-y-0 flex items-center text-gray-500 dark:text-dark-500 ltr:right-3 rtl:left-3 focus:outline-hidden">
                        <i data-lucide="eye" x-show="show" class="size-5"></i>
                        <i data-lucide="eye-off" x-show="!show" class="size-5"></i>
                    </button>
                </div>
            </div>
            <div x-data="{ show: false }" class="mb-5">
                <label for="newPasswordInput" class="form-label">New Password</label>
                <div class="relative">
                    <input type="password" id="newPasswordInput" x-bind:type="show ? 'text' : 'password'" class="ltr:pr-8 rtl:pl-8 form-input" autocomplete="off" placeholder="New password">
                    <button @click="show = !show" title="new-password" class="absolute inset-y-0 flex items-center text-gray-500 dark:text-dark-500 ltr:right-3 rtl:left-3 focus:outline-hidden">
                        <i data-lucide="eye" x-show="show" class="size-5"></i>
                        <i data-lucide="eye-off" x-show="!show" class="size-5"></i>
                    </button>
                </div>
            </div>
            <div x-data="{ show: false }" class="mb-5">
                <label for="confirmPasswordInput" class="form-label">Confirm New Password</label>
                <div class="relative">
                    <input type="password" id="confirmPasswordInput" x-bind:type="show ? 'text' : 'password'" class="ltr:pr-8 rtl:pl-8 form-input" autocomplete="off" placeholder="Confirm password">
                    <button @click="show = !show" title="confirm password" class="absolute inset-y-0 flex items-center text-gray-500 dark:text-dark-500 ltr:right-3 rtl:left-3 focus:outline-hidden">
                        <i data-lucide="eye" x-show="show" class="size-5"></i>
                        <i data-lucide="eye-off" x-show="!show" class="size-5"></i>
                    </button>
                </div>
            </div>
            <div class="flex items-center justify-end gap-2">
                <a href="#!" class="btn btn-primary">Update Password</a>
            </div>
        </form>
    </div>
</div>

</div>
{{> partials/footer }}
</div>

<div id="googleAuth1Modal" class="!hidden modal show">
    <div class="modal-wrap modal-center">
        <div class="modal-header">
            <h6>Set up two-factor authentication</h6>
            <button data-modal-close="googleAuth1Modal" class="link link-red"><i data-lucide="x" class="size-5"></i></button>
        </div>
        <div class="modal-content">
            <p class="mb-2 text-gray-500 dark:text-dark-500">To authorize transactions, kindly scan this QR code with your Google Authenticator App and then enter the verification code provided below.</p>
            <div class="p-4 mb-3">
                <img src="assets/images/others/qr.png" alt="" class="mx-auto size-28">
            </div>
            <form id="otp-form1">
                <div class="flex items-center justify-center gap-3">
                    <input type="text" class="text-2xl font-extrabold text-center text-gray-900 bg-gray-100 border border-transparent rounded-sm appearance-none outline-hidden size-14 dark:text-dark-50 hover:border-gray-200 dark:hover:border-dark-800 dark:bg-dark-850 focus:bg-white dark:focus:bg-dark-900 focus:border-primary-400 dark:focus:border-primary-400 focus:ring-2 focus:ring-primary-500/10" pattern="\d*" maxlength="1" />
                    <input type="text" class="text-2xl font-extrabold text-center text-gray-900 bg-gray-100 border border-transparent rounded-sm appearance-none outline-hidden size-14 dark:text-dark-50 hover:border-gray-200 dark:hover:border-dark-800 dark:bg-dark-850 focus:bg-white dark:focus:bg-dark-900 focus:border-primary-400 dark:focus:border-primary-400 focus:ring-2 focus:ring-primary-500/10" maxlength="1" />
                    <input type="text" class="text-2xl font-extrabold text-center text-gray-900 bg-gray-100 border border-transparent rounded-sm appearance-none outline-hidden size-14 dark:text-dark-50 hover:border-gray-200 dark:hover:border-dark-800 dark:bg-dark-850 focus:bg-white dark:focus:bg-dark-900 focus:border-primary-400 dark:focus:border-primary-400 focus:ring-2 focus:ring-primary-500/10" maxlength="1" />
                    <input type="text" class="text-2xl font-extrabold text-center text-gray-900 bg-gray-100 border border-transparent rounded-sm appearance-none outline-hidden size-14 dark:text-dark-50 hover:border-gray-200 dark:hover:border-dark-800 dark:bg-dark-850 focus:bg-white dark:focus:bg-dark-900 focus:border-primary-400 dark:focus:border-primary-400 focus:ring-2 focus:ring-primary-500/10" maxlength="1" />
                    <input type="text" class="text-2xl font-extrabold text-center text-gray-900 bg-gray-100 border border-transparent rounded-sm appearance-none outline-hidden size-14 dark:text-dark-50 hover:border-gray-200 dark:hover:border-dark-800 dark:bg-dark-850 focus:bg-white dark:focus:bg-dark-900 focus:border-primary-400 dark:focus:border-primary-400 focus:ring-2 focus:ring-primary-500/10" maxlength="1" />
                    <input type="text" class="text-2xl font-extrabold text-center text-gray-900 bg-gray-100 border border-transparent rounded-sm appearance-none outline-hidden size-14 dark:text-dark-50 hover:border-gray-200 dark:hover:border-dark-800 dark:bg-dark-850 focus:bg-white dark:focus:bg-dark-900 focus:border-primary-400 dark:focus:border-primary-400 focus:ring-2 focus:ring-primary-500/10" maxlength="1" />
                </div>
                <div class="mt-5">
                    <button type="button" data-modal-target="googleAuth2Modal" data-modal-close="googleAuth1Modal" class="w-full btn btn-primary">Verify Account</button>
                </div>
            </form>
        </div>
    </div>
</div><!--end-->

<div id="googleAuth2Modal" class="!hidden modal show">
    <div class="modal-wrap modal-center modal-sm">
        <div class="modal-content p-7">
            <div class="mb-5 text-center">
                <h6 class="mb-2 text-16">Please check your email</h6>
                <p class="mb-2 text-gray-500 dark:text-dark-500">We're sent a code to <b><EMAIL></b></p>
            </div>
            <form id="otp-form2">
                <div class="flex items-center justify-center gap-3">
                    <input type="text" class="text-2xl font-extrabold text-center text-gray-900 bg-gray-100 border border-transparent rounded-sm appearance-none outline-hidden size-14 dark:text-dark-50 hover:border-gray-200 dark:hover:border-dark-800 dark:bg-dark-850 focus:bg-white dark:focus:bg-dark-900 focus:border-primary-400 dark:focus:border-primary-400 focus:ring-2 focus:ring-primary-500/10" pattern="\d*" maxlength="1" />
                    <input type="text" class="text-2xl font-extrabold text-center text-gray-900 bg-gray-100 border border-transparent rounded-sm appearance-none outline-hidden size-14 dark:text-dark-50 hover:border-gray-200 dark:hover:border-dark-800 dark:bg-dark-850 focus:bg-white dark:focus:bg-dark-900 focus:border-primary-400 dark:focus:border-primary-400 focus:ring-2 focus:ring-primary-500/10" maxlength="1" />
                    <input type="text" class="text-2xl font-extrabold text-center text-gray-900 bg-gray-100 border border-transparent rounded-sm appearance-none outline-hidden size-14 dark:text-dark-50 hover:border-gray-200 dark:hover:border-dark-800 dark:bg-dark-850 focus:bg-white dark:focus:bg-dark-900 focus:border-primary-400 dark:focus:border-primary-400 focus:ring-2 focus:ring-primary-500/10" maxlength="1" />
                    <input type="text" class="text-2xl font-extrabold text-center text-gray-900 bg-gray-100 border border-transparent rounded-sm appearance-none outline-hidden size-14 dark:text-dark-50 hover:border-gray-200 dark:hover:border-dark-800 dark:bg-dark-850 focus:bg-white dark:focus:bg-dark-900 focus:border-primary-400 dark:focus:border-primary-400 focus:ring-2 focus:ring-primary-500/10" maxlength="1" />
                </div>
                <div class="flex items-center gap-2 mt-5">
                    <button type="submit" data-modal-close="googleAuth2Modal" class="w-full btn btn-active-red">Cancel</button>
                    <button type="submit" class="w-full btn btn-primary">Verify</button>
                </div>
            </form>
        </div>
    </div>
</div><!--end-->

{{> partials/vendor-scripts }}

<script type="module" src="assets/js/pages/account-security.init.js"></script>

<script type="module" src="assets/js/main.js"></script>


</body>
</html>