import { getColorCodes } from "../../helpers/helper";
import ApexCharts from 'apexcharts';

//Sales Per Minute Chart
document.addEventListener("alpine:init", () => {
    Alpine.data("salesPerMinuteApp", () => ({
        series: [
            {
                name: 'Sales',
                data: [9, 14, 17, 7, 5, 8, 6, 16, 9, 15, 13, 10]
            }
        ],
        init() {
            // Initial chart render
            this.renderChart();

            // Update data every minute
            setInterval(() => {
                this.updateData();
            }, 60000);

            // Reload chart on window resize
            window.addEventListener('resize', this.reloadChart.bind(this));
        },
        updateData() {
            // Generate new random data
            const newData = this.series[0].data.map(value =>
                Math.max(0, value + (Math.random() * 4 - 2))
            );

            this.series = [
                {
                    name: 'Sales',
                    data: newData
                }
            ];

            // Update animated counters
            const total = newData.reduce((a, b) => a + b, 0);
            const previousTotal = this.series[0].data.reduce((a, b) => a + b, 0);

            // Dispatch events to update counters
            const currentCounter = document.querySelector('[x-data="animatedCounter(1234, 500, 0)"]');
            const previousCounter = document.querySelector('[x-data="animatedCounter(1098, 500, 0)"]');

            if (currentCounter) {
                currentCounter.__x.$data.target = total * 100;
                currentCounter.__x.$data.updateCounter();
            }

            if (previousCounter) {
                previousCounter.__x.$data.target = previousTotal * 100;
                previousCounter.__x.$data.updateCounter();
            }

            this.renderChart();
        },
        renderChart() {
            // Destroy previous instance if exists
            if (this.salesPerMinuteChart)
                this.salesPerMinuteChart.destroy();

            // Initialize new chart
            this.salesPerMinuteChart = new ApexCharts(this.$refs.salesPerMinuteChart, this.options);
            this.salesPerMinuteChart.render();
        },
        reloadChart() {
            // Handle the logic for resizing
            this.renderChart(); // Re-render chart on resize
        },
        get options() {
            return {
                series: this.series,
                chart: {
                    height: 235,
                    type: "bar",
                    toolbar: {
                        show: false,
                    },
                    sparkline: { enabled: false },
                    animations: {
                        enabled: true,
                        easing: 'easeinout',
                        speed: 800,
                        animateGradually: {
                            enabled: true,
                            delay: 150
                        },
                        dynamicAnimation: {
                            enabled: true,
                            speed: 350
                        }
                    }
                },
                plotOptions: {
                    bar: {
                        horizontal: false,
                        columnWidth: '90%',
                        borderRadius: 5,
                        dataLabels: {
                            position: 'top',
                        },
                    },
                },
                stroke: {
                    show: true,
                    width: 1,
                    colors: ['transparent']
                },
                grid: {
                    padding: {
                        top: 0,
                        right: -10,
                        bottom: 0
                    },
                    borderColor: '#f1f1f1',
                    strokeDashArray: 4,
                },
                xaxis: {
                    categories: ['12:00', '12:05', '12:10', '12:15', '12:20', '12:25', '12:30', '12:35', '12:40', '12:45', '12:50', '12:55'],
                    axisBorder: {
                        show: false,
                    },
                    axisTicks: {
                        show: false,
                    },
                },
                fill: {
                    opacity: 1,
                    type: 'gradient',
                    gradient: {
                        shade: 'light',
                        type: "vertical",
                        shadeIntensity: 0.25,
                        gradientToColors: undefined,
                        inverseColors: true,
                        opacityFrom: 0.85,
                        opacityTo: 0.85,
                        stops: [50, 100]
                    },
                },
                colors: getColorCodes(this.$refs.salesPerMinuteChart.dataset),
            };
        }
    }));
});

//Line with Data Labels Charts
document.addEventListener("alpine:init", () => {
    // Create a shared state object
    const sharedState = {
        currentYear: '2025',
        yearData: {
            '2022': {
                // Seasonal pattern with summer peak
                revenue: [35, 42, 48, 65, 78, 85, 72],
                expenses: [25, 28, 32, 45, 52, 58, 48]
            },
            '2023': {
                // Steady growth pattern
                revenue: [45, 52, 58, 65, 72, 78, 85],
                expenses: [32, 35, 38, 42, 45, 48, 52]
            },
            '2024': {
                // Fluctuating pattern with Q2 dip
                revenue: [65, 72, 58, 45, 68, 82, 75],
                expenses: [45, 48, 42, 38, 45, 52, 48]
            },
            '2025': {
                // Exponential growth pattern
                revenue: [55, 65, 78, 92, 108, 125, 145],
                expenses: [42, 48, 55, 65, 75, 85, 95]
            }
        }
    };

    // Chart component
    Alpine.data("labelLineApp", () => ({
        currentYear: sharedState.currentYear,
        yearData: sharedState.yearData,
        series: [
            {
                name: "Revenue",
                data: [55, 65, 78, 92, 108, 125, 145]
            },
            {
                name: "Expenses",
                data: [42, 48, 55, 65, 75, 85, 95]
            }
        ],
        labels: ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul"],
        init() {
            // Initial chart render
            this.renderChart();

            // Reload chart on window resize
            window.addEventListener('resize', this.reloadChart.bind(this));
        },
        updateChartData() {
            const yearData = this.yearData[this.currentYear];
            if (yearData) {
                this.series = [
                    {
                        name: "Revenue",
                        data: yearData.revenue
                    },
                    {
                        name: "Expenses",
                        data: yearData.expenses
                    }
                ];
                this.renderChart();
            }
        },
        renderChart() {
            // Destroy previous instance if exists
            if (this.labelLineChart)
                this.labelLineChart.destroy();

            // Initialize new chart
            this.labelLineChart = new ApexCharts(this.$refs.labelLineChart, this.options);
            this.labelLineChart.render();
        },
        reloadChart() {
            // Handle the logic for resizing
            this.renderChart(); // Re-render chart on resize
        },
        get options() {
            return {
                series: this.series,
                chart: {
                    defaultLocale: "en",
                    height: 315,
                    type: "line",
                    dropShadow: {
                        enabled: true,
                        color: '#000',
                        top: 18,
                        left: 7,
                        blur: 10,
                        opacity: 0.2
                    },
                    toolbar: {
                        show: false
                    }
                },
                dataLabels: {
                    enabled: true,
                },
                stroke: {
                    curve: 'smooth'
                },
                grid: {
                    padding: {
                        top: -20,
                        right: 0,
                        bottom: 0,
                    },
                },
                xaxis: {
                    categories: this.labels
                },
                legend: {
                    show: false
                },
                colors: getColorCodes(this.$refs.labelLineChart.dataset), // Utilize the function to get hash color codes
            };
        }
    }));

    // Dropdown component
    Alpine.data("dropdownBehavior", () => ({
        open: false,
        currentYear: sharedState.currentYear,
        yearData: sharedState.yearData,
        toggle() {
            this.open = !this.open;
        },
        close() {
            this.open = false;
        },
        calculatePosition() {
            // Your existing position calculation code
        },
        changeYear(year) {
            this.currentYear = year;
            sharedState.currentYear = year;
            // Find all labelLineApp instances and update them
            document.querySelectorAll('[x-data="labelLineApp"]').forEach(el => {
                const app = el.__x.$data;
                app.currentYear = year;
                app.updateChartData();
            });
            this.close();
        }
    }));
});


//Top Selling Items Chart
document.addEventListener("alpine:init", () => {
    Alpine.data("topSellingItemsApp", () => ({
        series: [{
            name: 'Classic Burger',
            data: [44, 55, 41, 67, 22, 43, 20, 35, 28, 42]
        }, {
            name: 'Margherita Pizza',
            data: [13, 23, 20, 8, 13, 27, 19, 15, 19, 22]
        }, {
            name: 'Chicken Wings',
            data: [11, 17, 15, 15, 21, 14, 11, 18, 23, 16]
        }, {
            name: 'French Fries',
            data: [21, 7, 25, 13, 22, 16, 10, 17, 14, 19]
        }, {
            name: 'Soft Drinks',
            data: [21, 7, 25, 13, 22, 8, 18, 16, 20, 24]
        }],
        labels: ['01/01/2024 GMT', '01/02/2024 GMT', '01/03/2024 GMT', '01/04/2024 GMT', '01/05/2024 GMT', '01/06/2024 GMT', '01/07/2024 GMT', '01/08/2024 GMT', '01/09/2024 GMT', '01/10/2024 GMT'],
        init() {
            // Initial chart render
            this.renderChart();

            // Reload chart on window resize
            window.addEventListener('resize', this.reloadChart.bind(this));
        },
        renderChart() {
            // Destroy previous instance if exists
            if (this.topSellingItemsChart)
                this.topSellingItemsChart.destroy();

            // Initialize new chart
            this.topSellingItemsChart = new ApexCharts(this.$refs.topSellingItemsChart, this.options);
            this.topSellingItemsChart.render();
        },
        reloadChart() {
            // Handle the logic for resizing
            this.renderChart(); // Re-render chart on resize
        },
        get options() {
            return {
                series: this.series,
                chart: {
                    height: 300,
                    type: "bar",
                    stacked: true,
                    toolbar: {
                        show: false
                    },
                    zoom: {
                        enabled: true
                    }
                },
                colors: getColorCodes(this.$refs.topSellingItemsChart.dataset),
                plotOptions: {
                    bar: {
                        horizontal: false,
                        borderRadius: 11,
                        dataLabels: {
                            enabled: false,
                            total: {
                                enabled: true,
                                style: {
                                    fontSize: '13px',
                                    fontWeight: 900
                                }
                            }
                        }
                    },
                },
                fill: {
                    opacity: 1,
                    type: 'gradient',
                    gradient: {
                        shade: 'light',
                        type: "vertical",
                        shadeIntensity: 0.25,
                        gradientToColors: undefined,
                        inverseColors: true,
                        opacityFrom: 0.85,
                        opacityTo: 0.85,
                        stops: [50, 100]
                    },
                },
                xaxis: {
                    categories: this.labels,
                    type: 'datetime'
                },
                legend: {
                    show: false,
                    position: 'right',
                    offsetY: 40
                },
                fill: {
                    opacity: 1
                },
                grid: {
                    padding: {
                        top: -20,
                        right: -10,
                        bottom: 0
                    },
                },
            };
        }
    }));
});

//Sales Trend Chart
document.addEventListener("alpine:init", () => {
    Alpine.data("salesTrendApp", () => ({
        series: [{
            name: 'Daily Sales',
            data: [2850, 3200, 2950, 3100, 3650, 4200, 3950, 3800, 3450, 3600]
        }, {
            name: 'Weekly Sales',
            data: [18500, 19200, 18800, 19500, 21200, 22800, 22200, 21800, 20500, 21600]
        }, {
            name: 'Monthly Sales',
            data: [72000, 75000, 78000, 82000, 88000, 92000, 95000, 92000, 89000, 94000]
        }],
        labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct'],
        init() {
            // Initial chart render
            this.renderChart();

            // Reload chart on window resize
            window.addEventListener('resize', this.reloadChart.bind(this));
        },
        renderChart() {
            // Destroy previous instance if exists
            if (this.salesTrendChart)
                this.salesTrendChart.destroy();

            // Initialize new chart
            this.salesTrendChart = new ApexCharts(this.$refs.salesTrendChart, this.options);
            this.salesTrendChart.render();
        },
        reloadChart() {
            // Handle the logic for resizing
            this.renderChart(); // Re-render chart on resize
        },
        get options() {
            return {
                series: this.series,
                chart: {
                    height: 300,
                    type: 'line',
                    toolbar: {
                        show: false
                    },
                    zoom: {
                        enabled: false
                    }
                },
                dataLabels: {
                    enabled: false
                },
                stroke: {
                    curve: 'smooth',
                    width: 3
                },
                grid: {
                    padding: {
                        top: -20,
                        right: -10,
                        bottom: 0
                    },
                    borderColor: '#f1f1f1',
                    strokeDashArray: 4,
                },
                xaxis: {
                    categories: this.labels,
                    axisBorder: {
                        show: false
                    },
                    axisTicks: {
                        show: false
                    }
                },
                yaxis: {
                    labels: {
                        formatter: function(value) {
                            return '$' + value.toFixed(0);
                        }
                    }
                },
                legend: {
                    position: 'top',
                    horizontalAlign: 'right'
                },
                colors: getColorCodes(this.$refs.salesTrendChart.dataset),
                fill: {
                    type: 'gradient',
                    gradient: {
                        shade: 'light',
                        type: "vertical",
                        shadeIntensity: 0.25,
                        gradientToColors: undefined,
                        inverseColors: true,
                        opacityFrom: 0.85,
                        opacityTo: 0.85,
                        stops: [50, 100]
                    },
                },
                tooltip: {
                    y: {
                        formatter: function(value) {
                            return '$' + value.toFixed(0);
                        }
                    }
                }
            };
        }
    }));
});