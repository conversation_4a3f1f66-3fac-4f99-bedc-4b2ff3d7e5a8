{{> partials/main }}

<head>

    {{> partials/title-meta title="Collapse Expand" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}

{{> partials/page-heading title="Collapse Expand" sub-title="Apextree" }}

<div class="grid grid-cols-12 gap-x-space">
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Collapse Expand</h6>
        </div>
        <div class="card-body">
            <div x-data="apexTreeApp()" x-init="init" class="border border-gray-200 rounded-md dark:border-dark-800">
                <div id="apex-tree-container" x-ref="apexTreeContainer" data-chart-colors="[bg-gray-200, bg-gray-500, bg-white, bg-primary-100, bg-purple-100, bg-yellow-100, bg-gray-100, bg-orange-100, bg-green-100, bg-pink-100, bg-indigo-100]" data-chart-dark-colors="[bg-dark-800, bg-dark-500, bg-white, bg-primary-500, bg-purple-500, bg-yellow-500, bg-dark-850, bg-orange-500, bg-green-500, bg-pink-500, bg-indigo-500]"></div>
            </div>
            
        </div>
    </div><!--end col-->
</div><!--end grid-->

</div>
{{> partials/footer }}
</div>

{{> partials/vendor-scripts }}

<script type="module" src="assets/js/charts/apextree-collapse-expand.init.js"></script>

<script type="module" src="assets/js/main.js"></script>

</body>
</html>