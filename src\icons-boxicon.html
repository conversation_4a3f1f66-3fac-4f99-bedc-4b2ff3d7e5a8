{{> partials/main }}

<head>

    {{> partials/title-meta title="Boxicon" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}

{{> partials/page-heading title="Boxicon" sub-title="Icons" }}

<div class="grid grid-cols-12 gap-x-space">
    <div class="col-span-12 card">
        <div class="flex items-center card-header">
            <h6 class="text-15 grow">Box Icons</h6>
            <a href="https://boxicons.com/" target="_blank" class="font-medium text-red-500 underline transition duration-200 ease-linear hover:text-red-600 shrink-0">View All Icons</a>
        </div>
        <div class="card-body">
            <p class="mb-3 text-gray-500 dark:text-dark-500">'Boxicons' is a carefully designed open source iconset with <code class="text-pink-500">1500+</code> icons. It's crafted to look enrich your website/app experience.</p>

            <h6 class="mb-2 text-16">Installation</h6>
            <p class="text-gray-500 dark:text-dark-500 mb-2">To install via npm, simply do the following:</p>

            <pre><deckgo-highlight-code lang="js">
                        <code slot="code">$ npm install boxicons --save</code>
                    </deckgo-highlight-code></pre>

            <p class="text-gray-500 dark:text-dark-500 my-2">import CSS to your <code class="text-pink-500">icons.scss</code></p>

            <pre><deckgo-highlight-code lang="js">
                        <code slot="code">@import 'boxicons';</code>
                    </deckgo-highlight-code></pre>

            <h6 class="mb-1 mt-2">CDN</h6>
            <p class="text-gray-500 dark:text-dark-500 mb-2">Instead of installing you may use the remote version</p>

            <pre>
<deckgo-highlight-code lang="js"><code slot="code">&lt;link rel=&quot;stylesheet&quot; href=&quot;https://cdn.jsdelivr.net/npm/boxicons@latest/css/boxicons.min.css&quot;&gt;

&lt;!-- or --&gt;

&lt;link rel=&quot;stylesheet&quot; href=&quot;https://unpkg.com/boxicons@latest/css/boxicons.min.css&quot;&gt;</code>
</deckgo-highlight-code></pre>

            <h6 class="mb-1 mt-2">Usage</h6>
            <p class="text-gray-500 dark:text-dark-500 mb-2">To use an icon on your page, add a class '<code class="text-pink-500">bx</code>' and seperate class with the icons name with a prefix '<code class="text-pink-500">bx-</code>' for regular icons , '<code class="text-pink-500">bxs-</code>' for solid icons and '<code class="text-pink-500">bxl-</code>' for logos:</p>

            <pre>
<deckgo-highlight-code lang="js"><code slot="code">&lt;i class=&quot;bx bx-hot&quot;&gt;&lt;/i&gt;
&lt;i class=&quot;bx bxs-hot&quot;&gt;&lt;/i&gt;
&lt;i class=&quot;bx bxl-facebook-square&quot;&gt;&lt;/i&gt;</code>
</deckgo-highlight-code></pre>

            <p class="mb-0 text-gray-500 dark:text-dark-500 mt-2">For more details, see the <a href="https://github.com/atisawd/boxicons" target="_blank" class="transition duration-200 ease-linear hover:text-primary-600 text-primary-500">documentation</a>.</p>
        </div>
    </div><!--end col-->
    <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Color Icons</h6>
        </div>
        <div class="card-body">
            <div class="*:size-10 *:flex *:items-center *:justify-center flex-wrap flex items-center *:border *:border-gray-200 dark:*:border-dark-800 gap-2 *:rounded-md text-xl">
                <div><i class='bx bxs-balloon'></i></div>
                <div><i class='bx bx-color'></i></div>
                <div><i class='bx bx-qr'></i></div>
                <div><i class='bx bxl-unity'></i></div>
                <div><i class='bx bx-chat'></i></div>
                <div><i class='bx bx-search-alt-2'></i></div>
                <div><i class='bx bx-receipt'></i></div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Using SVG Code</h6>
        </div>
        <div class="card-body">
            <div class="flex items-center gap-5">
                <div>
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" class="size-6 fill-primary-500">
                        <path d="M21 11h-3V4a1 1 0 0 0-1-1H3a1 1 0 0 0-1 1v14c0 1.654 1.346 3 3 3h14c1.654 0 3-1.346 3-3v-6a1 1 0 0 0-1-1zM5 19a1 1 0 0 1-1-1V5h12v13c0 .351.061.688.171 1H5zm15-1a1 1 0 0 1-2 0v-5h2v5z"></path>
                        <path d="M6 7h8v2H6zm0 4h8v2H6zm5 4h3v2h-3z"></path>
                    </svg>
                </div>
                <div>
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" class="fill-pink-500 size-6">
                        <path d="M19 8.001h-4V4.999a2.92 2.92 0 0 0-.874-2.108 2.943 2.943 0 0 0-2.39-.879C10.202 2.144 9 3.508 9 5.117V8H5c-1.103 0-2 .897-2 2v10c0 1.103.897 2 2 2h14c1.103 0 2-.897 2-2v-9.999c0-1.103-.897-2-2-2zM5 10h6V5.117c0-.57.407-1.07 1.002-1.117.266 0 .512.103.712.307a.956.956 0 0 1 .286.692V10h.995l.005.001h5V12H5v-2zm0 10v-6h14l.002 6H5z"></path>
                    </svg>
                </div>
                <div>
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" class="fill-sky-500 size-6">
                        <path d="M12 2C6.486 2 2 6.486 2 12s4.486 10 10 10 10-4.486 10-10S17.514 2 12 2zm0 18c-4.411 0-8-3.589-8-8s3.589-8 8-8 8 3.589 8 8-3.589 8-8 8z"></path>
                        <path d="M14.829 14.828a4.055 4.055 0 0 1-1.272.858 4.002 4.002 0 0 1-4.875-1.45l-1.658 1.119a6.063 6.063 0 0 0 1.621 1.62 5.963 5.963 0 0 0 2.148.903 6.042 6.042 0 0 0 2.415 0 5.972 5.972 0 0 0 2.148-.903c.313-.212.612-.458.886-.731.272-.271.52-.571.734-.889l-1.658-1.119a4.017 4.017 0 0 1-.489.592z"></path>
                        <circle cx="8.5" cy="10.5" r="1.5"></circle>
                        <circle cx="15.493" cy="10.493" r="1.493"></circle>
                    </svg>
                </div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Sizes Icons</h6>
        </div>
        <div class="card-body">
            <div class="flex flex-wrap items-center gap-3">
                <div><i class="text-xs bx bx-qr"></i></div>
                <div><i class="bx bx-qr text-14"></i></div>
                <div><i class="bx bx-qr"></i></div>
                <div><i class="bx bx-qr text-16"></i></div>
                <div><i class="text-lg bx bx-qr"></i></div>
                <div><i class="text-xl bx bx-qr"></i></div>
                <div><i class="text-2xl bx bx-qr"></i></div>
                <div><i class="text-3xl bx bx-qr"></i></div>
                <div><i class="text-4xl bx bx-qr"></i></div>
                <div><i class="text-5xl bx bx-qr"></i></div>
                <div><i class="text-6xl bx bx-qr"></i></div>
            </div>
        </div>
    </div><!--end col-->
</div><!--end grid-->



</div>
{{> partials/footer }}
</div>

{{> partials/vendor-scripts }}

<script type="module" src="assets/js/ui/advanced-highlight-code.init.js"></script>
<script type="module" src="assets/js/main.js"></script>

</body>
</html>