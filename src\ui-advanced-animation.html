{{> partials/main }}

<head>

    {{> partials/title-meta title="Animation" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}

{{> partials/page-heading title="Animation" sub-title="UI Advanced" }}

<h5 class="mb-5 underline">Fade Animation:</h5>

<div class="grid md:grid-cols-2 2xl:grid-cols-3 gap-x-space">
    <div class="card">
        <div class="card-body">
            <div class="mx-auto size-56 sm:size-64 md:size-80 bg-gradient-to-br from-primary-500/20 via-purple-500/20 to-sky-500/20" data-aos="fade-up"></div>
            <div class="mt-3 text-center">
                <code class="text-pink-500">data-aos="fade-up"</code>
            </div>
        </div>
    </div><!--end col-->
    <div class="card">
        <div class="card-body">
            <div class="mx-auto size-56 sm:size-64 md:size-80 bg-gradient-to-br from-primary-500/20 via-purple-500/20 to-sky-500/20" data-aos="fade-down"></div>
            <div class="mt-3 text-center">
                <code class="text-pink-500">data-aos="fade-down"</code>
            </div>
        </div>
    </div><!--end col-->
    <div class="card">
        <div class="card-body">
            <div class="mx-auto size-56 sm:size-64 md:size-80 bg-gradient-to-br from-primary-500/20 via-purple-500/20 to-sky-500/20" data-aos="fade-right"></div>
            <div class="mt-3 text-center">
                <code class="text-pink-500">data-aos="fade-right"</code>
            </div>
        </div>
    </div><!--end col-->
    <div class="card">
        <div class="card-body">
            <div class="mx-auto size-56 sm:size-64 md:size-80 bg-gradient-to-br from-primary-500/20 via-purple-500/20 to-sky-500/20" data-aos="fade-left"></div>
            <div class="mt-3 text-center">
                <code class="text-pink-500">data-aos="fade-left"</code>
            </div>
        </div>
    </div><!--end col-->
    <div class="card">
        <div class="card-body">
            <div class="mx-auto size-56 sm:size-64 md:size-80 bg-gradient-to-br from-primary-500/20 via-purple-500/20 to-sky-500/20" data-aos="fade-up-right"></div>
            <div class="mt-3 text-center">
                <code class="text-pink-500">data-aos="fade-up-right"</code>
            </div>
        </div>
    </div><!--end col-->
    <div class="card">
        <div class="card-body">
            <div class="mx-auto size-56 sm:size-64 md:size-80 bg-gradient-to-br from-primary-500/20 via-purple-500/20 to-sky-500/20" data-aos="fade-up-left"></div>
            <div class="mt-3 text-center">
                <code class="text-pink-500">data-aos="fade-up-left"</code>
            </div>
        </div>
    </div><!--end col-->
    <div class="card">
        <div class="card-body">
            <div class="mx-auto size-56 sm:size-64 md:size-80 bg-gradient-to-br from-primary-500/20 via-purple-500/20 to-sky-500/20" data-aos="fade-down-right"></div>
            <div class="mt-3 text-center">
                <code class="text-pink-500">data-aos="fade-down-right"</code>
            </div>
        </div>
    </div><!--end col-->
    <div class="card">
        <div class="card-body">
            <div class="mx-auto size-56 sm:size-64 md:size-80 bg-gradient-to-br from-primary-500/20 via-purple-500/20 to-sky-500/20" data-aos="fade-down-left"></div>
            <div class="mt-3 text-center">
                <code class="text-pink-500">data-aos="fade-down-left"</code>
            </div>
        </div>
    </div><!--end col-->
</div><!--end grid-->

<h5 class="mb-5 underline">Flip Animation:</h5>
<div class="grid md:grid-cols-2 2xl:grid-cols-3 gap-x-space">
    <div class="card">
        <div class="card-body">
            <div class="mx-auto size-56 sm:size-64 md:size-80 bg-gradient-to-br from-primary-500/20 via-purple-500/20 to-sky-500/20" data-aos="flip-left"></div>
            <div class="mt-3 text-center">
                <code class="text-pink-500">data-aos="flip-left"</code>
            </div>
        </div>
    </div><!--end col-->
    <div class="card">
        <div class="card-body">
            <div class="mx-auto size-56 sm:size-64 md:size-80 bg-gradient-to-br from-primary-500/20 via-purple-500/20 to-sky-500/20" data-aos="flip-right"></div>
            <div class="mt-3 text-center">
                <code class="text-pink-500">data-aos="flip-right"</code>
            </div>
        </div>
    </div><!--end col-->
    <div class="card">
        <div class="card-body">
            <div class="mx-auto size-56 sm:size-64 md:size-80 bg-gradient-to-br from-primary-500/20 via-purple-500/20 to-sky-500/20" data-aos="flip-up"></div>
            <div class="mt-3 text-center">
                <code class="text-pink-500">data-aos="flip-up"</code>
            </div>
        </div>
    </div><!--end col-->
    <div class="card">
        <div class="card-body">
            <div class="mx-auto size-56 sm:size-64 md:size-80 bg-gradient-to-br from-primary-500/20 via-purple-500/20 to-sky-500/20" data-aos="flip-down"></div>
            <div class="mt-3 text-center">
                <code class="text-pink-500">data-aos="flip-down"</code>
            </div>
        </div>
    </div><!--end col-->
</div><!--end grid-->

<h5 class="mb-5 underline">Zoom Animation:</h5>
<div class="grid md:grid-cols-2 2xl:grid-cols-3 gap-x-space">
    <div class="card">
        <div class="card-body">
            <div class="mx-auto size-56 sm:size-64 md:size-80 bg-gradient-to-br from-primary-500/20 via-purple-500/20 to-sky-500/20" data-aos="zoom-in"></div>
            <div class="mt-3 text-center">
                <code class="text-pink-500">data-aos="zoom-in"</code>
            </div>
        </div>
    </div><!--end col-->
    <div class="card">
        <div class="card-body">
            <div class="mx-auto size-56 sm:size-64 md:size-80 bg-gradient-to-br from-primary-500/20 via-purple-500/20 to-sky-500/20" data-aos="zoom-in-up"></div>
            <div class="mt-3 text-center">
                <code class="text-pink-500">data-aos="zoom-in-up"</code>
            </div>
        </div>
    </div><!--end col-->
    <div class="card">
        <div class="card-body">
            <div class="mx-auto size-56 sm:size-64 md:size-80 bg-gradient-to-br from-primary-500/20 via-purple-500/20 to-sky-500/20" data-aos="zoom-in-down"></div>
            <div class="mt-3 text-center">
                <code class="text-pink-500">data-aos="zoom-in-down"</code>
            </div>
        </div>
    </div><!--end col-->
    <div class="card">
        <div class="card-body">
            <div class="mx-auto size-56 sm:size-64 md:size-80 bg-gradient-to-br from-primary-500/20 via-purple-500/20 to-sky-500/20" data-aos="zoom-in-left"></div>
            <div class="mt-3 text-center">
                <code class="text-pink-500">data-aos="zoom-in-left"</code>
            </div>
        </div>
    </div><!--end col-->
    <div class="card">
        <div class="card-body">
            <div class="mx-auto size-56 sm:size-64 md:size-80 bg-gradient-to-br from-primary-500/20 via-purple-500/20 to-sky-500/20" data-aos="zoom-in-right"></div>
            <div class="mt-3 text-center">
                <code class="text-pink-500">data-aos="zoom-in-right"</code>
            </div>
        </div>
    </div><!--end col-->
    <div class="card">
        <div class="card-body">
            <div class="mx-auto size-56 sm:size-64 md:size-80 bg-gradient-to-br from-primary-500/20 via-purple-500/20 to-sky-500/20" data-aos="zoom-out"></div>
            <div class="mt-3 text-center">
                <code class="text-pink-500">data-aos="zoom-out"</code>
            </div>
        </div>
    </div><!--end col-->
    <div class="card">
        <div class="card-body">
            <div class="mx-auto size-56 sm:size-64 md:size-80 bg-gradient-to-br from-primary-500/20 via-purple-500/20 to-sky-500/20" data-aos="zoom-out-up"></div>
            <div class="mt-3 text-center">
                <code class="text-pink-500">data-aos="zoom-out-up"</code>
            </div>
        </div>
    </div><!--end col-->
    <div class="card">
        <div class="card-body">
            <div class="mx-auto size-56 sm:size-64 md:size-80 bg-gradient-to-br from-primary-500/20 via-purple-500/20 to-sky-500/20" data-aos="zoom-out-down"></div>
            <div class="mt-3 text-center">
                <code class="text-pink-500">data-aos="zoom-out-down"</code>
            </div>
        </div>
    </div><!--end col-->
    <div class="card">
        <div class="card-body">
            <div class="mx-auto size-56 sm:size-64 md:size-80 bg-gradient-to-br from-primary-500/20 via-purple-500/20 to-sky-500/20" data-aos="zoom-out-right"></div>
            <div class="mt-3 text-center">
                <code class="text-pink-500">data-aos="zoom-out-right"</code>
            </div>
        </div>
    </div><!--end col-->
    <div class="card">
        <div class="card-body">
            <div class="mx-auto size-56 sm:size-64 md:size-80 bg-gradient-to-br from-primary-500/20 via-purple-500/20 to-sky-500/20" data-aos="zoom-out-left"></div>
            <div class="mt-3 text-center">
                <code class="text-pink-500">data-aos="zoom-out-left"</code>
            </div>
        </div>
    </div><!--end col-->
</div><!--end grid-->

<h5 class="mb-5 underline">Different settings examples Animation:</h5>
<div class="grid md:grid-cols-2 2xl:grid-cols-3 gap-x-space">
    <div class="card">
        <div class="card-body">
            <div class="mx-auto size-56 sm:size-64 md:size-80 bg-gradient-to-br from-primary-500/20 via-purple-500/20 to-sky-500/20" data-aos="fade-up" data-aos-duration="3000"></div>
            <div class="mt-3 text-center">
                <code class="text-pink-500">data-aos="fade-up" data-aos-duration="3000"</code>
            </div>
        </div>
    </div><!--end col-->
    <div class="card">
        <div class="card-body">
            <div class="mx-auto size-56 sm:size-64 md:size-80 bg-gradient-to-br from-primary-500/20 via-purple-500/20 to-sky-500/20" data-aos="fade-down" data-aos-easing="linear" data-aos-duration="1500"></div>
            <div class="mt-3 text-center">
                <code class="text-pink-500">data-aos="fade-down" data-aos-easing="linear" data-aos-duration="1500"</code>
            </div>
        </div>
    </div><!--end col-->
    <div class="card">
        <div class="card-body">
            <div class="mx-auto size-56 sm:size-64 md:size-80 bg-gradient-to-br from-primary-500/20 via-purple-500/20 to-sky-500/20" data-aos="fade-right" data-aos-offset="300" data-aos-easing="ease-in-sine"></div>
            <div class="mt-3 text-center">
                <code class="text-pink-500">data-aos="fade-right" data-aos-offset="300" data-aos-easing="ease-in-sine"</code>
            </div>
        </div>
    </div><!--end col-->
    <div class="card">
        <div class="card-body">
            <div class="mx-auto size-56 sm:size-64 md:size-80 bg-gradient-to-br from-primary-500/20 via-purple-500/20 to-sky-500/20" data-aos="fade-left" data-aos-anchor="#example-anchor" data-aos-offset="500" data-aos-duration="500"></div>
            <div class="mt-3 text-center">
                <code class="text-pink-500">data-aos="fade-left" data-aos-anchor="#example-anchor" data-aos-offset="500" data-aos-duration="500"</code>
            </div>
        </div>
    </div><!--end col-->
    <div class="card">
        <div class="card-body">
            <div class="mx-auto size-56 sm:size-64 md:size-80 bg-gradient-to-br from-primary-500/20 via-purple-500/20 to-sky-500/20" data-aos="fade-zoom-in" data-aos-easing="ease-in-back" data-aos-delay="300" data-aos-offset="0"></div>
            <div class="mt-3 text-center">
                <code class="text-pink-500">data-aos="fade-zoom-in" data-aos-easing="ease-in-back" data-aos-delay="300" data-aos-offset="0"</code>
            </div>
        </div>
    </div><!--end col-->
    <div class="card">
        <div class="card-body">
            <div class="mx-auto size-56 sm:size-64 md:size-80 bg-gradient-to-br from-primary-500/20 via-purple-500/20 to-sky-500/20" data-aos="flip-left" data-aos-easing="ease-out-cubic" data-aos-duration="2000"></div>
            <div class="mt-3 text-center">
                <code class="text-pink-500">data-aos="flip-left" data-aos-easing="ease-out-cubic" data-aos-duration="2000"</code>
            </div>
        </div>
    </div><!--end col-->
</div><!--end grid-->

<h5 class="mb-5 underline">Anchor placement:</h5>
<div class="grid md:grid-cols-2 2xl:grid-cols-3 gap-x-space">
    <div class="card">
        <div class="card-body">
            <div class="mx-auto size-56 sm:size-64 md:size-80 bg-gradient-to-br from-primary-500/20 via-purple-500/20 to-sky-500/20" data-aos="fade-up" data-aos-anchor-placement="top-bottom"></div>
            <div class="mt-3 text-center">
                <code class="text-pink-500">data-aos="fade-up" data-aos-anchor-placement="top-bottom"</code>
            </div>
        </div>
    </div><!--end col-->
    <div class="card">
        <div class="card-body">
            <div class="mx-auto size-56 sm:size-64 md:size-80 bg-gradient-to-br from-primary-500/20 via-purple-500/20 to-sky-500/20" data-aos="fade-up" data-aos-anchor-placement="center-bottom"></div>
            <div class="mt-3 text-center">
                <code class="text-pink-500">data-aos="fade-up" data-aos-anchor-placement="center-bottom"</code>
            </div>
        </div>
    </div><!--end col-->
    <div class="card">
        <div class="card-body">
            <div class="mx-auto size-56 sm:size-64 md:size-80 bg-gradient-to-br from-primary-500/20 via-purple-500/20 to-sky-500/20" data-aos="fade-up" data-aos-anchor-placement="center-center"></div>
            <div class="mt-3 text-center">
                <code class="text-pink-500">data-aos="fade-up" data-aos-anchor-placement="center-center"</code>
            </div>
        </div>
    </div><!--end col-->
    <div class="card">
        <div class="card-body">
            <div class="mx-auto size-56 sm:size-64 md:size-80 bg-gradient-to-br from-primary-500/20 via-purple-500/20 to-sky-500/20" data-aos="fade-up" data-aos-anchor-placement="bottom-bottom"></div>
            <div class="mt-3 text-center">
                <code class="text-pink-500">data-aos="fade-up" data-aos-anchor-placement="bottom-bottom"</code>
            </div>
        </div>
    </div><!--end col-->
    <div class="card">
        <div class="card-body">
            <div class="mx-auto size-56 sm:size-64 md:size-80 bg-gradient-to-br from-primary-500/20 via-purple-500/20 to-sky-500/20" data-aos="fade-up" data-aos-anchor-placement="top-center"></div>
            <div class="mt-3 text-center">
                <code class="text-pink-500">data-aos="fade-up" data-aos-anchor-placement="top-center"</code>
            </div>
        </div>
    </div><!--end col-->
</div><!--end grid-->

</div>
{{> partials/footer }}
</div>

{{> partials/vendor-scripts }}

<script type="module" src="assets/js/ui/advanced-animation.init.js"></script>

<script type="module" src="assets/js/main.js"></script>

</body>
</html>