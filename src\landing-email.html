{{> partials/landing }}

<head>

    {{> partials/title-meta title="Email" }}

    <link href="/assets/libs/flatpickr/flatpickr.css" rel="stylesheet">
    <!-- plugins CSS -->
    <link rel="stylesheet" href="assets/css/plugins.css">
    <!-- Icons CSS -->
    <link rel="stylesheet" href="assets/css/icons.css">
    <!-- Tailwind CSS -->
    <link rel="stylesheet" href="assets/css/tailwind.css">

    <script type="module" src="assets/js/admin.bundle.js"></script>

</head>

{{> partials/body }}

<div x-data="{ isSticky: false , isMenuOpen: false , activeTab: 0 }" x-init="window.addEventListener('scroll', () => { isSticky = window.scrollY > 0 })">
    <header class="landing-navbar h-20 top-0 [&.scroll-sticky]:top-0 [&.scroll-sticky]:shadow-gray-200/50 [&.scroll-sticky]:shadow-lg [&.scroll-sticky]:bg-white dark:[&.scroll-sticky]:shadow-dark-850 dark:[&.scroll-sticky]:bg-dark-900" :class="{ 'scroll-sticky': isSticky }">
        <div class="container mx-auto px-4 flex items-center justify-between w-full gap-5">
            <a href="index.html" title="logo">
                <img src="assets/images/main-logo.png" alt="" class="inline-block h-7 dark:hidden">
                <img src="assets/images/logo-white.png" alt="" class="hidden h-7 dark:inline-block">
            </a>
            <div class="navbar-collapase ltr:mr-auto rtl:ml-auto" :class="{ 'hidden xl:flex': !isMenuOpen }">
                <div x-data="tabsHandler()" @scroll.window="updateTabOnScroll" class="flex flex-col xl:flex-row xl:items-center *:py-3 xl:py-0 xl:*:px-3 *:inline-block *:text-16 *:tracking-wide *:font-medium">

                    <a href="#services" @click="setActive(1)" :class="{ 'active': activeTab === 1 }" class="leading-normal [&.active]:text-primary-500 hover:text-primary-500 transition duration-300 ease-linear">
                        Service
                    </a>
                    <a href="#pricing" @click="setActive(2)" :class="{ 'active': activeTab === 2 }" class="leading-normal [&.active]:text-primary-500 hover:text-primary-500 transition duration-300 ease-linear">
                        Pricing
                    </a>
                    <a href="#features" @click="setActive(3)" :class="{ 'active': activeTab === 3 }" class="leading-normal [&.active]:text-primary-500 hover:text-primary-500 transition duration-300 ease-linear">
                        Features
                    </a>
                    <a href="#templates" @click="setActive(4)" :class="{ 'active': activeTab === 4 }" class="leading-normal [&.active]:text-primary-500 hover:text-primary-500 transition duration-300 ease-linear">
                        Templates
                    </a>
                    <a href="#faq" @click="setActive(5)" :class="{ 'active': activeTab === 5 }" class="leading-normal [&.active]:text-primary-500 hover:text-primary-500 transition duration-300 ease-linear">
                        FAQ's
                    </a>
                    <a href="#updates" @click="setActive(6)" :class="{ 'active': activeTab === 6 }" class="leading-normal [&.active]:text-primary-500 hover:text-primary-500 transition duration-300 ease-linear">
                        Updates
                    </a>
                </div>
            </div>
            <button @click="isMenuOpen = !isMenuOpen" type="button" class="rounded-full xl:ltr:ml-0 xl:rtl:mr-0 ltr:ml-auto rtl:mr-auto navbar-toggle btn btn-sub-sky btn-icon xl:!hidden">
                <i :class="isMenuOpen ? 'ri-close-line' : 'ri-menu-2-line'" class="text-lg"></i>
            </button>
            <div>
                <a href="auth-signin-basic.html" class="btn btn-active-gray">Sign In</a>
                <button type="button" class="hidden btn btn-sub-primary md:inline-block">Get for free</button>
            </div>
        </div>
    </header>
</div>
<section class="relative pb-20 overflow-hidden pt-28 lg:pt-40 xl:pb-48 2xl:pt-80">
    <div class="container mx-auto px-4">
        <div class="grid grid-cols-12 lg:gap-x-16">
            <div class="col-span-12 xl:col-span-5" data-aos="fade-up" data-aos-duration="2000">
                <h2 class="mb-2 leading-normal capitalize xl:leading-normal xl:text-5xl">Customers with the <span class="text-primary-500">#1</span> email marketing and automation.</h2>
                <p class="mb-5 text-lg text-gray-500 dark:text-dark-500">With AI-powered threat defenses, enterprise-grade security and privacy controls, and Google's security-by-design cloud infrastructure, Gmail helps keep your data protected, confidential, and compliant.</p>
                <div class="flex flex-wrap items-center gap-3">
                    <a href="apps-mailbox.html" class="btn btn-primary">Request Demo <i data-lucide="move-right" class="inline-block ml-1 size-4"></i></a>
                    <button type="button" class="border-gray-200 dark:border-dark-800 btn btn-outline-gray">Open in you browser</button>
                </div>
            </div>
            <div class="col-span-12 mt-8 xl:col-span-6 xl:col-start-7 md:mt-10 xl:mt-0">
                <div>
                    <img src="assets/images/email/main.png" alt="" class="relative dark:hidden xl:scale-[1.8] object-cover border border-gray-200 dark:border-dark-800 rounded-lg ltr:xl:-right-44 rtl:xl:-left-44 xl:top-16 2xl:top-auto">
                    <img src="assets/images/email/email-dark.png" alt="" class="relative hidden dark:block xl:scale-[1.8] object-cover border border-gray-200 dark:border-dark-800 rounded-lg ltr:xl:-right-44 rtl:xl:-left-44 xl:top-16 2xl:top-auto">
                </div>
            </div>
        </div>
    </div>
</section>

<section class="relative py-12 md:py-24 bg-gray-50 dark:bg-dark-900/50" id="services">
    <div class="container mx-auto px-4">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-space">
            <div data-aos="fade-up" data-aos-duration="2000">
                <div class="p-4 mb-4 bg-white rounded-lg text-primary-500 size-14 dark:bg-dark-900">
                    <i data-lucide="fan" class="fill-primary-500/20"></i>
                </div>
                <h5 class="mb-1">Stay Professional</h5>
                <p class="mb-4 text-gray-500 dark:text-dark-500 text-16">To most people, acting like a professional means working and behaving in such a way that others think of them as competent, reliable and respectful.</p>
                <a href="#!" class="link link-primary text-16">Read More <i data-lucide="move-right" class="inline-block ml-1 size-4"></i></a>
            </div>
            <div data-aos="fade-up" data-aos-duration="2000">
                <div class="p-4 mb-4 bg-white rounded-lg text-primary-500 size-14 dark:bg-dark-900">
                    <i data-lucide="gallery-vertical-end" class="fill-primary-500/20"></i>
                </div>
                <h5 class="mb-1">Notes & Documents</h5>
                <p class="mb-4 text-gray-500 dark:text-dark-500 text-16">A mortgage note document is a legally binding instrument that outlines the terms of a loan agreement between a borrower and a lender, specifying.</p>
                <a href="#!" class="link link-primary text-16">Read More <i data-lucide="move-right" class="inline-block ml-1 size-4"></i></a>
            </div>
            <div data-aos="fade-up" data-aos-duration="2000">
                <div class="p-4 mb-4 bg-white rounded-lg text-primary-500 size-14 dark:bg-dark-900">
                    <i data-lucide="gem" class="fill-primary-500/20"></i>
                </div>
                <h5 class="mb-1">Automation</h5>
                <p class="mb-4 text-gray-500 dark:text-dark-500 text-16">Automation is the application of technology, programs, robotics or processes to achieve outcomes with minimal human input.</p>
                <a href="#!" class="link link-primary text-16">Read More <i data-lucide="move-right" class="inline-block ml-1 size-4"></i></a>
            </div>
        </div>
    </div>
</section>

<section class="relative py-12 md:py-24" id="pricing">
    <div class="container mx-auto px-4">
        <div class="max-w-3xl mx-auto mb-12 text-center">
            <h2 class="mb-3 leading-normal capitalize">Choose the plan that suits you best</h2>
            <p class="text-gray-500 dark:text-dark-500 text-16">A pricing strategy is a model or method used to establish the best price for a product or service. It helps you choose prices to maximize profits and shareholder value while considering.</p>
        </div>

        <div class="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
            <div class="relative overflow-hidden card" data-aos="fade-up" data-aos-duration="2000">
                <div class="absolute border-[40px] border-gray-100 dark:border-dark-850 size-64 -bottom-16 -right-16 rounded-creative"></div>
                <div class="relative lg:p-8 card-body">
                    <div class="mb-8 text-center">
                        <h5 class="mb-1">Free</h5>
                        <p class="mb-6 text-gray-500 dark:text-dark-500">Explore domiex mail together, for the</p>
                        <h1>$0<sub class="text-sm text-gray-500 dark:text-dark-500">/month</sub></h1>
                    </div>
                    <a href="pages-pricing.html" class="w-full btn btn-sub-gray">Get Started for free</a>
                    <ul class="space-y-4 *:flex *:items-center *:gap-2 mt-7">
                        <li><i data-lucide="corner-down-right" class="inline-block text-green-500 fill-green-500/10 size-4"></i> 500 Emails</li>
                        <li><i data-lucide="corner-down-right" class="inline-block text-green-500 fill-green-500/10 size-4"></i> 5 Automation Action</li>
                        <li><i data-lucide="corner-down-right" class="inline-block text-green-500 fill-green-500/10 size-4"></i> 1 Products & Projects</li>
                        <li><i data-lucide="x" class="inline-block text-gray-500 fill-gray-100 dark:text-dark-500 dark:fill-dark-850 size-4"></i> Custom Permissions</li>
                        <li><i data-lucide="x" class="inline-block text-gray-500 fill-gray-100 dark:text-dark-500 dark:fill-dark-850 size-4"></i> Some Basic Integration</li>
                        <li><i data-lucide="x" class="inline-block text-gray-500 fill-gray-100 dark:text-dark-500 dark:fill-dark-850 size-4"></i> Up to 5 Team Members</li>
                        <li><i data-lucide="x" class="inline-block text-gray-500 fill-gray-100 dark:text-dark-500 dark:fill-dark-850 size-4"></i> 24/7 Support</li>
                    </ul>
                </div>
            </div>
            <div class="relative overflow-hidden card active group" data-aos="fade-up" data-aos-duration="2000">
                <div class="absolute border-[40px] border-gray-100 dark:border-dark-850 size-64 -bottom-16 -right-16 rounded-creative"></div>
                <div class="relative lg:p-8 card-body">
                    <div class="absolute right-5 top-5 badge badge-pink">Popular Plan</div>
                    <div class="mb-8 text-center">
                        <h5 class="mb-1">Professional</h5>
                        <p class="mb-6 text-gray-500">For professionals & small teams</p>
                        <h1>$29.99<sub class="text-sm text-gray-500">/month</sub></h1>
                    </div>
                    <a href="pages-pricing.html" class="w-full btn btn-primary">Get Started</a>
                    <ul class="space-y-4 *:flex *:items-center *:gap-2 mt-7">
                        <li><i data-lucide="corner-down-right" class="inline-block text-green-500 fill-green-500/10 size-4"></i> 2000 Emails</li>
                        <li><i data-lucide="corner-down-right" class="inline-block text-green-500 fill-green-500/10 size-4"></i> 20 Automation Action</li>
                        <li><i data-lucide="corner-down-right" class="inline-block text-green-500 fill-green-500/10 size-4"></i> 1 Products & Projects</li>
                        <li><i data-lucide="corner-down-right" class="inline-block text-green-500 fill-green-500/10 size-4"></i> Custom Permissions</li>
                        <li><i data-lucide="x" class="inline-block text-gray-500 fill-gray-100 dark:text-dark-500 dark:fill-dark-850 size-4"></i> Some Basic Integration</li>
                        <li><i data-lucide="x" class="inline-block text-gray-500 fill-gray-100 dark:text-dark-500 dark:fill-dark-850 size-4"></i> Up to 5 Team Members</li>
                        <li><i data-lucide="x" class="inline-block text-gray-500 fill-gray-100 dark:text-dark-500 dark:fill-dark-850 size-4"></i> 24/7 Support</li>
                    </ul>
                </div>
            </div>
            <div class="relative overflow-hidden card" data-aos="fade-up" data-aos-duration="2000">
                <div class="absolute border-[40px] border-gray-100 dark:border-dark-850 size-64 -bottom-16 -right-16 rounded-creative"></div>
                <div class="relative lg:p-8 card-body">
                    <div class="mb-8 text-center">
                        <h5 class="mb-1">Organization</h5>
                        <p class="mb-6 text-gray-500 dark:text-dark-500">Ideal for fastest growing businesses</p>
                        <h1>Lat's Chat</h1>
                    </div>
                    <a href="pages-contact-us.html" class="w-full btn btn-sub-gray">Contact Us</a>
                    <ul class="space-y-4 *:flex *:items-center *:gap-2 mt-7">
                        <li><i data-lucide="corner-down-right" class="inline-block text-green-500 fill-green-500/10 size-4"></i> Unlimited Emails</li>
                        <li><i data-lucide="corner-down-right" class="inline-block text-green-500 fill-green-500/10 size-4"></i> Unlimited automation</li>
                        <li><i data-lucide="corner-down-right" class="inline-block text-green-500 fill-green-500/10 size-4"></i> 1 Products & Projects</li>
                        <li><i data-lucide="corner-down-right" class="inline-block text-green-500 fill-green-500/10 size-4"></i> Access All Features</li>
                        <li><i data-lucide="corner-down-right" class="inline-block text-green-500 fill-green-500/10 size-4"></i> Flexible Contract</li>
                        <li><i data-lucide="corner-down-right" class="inline-block text-green-500 fill-green-500/10 size-4"></i> Advanced Integration</li>
                        <li><i data-lucide="corner-down-right" class="inline-block text-green-500 fill-green-500/10 size-4"></i> 24/7 Support</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</section>

<section class="relative md:pb-28" id="features">
    <div class="container mx-auto px-4">
        <div class="max-w-3xl mx-auto mb-12 text-center">
            <h2 class="mb-3 leading-normal capitalize">Domiex Email Features</h2>
            <p class="text-gray-500 dark:text-dark-500 text-16">Collaborate with your teammates using our real-time editor to compose professional emails.</p>
        </div>
        <div class="grid grid-cols-12 gap-x-space">
            <div class="col-span-12 row-span-2 text-center bg-gray-100 dark:bg-dark-900/40 md:col-span-3 card">
                <div class="card-body">
                    <img src="assets/images/email/features-1.png" alt="" class="w-full rounded-md dark:hidden">
                    <img src="assets/images/email/features-1-dark.png" alt="" class="w-full rounded-md hidden dark:block">
                    <h6 class="mt-4">Easy to change Email Accounts</h6>
                </div>
            </div>
            <div class="col-span-12 row-span-2 text-center bg-gray-100 dark:bg-dark-900/40 md:col-span-5 card">
                <div class="card-body">
                    <img src="assets/images/email/features-2.png" alt="" class="w-full rounded-md dark:hidden">
                    <img src="assets/images/email/features-2-dark.png" alt="" class="w-full hidden dark:block rounded-md">
                    <h6 class="mt-4">Filter wise Email Lists</h6>
                </div>
            </div>
            <div class="col-span-12 text-center bg-gray-100 dark:bg-dark-900/40 md:col-span-4 card">
                <div class="space-y-8 card-body">
                    <div>
                        <img src="assets/images/email/features-3.png" alt="" class="w-full rounded-md dark:hidden">
                        <img src="assets/images/email/features-3-dark.png" alt="" class="w-full rounded-md hidden dark:block">
                        <h6 class="mt-4">Reply & New Added</h6>
                    </div>

                    <div>
                        <img src="assets/images/email/features-4.png" alt="" class="w-full rounded-md dark:hidden">
                        <img src="assets/images/email/features-4-dark.png" alt="" class="w-full rounded-md hidden dark:block">
                        <h6 class="mt-4">Email Reads</h6>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<section class="relative lg:py-24 bg-primary-900" id="templates">
    <div class="container mx-auto px-4">
        <div class="lg:flex gap-x-16">
            <div class="w-full py-12 text-white lg:w-3/5 lg:py-0">
                <h2 class="mb-2 leading-normal capitalize xl:text-5xl">Customers with the <span class="text-primary-500">#1</span> email marketing and automation.</h2>
                <p class="mb-5 text-lg text-white">Email automation helps you find your audience and engage your customers. Unlike manual campaigns, automations run in the background while you tend to other valuable tasks. With automation, you can send personalized individual.</p>
                <div class="flex flex-wrap items-center gap-3">
                    <a href="dashboard-email.html" class="btn btn-primary">Request Demo <i data-lucide="move-right" class="inline-block ml-1 size-4"></i></a>
                    <button type="button" class="btn">Open in you browser <i data-lucide="move-right" class="inline-block ml-1 size-4"></i></button>
                </div>
            </div>
            <div class="lg:w-2/5">
                <img src="assets/images/email/cta.png" alt="" class="w-[300px] mx-auto xl:scale-150 xl:-mt-16 relative object-cover rounded-lg ">
            </div>
        </div>
    </div>
</section>

<section class="relative py-12 md:py-24" id="faq">
    <div class="container mx-auto px-4">
        <div class="max-w-3xl mx-auto mb-12 text-center">
            <h2 class="mb-3 leading-normal capitalize">Don't Just Take Our Word For It</h2>
            <p class="text-gray-500 dark:text-dark-500 text-16">Frequently asked questions. <a href="#!" class="font-medium text-gray-800 dark:text-dark-100 underline">View All FAQs</a></p>
        </div>
        <div class="grid grid-cols-12">
            <div class="col-span-12 lg:col-span-8 lg:col-start-3">
                <div x-data="{selected:1}">
                    <div class="relative border-b border-gray-200 dark:border-dark-800 last:border-0">
                        <button type="button" class="block w-full px-4 py-4 font-medium transition duration-200 ease-linear md:px-5" @click="selected !== 1 ? selected = 1 : selected = null" x-bind:class="{ 'bg-gray-50 text-primary-500 dark:bg-dark-900/30': selected === 1 }">
                            <div class="flex items-center justify-between">
                                <span>What are the features of email?</span>
                                <span class="text-gray-500 dark:text-dark-500 ico-down" x-bind:class="{ 'text-gray-500 dark:text-dark-500': selected === 1 }" x-show="selected !== 1"><i data-lucide="chevron-down" class="size-5"></i></span>
                                <span class="text-gray-500 dark:text-dark-500 ico-up" x-bind:class="{ 'text-gray-500 dark:text-dark-500': selected === 1 }" x-show="selected === 1"><i data-lucide="chevron-up" class="size-5"></i></span>
                            </div>
                        </button>
                        <div class="relative overflow-hidden transition-all duration-700 max-h-0" x-ref="container1" x-bind:style="selected == 1 ? 'max-height: ' + $refs.container1.scrollHeight + 'px' : ''">
                            <div class="p-5 text-gray-500 dark:text-dark-500">
                                <p>Emails are automatically date and time stamped. signatures can be attached. files, graphics close graphicA visual image displayed on screen or stored as data. or sound can be sent as attachments close attachmentA file that is sent with an email., often in compressed formats. webmail and mobile email.</p>
                            </div>
                        </div>
                    </div>
                    <div class="relative border-b border-gray-200 dark:border-dark-800 last:border-0">
                        <button type="button" class="block w-full px-4 py-4 font-medium transition duration-200 ease-linear md:px-5" @click="selected !== 2 ? selected = 2 : selected = null" x-bind:class="{ 'bg-gray-50 text-primary-500 dark:bg-dark-900/30': selected === 2 }">
                            <div class="flex items-center justify-between">
                                <span>How do you use email features?</span>
                                <span class="text-gray-500 dark:text-dark-500 ico-down" x-bind:class="{ 'text-gray-500 dark:text-dark-500': selected === 2 }" x-show="selected !== 2"><i data-lucide="chevron-down" class="size-5"></i></span>
                                <span class="text-gray-500 dark:text-dark-500 ico-up" x-bind:class="{ 'text-gray-500 dark:text-dark-500': selected === 2 }" x-show="selected === 2"><i data-lucide="chevron-up" class="size-5"></i></span>
                            </div>
                        </button>
                        <div class="relative overflow-hidden transition-all duration-700 max-h-0" x-ref="container2" x-bind:style="selected == 2 ? 'max-height: ' + $refs.container2.scrollHeight + 'px' : ''">
                            <div class="p-5">
                                <h6 class="mb-2">Change smart features & personalization settings</h6>
                                <ol class="space-y-2 list-decimal list-inside">
                                    <li>On your Android phone or tablet, open Gmail.</li>
                                    <li>Tap Menu. Settings. the account you want to change.</li>
                                    <li>Scroll to the "General" section.</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                    <div class="relative border-b border-gray-200 dark:border-dark-800 last:border-0">
                        <button type="button" class="block w-full px-4 py-4 font-medium transition duration-200 ease-linear md:px-5" @click="selected !== 3 ? selected = 3 : selected = null" x-bind:class="{ 'bg-gray-50 text-primary-500 dark:bg-dark-900/30': selected === 3 }">
                            <div class="flex items-center justify-between">
                                <span>What is the main use of email?</span>
                                <span class="text-gray-500 dark:text-dark-500 ico-down" x-bind:class="{ 'text-gray-500 dark:text-dark-500': selected === 3 }" x-show="selected !== 3"><i data-lucide="chevron-down" class="size-5"></i></span>
                                <span class="text-gray-500 dark:text-dark-500 ico-up" x-bind:class="{ 'text-gray-500 dark:text-dark-500': selected === 3 }" x-show="selected === 3"><i data-lucide="chevron-up" class="size-5"></i></span>
                            </div>
                        </button>
                        <div class="relative overflow-hidden transition-all duration-700 max-h-0" x-ref="container3" x-bind:style="selected == 3 ? 'max-height: ' + $refs.container3.scrollHeight + 'px' : ''">
                            <div class="p-5">
                                <p class="text-gray-500 dark:text-dark-500">Email is a beneficial way to communicate with individuals or small groups of friends or colleagues. It enables users to easily send and receive documents, images, links and other files. It also gives users the flexibility to communicate with others on their own schedule. Notifications, reminders and follow-ups.</p>
                            </div>
                        </div>
                    </div>
                    <div class="relative border-b border-gray-200 dark:border-dark-800 last:border-0">
                        <button type="button" class="block w-full px-4 py-4 font-medium transition duration-200 ease-linear md:px-5" @click="selected !== 4 ? selected = 4 : selected = null" x-bind:class="{ 'bg-gray-50 text-primary-500 dark:bg-dark-900/30': selected === 4 }">
                            <div class="flex items-center justify-between">
                                <span>How email really works?</span>
                                <span class="text-gray-500 dark:text-dark-500 ico-down" x-bind:class="{ 'text-gray-500 dark:text-dark-500': selected === 4 }" x-show="selected !== 4"><i data-lucide="chevron-down" class="size-5"></i></span>
                                <span class="text-gray-500 dark:text-dark-500 ico-up" x-bind:class="{ 'text-gray-500 dark:text-dark-500': selected === 4 }" x-show="selected === 4"><i data-lucide="chevron-up" class="size-5"></i></span>
                            </div>
                        </button>
                        <div class="relative overflow-hidden transition-all duration-700 max-h-0" x-ref="container4" x-bind:style="selected == 4 ? 'max-height: ' + $refs.container4.scrollHeight + 'px' : ''">
                            <div class="p-5">
                                <p class="text-gray-500 dark:text-dark-500">The email client (web/ mobile/ desktop) connects to the Outgoing SMTP server based on the email account you used. The email client handovers the email in MIME format to the Outgoing SMTP server. The Outgoing SMTP validates the sender's details and processes the message for sending.</p>
                            </div>
                        </div>
                    </div>
                    <div class="relative border-b border-gray-200 dark:border-dark-800 last:border-0">
                        <button type="button" class="block w-full px-4 py-4 font-medium transition duration-200 ease-linear md:px-5" @click="selected !== 5 ? selected = 5 : selected = null" x-bind:class="{ 'bg-gray-50 text-primary-500 dark:bg-dark-900/30': selected === 5 }">
                            <div class="flex items-center justify-between">
                                <span>What are emails used for?</span>
                                <span class="text-gray-500 dark:text-dark-500 ico-down" x-bind:class="{ 'text-gray-500 dark:text-dark-500': selected === 5 }" x-show="selected !== 5"><i data-lucide="chevron-down" class="size-5"></i></span>
                                <span class="text-gray-500 dark:text-dark-500 ico-up" x-bind:class="{ 'text-gray-500 dark:text-dark-500': selected === 5 }" x-show="selected === 5"><i data-lucide="chevron-up" class="size-5"></i></span>
                            </div>
                        </button>
                        <div class="relative overflow-hidden transition-all duration-700 max-h-0" x-ref="container5" x-bind:style="selected == 5 ? 'max-height: ' + $refs.container5.scrollHeight + 'px' : ''">
                            <div class="p-5">
                                <p class="text-gray-500 dark:text-dark-500">Email is used for many different purposes, including contacting friends, communicating with professors and supervisors, requesting information, and applying for jobs, internships, and scholarships.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<section class="relative pt-12 overflow-hidden md:pt-24 bg-gray-50 dark:bg-dark-900/50" id="updates">
    <div class="container mx-auto px-4">
        <div class="max-w-3xl mx-auto mb-10 text-center">
            <h2 class="mb-3 leading-normal capitalize">Customers can easily receive new updates</h2>
            <p class="text-gray-500 dark:text-dark-500 mb-7 text-16">Domiex enables seamless collaboration with clients and team members.</p>
            <div class="flex flex-wrap items-center justify-center gap-2">
                <button type="button" class="btn btn-primary"><i data-lucide="apple" class="inline-block mb-0.5 mr-1 size-4"></i> Download For Mac</button>
                <button type="button" class="btn btn-sub-gray">Open Live Demos <i data-lucide="move-right" class="inline-block ml-1 mb-0.5 size-4"></i></button>
            </div>
        </div>
        <div class="grid grid-cols-12">
            <div class="col-span-12 lg:col-span-10 lg:col-start-2">
                <img src="assets/images/email/email.png" alt="" class="relative border-2 border-gray-200 rounded-lg dark:border-dark-800 -bottom-5 dark:hidden" data-aos="fade-up" data-aos-duration="2000" data-aos-offset="300">
                <img src="assets/images/email/email-dark.png" alt="" class="relative border-2 border-gray-200 rounded-lg dark:border-dark-800 -bottom-5 hidden dark:block" data-aos="fade-up" data-aos-duration="2000" data-aos-offset="300">
            </div>
        </div>
    </div>
</section>

<footer class="relative bg-gray-900 dark:bg-dark-900">
    <div class="py-12 lg:py-20">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-12 gap-6">
                <div class="col-span-12 xl:col-span-6">
                    <div class="max-w-lg">
                        <a href="index.html" title="logo"><img src="assets/images/logo-white.png" alt="" class="h-7"></a>
                        <p class="mt-4 mb-6 text-gray-500 dark:text-dark-500 text-16">This email and any attachments may contain confidential information. If you are not the intended recipient, please notify the sender immediately and delete this email. Unauthorized use, disclosure, or copying of the contents is strictly prohibited.</p>
                        <div class="flex items-center gap-5">
                            <a href="#!" title="facebook" class="relative flex items-center justify-center transition duration-300 ease-linear hover:-translate-y-1 after:absolute after:inset-0 after:rounded-lg after:-rotate-45 after:bg-gray-800/50 dark:after:bg-dark-800/50 size-10 text-primary-500"><i data-lucide="facebook" class="relative z-10 size-5"></i></a>
                            <a href="#!" title="dribbble" class="relative flex items-center justify-center text-pink-500 transition duration-300 ease-linear hover:-translate-y-1 after:absolute after:inset-0 after:rounded-lg after:-rotate-45 after:bg-gray-800/50 dark:after:bg-dark-800/50 size-10"><i data-lucide="dribbble" class="relative z-10 size-5"></i></a>
                            <a href="#!" title="twitter" class="relative flex items-center justify-center transition duration-300 ease-linear hover:-translate-y-1 text-sky-500 after:absolute after:inset-0 after:rounded-lg after:-rotate-45 after:bg-gray-800/50 dark:after:bg-dark-800/50 size-10"><i data-lucide="twitter" class="relative z-10 size-5"></i></a>
                            <a href="#!" title="youtube" class="relative flex items-center justify-center text-red-500 transition duration-300 ease-linear hover:-translate-y-1 after:absolute after:inset-0 after:rounded-lg after:-rotate-45 after:bg-gray-800/50 dark:after:bg-dark-800/50 size-10"><i data-lucide="youtube" class="relative z-10 size-5"></i></a>
                        </div>
                    </div>
                </div><!--end col-->
                <div class="col-span-12 md:col-span-4 xl:col-span-2">
                    <h6 class="mb-4 text-gray-200 dark:text-dark-200 text-17">Features</h6>
                    <ul class="space-y-5 text-16">
                        <li><a href="#!" class="link link-primary">Modern Inbox</a></li>
                        <li><a href="#!" class="link link-primary">Search</a></li>
                        <li><a href="#!" class="link link-primary">Send Later</a></li>
                        <li><a href="#!" class="link link-primary">All Features</a></li>
                        <li><a href="#!" class="link link-primary">Dashboards</a></li>
                    </ul>
                </div>
                <div class="col-span-12 md:col-span-4 xl:col-span-2">
                    <h6 class="mb-4 text-gray-200 text-17">Resource</h6>
                    <ul class="space-y-5 text-16">
                        <li><a href="#!" class="link link-primary">Blog</a></li>
                        <li><a href="#!" class="link link-primary">Help Center</a></li>
                        <li><a href="#!" class="link link-primary">Community</a></li>
                        <li><a href="#!" class="link link-primary">Video</a></li>
                        <li><a href="#!" class="link link-primary">FAQ's</a></li>
                        <li><a href="#!" class="link link-primary">Systems Status</a></li>
                        <li><a href="#!" class="link link-primary">API</a></li>
                    </ul>
                </div>
                <div class="col-span-12 md:col-span-4 xl:col-span-2">
                    <h6 class="mb-4 text-gray-200 text-17">Company</h6>
                    <ul class="space-y-5 text-16">
                        <li><a href="#!" class="link link-primary">About</a></li>
                        <li><a href="#!" class="link link-primary">Careers</a></li>
                        <li><a href="#!" class="link link-primary">Feedback</a></li>
                        <li><a href="#!" class="link link-primary">Contact Us</a></li>
                        <li><a href="#!" class="link link-primary">News</a></li>
                        <li><a href="#!" class="link link-primary">Privacy Policy</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    <div class="container mx-auto px-4">
        <div class="py-6 text-gray-500 dark:text-dark-500 text-16">
            <p x-data="{ year: new Date().getFullYear() }">
                &copy; <span x-text="year"></span> Domiex. Crafted & Design by <a href="javascript:void(0);" class="font-medium text-gray-200 dark:text-dark-200 link">SRBThemes</a>
            </p>
        </div>
    </div>
</footer>

<button class="fixed flex items-center justify-center text-white ltr:right-0 rtl:left-0 bg-primary-500 ltr:rounded-l-md rtl:rounded-r-md size-12 top-1/2" x-on:click="let mode = document.querySelector('[data-mode]').getAttribute('data-mode');
let newMode = mode === 'light' ? 'dark' : 'light';
document.querySelector('[data-mode]').setAttribute('data-mode', newMode);">
    <i data-lucide="moon" class="inline-block size-5 dark:hidden"></i>
    <i data-lucide="sun" class="hidden size-5 dark:inline-block"></i>
</button>

{{> partials/vendor-scripts }}

<script type="module" src="assets/js/landing/email.init.js"></script>

<script>
    document.addEventListener("alpine:init", () => {
        Alpine.data("tabsHandler", () => ({
            activeTab: 1,
            sections: ["services", "pricing", "features", "templates", "faq", "updates"],

            setActive(tab) {
                this.activeTab = tab;
                document.getElementById(this.sections[tab - 1]).scrollIntoView({ behavior: "smooth" });
            },

            updateTabOnScroll() {
                let scrollPosition = window.scrollY;
                this.sections.forEach((id, index) => {
                    let section = document.getElementById(id);
                    if (section) {
                        let offset = section.offsetTop - 120; // Adjust based on header size
                        let height = section.offsetHeight;
                        if (scrollPosition >= offset && scrollPosition < offset + height) {
                            this.activeTab = index + 1;
                        }
                    }
                });
            }
        }));
    });
</script>

<script type="module" src="assets/js/main.js"></script>

</body>

</html>