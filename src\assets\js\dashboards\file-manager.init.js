/*
Template Name: Domiex - Admin & Dashboard Template
Author: SRBThemes
Version: 1.0.0
File: file manager dashboards init Js File
*/

import fileManagerData from "../../json/dashboards/file-manager";
import { getColorCodes } from "../helpers/helper";
import ApexCharts from 'apexcharts';

//Analytics Chart
document.addEventListener("alpine:init", () => {
    Alpine.data("analyticsApp", () => ({
        series: [{
            name: 'Total GB',
            data: [44, 55, 41, 67, 22]
        }],
        labels: ['Dropbox', 'Cloud', 'Mega', 'Google', 'Drive'],
        init() {
            // Fetch color codes first
            this.colorCodes = getColorCodes(this.$refs.analyticsChart.dataset);

            // Initialize the chart with options
            let analyticsChart = new ApexCharts(this.$refs.analyticsChart, this.options);
            analyticsChart.render();
        },
        get options() {
            return {
                series: this.series,
                chart: {
                    height: 315,
                    type: 'bar',
                    toolbar: {
                        show: false,
                    }
                },
                plotOptions: {
                    bar: {
                        borderRadius: 10,
                        columnWidth: '50%',
                    }
                },
                dataLabels: {
                    enabled: false
                },
                stroke: {
                    width: 1
                },
                colors: getColorCodes(this.$refs.analyticsChart.dataset),
                grid: {
                    padding: {
                        right: -12,
                        top: -18,
                        bottom: -8
                    }
                },
                xaxis: {
                    labels: {
                        rotate: -45
                    },
                    categories: this.labels,
                    tickPlacement: 'on'
                },
                fill: {
                    type: 'gradient',
                    gradient: {
                        shade: 'light',
                        type: "horizontal",
                        shadeIntensity: 0.25,
                        gradientToColors: undefined,
                        inverseColors: [this.colorCodes[0]],
                        opacityFrom: 0.85,
                        opacityTo: 0.85,
                        stops: [50, 0, 100]
                    },
                }
            };
        }
    }));
});

//Overview Storage Chart
document.addEventListener("alpine:init", () => {
    Alpine.data("overviewStorageApp", () => ({
        series: [44, 55, 41, 17, 15],
        labels: ["Docs", "Images", "Video", "Audio", "Others"],
        init() {
            // Initial chart render
            this.renderChart();

            // Reload chart on window resize
            window.addEventListener('resize', this.reloadChart.bind(this));
        },
        renderChart() {
            // Destroy previous instance if exists
            if (this.overviewStorageChart)
                this.overviewStorageChart.destroy();

            // Initialize new chart
            this.overviewStorageChart = new ApexCharts(this.$refs.overviewStorageChart, this.options);
            this.overviewStorageChart.render();
        },
        reloadChart() {
            // Handle the logic for resizing
            this.renderChart(); // Re-render chart on resize
        },
        get options() {
            return {
                series: this.series,
                chart: {
                    height: 340,
                    type: "donut",
                },
                plotOptions: {
                    pie: {
                        startAngle: -90,
                        endAngle: 90,
                        offsetY: 5
                    }
                },
                colors: getColorCodes(this.$refs.overviewStorageChart.dataset),
                grid: {
                    padding: {
                        bottom: -80
                    }
                },
                stroke: {
                    width: 0,
                },
                fill: {
                    type: 'gradient',
                },
                labels: this.labels,
                legend: {
                    position: 'bottom'
                }
            };
        }
    }));
});

//my favorite slider
var swiper = new Swiper(".mySwiper", {
    spaceBetween: 24,
    grabCursor: true,
    slidesPerView: 1,
    loop: true,
    autoplay: {
        delay: 2500,
        disableOnInteraction: false,
    },
    navigation: {
        nextEl: ".swiper-button-next",
        prevEl: ".swiper-button-prev",
    },
});

const filesImages = import.meta.glob('/assets/images/file-manager/icons/*.{png,jpg,jpeg,svg}');
function quickTable() {
    return {
        data: [],
        searchTerm: '',
        init() {
            this.data = fileManagerData;
            this.originalData = fileManagerData;
        },
        filterData() {
            const searchTerm = this.searchTerm.toLowerCase();
            if (searchTerm) {
                this.data = this.originalData.filter(item => {
                    return Object.values(item).some(value => value.toString().toLowerCase().includes(searchTerm));
                });
            } else {
                this.data = [...this.originalData];
            }
        },
        deleteFiles(file) {
            this.data = this.data.filter(item => item !== file);
        }
    }
}

document.addEventListener("alpine:init", () => {
    Alpine.data("quickTable", quickTable);
});
