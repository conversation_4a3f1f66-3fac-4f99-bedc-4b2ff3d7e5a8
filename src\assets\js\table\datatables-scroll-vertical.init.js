/*
Template Name: Domiex - Admin & Dashboard Template
Author: SRBThemes
Version: 1.0.0
File: scroll-vertical init Js File
*/

var groupColumn = 2;
var table = $('#example').DataTable({
    columnDefs: [{ visible: false, targets: groupColumn }],
    order: [[groupColumn, 'asc']],
    displayLength: 25,
    responsive: true,
    drawCallback: function (settings) {
        var api = this.api();
        var rows = api.rows({ page: 'current' }).nodes();
        var last = null;
        api.column(groupColumn, { page: 'current' })
            .data()
            .each(function (group, i) {
                if (last !== group) {
                    $(rows)
                        .eq(i)
                        .before(
                            '<tr class="group bg-gray-50 border-y dark:bg-dark-850"><td colspan="5" class="p-3">' +
                            group +
                            '</td></tr>'
                        );
                    last = group;
                }
            });
    }
});

// Order by the grouping
$('#example tbody').on('click', 'tr.group', function () {
    var currentOrder = table.order()[0];
    if (currentOrder[0] === groupColumn && currentOrder[1] === 'asc')
        table.order([groupColumn, 'desc']).draw();
    else
        table.order([groupColumn, 'asc']).draw();
});