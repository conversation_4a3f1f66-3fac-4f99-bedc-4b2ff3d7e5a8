@layer components {
    .vscomp-wrapper {
        @apply !text-gray-800 !text-base dark:!text-dark-50;

        &.show-value-as-tags.has-value .vscomp-clear-button {
            @apply rtl:!right-auto rtl:!left-0.5;
        }
    }

    .vscomp-wrapper.has-clear-button {
        .vscomp-toggle-button {
            @apply ltr:!pr-14 rtl:!pl-14 rtl:!pr-4;
        }
    }

    .vscomp-ele {
        @apply !w-full !max-w-full;
    }

    .vscomp-toggle-button {
        @apply !bg-white !border-gray-200 dark:!bg-dark-900 dark:!border-dark-800 rounded-md text-base py-2 px-4;
    }

    .vscomp-arrow {
        @apply ltr:!right-0 rtl:!left-0 rtl:!right-auto;
        @apply after:!border-b-gray-800 dark:after:!border-b-dark-500 after:!border-r-gray-800 dark:after:!border-r-dark-500;
    }

    .vscomp-clear-icon,
    .vscomp-clear-icon {
        @apply before:!bg-gray-400 dark:before:!bg-dark-800 after:!bg-gray-400 dark:after:!bg-dark-800 hover:before:!bg-gray-500 dark:hover:before:!bg-dark-500 hover:after:!bg-gray-500 dark:hover:after:!bg-dark-500;
    }

    .vscomp-clear-button {
        @apply hover:!bg-gray-100 dark:hover:!bg-dark-850 ltr:!right-7 rtl:!right-auto rtl:!left-7;

        &:hover {
            .vscomp-clear-icon::before,
            .vscomp-clear-icon::after {
                @apply hover:!bg-gray-200 dark:hover:!bg-dark-800;
            }
        }
    }

    .vscomp-option {
        @apply outline-0;
    }

    .vscomp-option-text {
        @apply rtl:!text-right;
    }

    .pop-comp-wrapper {
        @apply !shadow-lg !shadow-gray-200 dark:!shadow-dark-800 !text-gray-800 dark:!text-dark-100 !bg-white dark:!bg-dark-900;
    }

    .vscomp-wrapper.focused .vscomp-toggle-button,
    .vscomp-wrapper:focus .vscomp-toggle-button {
        @apply shadow-lg shadow-gray-200 border-primary-500 dark:shadow-dark-800;
    }

    .vscomp-wrapper:not(.has-value) .vscomp-value {
        @apply opacity-100 text-gray-500 dark:text-dark-500;
    }

    .vscomp-option.focused {
        @apply !bg-gray-100 dark:!bg-dark-850;
    }

    .vscomp-wrapper {
        @apply !text-gray-800 dark:!text-dark-100;
    }

    .vscomp-option.selected {
        @apply !bg-gray-100 dark:!bg-dark-850;
    }

    .vscomp-wrapper .checkbox-icon {
        @apply after:!border after:!border-gray-200 dark:after:!border-dark-800 ltr:!mr-2.5 rtl:!mr-auto rtl:!ml-2.5;
    }

    .vscomp-wrapper.multiple .vscomp-option.selected .checkbox-icon {
        @apply ltr:after:!border-l-0 rtl:after:!border-l rtl:after:!border-r-0 rtl:after:!-rotate-45 rtl:after:!-translate-x-1 rtl:after:!-translate-y-1 after:!border-t-0 after:!border-primary-500;
    }

    .vscomp-option-description {
        @apply !text-gray-500 dark:!text-dark-500;
    }

    .vscomp-search-clear {
        @apply !text-gray-500 hover:!text-red-500 dark:!text-dark-500 dark:hover:!text-red-500;
    }

    .vscomp-search-container {
        @apply !border-gray-200 dark:!border-dark-800 ltr:!pr-1.5 rtl:!pl-1.5 ltr:!pl-4 rtl:!pr-4;
    }

    .vscomp-wrapper.show-value-as-tags .vscomp-toggle-button {
        padding: 6px 22px 2px 10px;
    }

    .vscomp-new-option-icon::before {
        @apply !border-primary-500 !border-b-transparent !border-l-transparent;
    }

    .vscomp-dropbox {
        @apply !bg-white dark:!bg-dark-900;
    }

    .vscomp-wrapper.show-value-as-tags .vscomp-value-tag {
        @apply !border-gray-200 dark:!border-dark-800;
    }

    .vscomp-wrapper.show-as-popup .vscomp-dropbox-container {
        background-color: #00000080 !important;
    }
}