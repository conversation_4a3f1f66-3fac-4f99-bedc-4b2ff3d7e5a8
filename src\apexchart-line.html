{{> partials/main }}

<head>

    {{> partials/title-meta title="Line Charts" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}

{{> partials/page-heading title="Line Charts" sub-title="Apexcharts" }}

<div class="grid grid-cols-12 gap-x-space">
    <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Basic</h6>
        </div>
        <div class="card-body">
            <div x-data="basicLineApp" dir="ltr">
                <div class="!min-h-full" data-chart-colors="[bg-primary-500]" x-ref="basicLineChart"></div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Line with Data Labels</h6>
        </div>
        <div class="card-body">
            <div x-data="labelLineApp" dir="ltr">
                <div class="!min-h-full" data-chart-colors="[bg-primary-500, bg-gray-300]" data-chart-dark-colors="[bg-primary-500, bg-gray-300]" x-ref="labelLineChart"></div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Zoomable Timeseries</h6>
        </div>
        <div class="card-body">
            <div x-data="zoomableLineApp" dir="ltr">
                <div class="!min-h-full" data-chart-colors="[bg-sky-500]" x-ref="zoomableLineChart"></div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Stepline</h6>
        </div>
        <div class="card-body">
            <div x-data="steplineLineApp" dir="ltr">
                <div class="!min-h-full" data-chart-colors="[bg-green-500]" x-ref="steplineLineChart"></div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Gradient</h6>
        </div>
        <div class="card-body">
            <div x-data="gradientLineApp" dir="ltr">
                <div class="!min-h-full" data-chart-colors="[bg-orange-500, bg-primary-500]" x-ref="gradientLineChart"></div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Dashed</h6>
        </div>
        <div class="card-body">
            <div x-data="dashedLineApp" dir="ltr">
                <div class="!min-h-full" data-chart-colors="[bg-primary-500, bg-green-500, bg-gray-200]" x-ref="dashedLineChart"></div>
            </div>
        </div>
    </div><!--end col-->
</div><!--end grid-->

</div>
{{> partials/footer }}
</div>

{{> partials/vendor-scripts }}

<script type="module" src="assets/js/charts/line-chart.init.js"></script>

<script type="module" src="assets/js/main.js"></script>

</body>
</html>