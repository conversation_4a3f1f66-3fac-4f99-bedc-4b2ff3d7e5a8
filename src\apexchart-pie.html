{{> partials/main }}

<head>

    {{> partials/title-meta title="Pie/Donuts Charts" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}

{{> partials/page-heading title="Pie/Donuts Charts" sub-title="Apexcharts" }}

<div class="grid grid-cols-12 gap-x-space">
    <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Simple Pie</h6>
        </div>
        <div class="card-body">
            <div x-data="simplePieApp" dir="ltr">
                <div class="!min-h-full" data-chart-colors="[bg-primary-500, bg-green-500, bg-yellow-500, bg-purple-500, bg-red-500]" x-ref="simplePieChart"></div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Simple Donut</h6>
        </div>
        <div class="card-body">
            <div x-data="simpleDonutApp" dir="ltr">
                <div class="!min-h-full" data-chart-colors="[bg-primary-500, bg-green-500, bg-yellow-500, bg-red-500, bg-purple-500]" x-ref="simpleDonutChart"></div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Donut Update</h6>
        </div>
        <div class="card-body">
            <div x-data="updateDonutApp" dir="ltr">
                <div x-ref="chartInstance" class="flex flex-wrap items-center gap-2 mb-3">
                    <button @click="add" class="btn btn-sub-primary">Add</button>
                    <button @click="remove" class="btn btn-sub-primary">Remove</button>
                    <button @click="randomize" class="btn btn-sub-primary">Randomize</button>
                    <button @click="reset" class="btn btn-sub-primary">Reset</button>
                </div>
                <div class="!min-h-full" data-chart-colors="[bg-primary-500, bg-green-500, bg-yellow-500, bg-red-500]" x-ref="updateDonutChart"></div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Monochrome Pie</h6>
        </div>
        <div class="card-body">
            <div x-data="monochromePieApp" dir="ltr">
                <div class="!min-h-full" data-chart-colors="[bg-yellow-500]" x-ref="monochromePieChart"></div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Gradient Donut</h6>
        </div>
        <div class="card-body">
            <div x-data="gradientDonutApp" dir="ltr">
                <div class="!min-h-full" data-chart-colors="[bg-primary-500, bg-green-500, bg-yellow-500, bg-purple-500, bg-red-500]" x-ref="gradientDonutChart"></div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Semi Donut</h6>
        </div>
        <div class="card-body">
            <div x-data="semiDonutApp" dir="ltr">
                <div class="!min-h-full" data-chart-colors="[bg-primary-500, bg-green-500, bg-yellow-500, bg-purple-500, bg-red-500]" x-ref="semiDonutChart"></div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Donut with Pattern</h6>
        </div>
        <div class="card-body">
            <div x-data="patternDonutApp" dir="ltr">
                <div class="!min-h-full" data-chart-colors="[bg-primary-500, bg-green-500, bg-yellow-500, bg-purple-500, bg-red-500]" x-ref="patternDonutChart"></div>
            </div>
        </div>
    </div><!--end col-->
</div><!--end grid-->

</div>
{{> partials/footer }}
</div>

{{> partials/vendor-scripts }}

<script type="module" src="assets/js/charts/pie-chart.init.js"></script>

<script type="module" src="assets/js/main.js"></script>

</body>
</html>