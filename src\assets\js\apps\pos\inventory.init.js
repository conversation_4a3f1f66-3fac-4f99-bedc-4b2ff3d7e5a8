/*
Template Name: Domiex - Admin & Dashboard Template
Author: SRBThemes
Version: 1.0.0
File: inventory init Js File
*/

import "virtual-select-plugin/dist/virtual-select";

document.addEventListener('DOMContentLoaded', () => {
    try {
        VirtualSelect.init({
            ele: "#categorySelect",
            options: [
                { label: "Cooking Essentials", value: "Cooking Essentials" },
                { label: "Dairy", value: "Dairy" },
                { label: "Meat Products", value: "Meat Products" },
                { label: "Desserts", value: "Desserts" },
                { label: "Beverages", value: "Beverages" },
                { label: "Bakery", value: "Bakery" },
                { label: "Fruits", value: "Fruits" },
                { label: "Vegetables", value: "Vegetables" },
            ],
        });
    } catch (error) {
        console.error('Error initializing VirtualSelect:', error);
    }
});

function inventoryTable() {
    return {
        products: [],
        searchTerm: '',
        currentPage: 1,
        itemsPerPage: 10,
        selectedItems: [],
        sortBy: 'name',
        sortDirection: 'asc',
        showAddModal: false,
        showDeleteModal: false,
        editingProduct: null,
        newProduct: {
            name: '',
            description: '',
            sku: '',
            category: '',
            stock: 0,
            unit: 'kg',
            minStock: 0,
            maxStock: 0,
            price: 0,
            cost: 0
        },

        init() {
            // Check if Alpine is available
            if (typeof Alpine === 'undefined') {
                console.error('Alpine.js is not loaded');
                return;
            }

            // Load inventory data
            fetch('assets/js/apps/pos/inventory.json')
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (!data || !Array.isArray(data.products)) {
                        throw new Error('Invalid data format');
                    }
                    this.products = data.products.map(product => ({
                        ...product,
                        margin: this.calculateMargin(product.price, product.cost)
                    }));
                })
                .catch(error => {
                    console.error('Error loading inventory data:', error);
                    // Initialize with empty products array if data loading fails
                    this.products = [];
                });

            // Close modals when clicking outside
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape') {
                    this.showAddModal = false;
                    this.showDeleteModal = false;
                }
            });
        },

        calculateMargin(price, cost) {
            if (!cost || cost === 0) return 0;
            return Math.round(((price - cost) / cost) * 100);
        },

        sort(column) {
            if (this.sortBy === column) {
                this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
            } else {
                this.sortBy = column;
                this.sortDirection = 'asc';
            }
        },

        get filteredProducts() {
            let filtered = this.products.filter(product => {
                const searchLower = this.searchTerm.toLowerCase();
                return (
                    product.name.toLowerCase().includes(searchLower) ||
                    product.sku.toLowerCase().includes(searchLower) ||
                    product.category.toLowerCase().includes(searchLower)
                );
            });

            // Apply sorting
            return filtered.sort((a, b) => {
                let aValue = a[this.sortBy];
                let bValue = b[this.sortBy];

                // Handle special cases for sorting
                if (this.sortBy === 'stock') {
                    aValue = a.stock;
                    bValue = b.stock;
                } else if (this.sortBy === 'price') {
                    aValue = a.price;
                    bValue = b.price;
                } else if (this.sortBy === 'cost') {
                    aValue = a.cost;
                    bValue = b.cost;
                } else if (this.sortBy === 'margin') {
                    aValue = a.margin;
                    bValue = b.margin;
                }

                // Handle string comparison
                if (typeof aValue === 'string') {
                    aValue = aValue.toLowerCase();
                    bValue = bValue.toLowerCase();
                }

                if (aValue < bValue) {
                    return this.sortDirection === 'asc' ? -1 : 1;
                }
                if (aValue > bValue) {
                    return this.sortDirection === 'asc' ? 1 : -1;
                }
                return 0;
            });
        },

        get totalPages() {
            return Math.ceil(this.filteredProducts.length / this.itemsPerPage);
        },

        get displayedProducts() {
            const start = (this.currentPage - 1) * this.itemsPerPage;
            const end = start + this.itemsPerPage;
            return this.filteredProducts.slice(start, end);
        },

        get showingStart() {
            return this.filteredProducts.length === 0 ? 0 : (this.currentPage - 1) * this.itemsPerPage + 1;
        },

        get showingEnd() {
            return Math.min(this.currentPage * this.itemsPerPage, this.filteredProducts.length);
        },

        nextPage() {
            if (this.currentPage < this.totalPages) {
                this.currentPage++;
            }
        },

        prevPage() {
            if (this.currentPage > 1) {
                this.currentPage--;
            }
        },

        gotoPage(page) {
            this.currentPage = page;
        },

        getStockStatus(stock, minStock, maxStock) {
            if (stock <= 0) {
                return 'out_of_stock';
            }
            const range = maxStock - minStock;
            const lowThreshold = minStock + (range * 0.05); // 5% above min
            const mediumThreshold = minStock + (range * 0.2); // 20% above min

            if (stock <= lowThreshold) {
                return 'low';
            } else if (stock <= mediumThreshold) {
                return 'medium';
            } else {
                return 'active';
            }
        },

        getStockBadgeClass(status) {
            switch (status) {
                case 'out_of_stock':
                    return 'badge-red';
                case 'low':
                    return 'badge-red';
                case 'medium':
                    return 'badge-yellow';
                case 'active':
                    return 'badge-green';
                default:
                    return 'badge-gray';
            }
        },

        getStatusText(status) {
            switch (status) {
                case 'out_of_stock':
                    return 'Out of Stock';
                case 'low':
                    return 'Low Stock';
                case 'medium':
                    return 'Medium Stock';
                case 'active':
                    return 'Active';
                default:
                    return 'Unknown';
            }
        },

        get lowStockCount() {
            return this.products.filter(product => {
                const status = this.getStockStatus(product.stock, product.minStock, product.maxStock);
                return status === 'low' || status === 'out_of_stock';
            }).length;
        }
    };
}

// Initialize Alpine.js
document.addEventListener('alpine:init', () => {
    Alpine.data('inventory', inventoryTable);
});