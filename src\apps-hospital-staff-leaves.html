{{> partials/main }}

<head>

    {{> partials/title-meta title="Leave Management" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}
{{> partials/page-heading title="Leave Management" sub-title="Staff" }}

<div x-data="leavesTable()">
    <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-5 gap-x-space">
        <div class="card">
            <div class="flex items-center gap-4 card-body">
                <div x-text="availableLeaveCount" class="flex items-center justify-center text-xl font-medium text-green-500 rounded-md bg-green-500/10 size-14">
                </div>
                <p class="text-gray-500 dark:text-dark-500">Available Leaves</p>
            </div>
        </div>
        <div class="card">
            <div class="flex items-center gap-4 card-body">
                <div x-text="usedLeaveCount" class="flex items-center justify-center text-xl font-medium text-purple-500 rounded-md bg-purple-500/10 size-14">
                </div>
                <p class="text-gray-500 dark:text-dark-500">Used Leaves</p>
            </div>
        </div>
        <div class="card">
            <div class="flex items-center gap-4 card-body">
                <div x-text="pendingLeaveCount" class="flex items-center justify-center text-xl font-medium text-yellow-500 rounded-md bg-yellow-500/10 size-14">
                </div>
                <p class="text-gray-500 dark:text-dark-500">Pending Leaves Request</p>
            </div>
        </div>
        <div class="card">
            <div class="flex items-center gap-4 card-body">
                <div x-text="rejectedLeaveCount" class="flex items-center justify-center text-xl font-medium text-red-500 rounded-md bg-red-500/10 size-14">
                </div>
                <p class="text-gray-500 dark:text-dark-500">Rejected Leaves</p>
            </div>
        </div>
        <div class="card">
            <div class="flex items-center gap-4 card-body">
                <div x-text="totalLeaveCount" class="flex items-center justify-center text-xl font-medium rounded-md size-14 bg-sky-500/10 text-sky-500">
                </div>
                <p class="text-gray-500 dark:text-dark-500">Total Leaves</p>
            </div>
        </div>
    </div>

    <div class="card">
        <div class="flex items-center gap-3 card-header">
            <h6 class="card-title grow">Leaves</h6>
            <div class="shrink-0">
                <a href="apps-hospital-staff-leave-add.html" class="btn btn-primary">New Request</a>
            </div>
        </div>
        <div class="card-header">
            <div class="grid grid-cols-12 gap-space">
                <div class="col-span-12 md:col-span-6 xl:col-span-3 2xl:col-span-4">
                    <div class="relative group/form">
                        <input type="email" class="ltr:pl-9 rtl:pr-9 form-input ltr:group-[&.right]/form:pr-9 rtl:group-[&.right]/form:pl-9 ltr:group-[&.right]/form:pl-4 rtl:group-[&.right]/form:pr-4" placeholder="Leave Type reason" x-model="searchTerm" @input="filteredLeaves">
                        <div class="absolute inset-y-0 flex items-center text-gray-500 dark:text-dark-500 ltr:left-3 rtl:right-3 ltr:group-[&.right]/form:right-3 rtl:group-[&.right]/form:left-3 ltr:group-[&.right]/form:left-auto rtl:group-[&.right]/form:right-auto focus:outline-hidden">
                            <i data-lucide="search" class="size-4"></i>
                        </div>
                    </div>
                </div>
                <div class="col-span-12 md:col-span-6 xl:col-span-3 2xl:col-span-3">
                    <input type="text" class="form-input" placeholder="Select date range" x-ref="dateRangePicker">
                </div>
                <div class="col-span-12 md:col-span-6 xl:col-span-3 2xl:col-span-3">
                    <div id="statusSelect" placeholder="Select Status" @change="filteredLeaves"></div>
                </div>
                <div class="col-span-12 md:col-span-6 xl:col-span-3 2xl:col-span-2">
                    <button type="submit" class="w-full btn btn-sub-gray"><i data-lucide="filter" class="inline-block align-middle size-4"></i> Filter Now</button>
                </div>
            </div>
        </div>
        <div class="pt-0 card-body">
            <div data-simplebar class="table-box">
                <table class="table whitespace-nowrap">
                    <tbody>
                        <tr class="text-gray-500 bg-gray-100 dark:bg-dark-850 dark:text-dark-500">
                            <th x-on:click="sort('leaveID')" class="!font-medium cursor-pointer">ID <span x-show="sortBy === 'leaveID'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                            <th x-on:click="sort('leaveType')" class="!font-medium cursor-pointer">Leave Type <span x-show="sortBy === 'leaveType'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                            <th x-on:click="sort('startDate')" class="!font-medium cursor-pointer">Start Date <span x-show="sortBy === 'startDate'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                            <th x-on:click="sort('endDate')" class="!font-medium cursor-pointer">End Date <span x-show="sortBy === 'endDate'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                            <th x-on:click="sort('totalDays')" class="!font-medium cursor-pointer">Days <span x-show="sortBy === 'totalDays'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                            <th x-on:click="sort('reason')" class="!font-medium cursor-pointer">Reason <span x-show="sortBy === 'reason'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                            <th x-on:click="sort('approvedBy')" class="!font-medium cursor-pointer">Approved By <span x-show="sortBy === 'approvedBy'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                            <th x-on:click="sort('dateRequested')" class="!font-medium cursor-pointer">Request Date <span x-show="sortBy === 'dateRequested'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                            <th x-on:click="sort('dateApproved')" class="!font-medium cursor-pointer">Approved Date <span x-show="sortBy === 'dateApproved'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                            <th x-on:click="sort('status')" class="!font-medium cursor-pointer">Status <span x-show="sortBy === 'status'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                            <th class="!font-medium">Action</th>
                        </tr>
                        <template x-if="displayedLeaves.length > 0">
                        <template x-for="(leave, index) in displayedLeaves" :key="index">
                            <tr class="*:px-3 *:py-2.5">
                                <td x-text="leave.leaveID"></td>
                                <td x-text="leave.leaveType"></td>
                                <td x-text="leave.startDate"></td>
                                <td x-text="leave.endDate"></td>
                                <td x-text="leave.totalDays"></td>
                                <td x-text="leave.reason"></td>
                                <td x-text="leave.approvedBy"></td>
                                <td x-text="leave.dateRequested"></td>
                                <td x-text="leave.dateApproved"></td>
                                <td>
                                    <span x-text="leave.status" :class="{
                                        'badge badge-green': leave.status === 'Approved',
                                        'badge badge-red': leave.status === 'Rejected',
                                        'badge badge-yellow': leave.status === 'Pending'
                                    }"></span>
                                </td>
                                <td>
                                    <div class="flex items-center gap-2">
                                        <button title="Approved" @click="approveLeave(leave)" class="btn btn-sub-green btn-icon !size-8"><i class="ri-check-line"></i></button>
                                        <button title="edit" class="btn btn-sub-gray btn-icon !size-8"><i class="ri-pencil-line"></i></button>
                                        <button title="Rejected" class="btn btn-sub-red btn-icon !size-8" @click="rejectedLeave(leave)"><i class="ri-close-line"></i></button>
                                    </div>
                                </td>
                            </tr>
                        </template>
                        </template>
                        <tr>
                            <template x-if="displayedLeaves.length == 0">
                                <td colspan="10" class="!p-8">
                                    <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" class="mx-auto size-12" viewBox="0 0 48 48">
                                        <linearGradient id="SVGID_1__h35ynqzIJzH4_gr1" x1="34.598" x2="15.982" y1="15.982" y2="34.598" gradientUnits="userSpaceOnUse">
                                            <stop offset="0" stop-color="#60e8fe"></stop>
                                            <stop offset=".033" stop-color="#6ae9fe"></stop>
                                            <stop offset=".197" stop-color="#97f0fe"></stop>
                                            <stop offset=".362" stop-color="#bdf5ff"></stop>
                                            <stop offset=".525" stop-color="#dafaff"></stop>
                                            <stop offset=".687" stop-color="#eefdff"></stop>
                                            <stop offset=".846" stop-color="#fbfeff"></stop>
                                            <stop offset="1" stop-color="#fff"></stop>
                                        </linearGradient>
                                        <path fill="url(#SVGID_1__h35ynqzIJzH4_gr1)" d="M40.036,33.826L31.68,25.6c0.847-1.739,1.335-3.684,1.335-5.748c0-7.27-5.894-13.164-13.164-13.164	S6.688,12.582,6.688,19.852c0,7.27,5.894,13.164,13.164,13.164c2.056,0,3.995-0.485,5.728-1.326l3.914,4.015l4.331,4.331	c1.715,1.715,4.496,1.715,6.211,0C41.751,38.321,41.751,35.541,40.036,33.826z"></path>
                                        <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M31.95,25.739l8.086,8.086c1.715,1.715,1.715,4.496,0,6.211l0,0c-1.715,1.715-4.496,1.715-6.211,0	l-4.331-4.331"></path>
                                        <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M7.525,24.511c-1.771-4.694-0.767-10.196,3.011-13.975c3.847-3.847,9.48-4.817,14.228-2.912"></path>
                                        <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M30.856,12.603c3.376,5.114,2.814,12.063-1.688,16.565c-4.858,4.858-12.565,5.129-17.741,0.814"></path>
                                    </svg>
                                    <p class="mt-2 text-center text-gray-500 dark:text-dark-500">No matching records found</p>
                                </td>
                            </template>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="grid grid-cols-12 gap-5 mt-5 items-center" x-show="displayedLeaves.length !== 0">
                <div class="col-span-12 md:col-span-6 justify-center md:justify-start flex flex-wrap">
                    <p class="text-gray-500 dark:text-dark-500">Showing <b x-text="showingStart"></b> - <b x-text="showingEnd"></b> of <b x-text="leaves.length"></b> Results</p>
                </div>
                <div class="col-span-12 md:col-span-6">
                    <div class="flex justify-center md:justify-end pagination pagination-primary">
                        <button @click="prevPage()" :disabled="currentPage === 1" class="pagination-pre">
                            <i data-lucide="chevron-left" class="mr-1 ltr:inline-block rtl:hidden size-4"></i>
                            <i data-lucide="chevron-right" class="ml-1 ltr:hidden rtl:inline-block size-4"></i>
                            Prev
                        </button>
                        <template x-for="page in totalPages" :key="page">
                            <button @click="gotoPage(page)" :class="{ 'active': currentPage === page }" class="pagination-item">
                                <span x-text="page"></span>
                            </button>
                        </template>
                        <button @click="nextPage()" :disabled="currentPage === totalPages" class="pagination-next">
                            Next
                            <i data-lucide="chevron-right" class="ml-1 ltr:inline-block rtl:hidden size-4"></i>
                            <i data-lucide="chevron-left" class="mr-1 ltr:hidden rtl:inline-block size-4"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</div>
{{> partials/footer }}
</div>

{{> partials/vendor-scripts }}

<script type="module" src="assets/js/apps/hospital/staff/leaves.init.js"></script>

<script type="module" src="assets/js/main.js"></script>

</body>
</html>