{{> partials/main }}

<head>

    {{> partials/title-meta title="Gallery" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}

{{> partials/page-heading title="Gallery" sub-title="UI" }}

<div class="grid grid-cols-12 gap-x-space">
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Basic Gallery</h6>
        </div>
        <div class="card-body">
            <div class="grid grid-cols-12 gap-5">
                <div class="col-span-12 sm:col-span-6 lg:col-span-4">
                    <div class="relative overflow-hidden rounded-md group/item h-80">
                        <img src="assets/images/gallery/img-01.jpg" alt="" class="object-cover w-full h-full transition duration-[8s] ease-effect group-hover/item:scale-125">
                        <div class="absolute inset-0 flex items-end p-5 text-white bg-gradient-to-t from-gray-900/50">
                            <div>
                                <h5 class="mb-1">Gallery Title</h5>
                                <p class="text-white/75">Subtitle</p>
                            </div>
                        </div>
                    </div>
                </div><!--end col-->
                <div class="col-span-12 sm:col-span-6 lg:col-span-8">
                    <div class="relative overflow-hidden rounded-md h-80 group/item">
                        <img src="assets/images/gallery/long/img-02.jpg" alt="" class="object-cover w-full h-full transition duration-[8s] ease-effect group-hover/item:scale-125">
                        <div class="absolute inset-0 flex items-end p-5 text-white bg-gradient-to-t from-gray-900/50">
                            <div>
                                <h5 class="mb-1">Gallery Title</h5>
                                <p class="text-white/75">Subtitle</p>
                            </div>
                        </div>
                    </div>
                </div><!--end col-->
                <div class="col-span-12 sm:col-span-6 lg:col-span-4 xl:col-span-6">
                    <div class="relative overflow-hidden rounded-md h-80 group/item">
                        <img src="assets/images/gallery/long/img-03.jpg" alt="" class="object-cover w-full h-full transition duration-[8s] ease-effect group-hover/item:scale-125">
                        <div class="absolute inset-0 flex items-end p-5 text-white bg-gradient-to-t from-gray-900/50">
                            <div>
                                <h5 class="mb-1">Gallery Title</h5>
                                <p class="text-white/75">Subtitle</p>
                            </div>
                        </div>
                    </div>
                </div><!--end col-->
                <div class="col-span-12 sm:col-span-6 lg:col-span-4 xl:col-span-3">
                    <div class="relative overflow-hidden rounded-md group/item h-80">
                        <img src="assets/images/gallery/img-05.jpg" alt="" class="object-cover w-full h-full transition duration-[8s] ease-effect group-hover/item:scale-125">
                        <div class="absolute inset-0 flex items-end p-5 text-white bg-gradient-to-t from-gray-900/50">
                            <div>
                                <h5 class="mb-1">Gallery Title</h5>
                                <p class="text-white/75">Subtitle</p>
                            </div>
                        </div>
                    </div>
                </div><!--end col-->
                <div class="col-span-12 sm:col-span-6 lg:col-span-4 xl:col-span-3">
                    <div class="relative overflow-hidden rounded-md group/item h-80">
                        <img src="assets/images/gallery/img-06.jpg" alt="" class="object-cover w-full h-full transition duration-[8s] ease-effect group-hover/item:scale-125">
                        <div class="absolute inset-0 flex items-end p-5 text-white bg-gradient-to-t from-gray-900/50">
                            <div>
                                <h5 class="mb-1">Gallery Title</h5>
                                <p class="text-white/75">Subtitle</p>
                            </div>
                        </div>
                    </div>
                </div><!--end col-->
            </div><!--end grid-->
        </div>
    </div>
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Masonry With Lightbox Gallery</h6>
        </div>
        <div class="card-body" x-data="{ isOpen: false, imageSrc: '' }">
            <div x-show="isOpen" @click.away="isOpen = false" class="fixed inset-0 flex items-center justify-center bg-gray-900/60 z-[1050]">
                <div class="relative max-w-3xl mx-auto overflow-y-auto">
                    <!-- Close Button -->
                    <button @click="isOpen = false" class="absolute top-0 right-0 p-1.5 m-4 text-white rounded-full bg-gray-900 hover:text-red-500 transition ease-linear duration-200">
                        <i data-lucide="x"></i>
                    </button>
                    <!-- Image -->
                    <img :src="imageSrc" alt="" class="object-contain rounded-sm">
                </div>
            </div>
            <ul x-data x-masonry class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3">
                <li class="h-64">
                    <img src="assets/images/gallery/img-01.jpg" alt="" class="object-cover object-center w-full h-full rounded-sm cursor-pointer" @click="isOpen = true; imageSrc = $event.target.src">
                </li>
                <li class="h-[25rem]">
                    <img src="assets/images/gallery/img-02.jpg" alt="" class="object-cover object-center w-full h-full rounded-sm cursor-pointer" @click="isOpen = true; imageSrc = $event.target.src">
                </li>
                <li class="h-72">
                    <img src="assets/images/gallery/img-04.jpg" alt="" class="object-cover object-center w-full h-full rounded-sm cursor-pointer" @click="isOpen = true; imageSrc = $event.target.src">
                </li>
                <li class="h-48">
                    <img src="assets/images/gallery/img-05.jpg" alt="" class="object-cover object-center w-full h-full rounded-sm cursor-pointer" @click="isOpen = true; imageSrc = $event.target.src">
                </li>
                <li class="h-80">
                    <img src="assets/images/gallery/img-06.jpg" alt="" class="object-cover object-center w-full h-full rounded-sm cursor-pointer" @click="isOpen = true; imageSrc = $event.target.src">
                </li>
                <li class="h-80">
                    <img src="assets/images/gallery/img-03.jpg" alt="" class="object-cover object-center w-full h-full rounded-sm cursor-pointer" @click="isOpen = true; imageSrc = $event.target.src">
                </li>
                <li class="h-96">
                    <img src="assets/images/gallery/long/img-01.jpg" alt="" class="object-cover object-center w-full h-full rounded-sm cursor-pointer" @click="isOpen = true; imageSrc = $event.target.src">
                </li>
            </ul>
        </div>
    </div><!--end col-->
</div><!--end grid-->

</div>
{{> partials/footer }}
</div>

{{> partials/vendor-scripts }}

<script type="module" src="assets/js/main.js"></script>

</body>
</html>