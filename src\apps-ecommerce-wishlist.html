{{> partials/main }}

<head>

    {{> partials/title-meta title="Wishlist" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}
{{> partials/page-heading title="Wishlist" sub-title="Ecommerce" }}

<div class="card">
    <div class="card-header">
        <h6 class="card-title">💖 Wishlist</h6>
    </div>
    <div class="pt-0 card-body">
        <div x-data="wishlist()">
            <div class="overflow-x-auto table-box">
                <table class="table flush">
                    <thead>
                        <tr>
                            <th>Product</th>
                            <th>Price</th>
                            <th>Quantity</th>
                            <th>Subtotal</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <template x-if="data.length === 0">
                            <tr>
                                <td colspan="5" class="whitespace-nowrap">
                                    <div class="p-4 text-center">
                                        <img :src="cartImage" alt="" class="block mx-auto size-16">
                                        <h6 class="mt-4 mb-1">Your wishlist is waiting for you.</h6>
                                        <p class="mb-3 text-gray-500 dark:text-dark-500">Add items to your wishlist as you browse, and they will magically appear here.</p>

                                        <div class="flex items-center justify-center gap-2">
                                            <button class="btn btn-primary">Browse our catalog</button>
                                            <button class="btn btn-outline-purple">Go to your cart</button>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        </template>
                        <template x-for="(item, index) in data" :key="index">
                            <tr class="*:px-3 *:py-2.5">
                                <td class="whitespace-nowrap">
                                    <div class="flex items-center gap-4">
                                        <div class="relative flex items-center justify-center p-2 bg-gray-100 dark:bg-dark-850 size-16">
                                            <button class="absolute flex items-center justify-center bg-white dark:bg-dark-900 rounded-full shadow-lg shadow-gray-200 dark:shadow-dark-800 hover:text-red-500 transition duration-300 ease-linear -top-1.5 ltr:-right-1.5 rtl:-left-1.5 size-4" @click="data.splice(index, 1)"><i class="ri-close-line"></i></button>
                                            <img :src="item.image" alt="">
                                        </div>
                                        <div class="grow">
                                            <h6 x-text="item.productName" class="mb-1"></h6>
                                            <p class="text-gray-500 dark:text-dark-500 divide-x divide-gray-200 dark:divide-dark-800 flex gap-2 items-center mb-2">
                                                <span class="px-2 ltr:first:!pl-0 rtl:first:!pr-0" x-text="item.color"></span> 
                                                <span x-text="item.size"></span>
                                            </p>
                                        </div>
                                    </div>
                                </td>
                                <td x-text="`${item.price.toFixed(2)}`"></td>
                                <td>
                                    <div class="input-spin-group input-spin-primary">
                                        <button @click="item.qty = Math.max(0, item.qty - 1)" class="text-lg input-spin-minus"><i class="ri-subtract-line"></i></button>
                                        <input type="text" x-model.number="item.qty" class="text-center input-spin form-input" readonly>
                                        <button @click="item.qty++" class="text-lg input-spin-plus"><i class="ri-add-line"></i></button>
                                    </div>
                                </td>
                                <td x-text="`${(item.price * item.qty).toFixed(2)}`"></td>
                                <td><a href="apps-ecommerce-shop-cart.html" class="btn btn-sub-gray whitespace-nowrap"><i class="align-bottom ri-shopping-cart-line"></i> Add to Cart</a></td>
                            </tr>
                        </template>
                    </tbody>
                </table>
            </div>

            <div class="flex flex-wrap items-center justify-between gap-2 mt-3">
                <a href="apps-ecommerce-products-grid.html" class="btn btn-sub-purple"><i data-lucide="move-left" class="inline-block ltr:mr-1 rtl:ml-1 size-5"></i> Continue Shopping</a>
                <a href="apps-ecommerce-shop-cart.html" class="btn btn-primary">Update to Cart</a>
            </div>
        </div>
    </div>
</div>

</div>
{{> partials/footer }}
</div>

{{> partials/vendor-scripts }}

<script type="module" src="assets/js/main.js"></script>
<script type="module" src="assets/js/apps/ecommerce/wishlist.init.js"></script>

</body>
</html>