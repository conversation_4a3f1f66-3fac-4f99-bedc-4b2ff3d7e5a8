{{> partials/main }}

<head>

    {{> partials/title-meta title="Timeline" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}

{{> partials/page-heading title="Timeline" sub-title="UI" }}

<div class="grid grid-cols-12 gap-x-space">
    <div class="col-span-12 md:col-span-6 xl:col-span-4 card">
        <div class="card-header">
            <h6 class="card-title">Basic Timeline</h6>
        </div>
        <div class="card-body">
            <ul class="timeline">
                <li class="timeline-primary active">Dark, Light with RTL Supported</li>
                <li class="timeline-primary active">Multiple Layouts with Responsive</li>
                <li class="timeline-primary active">Fully Responsive Design</li>
                <li class="timeline-primary">W3C Validated HTML Pages</li>
                <li class="timeline-primary">Easy to Customize with Tailwind.config & SCSS</li>
                <li class="timeline-primary">Unlimited Template Possibilities</li>
            </ul>
        </div>
    </div><!--end col-->
    <div class="col-span-12 md:col-span-6 xl:col-span-4 card">
        <div class="card-header">
            <h6 class="card-title">Square Timeline</h6>
        </div>
        <div class="card-body">
            <ul class="timeline-square timeline">
                <li class="timeline-purple active">Dark, Light with RTL Supported</li>
                <li class="timeline-purple active">Multiple Layouts with Responsive</li>
                <li class="timeline-purple active">Fully Responsive Design</li>
                <li class="timeline-purple">W3C Validated HTML Pages</li>
                <li class="timeline-purple">Easy to Customize with Tailwind.config & SCSS</li>
                <li class="timeline-purple">Unlimited Template Possibilities</li>
            </ul>
        </div>
    </div><!--end col-->
    <div class="col-span-12 xl:col-span-4 card">
        <div class="card-header">
            <h6 class="card-title">Colored Timeline</h6>
        </div>
        <div class="card-body">
            <ul class="timeline-square timeline">
                <li class="timeline-red active">Dark, Light with RTL Supported</li>
                <li class="timeline-red active">Multiple Layouts with Responsive</li>
                <li class="timeline-red active">Fully Responsive Design</li>
                <li class="timeline-red">W3C Validated HTML Pages</li>
                <li class="timeline-red">Easy to Customize with Tailwind.config & SCSS</li>
                <li class="timeline-red">Unlimited Template Possibilities</li>
            </ul>
        </div>
    </div><!--end col-->
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">ChangLog Timeline</h6>
        </div>
        <div class="card-body">
            <div class="max-w-xl mx-auto">
                <ul class="*:before:absolute *:before:w-0.5 *:before:bg-gray-200 dark:*:before:bg-dark-800 *:before:inset-y-0 *:relative rtl:*:before:right-4 ltr:*:before:left-4 flex flex-col *:pb-6">
                    <li class="last:before:hidden last:pb-0">
                        <div class="relative">
                            <div class="relative flex items-start space-x-3 rtl:space-x-reverse">
                                <div class="relative shrink-0">
                                    <div class="flex items-center justify-center text-white rounded-full bg-primary-500 size-8 ring-8 ring-white dark:ring-dark-900">
                                        <i data-lucide="plus" class="size-4"></i>
                                    </div>
                                </div>
                                <div class="mt-1 grow">
                                    <div class="flex items-center gap-2 mb-2">
                                        <a href="#" class="font-medium">v1.3.0</a>
                                        <a href="#" class="relative flex items-center px-3 py-1 text-xs border border-gray-200 rounded-full dark:border-dark-800">
                                            <div class="absolute flex items-center justify-center shrink-0">
                                                <span class="size-1.5 rounded-full bg-green-500" aria-hidden="true"></span>
                                            </div>
                                            <h6 class="ltr:ml-3.5 rtl:mr-3.5 text-gray-500 dark:text-dark-500 text-xs">Feature</h6>
                                        </a>
                                    </div>
                                    <h6 class="mb-3 text-sm text-gray-500 dark:text-dark-500 whitespace-nowrap">15 May, 2024</h6>
                                    <ul class="list-disc list-inside">
                                        <li>Added Chat Application</li>
                                        <li>Added Modern Layout</li>
                                        <li>Added Email Application</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </li>
                    <li class="last:before:hidden last:pb-0">
                        <div class="relative">
                            <div class="relative flex items-start space-x-3 rtl:space-x-reverse">
                                <div class="relative shrink-0">
                                    <div class="flex items-center justify-center text-white rounded-full bg-primary-500 size-8 ring-8 ring-white dark:ring-dark-900">
                                        <i data-lucide="plus" class="size-4"></i>
                                    </div>
                                </div>
                                <div class="mt-1 grow">
                                    <div class="flex items-center gap-2 mb-2">
                                        <a href="#" class="font-medium">v1.2.0</a>
                                        <a href="#" class="relative flex items-center px-3 py-1 text-xs border border-gray-200 rounded-full dark:border-dark-800">
                                            <div class="absolute flex items-center justify-center shrink-0">
                                                <span class="size-1.5 rounded-full bg-green-500" aria-hidden="true"></span>
                                            </div>
                                            <h6 class="ltr:ml-3.5 rtl:mr-3.5 text-gray-500 text-xs dark:text-dark-500">Feature & Bag</h6>
                                        </a>
                                    </div>
                                    <h6 class="mb-3 text-sm text-gray-500 dark:text-dark-500 whitespace-nowrap">05 May, 2024</h6>
                                    <ul class="list-disc list-inside">
                                        <li>Added Chat Application</li>
                                        <li>Added Modern Layout</li>
                                        <li>Added Email Application</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </li>
                    <li class="last:before:hidden last:pb-0">
                        <div class="relative">
                            <div class="relative flex items-start space-x-3 rtl:space-x-reverse">
                                <div class="relative shrink-0">
                                    <div class="flex items-center justify-center text-white rounded-full bg-primary-500 size-8 ring-8 ring-white dark:ring-dark-900">
                                        <i data-lucide="plus" class="size-4"></i>
                                    </div>
                                </div>
                                <div class="mt-1 grow">
                                    <div class="flex items-center gap-2 mb-2">
                                        <a href="#" class="font-medium">v1.0.0</a>
                                    </div>
                                    <h6 class="mb-3 text-sm text-gray-500 dark:text-dark-500 whitespace-nowrap">29 April, 2024</h6>
                                    <ul class="list-disc list-inside">
                                        <li>Initial Released</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
    </div><!--end col-->
</div><!--end grid-->

</div>
{{> partials/footer }}
</div>


{{> partials/vendor-scripts }}
<script type="module" src="assets/js/main.js"></script>

</body>
</html>