{{> partials/main }}

<head>

    {{> partials/title-meta title="reCAPTCHA" }}

    {{> partials/head-css }}

    <script src="https://www.google.com/recaptcha/enterprise.js?render=6Lf5X14qAAAAAGIXi_OaoSLOnFjuPEhfBPr2HZEP"></script>
</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}

{{> partials/page-heading title="reCAPTCHA" sub-title="Forms" }}

<div class="grid grid-cols-12">
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">reCAPTCHA</h6>
        </div>
        <div class="card-body">
            <p class="mb-4 text-gray-500 dark:text-dark-500">reCAPTCHA protects your website from fraud and abuse without creating friction.</p>
            <div class="max-w-md p-6 mx-auto rounded-md shadow-md shadow-gray-200 dark:shadow-dark-800">
                <div x-data="formHandler()">
                    <form>
                        <div class="mb-4">
                            <label for="name" class="form-label">Name:</label>
                            <input type="text" id="name" x-model="formData.name" name="name" placeholder="Your Name" class="form-input">
                        </div>
                        <div class="mb-4">
                            <label for="email" class="form-label">Email:</label>
                            <input type="email" id="email" x-model="formData.email" name="email" placeholder="Your Email" class="form-input">
                        </div>
                        <div class="mb-4">
                            <label for="message" class="form-label">Message:</label>
                            <textarea id="message" x-model="formData.message" name="message" rows="4" placeholder="Your Message" class="h-auto form-input"></textarea>
                        </div>
                        <div class="mb-4" x-show="!submitted">
                            <div class="g-recaptcha" data-sitekey="6Lf5X14qAAAAAGIXi_OaoSLOnFjuPEhfBPr2HZEP"></div>
                        </div>
                        <div class="mb-4" x-show="submitted">
                            <p class="font-bold text-green-600">Form submitted successfully!</p>
                        </div>
                        <button type="submit" @click="submitForm($event)" class="btn btn-primary">Submit</button>
                    </form>
                </div>
            </div>
        </div>
    </div><!--end col-->
</div><!--end grid-->

</div>
{{> partials/footer }}
</div>


{{> partials/vendor-scripts }}

<script src="https://www.google.com/recaptcha/api.js" async defer></script>
<script type="module" src="assets/js/form/recaptcha.init.js"></script>

<script type="module" src="assets/js/main.js"></script>

</body>
</html>