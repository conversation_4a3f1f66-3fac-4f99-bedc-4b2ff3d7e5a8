{{> partials/main }}

<head>

    {{> partials/title-meta title="Create Patient" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}
{{> partials/page-heading title="Create Patient" sub-title="Patients" }}

<div class="card">
    <div class="card-header">
        <h6 class="card-title">Patient Personal Details</h6>
    </div>
    <div class="card-body">
        <form action="#!" x-data="submit">
            <div class="grid grid-cols-12 gap-space">
                <div class="col-span-12 md:col-span-6 xl:col-span-4">
                    <label for="firstNameInput" class="form-label">First Name <span class="text-red-500">*</span></label>
                    <input type="text" id="firstNameInput" class="form-input" placeholder="Enter your first name" pattern="[A-Za-z ]+" title="Only letters and spaces allowed" required>
                </div>
                <div class="col-span-12 md:col-span-6 xl:col-span-4">
                    <label for="middleNameInput" class="form-label">Middle Name</label>
                    <input type="text" id="middleNameInput" class="form-input" placeholder="Enter your middle name" pattern="[A-Za-z ]+" title="Only letters and spaces allowed">
                </div>
                <div class="col-span-12 md:col-span-6 xl:col-span-4">
                    <label for="lastNameInput" class="form-label">Last Name <span class="text-red-500">*</span></label>
                    <input type="text" id="lastNameInput" class="form-input" placeholder="Enter your last name" pattern="[A-Za-z ]+" title="Only letters and spaces allowed" required>
                </div>
                <div class="col-span-12 md:col-span-6 xl:col-span-4">
                    <label for="dateOfBirthInput" class="form-label">Date of Birth <span class="text-red-500">*</span></label>
                    <input type="text" id="dateOfBirthInput" class="form-input" placeholder="Select date" data-provider="flatpickr" data-date-format="d M, Y">
                </div>
                <div class="col-span-12 md:col-span-6 xl:col-span-4">
                    <label for="ageInput" class="form-label">Age <span class="text-red-500">*</span></label>
                    <input type="number" id="ageInput" minlength="1" maxlength="100" class="form-input" placeholder="0" required>
                </div>
                <div class="col-span-12 md:col-span-6 xl:col-span-4">
                    <label for="genderSelect" class="form-label">Gender <span class="text-red-500">*</span></label>
                    <div id="genderSelect"></div>
                </div>
                <div class="col-span-12 md:col-span-6 xl:col-span-4">
                    <label for="weightInput" class="form-label">Weight (KG) <span class="text-red-500">*</span></label>
                    <input type="text" id="weightInput" class="form-input" placeholder="0 kg" required>
                </div>
                <div class="col-span-12 md:col-span-6 xl:col-span-4">
                    <label for="heightInput" class="form-label">Height <span class="text-red-500">*</span></label>
                    <input type="text" id="heightInput" class="form-input" placeholder="0' 0'" required>
                </div>
                <div class="col-span-12 md:col-span-6 xl:col-span-4">
                    <label for="bloodPressureInput" class="form-label">Blood Pressure <span class="text-red-500">*</span></label>
                    <input type="text" id="bloodPressureInput" class="form-input" placeholder="Blood pressure" required>
                </div>
                <div class="col-span-12 md:col-span-6">
                    <label for="phoneNumber" class="form-label">Phone Number <span class="text-red-500">*</span></label>
                    <div x-data="intCodesComponent" x-init="initialize()" class="flex form-input">
                        <select id="countries" class="bg-transparent cursor-pointer outline-hidden dark:bg-dark-900 ltr:pl-3 rtl:pr-3" x-model="selectedDialCode" @change="onSelectChangeHandler">
                            <template x-for="country in intCodes" :key="country.code">
                                <option :value="country.dial_code" x-text="country.name"></option>
                            </template>
                        </select>
                        <input x-ref="phoneInput" type="text" class="w-full p-3 bg-transparent border-gray-200 outline-hidden ltr:ml-3 rtl:mr-3 ltr:border-l-2 dark:border-dark-800 rtl:border-r-2" :placeholder="selectedFormat" required>
                    </div>
                </div>
                <div class="col-span-12 md:col-span-6">
                    <label for="emergencyNumber" class="form-label">Emergency Number <span class="text-red-500">*</span></label>
                    <div x-data="intCodesComponent" x-init="initialize()" class="flex form-input">
                        <select id="countries" class="bg-transparent cursor-pointer outline-hidden dark:bg-dark-900 ltr:pl-3 rtl:pr-3" x-model="selectedDialCode" @change="onSelectChangeHandler">
                            <template x-for="country in intCodes" :key="country.code">
                                <option :value="country.dial_code" x-text="country.name"></option>
                            </template>
                        </select>
                        <input x-ref="phoneInput" type="text" class="w-full p-3 bg-transparent border-gray-200 outline-hidden ltr:ml-3 rtl:mr-3 dark:border-dark-800 ltr:border-l-2 rtl:border-r-2" :placeholder="selectedFormat">
                    </div>
                </div>
                <div class="col-span-12 md:col-span-6 xl:col-span-4">
                    <label for="emailInput" class="form-label">Email <span class="text-red-500">*</span></label>
                    <input type="email" id="emailInput" class="form-input" placeholder="<EMAIL>" required>
                </div>
                <div class="col-span-12 md:col-span-6 xl:col-span-4">
                    <label for="occupationInput" class="form-label">Occupation <span class="text-red-500">*</span></label>
                    <input type="text" id="occupationInput" class="form-input" placeholder="Enter your occupation" pattern="[A-Za-z ]+" title="Only letters and spaces allowed" required>
                </div>
                <div class="col-span-12 md:col-span-6 xl:col-span-4">
                    <label for="cityInput" class="form-label">City <span class="text-red-500">*</span></label>
                    <input type="text" id="cityInput" class="form-input" placeholder="Enter city name" pattern="[A-Za-z ]+" title="Only letters and spaces allowed" required>
                </div>
                <div class="col-span-12 md:col-span-6 xl:col-span-4">
                    <label for="stateInput" class="form-label">State Name <span class="text-red-500">*</span></label>
                    <input type="text" id="stateInput" class="form-input" placeholder="Enter state name" pattern="[A-Za-z ]+" title="Only letters and spaces allowed" required>
                </div>
                <div class="col-span-12 md:col-span-6 xl:col-span-4">
                    <label for="countryInput" class="form-label">Country Name <span class="text-red-500">*</span></label>
                    <input type="text" id="countryInput" class="form-input" placeholder="Enter country name" pattern="[A-Za-z ]+" title="Only letters and spaces allowed" required>
                </div>
                <div class="col-span-12 md:col-span-6 xl:col-span-4">
                    <label for="zipCodeInput" class="form-label">Zip Code <span class="text-red-500">*</span></label>
                    <input type="number" id="zipCodeInput" class="form-input" placeholder="000 000" required>
                </div>
                <div class="col-span-12 md:col-span-6 xl:col-span-3">
                    <label for="familyDoctorName" class="form-label">Family Doctor</label>
                    <input type="text" id="familyDoctorName" class="form-input" placeholder="Enter doctor name" pattern="[A-Za-z ]+" title="Only letters and spaces allowed" required>
                </div>
                <div class="col-span-12 md:col-span-6 xl:col-span-3">
                    <label for="referringDoctorName" class="form-label">Referring Doctor</label>
                    <input type="text" id="referringDoctorName" class="form-input" placeholder="Referring doctor" pattern="[A-Za-z ]+" title="Only letters and spaces allowed" required>
                </div>
                <div class="col-span-12 md:col-span-6 xl:col-span-3">
                    <label for="assignedDoctorName" class="form-label">Assigned Doctor</label>
                    <input type="text" id="assignedDoctorName" class="form-input" placeholder="Assigned doctor" pattern="[A-Za-z ]+" title="Only letters and spaces allowed" required>
                </div>
                <div class="col-span-12 md:col-span-6 xl:col-span-3">
                    <label for="pharmacyDoctorName" class="form-label">Pharmacy Name</label>
                    <input type="text" id="pharmacyDoctorName" class="form-input" placeholder="Pharmacy Name" pattern="[A-Za-z ]+" title="Only letters and spaces allowed" required>
                </div>
                <div class="col-span-12">
                    <div class="flex items-center justify-end gap-2">
                        <button @click="onSubmit($event)" class="btn btn-primary">Submit Now</button>
                        <button type="reset" class="btn btn-sub-gray">Reset</button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h6 class="card-title">Insurance Information</h6>
    </div>
    <div class="card-body">
        <form action="#!" x-data="submit">
            <div class="grid grid-cols-12 gap-space">
                <div class="col-span-12 md:col-span-6 xl:col-span-4">
                    <label for="insuranceProviderInput" class="form-label">Insurance Provider <span class="text-red-500">*</span></label>
                    <input type="text" id="insuranceProviderInput" class="form-input" placeholder="Insurance provider" pattern="[A-Za-z ]+" title="Only letters and spaces allowed" required>
                </div>
                <div class="col-span-12 md:col-span-6 xl:col-span-4">
                    <label for="policyNumberInput" class="form-label">Policy Number <span class="text-red-500">*</span></label>
                    <input type="text" id="policyNumberInput" class="form-input" placeholder="000 0000 0000 0000 000" pattern="^[A-Za-z0-9]+$" title="Policy number can only contain letters and numbers" required>
                </div>
                <div class="col-span-12 md:col-span-6 xl:col-span-4">
                    <label for="startDateInput" class="form-label">Start Date <span class="text-red-500">*</span></label>
                    <input type="text" id="startDateInput" class="form-input" placeholder="Select Date">
                </div>
                <div class="col-span-12 md:col-span-6 xl:col-span-4">
                    <label for="effectiveDateInput" class="form-label">Effective Date <span class="text-red-500">*</span></label>
                    <input type="text" id="effectiveDateInput" class="form-input" placeholder="Select Date">
                </div>
                <div class="col-span-12">
                    <div class="flex items-center justify-end gap-2">
                        <button @click="onSubmit($event)" class="btn btn-primary">Save Now</button>
                        <button type="reset" class="btn btn-sub-gray">Reset</button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

</div>
{{> partials/footer }}
</div>

{{> partials/vendor-scripts }}

<script src="https://cdn.jsdelivr.net/npm/imask@6.0.7/dist/imask.min.js"></script>

<script type="module" src="assets/js/apps/hospital/patients/create.init.js"></script>

<script type="module" src="assets/js/main.js"></script>
</body>
</html>