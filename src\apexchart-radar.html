{{> partials/main }}

<head>

    {{> partials/title-meta title="Radar Charts" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}

{{> partials/page-heading title="Radar Charts" sub-title="Apexcharts" }}

<div class="grid grid-cols-12 gap-x-space">
    <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Basic</h6>
        </div>
        <div class="card-body">
            <div x-data="basicRadarApp" dir="ltr">
                <div class="!min-h-full" data-chart-colors="[bg-primary-500]" x-ref="basicRadarChart"></div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Radar – Multiple Series</h6>
        </div>
        <div class="card-body">
            <div x-data="multipleRadarApp" dir="ltr">
                <div class="!min-h-full" data-chart-colors="[bg-primary-500, bg-yellow-500, bg-green-500]" x-ref="multipleRadarChart"></div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Radar with Polygon-fill</h6>
        </div>
        <div class="card-body">
            <div x-data="polygonRadarApp" dir="ltr">
                <div class="!min-h-full" data-chart-colors="[bg-red-500]" x-ref="polygonRadarChart"></div>
            </div>
        </div>
    </div><!--end col-->
</div><!--end grid-->

</div>
{{> partials/footer }}
</div>

{{> partials/vendor-scripts }}

<script type="module" src="assets/js/charts/radar-chart.init.js"></script>

<script type="module" src="assets/js/main.js"></script>

</body>
</html>