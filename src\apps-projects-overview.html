{{> partials/main }}

<head>

    {{> partials/title-meta title="Overview" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}
{{> partials/page-heading title="Overview" sub-title="Projects" }}

<div class="card">
    <div class="relative overflow-hidden rounded-md-t h-44 bg-primary-500/10">
        <div class="border-[60px] border-t-primary-500 border-l-primary-500 absolute opacity-10 -top-2 ltr:right-0 rtl:left-0 rotate-45 size-96"></div>
        <div class="border-[60px] border-green-500 absolute opacity-10 top-20 ltr:right-8 rtl:left-8 rotate-45 size-80"></div>
        <div class="border-[60px] border-pink-500 absolute opacity-10 top-36 ltr:right-28 rtl:left-28 rotate-45 size-40"></div>
    </div>
    <div class="card-body">
        <div class="relative mb-6">
            <div class="flex flex-wrap gap-5">
                <div class="relative flex items-center justify-center bg-white border border-gray-200 rounded-md shadow-lg dark:bg-dark-900 dark:border-dark-800 shadow-gray-100 dark:shadow-dark-850 -mt-14 size-28 shrink-0">
                    <img src="assets/images/brands/img-02.png" alt="" class="mx-auto size-16">
                </div>
                <div class="grow">
                    <h5 class="mb-1">AI Model Development <span class="leading-normal ltr:ml-1 rtl:mr-1 badge badge-yellow">In Progress</span></h5>
                    <p class="text-gray-500 dark:text-dark-500">Create Date: 25 Jan, 2024</p>
                </div>
                <div class="flex items-center gap-3 shrink-0">
                    <button type="button" class="btn btn-primary" data-modal-target="shareProjectModal">Add User</button>
                    <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown">
                        <button x-ref="button" x-on:click="toggle()" :aria-expanded="open.toString()" type="button" title="dropdown-button" class="btn-icon-text btn-icon btn-sub-gray btn">
                            <i data-lucide="ellipsis" class="size-5"></i>
                        </button>

                        <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden" dropdown-position="right">
                            <a href="#" class="dropdown-item">
                                Create Invoice
                            </a>

                            <a href="#" class="dropdown-item">
                                Generate Billing
                            </a>

                            <a href="#" class="dropdown-item">
                                Delete Project
                            </a>
                            <a href="#" class="dropdown-item">
                                Settings
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-12 gap-5 mb-5">
            <div class="col-span-12 p-3 text-center border border-gray-200 border-dashed rounded-md dark:border-dark-800 sm:col-span-6 md:col-span-3 xl:col-span-2">
                <h6 class="mb-1">31 May, 2024</h6>
                <p class="text-gray-500 dark:text-dark-500">Due Date</p>
            </div>
            <div class="col-span-12 p-3 text-center border border-gray-200 border-dashed rounded-md dark:border-dark-800 sm:col-span-6 md:col-span-3 xl:col-span-2">
                <h6 class="mb-1">$25,000</h6>
                <p class="text-gray-500 dark:text-dark-500">Budget ($)</p>
            </div>
            <div class="col-span-12 p-3 text-center border border-gray-200 border-dashed rounded-md dark:border-dark-800 sm:col-span-6 md:col-span-3 xl:col-span-2">
                <h6 class="mb-1">$8,000</h6>
                <p class="text-gray-500 dark:text-dark-500">Total Spend ($)</p>
            </div>
            <div class="col-span-12 p-3 text-center border border-gray-200 border-dashed rounded-md dark:border-dark-800 sm:col-span-6 md:col-span-3 xl:col-span-2">
                <div x-data="dropdown" x-on:keydown.escape.prevent.stop="close($refs.button)" x-on:focusin.window="!$refs.panel.contains($event.target) && close()" x-id="['dropdown-button']" class="dropdown">

                    <button x-ref="button" x-on:click="toggle()" :aria-expanded="open" :aria-controls="$id('dropdown-button')" type="button" class="flex gap-2 p-0 btn text-start">
                        <img :src="currentUser.image" alt="" class="rounded-full size-10 shrink-0">
                        <span class="block grow">
                            <span class="text-gray-500 dark:text-dark-500" x-text="currentUser.role"></span>
                            <span class="block font-medium" x-text="currentUser.name"></span>
                        </span>
                        <svg :class="{ 'transform rotate-180': open }" class="text-gray-400 transition-transform duration-300 size-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                        </svg>
                    </button>

                    <div x-ref="panel" x-show="open" x-transition.origin.top.left x-on:click.outside="close($refs.button)" :id="$id('dropdown-button')" style="display: none;" class="p-2 dropdown-menu dropdown-right !w-52">
                        <template x-for="user in users" :key="user.name">
                            <a href="#!" @click.prevent="selectUser(user)" :class="{ 'active': isActive(user) }" class="dropdown-item">
                                <img :src="user.image" alt="" class="rounded-full size-8 shrink-0">
                                <h6 x-text="user.name"></h6>
                            </a>
                        </template>
                    </div>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-12 gapo-space mb-space">
            <div class="col-span-12 md:col-span-4">
                <h6 class="mb-1">Assigned To:</h6>
                <div class="flex -space-x-3 rtl:space-x-reverse">
                    <a href="#!" title="avatar link" class="transition duration-300 ease-linear hover:z-10" x-data="{ tooltip: 'Ina Payne' }"><img class="border-2 border-white rounded-full dark:border-dark-900 size-10" x-tooltip.placement.top.no-flip="tooltip" src="assets/images/avatar/user-5.png" alt=""></a>
                    <a href="#!" title="avatar link" class="transition duration-300 ease-linear hover:z-10" x-data="{ tooltip: 'Robert Freeman' }"><img class="border-2 border-white rounded-full dark:border-dark-900 size-10" x-tooltip.placement.top.no-flip="tooltip" src="assets/images/avatar/user-11.png" alt=""></a>
                    <a href="#!" title="avatar link" class="transition duration-300 ease-linear hover:z-10" x-data="{ tooltip: 'Michelle Wile' }"><img class="border-2 border-white rounded-full dark:border-dark-900 size-10" x-tooltip.placement.top.no-flip="tooltip" src="assets/images/avatar/user-13.png" alt=""></a>
                    <a href="#!" title="avatar link" class="transition duration-300 ease-linear hover:z-10" x-data="{ tooltip: 'William Keen' }"><img class="border-2 border-white rounded-full dark:border-dark-900 size-10" x-tooltip.placement.top.no-flip="tooltip" src="assets/images/avatar/user-14.png" alt=""></a>
                    <a href="#!" title="avatar link" class="transition duration-300 ease-linear hover:z-10" x-data="{ tooltip: 'Carol Kincaid' }"><img class="border-2 border-white rounded-full dark:border-dark-900 size-10" x-tooltip.placement.top.no-flip="tooltip" src="assets/images/avatar/user-16.png" alt=""></a>
                    <a href="#!" title="avatar link" class="transition duration-300 ease-linear hover:z-10" x-data="{ tooltip: 'Rachel Jackson' }"><img class="border-2 border-white rounded-full dark:border-dark-900 size-10" x-tooltip.placement.top.no-flip="tooltip" src="assets/images/avatar/user-17.png" alt=""></a>
                </div>
            </div>
            <div class="col-span-12 md:col-span-4">
                <h6 class="mb-1">Report To:</h6>
                <div class="flex -space-x-3 rtl:space-x-reverse">
                    <a href="#!" title="avatar link" class="transition duration-300 ease-linear hover:z-10" x-data="{ tooltip: 'Leal Bureau' }"><img class="border-2 border-white rounded-full dark:border-dark-900 size-10" x-tooltip.placement.top.no-flip="tooltip" src="assets/images/avatar/user-20.png" alt=""></a>
                    <a href="#!" title="avatar link" class="transition duration-300 ease-linear hover:z-10" x-data="{ tooltip: 'Julie Seltzer' }"><img class="border-2 border-white rounded-full dark:border-dark-900 size-10" x-tooltip.placement.top.no-flip="tooltip" src="assets/images/avatar/user-18.png" alt=""></a>
                </div>
            </div>
        </div>

        <ul class="overflow-x-auto whitespace-normal tabs-pills">
            <li>
                <a href="apps-projects-overview.html" class="nav-item [&.active]:bg-primary-500 [&.active]:text-primary-50 active">
                    <i data-lucide="eye" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-middle">Overview</span>
                </a>
            </li>
            <li>
                <a href="apps-projects-roadmap.html" class="nav-item [&.active]:bg-primary-500 [&.active]:text-primary-50">
                    <i data-lucide="sparkles" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-middle">RoadMap</span>
                </a>
            </li>
            <li>
                <a href="apps-projects-task.html" class="nav-item [&.active]:bg-primary-500 [&.active]:text-primary-50">
                    <i data-lucide="align-left" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-middle">Tasks</span>
                </a>
            </li>
            <li>
                <a href="apps-projects-files.html" class="nav-item [&.active]:bg-primary-500 [&.active]:text-primary-50">
                    <i data-lucide="file-text" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-middle">Files</span>
                </a>
            </li>
            <li>
                <a href="apps-projects-users.html" class="nav-item [&.active]:bg-primary-500 [&.active]:text-primary-50">
                    <i data-lucide="user-round" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-middle">Users</span>
                </a>
            </li>
        </ul>
    </div>
</div>

<div class="grid grid-cols-12 gap-x-space">
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Project Overview</h6>
        </div>
        <div class="card-body">
            <p class="mb-3 text-gray-500 dark:text-dark-500">The goal of this project is to develop an advanced AI model capable of predictive analytics within the e-commerce domain. This model will leverage machine learning techniques to analyze customer behavior, forecast sales trends, and optimize marketing strategies, ultimately enhancing the overall customer experience and boosting revenue for e-commerce platforms.</p>
            <h6 class="mb-2">Objectives:</h6>
            <ul class="mb-5 space-y-2 list-inside list-circle">
                <li>Customer Behavior Analysis</li>
                <li>Sales Forecasting</li>
                <li>Marketing Optimization</li>
                <li>Personalized Recommendations</li>
                <li>Performance Monitoring and Evaluation</li>
            </ul>

            <h6 class="mb-2">Deliverables:</h6>
            <ul class="space-y-2 list-inside list-circle">
                <li>Data Requirements Document</li>
                <li>Data Extraction Scripts</li>
                <li>Cleaned and Transformed Datasets</li>
                <li>Data Quality Report</li>
                <li>Data Storage Setup</li>
            </ul>
        </div>
    </div>
    <div class="col-span-12 md:col-span-6 lg:col-span-8 card">
        <div class="card-header">
            <h6 class="card-title">Working Hours</h6>
        </div>
        <div class="card-body">
            <div x-data="workingHoursApp" class="-ml-space" dir="ltr">
                <div class="!min-h-full" data-chart-colors="[bg-purple-500, bg-primary-500]" x-ref="workingHoursChart"></div>
            </div>
        </div>
    </div>
    <div class="col-span-12 md:col-span-6 lg:col-span-4 card">
        <div class="card-header">
            <h6 class="card-title">Task Activities</h6>
        </div>
        <div class="card-body">
            <div x-data="taskActivitiesApp" class="-ml-space" dir="ltr">
                <div class="!min-h-full" data-chart-colors="[bg-primary-500]" x-ref="taskActivitiesChart"></div>
            </div>
        </div>
    </div>
</div>

</div>
{{> partials/footer }}
</div>

<div id="shareProjectModal" class="!hidden modal show">
    <div class="modal-wrap modal-center">
        <div class="modal-header">
            <h6>Share Project</h6>
            <button data-modal-close="shareProjectModal" class="link link-red"><i data-lucide="x" class="size-5"></i></button>
        </div>
        <div class="modal-content">
            <div class="flex items-center gap-3">
                <input type="text" id="basicInput1" class="form-input grow" placeholder="<EMAIL>">
                <button type="button" class="btn btn-primary shrink-0">Send Invite</button>
            </div>
            <div class="mt-5">
                <div class="space-y-3">
                    <div class="flex items-center gap-2">
                        <img src="assets/images/avatar/user-5.png" alt="" class="rounded-full size-7 shrink-0">
                        <h6 class="grow">Ina Payne</h6>
                        <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown shrink-0">
                            <button x-ref="button" x-on:click="toggle()" :aria-expanded="open.toString()" type="button" class="flex items-center gap-2 text-gray-500 dark:text-dark-500">
                                Admin
                                <svg :class="{ 'transform rotate-180': open }" class="transition-transform duration-300 size-5" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                </svg>
                            </button>

                            <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden" dropdown-position="right">
                                <a href="#" class="dropdown-item">
                                    Edit
                                </a>
                                <a href="#" class="dropdown-item">
                                    View Only
                                </a>
                                <a href="#" class="dropdown-item">
                                    Admin
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center gap-2">
                        <img src="assets/images/avatar/user-11.png" alt="" class="rounded-full size-7 shrink-0">
                        <h6 class="grow">Robert Freeman</h6>
                        <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown shrink-0">
                            <button x-ref="button" x-on:click="toggle()" :aria-expanded="open.toString()" type="button" class="flex items-center gap-2 text-gray-500 dark:text-dark-500">
                                View Only
                                <svg :class="{ 'transform rotate-180': open }" class="transition-transform duration-300 size-5" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                </svg>
                            </button>

                            <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden" dropdown-position="right">
                                <a href="#" class="dropdown-item">
                                    Edit
                                </a>
                                <a href="#" class="dropdown-item">
                                    View Only
                                </a>
                                <a href="#" class="dropdown-item">
                                    Admin
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center gap-2">
                        <img src="assets/images/avatar/user-13.png" alt="" class="rounded-full size-7 shrink-0">
                        <h6 class="grow">Michelle Wile</h6>
                        <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown shrink-0">
                            <button x-ref="button" x-on:click="toggle()" :aria-expanded="open.toString()" type="button" class="flex items-center gap-2 text-gray-500 dark:text-dark-500">
                                Edit
                                <svg :class="{ 'transform rotate-180': open }" class="transition-transform duration-300 size-5" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                </svg>
                            </button>

                            <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden" dropdown-position="right">
                                <a href="#" class="dropdown-item">
                                    Edit
                                </a>
                                <a href="#" class="dropdown-item">
                                    View Only
                                </a>
                                <a href="#" class="dropdown-item">
                                    Admin
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center gap-2">
                        <img src="assets/images/avatar/user-14.png" alt="" class="rounded-full size-7 shrink-0">
                        <h6 class="grow">William Keen</h6>
                        <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown shrink-0">
                            <button x-ref="button" x-on:click="toggle()" :aria-expanded="open.toString()" type="button" class="flex items-center gap-2 text-gray-500 dark:text-dark-500">
                                Edit
                                <svg :class="{ 'transform rotate-180': open }" class="transition-transform duration-300 size-5" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                </svg>
                            </button>

                            <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden" dropdown-position="right">
                                <a href="#" class="dropdown-item">
                                    Edit
                                </a>
                                <a href="#" class="dropdown-item">
                                    View Only
                                </a>
                                <a href="#" class="dropdown-item">
                                    Admin
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center gap-2">
                        <img src="assets/images/avatar/user-16.png" alt="" class="rounded-full size-7 shrink-0">
                        <h6 class="grow">Carol Kincaid</h6>
                        <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown shrink-0">
                            <button x-ref="button" x-on:click="toggle()" :aria-expanded="open.toString()" type="button" class="flex items-center gap-2 text-gray-500 dark:text-dark-500">
                                View Only
                                <svg :class="{ 'transform rotate-180': open }" class="transition-transform duration-300 size-5" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                </svg>
                            </button>

                            <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden" dropdown-position="right">
                                <a href="#" class="dropdown-item">
                                    Edit
                                </a>
                                <a href="#" class="dropdown-item">
                                    View Only
                                </a>
                                <a href="#" class="dropdown-item">
                                    Admin
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center gap-2">
                        <img src="assets/images/avatar/user-17.png" alt="" class="rounded-full size-7 shrink-0">
                        <h6 class="grow">Rachel Jackson</h6>
                        <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown shrink-0">
                            <button x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" type="button" class="flex items-center gap-2 text-gray-500 dark:text-dark-500">
                                Edit
                                <svg :class="{ 'transform rotate-180': open }" class="transition-transform duration-300 size-5" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                </svg>
                            </button>

                            <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden" dropdown-position="right">
                                <a href="#" class="dropdown-item">
                                    Edit
                                </a>
                                <a href="#" class="dropdown-item">
                                    View Only
                                </a>
                                <a href="#" class="dropdown-item">
                                    Admin
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <p><a href="#!" class="link link-primary"><i data-lucide="link" class="inline-block align-middle ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-baseline">Copy URL</span></a></p>
        </div>
    </div>
</div><!--end-->

{{> partials/vendor-scripts }}

<script type="module" src="assets/js/apps/projects/overview.init.js"></script>

<script type="module" src="assets/js/main.js"></script>

</body>
</html>