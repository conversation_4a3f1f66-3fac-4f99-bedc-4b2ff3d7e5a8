{{> partials/main }}

<head>

    {{> partials/title-meta title="Pricing" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}
        {{> partials/page-heading title="Pricing" sub-title="Pages" }}

        <h6 class="mb-5 underline">Step 1:</h6>

        <div class="grid grid-cols-12 gap-x-5">
            <div class="col-span-12 xl:col-span-10 xl:col-start-2">
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-x-5">
                    <div>
                        <div class="relative card">
                            <div class="card-body">
                                <div class="relative">
                                    <div class="absolute top-0 z-0 size-11 blur-md bg-sky-500/20"></div>
                                    <i data-lucide="fan" class="relative stroke-1 size-10 fill-sky-500/20 text-sky-500"></i>
                                </div>
                                <h6 class="mt-5">Basic Plan</h6>
                                <p class="mb-3 text-sm text-gray-500 dark:text-dark-500">Forever</p>
                                <h2 class="font-semibold">$29.99</h2>
                                <div class="pt-5 my-5 border-t border-gray-200 dark:border-dark-800">
                                    <ul class="space-y-3 *:flex *:items-center *:gap-2">
                                        <li><i data-lucide="redo-2" class="inline-block text-green-500 size-4"></i> 1 Products & Projects</li>
                                        <li><i data-lucide="redo-2" class="inline-block text-green-500 size-4"></i> Custom Permissions</li>
                                        <li><i data-lucide="redo-2" class="inline-block text-green-500 size-4"></i> Some Basic Integration</li>
                                        <li><i data-lucide="redo-2" class="inline-block text-green-500 size-4"></i> Up to 5 Team Members</li>
                                        <li><i data-lucide="redo-2" class="inline-block text-green-500 size-4"></i> 24/7 Support</li>
                                    </ul>
                                </div>
                                <button data-modal-target="confirmModal" class="w-full btn btn-primary">Choose Plan</button>
                            </div>
                        </div>
                    </div><!--end col-->
                    <div>
                        <div class="relative card">
                            <div class="card-body">
                                <div class="absolute badge badge-sub-pink ltr:right-5 rtl:left-5 top-5">Popular</div>
                                <div class="relative">
                                    <div class="absolute top-0 z-0 bg-green-500/20 size-11 blur-md"></div>
                                    <i data-lucide="pyramid" class="relative text-green-500 stroke-1 size-10 fill-green-500/20"></i>
                                </div>
                                <h6 class="mt-5">Professional Plan</h6>
                                <p class="mb-3 text-sm text-gray-500 dark:text-dark-500">Saving $24.99 a year</p>
                                <h2 class="font-semibold">$49.99</h2>
                                <div class="pt-5 my-5 border-t border-gray-200 dark:border-dark-800">
                                    <ul class="space-y-3 *:flex *:items-center *:gap-2">
                                        <li><i data-lucide="redo-2" class="inline-block text-green-500 size-4"></i> 10 Products & Projects</li>
                                        <li><i data-lucide="redo-2" class="inline-block text-green-500 size-4"></i> Custom Permissions</li>
                                        <li><i data-lucide="redo-2" class="inline-block text-green-500 size-4"></i> Some Basic Integration</li>
                                        <li><i data-lucide="redo-2" class="inline-block text-green-500 size-4"></i> Up to 10 Team Members</li>
                                        <li><i data-lucide="redo-2" class="inline-block text-green-500 size-4"></i> Advanced Security</li>
                                        <li><i data-lucide="redo-2" class="inline-block text-green-500 size-4"></i> 24/7 Support</li>
                                    </ul>
                                </div>
                                <button data-modal-target="confirmModal" class="w-full btn btn-primary">Choose Plan</button>
                            </div>
                        </div>
                    </div><!--end col-->
                    <div>
                        <div class="relative card">
                            <div class="card-body">
                                <div class="relative">
                                    <div class="absolute top-0 z-0 bg-red-500/20 size-11 blur-md"></div>
                                    <i data-lucide="zap" class="relative text-red-500 stroke-1 size-10 fill-red-500/20"></i>
                                </div>
                                <h6 class="mt-5">Enterprise Plan</h6>
                                <p class="mb-3 text-sm text-gray-500 dark:text-dark-500">Saving $24.99 a year</p>
                                <h2 class="font-semibold">$59.99</h2>
                                <div class="pt-5 my-5 border-t border-gray-200 dark:border-dark-800">
                                    <ul class="space-y-3 *:flex *:items-center *:gap-2">
                                        <li><i data-lucide="redo-2" class="inline-block text-green-500 size-4"></i> 30 Products & Projects</li>
                                        <li><i data-lucide="redo-2" class="inline-block text-green-500 size-4"></i> Custom Permissions</li>
                                        <li><i data-lucide="redo-2" class="inline-block text-green-500 size-4"></i> Some Basic Integration</li>
                                        <li><i data-lucide="redo-2" class="inline-block text-green-500 size-4"></i> Up to 50 Team Members</li>
                                        <li><i data-lucide="redo-2" class="inline-block text-green-500 size-4"></i> Advanced Security</li>
                                        <li><i data-lucide="redo-2" class="inline-block text-green-500 size-4"></i> Permissions & Workflows</li>
                                        <li><i data-lucide="redo-2" class="inline-block text-green-500 size-4"></i> 24/7 Support</li>
                                    </ul>
                                </div>
                                <button data-modal-target="confirmModal" class="w-full btn btn-primary">Choose Plan</button>
                            </div>
                        </div>
                    </div><!--end col-->
                </div><!--end grid-->
            </div><!--end col-->
        </div><!--end grid-->

        <h6 class="mt-2 mb-5 underline">Step 2:</h6>
        <div class="grid grid-cols-12 gap-x-5">
            <div class="col-span-12 sm:col-span-6 lg:col-span-4 card">
                <div class="card-body">
                    <div class="flex items-start">
                        <div class="flex items-center justify-center ltr:mr-auto rtl:ml-auto size-16 bg-gradient-to-b from-yellow-500/20 rounded-modern">
                            <i data-lucide="arrow-down-from-line" class="relative text-yellow-500 stroke-1 size-10 fill-yellow-500/20"></i>
                        </div>
                        <label for="addOns1" class="switch-group switch-soft shrink-0">
                            <div class="relative">
                                <input type="checkbox" id="addOns1" class="hidden sr-only peer" />
                                <div class="switch-wrapper peer-checked:!bg-green-500/15"></div>
                                <div class="switch-dot peer-checked:translate-x-full rtl:peer-checked:-translate-x-full peer-checked:!bg-green-500"></div>
                            </div>
                        </label>
                    </div>
                    <div class="flex items-center mt-4">
                        <h6 class="grow">Comodo Media Downloader</h6>
                        <h6>$3.99/month</h6>
                    </div>
                    <p class="text-gray-500 truncate dark:text-dark-500">Comodo Media Downloader Service</p>
                </div>
            </div><!--end col-->
            <div class="col-span-12 sm:col-span-6 lg:col-span-4 card">
                <div class="card-body">
                    <div class="flex items-start">
                        <div class="flex items-center justify-center ltr:mr-auto rtl:ml-auto size-16 bg-gradient-to-r from-purple-500/20 rounded-modern">
                            <i data-lucide="droplet" class="relative text-purple-500 stroke-1 size-10 fill-purple-500/20"></i>
                        </div>
                        <label for="addOns2" class="switch-group switch-soft shrink-0">
                            <div class="relative">
                                <input type="checkbox" id="addOns2" class="hidden sr-only peer" />
                                <div class="switch-wrapper peer-checked:!bg-green-500/15"></div>
                                <div class="switch-dot peer-checked:translate-x-full rtl:peer-checked:-translate-x-full peer-checked:!bg-green-500"></div>
                            </div>
                        </label>
                    </div>
                    <div class="flex items-center mt-4">
                        <h6 class="grow">Https Enforcement</h6>
                        <h6>$6.99/month</h6>
                    </div>
                    <p class="text-gray-500 truncate dark:text-dark-500">Automatic Security</p>
                </div>
            </div><!--end col-->
            <div class="col-span-12 sm:col-span-6 lg:col-span-4 card">
                <div class="card-body">
                    <div class="flex items-start">
                        <div class="flex items-center justify-center ltr:mr-auto rtl:ml-auto size-16 bg-gradient-to-bl from-red-500/20 rounded-modern">
                            <i data-lucide="gem" class="relative text-red-500 stroke-1 size-10 fill-red-500/20"></i>
                        </div>
                        <label for="addOns3" class="switch-group switch-soft shrink-0">
                            <div class="relative">
                                <input type="checkbox" id="addOns3" class="hidden sr-only peer" />
                                <div class="switch-wrapper peer-checked:!bg-green-500/15"></div>
                                <div class="switch-dot peer-checked:translate-x-full rtl:peer-checked:-translate-x-full peer-checked:!bg-green-500"></div>
                            </div>
                        </label>
                    </div>
                    <div class="flex items-center mt-4">
                        <h6 class="grow">Online Security Pro</h6>
                        <h6>$6.99/month</h6>
                    </div>
                    <p class="text-gray-500 truncate dark:text-dark-500">Online Security Pro protects you against malware, phishing and malicious websites.</p>
                </div>
            </div><!--end col-->
        </div><!--end grid-->
    </div>
    {{> partials/footer }}
</div>

<div id="confirmModal" class="!hidden modal show">
    <div class="modal-wrap modal-sm modal-center">
        <div class="modal-content">
            <h5 class="mb-2">Upgraded Plan</h5>
            <p class="mb-5 text-gray-500 dark:text-dark-500">Please confirm your upgrade by selecting the 'Upgrade Plan' option below.</p>
            <div class="flex items-center justify-end gap-2">
                <button class="btn btn-yellow">Confirm</button>
                <button data-modal-close="confirmModal" class="btn btn-active-red">Cancel</button>
            </div>
        </div>
    </div>
</div><!--end-->

{{> partials/vendor-scripts }}

<script type="module" src="assets/js/main.js"></script>

</body>
</html>