{{> partials/main }}

<head>

    {{> partials/title-meta title="Analytics" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}
        {{> partials/page-heading title="Analytics" sub-title="Dashboards" }}

        <div class="grid grid-cols-12 gap-x-space">
            <div class="col-span-12 xl:col-span-6 2xl:col-span-4 card">
                <div class="card-body">
                    <div class="flex gap-3 mb-3">
                        <div class="flex items-center justify-center border-2 rounded-full text-primary-500 ring-1 ring-offset-2 dark:ring-offset-dark-900 ring-primary-500/20 size-12 border-primary-500">
                            <i data-lucide="circle-arrow-up" class="fill-primary-500/10"></i>
                        </div>
                        <div>
                            <p class="mb-1 text-gray-500 dark:text-dark-500">Sales Revenue</p>
                            <h5>$<span x-data="animatedCounter(145, 500, 0)" x-init="updateCounter" x-text="Math.round(current)"></span>M</h5>
                        </div>
                    </div>
                    <div x-data="salesRevenueApp" dir="ltr">
                        <div class="!min-h-full" data-chart-colors="[bg-primary-500, bg-primary-100, bg-primary-50, bg-primary-300]" x-ref="salesRevenueChart"></div>
                    </div>
                </div>
            </div><!--end Sales Revenue-->

            <div class="col-span-12 xl:col-span-6 2xl:col-span-4 card">
                <div class="card-body">
                    <div class="flex gap-3 mb-3">
                        <div class="flex items-center justify-center text-red-500 border-2 border-red-400 rounded-full ring-1 ring-offset-2 dark:ring-offset-dark-900 ring-red-500/20 size-12">
                            <i data-lucide="activity" class="fill-red-500/10"></i>
                        </div>
                        <div>
                            <p class="mb-1 text-gray-500 dark:text-dark-500">Ads Revenue</p>
                            <h5>$<span x-data="animatedCounter(145, 500, 0)" x-init="updateCounter" x-text="Math.round(current)"></span>M</h5>
                        </div>
                    </div>
                    <div x-data="adsRevenueApp" dir="ltr">
                        <div class="!min-h-full" data-chart-colors="[bg-red-500, bg-red-100, bg-red-50, bg-red-300]" x-ref="adsRevenueChart"></div>
                    </div>
                </div>
            </div><!--end Ads Revenue-->

            <div class="relative col-span-12 overflow-hidden md:col-span-6 2xl:col-span-2 card">
                <div class="absolute inset-0">
                    <svg viewBox="0 0 800 800" opacity="1">
                        <g fill="none" stroke-width="0.5" class="stroke-green-500/20" stroke-linecap="round" stroke-dasharray="52.5 5.5" transform="matrix(1.8,0,0,1.8,-319.99998474121094,-320)">
                            <line x1="236" y1="400" x2="564" y2="400" transform="rotate(0, 400, 400)" opacity="0.60"></line>
                            <line x1="213" y1="400" x2="587" y2="400" transform="rotate(5, 400, 400)" opacity="0.29"></line>
                            <line x1="394" y1="400" x2="406" y2="400" transform="rotate(10, 400, 400)" opacity="0.38"></line>
                            <line x1="767.5" y1="400" x2="32.5" y2="400" transform="rotate(15, 400, 400)" opacity="0.43"></line>
                            <line x1="210" y1="400" x2="590" y2="400" transform="rotate(20, 400, 400)" opacity="0.26"></line>
                            <line x1="760.5" y1="400" x2="39.5" y2="400" transform="rotate(25, 400, 400)" opacity="0.86"></line>
                            <line x1="263" y1="400" x2="537" y2="400" transform="rotate(30, 400, 400)" opacity="0.82"></line>
                            <line x1="384.5" y1="400" x2="415.5" y2="400" transform="rotate(35, 400, 400)" opacity="0.83"></line>
                            <line x1="188" y1="400" x2="612" y2="400" transform="rotate(40, 400, 400)" opacity="0.69"></line>
                            <line x1="268" y1="400" x2="532" y2="400" transform="rotate(45, 400, 400)" opacity="0.42"></line>
                            <line x1="710.5" y1="400" x2="89.5" y2="400" transform="rotate(50, 400, 400)" opacity="0.42"></line>
                            <line x1="209" y1="400" x2="591" y2="400" transform="rotate(55, 400, 400)" opacity="0.69"></line>
                            <line x1="644.5" y1="400" x2="155.5" y2="400" transform="rotate(60, 400, 400)" opacity="0.68"></line>
                            <line x1="670" y1="400" x2="130" y2="400" transform="rotate(65, 400, 400)" opacity="0.74"></line>
                            <line x1="180.5" y1="400" x2="619.5" y2="400" transform="rotate(70, 400, 400)" opacity="0.21"></line>
                            <line x1="622.5" y1="400" x2="177.5" y2="400" transform="rotate(75, 400, 400)" opacity="0.49"></line>
                            <line x1="530.5" y1="400" x2="269.5" y2="400" transform="rotate(80, 400, 400)" opacity="0.09"></line>
                            <line x1="645" y1="400" x2="155" y2="400" transform="rotate(85, 400, 400)" opacity="0.95"></line>
                            <line x1="460.5" y1="400" x2="339.5" y2="400" transform="rotate(90, 400, 400)" opacity="0.15"></line>
                            <line x1="482.5" y1="400" x2="317.5" y2="400" transform="rotate(95, 400, 400)" opacity="0.34"></line>
                            <line x1="182" y1="400" x2="618" y2="400" transform="rotate(100, 400, 400)" opacity="0.49"></line>
                            <line x1="398" y1="400" x2="402" y2="400" transform="rotate(105, 400, 400)" opacity="0.88"></line>
                            <line x1="340" y1="400" x2="460" y2="400" transform="rotate(110, 400, 400)" opacity="0.17"></line>
                            <line x1="437.5" y1="400" x2="362.5" y2="400" transform="rotate(115, 400, 400)" opacity="0.12"></line>
                            <line x1="173.5" y1="400" x2="626.5" y2="400" transform="rotate(120, 400, 400)" opacity="0.64"></line>
                            <line x1="513" y1="400" x2="287" y2="400" transform="rotate(125, 400, 400)" opacity="0.27"></line>
                            <line x1="466.5" y1="400" x2="333.5" y2="400" transform="rotate(130, 400, 400)" opacity="0.17"></line>
                            <line x1="128.5" y1="400" x2="671.5" y2="400" transform="rotate(135, 400, 400)" opacity="0.67"></line>
                            <line x1="369" y1="400" x2="431" y2="400" transform="rotate(140, 400, 400)" opacity="0.22"></line>
                            <line x1="150" y1="400" x2="650" y2="400" transform="rotate(145, 400, 400)" opacity="0.26"></line>
                            <line x1="520.5" y1="400" x2="279.5" y2="400" transform="rotate(150, 400, 400)" opacity="0.71"></line>
                            <line x1="443.5" y1="400" x2="356.5" y2="400" transform="rotate(155, 400, 400)" opacity="0.47"></line>
                            <line x1="734.5" y1="400" x2="65.5" y2="400" transform="rotate(160, 400, 400)" opacity="0.23"></line>
                            <line x1="569.5" y1="400" x2="230.5" y2="400" transform="rotate(165, 400, 400)" opacity="0.52"></line>
                            <line x1="541.5" y1="400" x2="258.5" y2="400" transform="rotate(170, 400, 400)" opacity="0.15"></line>
                            <line x1="405" y1="400" x2="395" y2="400" transform="rotate(175, 400, 400)" opacity="0.19"></line>
                            <line x1="546.5" y1="400" x2="253.5" y2="400" transform="rotate(180, 400, 400)" opacity="0.18"></line>
                            <line x1="549" y1="400" x2="251" y2="400" transform="rotate(185, 400, 400)" opacity="0.57"></line>
                            <line x1="443" y1="400" x2="357" y2="400" transform="rotate(190, 400, 400)" opacity="0.26"></line>
                            <line x1="308" y1="400" x2="492" y2="400" transform="rotate(195, 400, 400)" opacity="0.91"></line>
                            <line x1="758.5" y1="400" x2="41.5" y2="400" transform="rotate(200, 400, 400)" opacity="0.25"></line>
                            <line x1="280" y1="400" x2="520" y2="400" transform="rotate(205, 400, 400)" opacity="0.59"></line>
                            <line x1="232" y1="400" x2="568" y2="400" transform="rotate(210, 400, 400)" opacity="0.67"></line>
                            <line x1="326.5" y1="400" x2="473.5" y2="400" transform="rotate(215, 400, 400)" opacity="0.32"></line>
                            <line x1="337.5" y1="400" x2="462.5" y2="400" transform="rotate(220, 400, 400)" opacity="0.15"></line>
                            <line x1="622" y1="400" x2="178" y2="400" transform="rotate(225, 400, 400)" opacity="0.51"></line>
                            <line x1="714.5" y1="400" x2="85.5" y2="400" transform="rotate(230, 400, 400)" opacity="0.44"></line>
                            <line x1="248" y1="400" x2="552" y2="400" transform="rotate(235, 400, 400)" opacity="0.31"></line>
                            <line x1="531" y1="400" x2="269" y2="400" transform="rotate(240, 400, 400)" opacity="0.12"></line>
                            <line x1="147.5" y1="400" x2="652.5" y2="400" transform="rotate(245, 400, 400)" opacity="0.66"></line>
                            <line x1="131.5" y1="400" x2="668.5" y2="400" transform="rotate(250, 400, 400)" opacity="0.54"></line>
                            <line x1="150.5" y1="400" x2="649.5" y2="400" transform="rotate(255, 400, 400)" opacity="0.56"></line>
                            <line x1="677" y1="400" x2="123" y2="400" transform="rotate(260, 400, 400)" opacity="0.26"></line>
                            <line x1="262.5" y1="400" x2="537.5" y2="400" transform="rotate(265, 400, 400)" opacity="0.62"></line>
                            <line x1="629.5" y1="400" x2="170.5" y2="400" transform="rotate(270, 400, 400)" opacity="0.89"></line>
                            <line x1="253.5" y1="400" x2="546.5" y2="400" transform="rotate(275, 400, 400)" opacity="0.94"></line>
                            <line x1="589" y1="400" x2="211" y2="400" transform="rotate(280, 400, 400)" opacity="0.99"></line>
                            <line x1="547" y1="400" x2="253" y2="400" transform="rotate(285, 400, 400)" opacity="0.72"></line>
                            <line x1="418.5" y1="400" x2="381.5" y2="400" transform="rotate(290, 400, 400)" opacity="0.98"></line>
                            <line x1="575" y1="400" x2="225" y2="400" transform="rotate(295, 400, 400)" opacity="0.33"></line>
                            <line x1="664" y1="400" x2="136" y2="400" transform="rotate(300, 400, 400)" opacity="0.29"></line>
                            <line x1="677" y1="400" x2="123" y2="400" transform="rotate(305, 400, 400)" opacity="0.10"></line>
                            <line x1="423" y1="400" x2="377" y2="400" transform="rotate(310, 400, 400)" opacity="0.20"></line>
                            <line x1="216" y1="400" x2="584" y2="400" transform="rotate(315, 400, 400)" opacity="0.44"></line>
                            <line x1="626.5" y1="400" x2="173.5" y2="400" transform="rotate(320, 400, 400)" opacity="0.18"></line>
                            <line x1="507.5" y1="400" x2="292.5" y2="400" transform="rotate(325, 400, 400)" opacity="0.51"></line>
                            <line x1="454" y1="400" x2="346" y2="400" transform="rotate(330, 400, 400)" opacity="1.00"></line>
                            <line x1="342" y1="400" x2="458" y2="400" transform="rotate(335, 400, 400)" opacity="0.12"></line>
                            <line x1="571.5" y1="400" x2="228.5" y2="400" transform="rotate(340, 400, 400)" opacity="0.46"></line>
                            <line x1="150.5" y1="400" x2="649.5" y2="400" transform="rotate(345, 400, 400)" opacity="0.55"></line>
                            <line x1="582.5" y1="400" x2="217.5" y2="400" transform="rotate(350, 400, 400)" opacity="0.27"></line>
                            <line x1="728" y1="400" x2="72" y2="400" transform="rotate(355, 400, 400)" opacity="0.18"></line>
                            <line x1="625.5" y1="400" x2="174.5" y2="400" transform="rotate(360, 400, 400)" opacity="0.72"></line>
                            <line x1="481.5" y1="400" x2="318.5" y2="400" transform="rotate(365, 400, 400)" opacity="0.31"></line>
                            <line x1="676" y1="400" x2="124" y2="400" transform="rotate(370, 400, 400)" opacity="0.43"></line>
                            <line x1="586.5" y1="400" x2="213.5" y2="400" transform="rotate(375, 400, 400)" opacity="0.72"></line>
                        </g>
                    </svg>
                </div>
                <div class="relative flex flex-col h-full card-body">
                    <p class="text-gray-500 uppercase dark:text-dark-500">Impressions</p>

                    <div class="my-auto text-green-500">
                        <i data-lucide="arrow-up-from-dot" class="mx-auto"></i>
                        <h4 class="mt-2 text-center">+39.7%</h4>
                    </div>
                    <p class="text-green-500"><i data-lucide="eye" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> 47,859</p>
                </div>
            </div><!--end Impressions-->

            <div class="relative col-span-12 overflow-hidden md:col-span-6 2xl:col-span-2 card">
                <div class="absolute inset-0">
                    <svg viewBox="0 0 800 800" opacity="1">
                        <g fill="none" stroke-width="0.5" class="stroke-sky-500/20" stroke-linecap="round" stroke-dasharray="52.5 5.5" transform="matrix(1.8,0,0,1.8,-319.99998474121094,-320)">
                            <line x1="236" y1="400" x2="564" y2="400" transform="rotate(0, 400, 400)" opacity="0.60"></line>
                            <line x1="213" y1="400" x2="587" y2="400" transform="rotate(5, 400, 400)" opacity="0.29"></line>
                            <line x1="394" y1="400" x2="406" y2="400" transform="rotate(10, 400, 400)" opacity="0.38"></line>
                            <line x1="767.5" y1="400" x2="32.5" y2="400" transform="rotate(15, 400, 400)" opacity="0.43"></line>
                            <line x1="210" y1="400" x2="590" y2="400" transform="rotate(20, 400, 400)" opacity="0.26"></line>
                            <line x1="760.5" y1="400" x2="39.5" y2="400" transform="rotate(25, 400, 400)" opacity="0.86"></line>
                            <line x1="263" y1="400" x2="537" y2="400" transform="rotate(30, 400, 400)" opacity="0.82"></line>
                            <line x1="384.5" y1="400" x2="415.5" y2="400" transform="rotate(35, 400, 400)" opacity="0.83"></line>
                            <line x1="188" y1="400" x2="612" y2="400" transform="rotate(40, 400, 400)" opacity="0.69"></line>
                            <line x1="268" y1="400" x2="532" y2="400" transform="rotate(45, 400, 400)" opacity="0.42"></line>
                            <line x1="710.5" y1="400" x2="89.5" y2="400" transform="rotate(50, 400, 400)" opacity="0.42"></line>
                            <line x1="209" y1="400" x2="591" y2="400" transform="rotate(55, 400, 400)" opacity="0.69"></line>
                            <line x1="644.5" y1="400" x2="155.5" y2="400" transform="rotate(60, 400, 400)" opacity="0.68"></line>
                            <line x1="670" y1="400" x2="130" y2="400" transform="rotate(65, 400, 400)" opacity="0.74"></line>
                            <line x1="180.5" y1="400" x2="619.5" y2="400" transform="rotate(70, 400, 400)" opacity="0.21"></line>
                            <line x1="622.5" y1="400" x2="177.5" y2="400" transform="rotate(75, 400, 400)" opacity="0.49"></line>
                            <line x1="530.5" y1="400" x2="269.5" y2="400" transform="rotate(80, 400, 400)" opacity="0.09"></line>
                            <line x1="645" y1="400" x2="155" y2="400" transform="rotate(85, 400, 400)" opacity="0.95"></line>
                            <line x1="460.5" y1="400" x2="339.5" y2="400" transform="rotate(90, 400, 400)" opacity="0.15"></line>
                            <line x1="482.5" y1="400" x2="317.5" y2="400" transform="rotate(95, 400, 400)" opacity="0.34"></line>
                            <line x1="182" y1="400" x2="618" y2="400" transform="rotate(100, 400, 400)" opacity="0.49"></line>
                            <line x1="398" y1="400" x2="402" y2="400" transform="rotate(105, 400, 400)" opacity="0.88"></line>
                            <line x1="340" y1="400" x2="460" y2="400" transform="rotate(110, 400, 400)" opacity="0.17"></line>
                            <line x1="437.5" y1="400" x2="362.5" y2="400" transform="rotate(115, 400, 400)" opacity="0.12"></line>
                            <line x1="173.5" y1="400" x2="626.5" y2="400" transform="rotate(120, 400, 400)" opacity="0.64"></line>
                            <line x1="513" y1="400" x2="287" y2="400" transform="rotate(125, 400, 400)" opacity="0.27"></line>
                            <line x1="466.5" y1="400" x2="333.5" y2="400" transform="rotate(130, 400, 400)" opacity="0.17"></line>
                            <line x1="128.5" y1="400" x2="671.5" y2="400" transform="rotate(135, 400, 400)" opacity="0.67"></line>
                            <line x1="369" y1="400" x2="431" y2="400" transform="rotate(140, 400, 400)" opacity="0.22"></line>
                            <line x1="150" y1="400" x2="650" y2="400" transform="rotate(145, 400, 400)" opacity="0.26"></line>
                            <line x1="520.5" y1="400" x2="279.5" y2="400" transform="rotate(150, 400, 400)" opacity="0.71"></line>
                            <line x1="443.5" y1="400" x2="356.5" y2="400" transform="rotate(155, 400, 400)" opacity="0.47"></line>
                            <line x1="734.5" y1="400" x2="65.5" y2="400" transform="rotate(160, 400, 400)" opacity="0.23"></line>
                            <line x1="569.5" y1="400" x2="230.5" y2="400" transform="rotate(165, 400, 400)" opacity="0.52"></line>
                            <line x1="541.5" y1="400" x2="258.5" y2="400" transform="rotate(170, 400, 400)" opacity="0.15"></line>
                            <line x1="405" y1="400" x2="395" y2="400" transform="rotate(175, 400, 400)" opacity="0.19"></line>
                            <line x1="546.5" y1="400" x2="253.5" y2="400" transform="rotate(180, 400, 400)" opacity="0.18"></line>
                            <line x1="549" y1="400" x2="251" y2="400" transform="rotate(185, 400, 400)" opacity="0.57"></line>
                            <line x1="443" y1="400" x2="357" y2="400" transform="rotate(190, 400, 400)" opacity="0.26"></line>
                            <line x1="308" y1="400" x2="492" y2="400" transform="rotate(195, 400, 400)" opacity="0.91"></line>
                            <line x1="758.5" y1="400" x2="41.5" y2="400" transform="rotate(200, 400, 400)" opacity="0.25"></line>
                            <line x1="280" y1="400" x2="520" y2="400" transform="rotate(205, 400, 400)" opacity="0.59"></line>
                            <line x1="232" y1="400" x2="568" y2="400" transform="rotate(210, 400, 400)" opacity="0.67"></line>
                            <line x1="326.5" y1="400" x2="473.5" y2="400" transform="rotate(215, 400, 400)" opacity="0.32"></line>
                            <line x1="337.5" y1="400" x2="462.5" y2="400" transform="rotate(220, 400, 400)" opacity="0.15"></line>
                            <line x1="622" y1="400" x2="178" y2="400" transform="rotate(225, 400, 400)" opacity="0.51"></line>
                            <line x1="714.5" y1="400" x2="85.5" y2="400" transform="rotate(230, 400, 400)" opacity="0.44"></line>
                            <line x1="248" y1="400" x2="552" y2="400" transform="rotate(235, 400, 400)" opacity="0.31"></line>
                            <line x1="531" y1="400" x2="269" y2="400" transform="rotate(240, 400, 400)" opacity="0.12"></line>
                            <line x1="147.5" y1="400" x2="652.5" y2="400" transform="rotate(245, 400, 400)" opacity="0.66"></line>
                            <line x1="131.5" y1="400" x2="668.5" y2="400" transform="rotate(250, 400, 400)" opacity="0.54"></line>
                            <line x1="150.5" y1="400" x2="649.5" y2="400" transform="rotate(255, 400, 400)" opacity="0.56"></line>
                            <line x1="677" y1="400" x2="123" y2="400" transform="rotate(260, 400, 400)" opacity="0.26"></line>
                            <line x1="262.5" y1="400" x2="537.5" y2="400" transform="rotate(265, 400, 400)" opacity="0.62"></line>
                            <line x1="629.5" y1="400" x2="170.5" y2="400" transform="rotate(270, 400, 400)" opacity="0.89"></line>
                            <line x1="253.5" y1="400" x2="546.5" y2="400" transform="rotate(275, 400, 400)" opacity="0.94"></line>
                            <line x1="589" y1="400" x2="211" y2="400" transform="rotate(280, 400, 400)" opacity="0.99"></line>
                            <line x1="547" y1="400" x2="253" y2="400" transform="rotate(285, 400, 400)" opacity="0.72"></line>
                            <line x1="418.5" y1="400" x2="381.5" y2="400" transform="rotate(290, 400, 400)" opacity="0.98"></line>
                            <line x1="575" y1="400" x2="225" y2="400" transform="rotate(295, 400, 400)" opacity="0.33"></line>
                            <line x1="664" y1="400" x2="136" y2="400" transform="rotate(300, 400, 400)" opacity="0.29"></line>
                            <line x1="677" y1="400" x2="123" y2="400" transform="rotate(305, 400, 400)" opacity="0.10"></line>
                            <line x1="423" y1="400" x2="377" y2="400" transform="rotate(310, 400, 400)" opacity="0.20"></line>
                            <line x1="216" y1="400" x2="584" y2="400" transform="rotate(315, 400, 400)" opacity="0.44"></line>
                            <line x1="626.5" y1="400" x2="173.5" y2="400" transform="rotate(320, 400, 400)" opacity="0.18"></line>
                            <line x1="507.5" y1="400" x2="292.5" y2="400" transform="rotate(325, 400, 400)" opacity="0.51"></line>
                            <line x1="454" y1="400" x2="346" y2="400" transform="rotate(330, 400, 400)" opacity="1.00"></line>
                            <line x1="342" y1="400" x2="458" y2="400" transform="rotate(335, 400, 400)" opacity="0.12"></line>
                            <line x1="571.5" y1="400" x2="228.5" y2="400" transform="rotate(340, 400, 400)" opacity="0.46"></line>
                            <line x1="150.5" y1="400" x2="649.5" y2="400" transform="rotate(345, 400, 400)" opacity="0.55"></line>
                            <line x1="582.5" y1="400" x2="217.5" y2="400" transform="rotate(350, 400, 400)" opacity="0.27"></line>
                            <line x1="728" y1="400" x2="72" y2="400" transform="rotate(355, 400, 400)" opacity="0.18"></line>
                            <line x1="625.5" y1="400" x2="174.5" y2="400" transform="rotate(360, 400, 400)" opacity="0.72"></line>
                            <line x1="481.5" y1="400" x2="318.5" y2="400" transform="rotate(365, 400, 400)" opacity="0.31"></line>
                            <line x1="676" y1="400" x2="124" y2="400" transform="rotate(370, 400, 400)" opacity="0.43"></line>
                            <line x1="586.5" y1="400" x2="213.5" y2="400" transform="rotate(375, 400, 400)" opacity="0.72"></line>
                        </g>
                    </svg>
                </div>
                <div class="relative flex flex-col h-full card-body">
                    <p class="text-gray-500 uppercase dark:text-dark-500">Clicks</p>

                    <div class="my-auto text-sky-500">
                        <i data-lucide="mouse-pointer-click" class="mx-auto"></i>
                        <h4 class="mt-2 text-center">+4.8%</h4>
                    </div>
                    <p class="text-sky-500"><i data-lucide="mouse" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> 15,487</p>
                </div>
            </div><!--end Clicks-->

            <div class="col-span-12 xl:col-span-6 2xl:col-span-3 card">
                <div class="flex items-center gap-3 card-header">
                    <h6 class="card-title grow">Performance</h6>
                    <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown shrink-0">
                        <button x-ref="button" x-on:click="toggle()" title="dropdown-button" :aria-expanded="open.toString()" type="button" class="flex items-center text-gray-500 dark:text-dark-500">
                            <i data-lucide="ellipsis" class="size-5"></i>
                        </button>
                        <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden" dropdown-position="right">
                            <ul>
                                <li>
                                    <a href="#!" class="dropdown-item">
                                        <span>Weekly</span>
                                    </a>
                                </li>
                                <li>
                                    <a href="#!" class="dropdown-item">
                                        <span>Monthly</span>
                                    </a>
                                </li>
                                <li>
                                    <a href="#!" class="dropdown-item">
                                        <span>Yearly</span>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div x-data="{ 
                        openTab: 1,
                        activeClasses: 'bg-white text-gray-800 dark:bg-dark-900 dark:text-dark-50',
                        inactiveClasses: 'hover:text-gray-800 dark:hover:text-dark-50'
                    }">
                        <ul class="flex p-1.5 bg-gray-100 dark:bg-dark-850 rounded-full *:grow">
                            <li @click="openTab = 1">
                                <button type="button" :class="openTab === 1 ? activeClasses : inactiveClasses" class="relative block px-2 py-1 font-medium text-center rounded-full link text-13">
                                    New Users
                                </button>
                            </li>
                            <li @click="openTab = 2">
                                <button type="button" :class="openTab === 2 ? activeClasses : inactiveClasses" class="relative block px-2 py-1 font-medium text-center rounded-full link text-13">
                                    Online Sales
                                </button>
                            </li>
                            <li @click="openTab = 3">
                                <button type="button" :class="openTab === 3 ? activeClasses : inactiveClasses" class="relative block px-2 py-1 font-medium text-center rounded-full link text-13">
                                    Daily Sales
                                </button>
                            </li>
                        </ul>
                        <div class="w-full mt-4">
                            <div x-show="openTab === 1">
                                <div class="card">
                                    <div class="card-body">
                                        <div class="flex items-center gap-3">
                                            <div class="rounded-full size-24">
                                                <img src="assets/images/avatar/user-13.png" alt="" class="rounded-full">
                                            </div>
                                            <div>
                                                <p class="mb-1 text-gray-500 dark:text-dark-500 text-13">Top User</p>
                                                <h6 class="mb-2">Jabari Mayer</h6>
                                                <p class="mb-1 text-gray-500 dark:text-dark-500 text-13">Daily Visits</p>
                                                <h6>244 Clicks</h6>
                                            </div>
                                        </div>
                                        <div class="pt-4 mt-4 border-t border-gray-200 border-dashed dark:border-dark-800">
                                            <div class="flex items-center gap-3">
                                                <div class="grow">
                                                    <p class="mb-1 text-gray-500 dark:text-dark-500 text-13">New Users</p>
                                                    <h5>+<span x-data="animatedCounter(54, 500, 0)" x-init="updateCounter" x-text="Math.round(current)"></span>M</h5>
                                                </div>
                                                <div class="shrink-0">
                                                    <span class="inline-block font-medium text-green-500"><i data-lucide="move-up" class="inline-block size-3"></i> 9.63%</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <p class="text-center text-gray-500 dark:text-dark-500">Increase your email marketing by <span class="text-primary-500">41%</span> to reach your user acquisition and monthly targets.</p>
                            </div>
                            <div x-show="openTab === 2">
                                <div class="grid grid-cols-12 gap-space">
                                    <div class="col-span-6 p-3 text-center border border-gray-200 border-dashed rounded-md dark:border-dark-800">
                                        <p class="mb-1 text-gray-500 dark:text-dark-500 text-13">Landing Products</p>
                                        <h6><span x-data="animatedCounter(1154, 500, 0)" x-init="updateCounter" x-text="Math.round(current)"></span>+</h6>
                                    </div>
                                    <div class="col-span-6 p-3 text-center border border-gray-200 border-dashed rounded-md dark:border-dark-800">
                                        <p class="mb-1 text-gray-500 dark:text-dark-500 text-13">Admin Products</p>
                                        <h6><span x-data="animatedCounter(2387, 500, 0)" x-init="updateCounter" x-text="Math.round(current)"></span>+</h6>
                                    </div>
                                </div>
                                <p class="mt-3 mb-1 text-gray-500 dark:text-dark-500">Average Online Sales</p>
                                <h5 class="mb-2"><span x-data="animatedCounter(4321, 500, 0)" x-init="updateCounter" x-text="Math.round(current)"></span>+</h5>
                                <div x-data="averageOnlineSalesApp" dir="ltr">
                                    <div class="!min-h-full" data-chart-colors="[bg-sky-500]" x-ref="averageOnlineSalesChart"></div>
                                </div>
                            </div>
                            <div x-show="openTab === 3">
                                <div class="grid grid-cols-12 gap-space">
                                    <div class="col-span-6 p-3 text-center border border-gray-200 border-dashed rounded-md dark:border-dark-800">
                                        <p class="mb-1 text-gray-500 dark:text-dark-500 text-13">Landing Products</p>
                                        <h6><span x-data="animatedCounter(46, 500, 0)" x-init="updateCounter" x-text="Math.round(current)"></span>+</h6>
                                    </div>
                                    <div class="col-span-6 p-3 text-center border border-gray-200 border-dashed rounded-md dark:border-dark-800">
                                        <p class="mb-1 text-gray-500 dark:text-dark-500 text-13">Admin Products</p>
                                        <h6><span x-data="animatedCounter(78, 500, 0)" x-init="updateCounter" x-text="Math.round(current)"></span>+</h6>
                                    </div>
                                </div>
                                <p class="mt-3 mb-1 text-gray-500 dark:text-dark-500">Average Weekly Sales</p>
                                <h5 class="mb-2"><span x-data="animatedCounter(1173, 500, 0)" x-init="updateCounter" x-text="Math.round(current)"></span>+</h5>
                                <div x-data="averageOnlineWeeklyApp" dir="ltr">
                                    <div class="!min-h-full" data-chart-colors="[bg-sky-500]" x-ref="averageOnlineWeeklyChart"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div><!--end Performance-->

            <div class="col-span-12 xl:col-span-6 2xl:col-span-9 card">
                <div class="flex flex-col gap-3 md:flex-row md:items-center card-header">
                    <h6 class="card-title grow">Web Analytics</h6>
                    <div class="flex items-center gap-2">
                        <a href="#!" class="link link-primary"><i data-lucide="circle-dot-dashed" class="inline-block text-primary-500 size-4"></i> <span class="leading-none align-middle">Referral</span></a>
                        <a href="#!" class="link link-green"><i data-lucide="circle-dot-dashed" class="inline-block text-green-500 size-4"></i> <span class="leading-none align-middle">Direct</span></a>
                        <a href="#!" class="link link-purple"><i data-lucide="circle-dot-dashed" class="inline-block text-purple-500 size-4"></i> <span class="leading-none align-middle">Ads</span></a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="grid grid-cols-12 gap-3">
                        <div class="col-span-12 sm:col-span-6 lg:col-span-3">
                            <p class="text-gray-500 dark:text-dark-500">Page Views</p>
                            <h5><span x-data="animatedCounter(17415, 500, 0)" x-init="updateCounter" x-text="Math.round(current)"></span>+ <span class="text-xs text-green-500"><i class="align-baseline ri-arrow-up-line"></i> 4.5%</span></h5>
                        </div>
                        <div class="col-span-12 sm:col-span-6 lg:col-span-3">
                            <p class="text-gray-500 dark:text-dark-500">Page Views</p>
                            <h5><span x-data="animatedCounter(2, 500, 0)" x-init="updateCounter" x-text="Math.round(current)"></span>m <span x-data="animatedCounter(18, 500, 0)" x-init="updateCounter" x-text="Math.round(current)"></span>s <span class="text-xs text-red-500"><i class="align-baseline ri-arrow-down-line"></i> 0.9%</span></h5>
                        </div>
                    </div>
                    <div x-data="webAnalyticsApp" dir="ltr">
                        <div class="!min-h-full" data-chart-colors="[bg-primary-500, bg-green-500, bg-purple-500]" x-ref="webAnalyticsChart"></div>
                    </div>
                </div>
            </div><!--end Web Analytics-->

            <div class="col-span-12 2xl:col-span-4 card">
                <div class="flex items-center gap-3 card-header">
                    <h6 class="card-title grow">On Boarding Campaign</h6>
                    <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown shrink-0">
                        <button x-ref="button" x-on:click="toggle()" title="dropdown-button" :aria-expanded="open.toString()" type="button" class="flex items-center text-gray-500 dark:text-dark-500">
                            <i data-lucide="ellipsis" class="size-5"></i>
                        </button>
                        <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden" dropdown-position="right">
                            <ul>
                                <li>
                                    <a href="#!" class="dropdown-item">
                                        <span>Weekly</span>
                                    </a>
                                </li>
                                <li>
                                    <a href="#!" class="dropdown-item">
                                        <span>Monthly</span>
                                    </a>
                                </li>
                                <li>
                                    <a href="#!" class="dropdown-item">
                                        <span>Yearly</span>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="mb-5 alert alert-purple">
                        Help new customers appreciate the value they'll receive from your admin.
                    </div>

                    <div class="grid grid-cols-12 gap-space">
                        <div class="col-span-12 mb-0 md:col-span-6 card">
                            <div class="card-body">
                                <p class="badge badge-sub-green"><i class="align-baseline text-11 ri-circle-fill"></i> Delivered</p>
                                <h5 class="mt-3">17.9%</h5>
                                <p class="text-gray-500 dark:text-dark-500">Last week</p>
                            </div>
                        </div>
                        <div class="col-span-12 mb-0 md:col-span-6 card">
                            <div class="card-body">
                                <p class="badge badge-sub-sky"><i class="align-baseline text-11 ri-circle-fill"></i> Clicked</p>
                                <h5 class="mt-3">54.6%</h5>
                                <p class="text-gray-500 dark:text-dark-500">Last week</p>
                            </div>
                        </div>
                        <div class="col-span-12 mb-0 md:col-span-6 card">
                            <div class="card-body">
                                <p class="badge badge-sub-gray"><i class="align-baseline text-11 ri-circle-fill"></i> Opened</p>
                                <h5 class="mt-3">47.3%</h5>
                                <p class="text-gray-500 dark:text-dark-500">Last week</p>
                            </div>
                        </div>
                        <div class="col-span-12 mb-0 md:col-span-6 card">
                            <div class="card-body">
                                <p class="badge badge-sub-yellow"><i class="align-baseline text-11 ri-circle-fill"></i> Converted</p>
                                <h5 class="mt-3">11.8%</h5>
                                <p class="text-gray-500 dark:text-dark-500">Last week</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div><!--end On Boarding Campaign-->

            <div class="col-span-12 2xl:col-span-4 card">
                <div class="flex items-center gap-3 card-header">
                    <h6 class="card-title grow">Recent Transactions</h6>
                    <a href="#!" class="link link-primary shrink-0">See all <i class="align-baseline ri-arrow-right-line"></i></a>
                </div>
                <div class="card-body">
                    <div x-data="{ data: [
                        { name: 'Jeremy McMullen', status: 'Success', date: '21 Jan, 2024', price: '$154' },
                        { name: 'Charles Fischer', status: 'Cancel', date: '28 Jan, 2024', price: '$150'},
                        { name: 'Louise Harms', status: 'Success', date: '02 Feb, 2024', price: '$255'},
                        { name: 'Henry Boyle', status: 'Success', date: '11 Feb, 2024', price: '$347'},
                        { name: 'Isabella Smith', status: 'Success', date: '15 Feb, 2024', price: '$398' },
                        { name: 'Ethan Johnson', status: 'Cancel', date: '20 Feb, 2024', price: '$495' },
                        { name: 'Marina Bashirian', status: 'Success', date: '18 Mar, 2025', price: '$174' }
                    ] }">
                        <div class="overflow-x-auto">
                            <table class="table flush whitespace-nowrap">
                                <tbody>
                                    <tr>
                                        <th class="!font-medium text-gray-500 dark:text-dark-500">Order Date</th>
                                        <th class="!font-medium text-gray-500 dark:text-dark-500">Product</th>
                                        <th class="!font-medium text-gray-500 dark:text-dark-500">Price</th>
                                        <th class="!font-medium text-gray-500 dark:text-dark-500">Status</th>
                                    </tr>
                                    <template x-for="(item, index) in data" :key="index">
                                        <tr>
                                            <td x-text="item.date"></td>
                                            <td x-text="item.name"></td>
                                            <td x-text="item.price"></td>
                                            <td>
                                                <span x-text="item.status" :class="{
                                                    'badge badge-red': item.status === 'Cancel',
                                                    'badge badge-green': item.status === 'Success'
                                                }"></span>
                                            </td>
                                        </tr>
                                    </template>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div><!--end Recent Transaction-->

            <div class="col-span-12 2xl:col-span-4 card">
                <div class="flex items-center gap-3 card-header">
                    <h6 class="card-title grow">Followers</h6>
                    <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown shrink-0">
                        <button x-ref="button" x-on:click="toggle()" title="dropdown-button" :aria-expanded="open.toString()" type="button" class="flex items-center text-gray-500 dark:text-dark-500">
                            <i data-lucide="ellipsis" class="size-5"></i>
                        </button>
                        <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden" dropdown-position="right">
                            <ul>
                                <li>
                                    <a href="#!" class="dropdown-item">
                                        <span>Weekly</span>
                                    </a>
                                </li>
                                <li>
                                    <a href="#!" class="dropdown-item">
                                        <span>Monthly</span>
                                    </a>
                                </li>
                                <li>
                                    <a href="#!" class="dropdown-item">
                                        <span>Yearly</span>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div x-data="followersApp" dir="ltr">
                        <div class="!min-h-full -ml-space" data-chart-colors="[bg-primary-500, bg-primary-200]" x-ref="followersChart"></div>
                    </div>
                </div>
            </div><!--end col-->

            <div class="col-span-12 md:col-span-6 2xl:col-span-2 card">
                <div class="card-body">
                    <p class="mb-1 text-gray-500 dark:text-dark-500 text-13">Visit Browsers</p>
                    <h5><span x-data="animatedCounter(4510, 500, 0)" x-init="updateCounter" x-text="Math.round(current)"></span>+ <span class="text-xs text-green-500"><i class="align-baseline ri-arrow-up-line"></i> 1.9%</span></h5>
                    <div x-data="visitBrowsersApp" dir="ltr">
                        <div class="!min-h-full" data-chart-colors="[bg-primary-500, bg-orange-500, bg-yellow-500]" x-ref="visitBrowsersChart"></div>
                    </div>
                </div>
            </div><!--end Visit Browsers-->

            <div class="col-span-12 md:col-span-6 2xl:col-span-4 card">
                <div class="flex items-center gap-3 card-header">
                    <h6 class="card-title grow">Traffic Source</h6>
                    <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown shrink-0">
                        <button x-ref="button" x-on:click="toggle()" title="dropdown-button" :aria-expanded="open.toString()" type="button" class="flex items-center text-gray-500 dark:text-dark-500">
                            <i data-lucide="ellipsis" class="size-5"></i>
                        </button>
                        <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden" dropdown-position="right">
                            <ul>
                                <li>
                                    <a href="#!" class="dropdown-item">
                                        <span>Weekly</span>
                                    </a>
                                </li>
                                <li>
                                    <a href="#!" class="dropdown-item">
                                        <span>Monthly</span>
                                    </a>
                                </li>
                                <li>
                                    <a href="#!" class="dropdown-item">
                                        <span>Yearly</span>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div x-data="trafficSourceApp" dir="ltr">
                        <div class="!min-h-full" data-chart-colors="[bg-primary-500, bg-gray-200]" x-ref="trafficSourceChart"></div>
                    </div>
                </div>
            </div>
            <div class="col-span-12 md:col-span-6 2xl:col-span-3 card">
                <div class="card-header">
                    <h6 class="card-title">Top Users (Contributors)</h6>
                </div>
                <div class="card-body">
                    <div data-simplebar class="h-36 -mx-space px-space">
                        <div class="space-y-3 ">
                            <div class="flex items-center gap-3">
                                <img src="assets/images/avatar/user-18.png" loading="lazy" alt="" class="rounded-full size-10 shrink-0">
                                <div class="grow">
                                    <h6 class="mb-0.5">Julian Glover</h6>
                                    <p class="text-gray-500 dark:text-dark-500 text-13">78.4M Followers</p>
                                </div>
                            </div>
                            <div class="flex items-center gap-3">
                                <img src="assets/images/avatar/user-11.png" loading="lazy" alt="" class="rounded-full size-10 shrink-0">
                                <div class="grow">
                                    <h6 class="mb-0.5">Steve Powlowski</h6>
                                    <p class="text-gray-500 dark:text-dark-500 text-13">64.9M Followers</p>
                                </div>
                            </div>
                            <div class="flex items-center gap-3">
                                <img src="assets/images/avatar/user-14.png" loading="lazy" alt="" class="rounded-full size-10 shrink-0">
                                <div class="grow">
                                    <h6 class="mb-0.5">Della Brekke</h6>
                                    <p class="text-gray-500 dark:text-dark-500 text-13">63.2M Followers</p>
                                </div>
                            </div>
                            <div class="flex items-center gap-3">
                                <img src="assets/images/avatar/user-15.png" loading="lazy" alt="" class="rounded-full size-10 shrink-0">
                                <div class="grow">
                                    <h6 class="mb-0.5">Jerod Bernhard</h6>
                                    <p class="text-gray-500 dark:text-dark-500 text-13">59.3M Followers</p>
                                </div>
                            </div>
                            <div class="flex items-center gap-3">
                                <img src="assets/images/avatar/user-17.png" loading="lazy" alt="" class="rounded-full size-10 shrink-0">
                                <div class="grow">
                                    <h6 class="mb-0.5">Dominic Larkin</h6>
                                    <p class="text-gray-500 dark:text-dark-500 text-13">47.2M Followers</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-span-12 md:col-span-6 2xl:col-span-3 card">
                <div class="flex items-center gap-3 card-header">
                    <h6 class="card-title grow">Top Countries</h6>
                    <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown shrink-0">
                        <button x-ref="button" x-on:click="toggle()" title="dropdown-button" :aria-expanded="open.toString()" type="button" class="flex items-center text-gray-500 dark:text-dark-500">
                            <i data-lucide="ellipsis" class="size-5"></i>
                        </button>
                        <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden" dropdown-position="right">
                            <ul>
                                <li>
                                    <a href="#!" class="dropdown-item">
                                        <span>Weekly</span>
                                    </a>
                                </li>
                                <li>
                                    <a href="#!" class="dropdown-item">
                                        <span>Monthly</span>
                                    </a>
                                </li>
                                <li>
                                    <a href="#!" class="dropdown-item">
                                        <span>Yearly</span>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <p class="text-gray-500 dark:text-dark-500">visit</p>
                    <h5 class="mb-2"><span x-data="animatedCounter(3145, 500, 0)" x-init="updateCounter" x-text="Math.round(current)"></span>+ <span class="text-xs text-green-500"><i class="align-baseline ri-arrow-up-line"></i> 3.87%</span></h5>
                    <div class="flex flex-col gap-2">
                        <div class="flex items-center gap-2">
                            <img src="assets/images/flag/us.svg" loading="lazy" alt="" class="object-cover rounded-full size-5 shrink-0">
                            <h6 class="grow">United States</h6>
                            <p class="shrink-0">24%</p>
                        </div>
                        <div class="flex items-center gap-2">
                            <img src="assets/images/flag/de.svg" loading="lazy" alt="" class="object-cover rounded-full size-5 shrink-0">
                            <h6 class="grow">Germany</h6>
                            <p class="shrink-0">17%</p>
                        </div>
                        <div class="flex items-center gap-2">
                            <img src="assets/images/flag/it.svg" loading="lazy" alt="" class="object-cover rounded-full size-5 shrink-0">
                            <h6 class="grow">Italy</h6>
                            <p class="shrink-0">16%</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-span-12 card" x-data="campaignPerformanceTable()">
                <div class="grid items-center grid-cols-12 card-header gap-space">
                    <div class="col-span-12 xl:col-span-3">
                        <h6 class="card-title">Campaigns Performance</h6>
                    </div>
                    <div class="col-span-12 xl:col-start-9 xl:col-span-4">
                        <div class="flex items-center gap-space">
                            <div class="relative group/form grow">
                                <input type="text" class="ltr:pl-9 rtl:pr-9 form-input ltr:group-[&.right]/form:pr-9 rtl:group-[&.right]/form:pl-9 ltr:group-[&.right]/form:pl-4 rtl:group-[&.right]/form:pr-4" placeholder="Search for campaign etc..." x-model="searchTerm" @input="filteredCampaigns">
                                <button title="search-button" class="absolute inset-y-0 flex items-center text-gray-500 dark:text-dark-500 ltr:left-3 rtl:right-3 ltr:group-[&.right]/form:right-3 rtl:group-[&.right]/form:left-3 ltr:group-[&.right]/form:left-auto rtl:group-[&.right]/form:right-auto focus:outline-hidden">
                                    <i data-lucide="search" class="size-4"></i>
                                </button>
                            </div>
                            <button type="button" @click="exportTable" class="btn btn-primary shrink-0"><i data-lucide="download" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> Export</button>
                        </div>
                    </div><!--end col-->
                </div>
                <div class="card-body">
                    <div class="overflow-x-auto">
                        <table class="table whitespace-nowrap">
                            <tbody>
                                <tr>
                                    <th x-on:click="sort('campaignTitle')" class="cursor-pointer">Campaigns <span x-show="sortBy === 'campaignTitle'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                    <th x-on:click="sort('clickRate')" class="cursor-pointer">Clicks <span x-show="sortBy === 'clickRate'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                    <th x-on:click="sort('deliveredRate')" class="cursor-pointer">Delivered Rate (%) <span x-show="sortBy === 'deliveredRate'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                    <th x-on:click="sort('impressions')" class="cursor-pointer">Impressions <span x-show="sortBy === 'impressions'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                    <th x-on:click="sort('cpc')" class="cursor-pointer">CPC <span x-show="sortBy === 'cpc'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                    <th x-on:click="sort('cr')" class="cursor-pointer">CR <span x-show="sortBy === 'cr'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                    <th x-on:click="sort('revenue')" class="cursor-pointer">Revenue <span x-show="sortBy === 'revenue'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                </tr>
                                <template x-if="displayedCampaigns.length > 0">
                                <template x-for="(campaign, index) in displayedCampaigns" :key="index">
                                    <tr>
                                        <td x-text="campaign.campaignTitle"></td>
                                        <td x-text="campaign.clickRate"></td>
                                        <td x-text="campaign.deliveredRate"></td>
                                        <td x-text="campaign.impressions"></td>
                                        <td x-text="campaign.cpc"></td>
                                        <td x-text="campaign.cr"></td>
                                        <td x-text="campaign.revenue"></td>
                                    </tr>
                                </template>
                                </template>
                                <tr>
                                    <template x-if="displayedCampaigns.length == 0">
                                        <td colspan="10" class="!p-8">
                                            <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" class="mx-auto size-12" viewBox="0 0 48 48">
                                                <linearGradient id="SVGID_1__h35ynqzIJzH4_gr1" x1="34.598" x2="15.982" y1="15.982" y2="34.598" gradientUnits="userSpaceOnUse">
                                                    <stop offset="0" stop-color="#60e8fe"></stop>
                                                    <stop offset=".033" stop-color="#6ae9fe"></stop>
                                                    <stop offset=".197" stop-color="#97f0fe"></stop>
                                                    <stop offset=".362" stop-color="#bdf5ff"></stop>
                                                    <stop offset=".525" stop-color="#dafaff"></stop>
                                                    <stop offset=".687" stop-color="#eefdff"></stop>
                                                    <stop offset=".846" stop-color="#fbfeff"></stop>
                                                    <stop offset="1" stop-color="#fff"></stop>
                                                </linearGradient>
                                                <path fill="url(#SVGID_1__h35ynqzIJzH4_gr1)" d="M40.036,33.826L31.68,25.6c0.847-1.739,1.335-3.684,1.335-5.748c0-7.27-5.894-13.164-13.164-13.164	S6.688,12.582,6.688,19.852c0,7.27,5.894,13.164,13.164,13.164c2.056,0,3.995-0.485,5.728-1.326l3.914,4.015l4.331,4.331	c1.715,1.715,4.496,1.715,6.211,0C41.751,38.321,41.751,35.541,40.036,33.826z"></path>
                                                <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M31.95,25.739l8.086,8.086c1.715,1.715,1.715,4.496,0,6.211l0,0c-1.715,1.715-4.496,1.715-6.211,0	l-4.331-4.331"></path>
                                                <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M7.525,24.511c-1.771-4.694-0.767-10.196,3.011-13.975c3.847-3.847,9.48-4.817,14.228-2.912"></path>
                                                <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M30.856,12.603c3.376,5.114,2.814,12.063-1.688,16.565c-4.858,4.858-12.565,5.129-17.741,0.814"></path>
                                            </svg>
                                            <p class="mt-2 text-center text-gray-500 dark:text-dark-500">No matching records found</p>
                                        </td>
                                    </template>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <div class="grid grid-cols-12 gap-5 mt-5" x-show="displayedCampaigns.length > 0">
                        <div class="col-span-12 text-center lg:col-span-6 ltr:lg:text-left rtl:lg:text-right">
                            <p class="text-gray-500 dark:text-dark-500">Showing <b x-text="showingStart"></b> - <b x-text="showingEnd"></b> of <b x-text="filterCampaigns.length"></b> Results</p>
                        </div>
                        <div class="col-span-12 lg:col-span-6">
                            <div class="flex justify-center lg:justify-end pagination pagination-primary">
                                <button @click="prevPage()" :disabled="currentPage === 1" class="pagination-pre">
                                    <i data-lucide="chevron-left" class="mr-1 ltr:inline-block rtl:hidden size-4"></i>
                                    <i data-lucide="chevron-right" class="ml-1 rtl:inline-block ltr:hidden size-4"></i>
                                    Prev
                                </button>
                                <template x-for="page in totalPages" :key="page">
                                    <button @click="gotoPage(page)" :class="{ 'active': currentPage === page }" class="pagination-item">
                                        <span x-text="page"></span>
                                    </button>
                                </template>
                                <button @click="nextPage()" :disabled="currentPage === totalPages" class="pagination-next">
                                    Next
                                    <i data-lucide="chevron-right" class="ml-1 ltr:inline-block rtl:hidden size-4"></i>
                                    <i data-lucide="chevron-left" class="mr-1 rtl:inline-block ltr:hidden size-4"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>

    </div>
    {{> partials/footer }}
</div>


{{> partials/vendor-scripts }}

<script type="module" src="assets/js/dashboards/analytics.init.js"></script>

<script type="module" src="assets/js/main.js"></script>

</body>
</html>