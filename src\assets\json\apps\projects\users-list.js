import user11 from "/assets/images/avatar/user-11.png"
import user13 from "/assets/images/avatar/user-13.png"
import user15 from "/assets/images/avatar/user-15.png"
import user14 from "/assets/images/avatar/user-14.png"
import user16 from "/assets/images/avatar/user-16.png"
import user17 from "/assets/images/avatar/user-17.png"
import user18 from "/assets/images/avatar/user-18.png"
import user19 from "/assets/images/avatar/user-19.png"
import user20 from "/assets/images/avatar/user-20.png"
import user21 from "/assets/images/avatar/user-21.png"
import user22 from "/assets/images/avatar/user-22.png"
import user23 from "/assets/images/avatar/user-23.png"
import user24 from "/assets/images/avatar/user-24.png"
import user25 from "/assets/images/avatar/user-25.png"

const usersData = [
    { "image": user11, "name": "<PERSON>", "role": "Web Designer", "task": "15", "earning": "$8,500", "date": "2023-04-01" },
    { "image": user13, "name": "<PERSON>", "role": "ASP.Net Developer", "task": "04", "earning": "$4,987", "date": "2023-03-15" },
    { "image": user15, "name": "Diana Huber", "role": "React Developer", "task": "19", "earning": "$9,065", "date": "2023-05-20" },
    { "image": user14, "name": "Robert Foerster", "role": "Laravel Developer", "task": "08", "earning": "$11,000", "date": "2023-02-10" },
    { "image": user16, "name": "Emily Clarke", "role": "UI/UX Designer", "task": "22", "earning": "$7,250", "date": "2023-01-25" },
    { "image": user17, "name": "James Smith", "role": "Java Developer", "task": "10", "earning": "$10,500", "date": "2023-05-10" },
    { "image": user18, "name": "Olivia Brown", "role": "PHP Developer", "task": "13", "earning": "$6,800", "date": "2023-04-05" },
    { "image": user19, "name": "Liam Johnson", "role": "Python Developer", "task": "07", "earning": "$9,200", "date": "2023-06-15" },
    { "image": user20, "name": "Sophia Williams", "role": "Project Manager", "task": "20", "earning": "$12,000", "date": "2023-07-01" },
    { "image": user21, "name": "Mason Martinez", "role": "Full Stack Developer", "task": "14", "earning": "$10,750", "date": "2023-07-10" },
    { "image": user22, "name": "Isabella Davis", "role": "Data Scientist", "task": "12", "earning": "$11,300", "date": "2023-03-30" },
    { "image": user23, "name": "Lucas Garcia", "role": "DevOps Engineer", "task": "09", "earning": "$9,850", "date": "2023-05-05" },
    { "image": user24, "name": "Mia Rodriguez", "role": "Marketing Specialist", "task": "18", "earning": "$8,600", "date": "2023-04-20" },
    { "image": user25, "name": "Ethan Lee", "role": "QA Engineer", "task": "11", "earning": "$7,400", "date": "2023-03-05" }
]
export default usersData