{{> partials/main }}

<head>

    {{> partials/title-meta title="Bar Charts" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}

{{> partials/page-heading title="Bar Charts" sub-title="Apexcharts" }}

<div class="grid grid-cols-12 gap-x-space">
    <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Basic</h6>
        </div>
        <div class="card-body">
            <div x-data="basicBarApp" dir="ltr">
                <div class="!min-h-full" data-chart-colors="[bg-primary-500]" x-ref="basicBarChart"></div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Grouped</h6>
        </div>
        <div class="card-body">
            <div x-data="groupedBarApp" dir="ltr">
                <div class="!min-h-full" data-chart-colors="[bg-primary-500, bg-sky-500]" x-ref="groupedBarChart"></div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Stacked Bar</h6>
        </div>
        <div class="card-body">
            <div x-data="stackedBarApp" dir="ltr">
                <div class="!min-h-full" data-chart-colors="[bg-primary-500, bg-green-500, bg-red-500, bg-purple-500, bg-sky-500]" x-ref="stackedBarChart"></div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Stacked Bars 100</h6>
        </div>
        <div class="card-body">
            <div x-data="stackedBar100App" dir="ltr">
                <div class="!min-h-full" data-chart-colors="[bg-primary-500, bg-orange-500, bg-green-500, bg-indigo-500, bg-sky-500]" x-ref="stackedBar100Chart"></div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Grouped Stacked Bars</h6>
        </div>
        <div class="card-body">
            <div x-data="groupedStackedBarApp" dir="ltr">
                <div class="!min-h-full" data-chart-colors="[bg-primary-500, bg-green-500, bg-primary-300, bg-green-400]" x-ref="groupedStackedBarChart"></div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Bar with Negative Values</h6>
        </div>
        <div class="card-body">
            <div x-data="negativeValueBarApp" dir="ltr">
                <div class="!min-h-full" data-chart-colors="[bg-sky-500, bg-indigo-500]" x-ref="negativeValueBarChart"></div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Bar with Markers</h6>
        </div>
        <div class="card-body">
            <div x-data="markersBarApp" dir="ltr">
                <div class="!min-h-full" data-chart-colors="[bg-green-500]" x-ref="markersBarChart"></div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Reversed Bar Chart</h6>
        </div>
        <div class="card-body">
            <div x-data="reversedBarApp" dir="ltr">
                <div class="!min-h-full" data-chart-colors="[bg-primary-500]" x-ref="reversedBarChart"></div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Patterned</h6>
        </div>
        <div class="card-body">
            <div x-data="patternedBarApp" dir="ltr">
                <div class="!min-h-full" data-chart-colors="[bg-primary-500, bg-green-500, bg-red-500, bg-purple-500]" x-ref="patternedBarChart"></div>
            </div>
        </div>
    </div><!--end col-->
</div><!--end grid-->

</div>
{{> partials/footer }}
</div>

{{> partials/vendor-scripts }}

<script type="module" src="assets/js/charts/bar-chart.init.js"></script>

<script type="module" src="assets/js/main.js"></script>

</body>
</html>