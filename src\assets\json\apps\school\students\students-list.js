import user1 from "/assets/images/avatar/user-1.png"
import user5 from "/assets/images/avatar/user-5.png"
import user3 from "/assets/images/avatar/user-3.png"
import user6 from "/assets/images/avatar/user-6.png"
import user8 from "/assets/images/avatar/user-8.png"
import user9 from "/assets/images/avatar/user-9.png"
import user10 from "/assets/images/avatar/user-10.png"
import user11 from "/assets/images/avatar/user-11.png"
import user12 from "/assets/images/avatar/user-12.png"
import user13 from "/assets/images/avatar/user-13.png"
import user14 from "/assets/images/avatar/user-14.png"
import user16 from "/assets/images/avatar/user-16.png"
import user18 from "/assets/images/avatar/user-18.png"
import user19 from "/assets/images/avatar/user-19.png"
import user23 from "/assets/images/avatar/user-23.png"
import user25 from "/assets/images/avatar/user-25.png"
import user26 from "/assets/images/avatar/user-26.png"
import user27 from "/assets/images/avatar/user-27.png"
import user30 from "/assets/images/avatar/user-30.png"
import user31 from "/assets/images/avatar/user-31.png"
import user33 from "/assets/images/avatar/user-33.png"
import user34 from "/assets/images/avatar/user-34.png"
import user37 from "/assets/images/avatar/user-37.png"
import user38 from "/assets/images/avatar/user-38.png"
const studentsListData = [
    {
        "studentName": "Dorothy Daley",
        "image": user1,
        "gender": "Female",
        "rollNo": "15",
        "class": "12 (A)",
        "email": "<EMAIL>",
        "phone": "****** 15 1542",
        "birthDate": "12 Jan, 2004",
        "date": "20 June, 2024"
    },
    {
        "studentName": "Silvia Hoover",
        "image": user5,
        "gender": "Female",
        "rollNo": "48",
        "class": "11 (B)",
        "email": "<EMAIL>",
        "phone": "+54 4514 45785",
        "birthDate": "22 Dec, 2006",
        "date": "19 May, 2024"
    },
    {
        "studentName": "Robert Ferguson",
        "gender": "Male",
        "rollNo": "32",
        "class": "11 (A)",
        "email": "<EMAIL>",
        "phone": "+245 15 145 74",
        "birthDate": "15 June, 2002",
        "date": "02 June, 2024"
    },
    {
        "studentName": "Emily Brown",
        "gender": "Female",
        "rollNo": "20",
        "class": "9",
        "email": "<EMAIL>",
        "phone": "****** 15 1523",
        "birthDate": "10 Feb, 2004",
        "date": "18 June, 2024"
    },
    {
        "studentName": "Michael Johnson",
        "image": user3,
        "gender": "Male",
        "rollNo": "8",
        "class": "10 (A)",
        "email": "<EMAIL>",
        "phone": "****** 25 1525",
        "birthDate": "05 Mar, 2007",
        "date": "15 June, 2024"
    },
    {
        "studentName": "Jessica Lee",
        "gender": "Female",
        "rollNo": "16",
        "class": "12 (A)",
        "email": "<EMAIL>",
        "phone": "****** 35 1623",
        "birthDate": "25 Apr, 2004",
        "date": "12 June, 2024"
    },
    {
        "studentName": "Daniel Harris",
        "image": user6,
        "gender": "Female",
        "rollNo": "49",
        "class": "11 (B)",
        "email": "<EMAIL>",
        "phone": "****** 15 1627",
        "birthDate": "20 May, 2006",
        "date": "10 June, 2024"
    },
    {
        "studentName": "Olivia Martin",
        "gender": "Male",
        "rollNo": "57",
        "class": "10 (B)",
        "email": "<EMAIL>",
        "phone": "****** 25 1823",
        "birthDate": "14 Jun, 2007",
        "date": "08 June, 2024"
    },
    {
        "studentName": "Ethan Wilson",
        "image": user8,
        "gender": "Female",
        "rollNo": "16",
        "class": "12 (A)",
        "email": "<EMAIL>",
        "phone": "****** 35 1923",
        "birthDate": "30 Jul, 2004",
        "date": "06 June, 2024"
    },
    {
        "studentName": "Sophia Moore",
        "image": user9,
        "gender": "Female",
        "rollNo": "49",
        "class": "11 (B)",
        "email": "<EMAIL>",
        "phone": "****** 45 2025",
        "birthDate": "05 Aug, 2006",
        "date": "04 June, 2024"
    },
    {
        "studentName": "William Martinez",
        "image": user10,
        "gender": "Female",
        "rollNo": "11",
        "class": "9",
        "email": "<EMAIL>",
        "phone": "****** 55 2156",
        "birthDate": "15 Sep, 2007",
        "date": "02 June, 2024"
    },
    {
        "studentName": "Ava Davis",
        "image": user11,
        "gender": "Male",
        "rollNo": "29",
        "class": "10 (10)",
        "email": "<EMAIL>",
        "phone": "****** 65 2257",
        "birthDate": "25 Oct, 2004",
        "date": "01 June, 2024"
    },
    {
        "studentName": "James Anderson",
        "image": user12,
        "gender": "Male",
        "rollNo": "5",
        "class": "11 (A)",
        "email": "<EMAIL>",
        "phone": "****** 75 2358",
        "birthDate": "30 Nov, 2006",
        "date": "30 May, 2024"
    },
    {
        "studentName": "Mia Taylor",
        "image": user13,
        "gender": "Female",
        "rollNo": "17",
        "class": "10 (C)",
        "email": "<EMAIL>",
        "phone": "****** 85 2459",
        "birthDate": "05 Dec, 2007",
        "date": "28 May, 2024"
    },
    {
        "studentName": "Lucas Thomas",
        "image": user14,
        "gender": "Female",
        "rollNo": "42",
        "class": "12 (B)",
        "email": "<EMAIL>",
        "phone": "****** 95 2560",
        "birthDate": "10 Jan, 2004",
        "date": "26 May, 2024"
    },
    {
        "studentName": "Charlotte Garcia",
        "gender": "Female",
        "rollNo": "26",
        "class": "11 (C)",
        "email": "<EMAIL>",
        "phone": "****** 05 2661",
        "birthDate": "20 Feb, 2006",
        "date": "24 May, 2024"
    },
    {
        "studentName": "Liam White",
        "image": user16,
        "gender": "Female",
        "rollNo": "39",
        "class": "10 (B)",
        "email": "<EMAIL>",
        "phone": "****** 15 2762",
        "birthDate": "25 Mar, 2007",
        "date": "22 May, 2024"
    },
    {
        "studentName": "Amelia Clark",
        "gender": "Female",
        "rollNo": "13",
        "class": "12 (A)",
        "email": "<EMAIL>",
        "phone": "****** 25 2863",
        "birthDate": "30 Apr, 2004",
        "date": "20 May, 2024"
    },
    {
        "studentName": "Noah Lewis",
        "image": user18,
        "gender": "Male",
        "rollNo": "22",
        "class": "11 (B)",
        "email": "<EMAIL>",
        "phone": "****** 35 2964",
        "birthDate": "05 May, 2006",
        "date": "18 May, 2024"
    },
    {
        "studentName": "Isabella Walker",
        "image": user19,
        "gender": "Female",
        "rollNo": "45",
        "class": "10 (C)",
        "email": "<EMAIL>",
        "phone": "****** 45 3065",
        "birthDate": "10 Jun, 2007",
        "date": "16 May, 2024"
    },
    {
        "studentName": "Mason Young",
        "gender": "Male",
        "rollNo": "33",
        "class": "12 (B)",
        "email": "<EMAIL>",
        "phone": "****** 55 3166",
        "birthDate": "15 Jul, 2004",
        "date": "14 May, 2024"
    },
    {
        "studentName": "Harper King",
        "gender": "Male",
        "rollNo": "14",
        "class": "11 (C)",
        "email": "<EMAIL>",
        "phone": "****** 65 3267",
        "birthDate": "20 Aug, 2006",
        "date": "12 May, 2024"
    },
    {
        "studentName": "Evelyn Scott",
        "gender": "Male",
        "rollNo": "37",
        "class": "10 (B)",
        "email": "<EMAIL>",
        "phone": "****** 75 3368",
        "birthDate": "25 Sep, 2007",
        "date": "10 May, 2024"
    },
    {
        "studentName": "Aiden Green",
        "image": user23,
        "gender": "Female",
        "rollNo": "20",
        "class": "12 (A)",
        "email": "<EMAIL>",
        "phone": "****** 85 3469",
        "birthDate": "30 Oct, 2004",
        "date": "08 May, 2024"
    },
    {
        "studentName": "Ella Adams",
        "gender": "Female",
        "rollNo": "25",
        "class": "11 (B)",
        "email": "<EMAIL>",
        "phone": "****** 95 3570",
        "birthDate": "05 Nov, 2006",
        "date": "06 May, 2024"
    },
    {
        "studentName": "Jacob Baker",
        "image": user25,
        "gender": "Male",
        "rollNo": "38",
        "class": "10 (C)",
        "email": "<EMAIL>",
        "phone": "****** 05 3671",
        "birthDate": "10 Dec, 2007",
        "date": "04 May, 2024"
    },
    {
        "studentName": "Abigail Nelson",
        "image": user26,
        "gender": "Female",
        "rollNo": "12",
        "class": "12 (B)",
        "email": "<EMAIL>",
        "phone": "****** 15 3772",
        "birthDate": "15 Jan, 2004",
        "date": "02 May, 2024"
    },
    {
        "studentName": "Samuel Hill",
        "image": user27,
        "gender": "Female",
        "rollNo": "10",
        "class": "11 (C)",
        "email": "<EMAIL>",
        "phone": "****** 25 3873",
        "birthDate": "20 Feb, 2006",
        "date": "30 Apr, 2024"
    },
    {
        "studentName": "Sofia Wright",
        "gender": "Male",
        "rollNo": "31",
        "class": "10 (B)",
        "email": "<EMAIL>",
        "phone": "****** 35 3974",
        "birthDate": "25 Mar, 2007",
        "date": "28 Apr, 2024"
    },
    {
        "studentName": "Henry Lopez",
        "gender": "Male",
        "rollNo": "9",
        "class": "12 (A)",
        "email": "<EMAIL>",
        "phone": "****** 45 4075",
        "birthDate": "30 Apr, 2004",
        "date": "26 Apr, 2024"
    },
    {
        "studentName": "Zoe Harris",
        "image": user30,
        "gender": "Female",
        "rollNo": "18",
        "class": "11 (B)",
        "email": "<EMAIL>",
        "phone": "****** 55 4176",
        "birthDate": "05 May, 2006",
        "date": "24 Apr, 2024"
    },
    {
        "studentName": "Lily Parker",
        "image": user31,
        "gender": "Male",
        "rollNo": "50",
        "class": "11 (A)",
        "email": "<EMAIL>",
        "phone": "****** 67 8901",
        "birthDate": "12 Jan, 2006",
        "date": "10 June, 2024"
    },
    {
        "studentName": "Jack Mitchell",
        "gender": "Female",
        "rollNo": "41",
        "class": "12 (B)",
        "email": "<EMAIL>",
        "phone": "****** 56 7890",
        "birthDate": "23 Feb, 2004",
        "date": "08 June, 2024"
    },
    {
        "studentName": "Sophie Bell",
        "image": user33,
        "gender": "Female",
        "rollNo": "53",
        "class": "10 (C)",
        "email": "<EMAIL>",
        "phone": "****** 45 6789",
        "birthDate": "14 Mar, 2007",
        "date": "06 June, 2024"
    },
    {
        "studentName": "Ryan Turner",
        "image": user34,
        "gender": "Male",
        "rollNo": "35",
        "class": "11 (B)",
        "email": "<EMAIL>",
        "phone": "****** 89 0123",
        "birthDate": "25 Apr, 2006",
        "date": "04 June, 2024"
    },
    {
        "studentName": "Zara Bennett",
        "gender": "Female",
        "rollNo": "28",
        "class": "12 (A)",
        "email": "<EMAIL>",
        "phone": "****** 90 1234",
        "birthDate": "06 May, 2004",
        "date": "02 June, 2024"
    },
    {
        "studentName": "Oliver Martinez",
        "gender": "Male",
        "rollNo": "49",
        "class": "10 (B)",
        "email": "<EMAIL>",
        "phone": "****** 01 2345",
        "birthDate": "17 Jun, 2007",
        "date": "30 May, 2024"
    },
    {
        "studentName": "Chloe Rivera",
        "image": user37,
        "gender": "Female",
        "rollNo": "46",
        "class": "11 (A)",
        "email": "<EMAIL>",
        "phone": "****** 12 3456",
        "birthDate": "28 Jul, 2006",
        "date": "28 May, 2024"
    },
    {
        "studentName": "Lucas Campbell",
        "image": user38,
        "gender": "Female",
        "rollNo": "44",
        "class": "12 (C)",
        "email": "<EMAIL>",
        "phone": "****** 23 4567",
        "birthDate": "09 Aug, 2004",
        "date": "26 May, 2024"
    },
    {
        "studentName": "Emma Peterson",
        "gender": "Male",
        "rollNo": "19",
        "class": "9",
        "email": "<EMAIL>",
        "phone": "****** 34 5678",
        "birthDate": "20 Sep, 2007",
        "date": "24 May, 2024"
    },
    {
        "studentName": "Benjamin Foster",
        "gender": "Male",
        "rollNo": "5",
        "class": "8",
        "email": "<EMAIL>",
        "phone": "****** 56 7890",
        "birthDate": "31 Oct, 2006",
        "date": "22 May, 2024"
    }
]
export default studentsListData