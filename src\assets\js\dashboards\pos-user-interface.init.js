import "virtual-select-plugin/dist/virtual-select";
import Swiper from 'swiper/bundle';
import "@alpinejs/mask/dist/cdn";
import { posData, posStore } from './pos-data';
import { createIcons, icons } from 'lucide';

// Table Location Select
VirtualSelect.init({
    ele: "#tableLocation",
    options: posData.tables
});

// Initialize Alpine.js data
document.addEventListener('alpine:init', () => {
    Alpine.data('posSystem', () => ({
        ...posStore,
        swiper: null,
        categories: posData.categories,
        cart: [],
        orderType: 'dine-in',
        customerInfo: {
            name: '',
            contact: '',
            table: '',
            address: '',
            deliveryTime: '',
            notes: ''
        },
        paymentMethod: 'cash',

        // Format currency to 2 decimal places
        formatCurrency(amount) {
            return Number(amount).toFixed(2);
        },
        
        // Initialize with first category
        init() {
            this.setCategory(posData.categories[0].id);
            
            // Initialize Swiper after Alpine has rendered the DOM
            this.$nextTick(() => {
                this.initSwiper();
                // Initialize Lucide icons
                createIcons({ icons });
            });
        },

        // Initialize Swiper
        initSwiper() {
            this.swiper = new Swiper(".mySwiper", {
                slidesPerView: 1,
                spaceBetween: 10,
                pagination: {
                    el: ".swiper-pagination",
                    clickable: true,
                },
                navigation: {
                    nextEl: '.swiper-button-next',
                    prevEl: '.swiper-button-prev',
                },
                breakpoints: {
                    640: {
                        slidesPerView: 2,
                        spaceBetween: 20,
                    },
                    768: {
                        slidesPerView: 4,
                        spaceBetween: 40,
                    },
                    1024: {
                        slidesPerView: 6,
                        spaceBetween: 24,
                    },
                },
                on: {
                    slideChange: () => {
                        console.log('Slide changed');
                    }
                }
            });
        },

        // Get filtered products for current category
        get filteredProducts() {
            return posData.products.filter(
                product => product.category === this.currentCategory
            );
        },

        // Handle category selection
        selectCategory(categoryId) {
            this.setCategory(categoryId);
            this.$nextTick(() => {
                createIcons({ icons });
            });
        },

        // Handle adding item to cart
        addItemToCart(product, quantity, options = {}) {
            if (quantity < 1) return;
            
            const cartItem = {
                id: Date.now(),
                productId: product.id,
                name: product.name,
                price: product.price,
                quantity: quantity,
                size: options.size,
                sugarLevel: options.sugarLevel,
                total: Number((product.price * quantity).toFixed(2))
            };
            this.cart.push(cartItem);
            // Reinitialize Lucide icons after adding to cart
            this.$nextTick(() => {
                createIcons({ icons });
            });
        },

        // Remove item from cart
        removeFromCart(cartItemId) {
            this.cart = this.cart.filter(item => item.id !== cartItemId);
            // Reinitialize Lucide icons after removing from cart
            this.$nextTick(() => {
                createIcons({ icons });
            });
        },

        // Clear all items from cart
        clearCart() {
            this.cart = [];
            // Reinitialize Lucide icons after clearing cart
            this.$nextTick(() => {
                createIcons({ icons });
            });
        },

        // Handle order type change
        changeOrderType(type) {
            this.orderType = type;
            // Clear customer info when changing order type
            this.customerInfo = {
                name: '',
                contact: '',
                table: '',
                address: '',
                deliveryTime: '',
                notes: ''
            };
            // Reinitialize Lucide icons after changing order type
            this.$nextTick(() => {
                createIcons({ icons });
            });
        },

        // Handle payment method change
        changePaymentMethod(method) {
            this.paymentMethod = method;
            // Reinitialize Lucide icons after changing payment method
            this.$nextTick(() => {
                createIcons({ icons });
            });
        },

        // Handle customer info update
        updateCustomerInfo(info) {
            this.customerInfo = { ...this.customerInfo, ...info };
        },

        // Place order
        placeOrder() {
            const orderData = {
                orderType: this.orderType,
                customerInfo: this.customerInfo,
                items: this.cart,
                paymentMethod: this.paymentMethod,
                totals: {
                    subtotal: this.cartTotal,
                    tax: this.tax,
                    deliveryFee: this.orderType === 'delivery' ? 3.50 : 0,
                    grandTotal: this.grandTotal
                }
            };
            
            console.log('Order placed:', orderData);
            this.clearCart();
        },

        // Get cart items for display
        get cartItems() {
            return this.cart;
        },

        // Get cart item count
        get cartItemCount() {
            return this.cart.reduce((total, item) => total + item.quantity, 0);
        },

        // Get cart total
        get cartTotal() {
            return Number(this.cart.reduce((total, item) => total + item.total, 0).toFixed(2));
        },

        // Get tax amount (5.6%)
        get tax() {
            return Number((this.cartTotal * 0.056).toFixed(2));
        },

        // Get grand total
        get grandTotal() {
            const deliveryFee = this.orderType === 'delivery' ? 3.50 : 0;
            return Number((this.cartTotal + this.tax + deliveryFee).toFixed(2));
        }
    }));
});