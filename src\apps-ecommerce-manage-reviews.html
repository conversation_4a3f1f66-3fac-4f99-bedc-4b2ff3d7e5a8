{{> partials/main }}

<head>

    {{> partials/title-meta title="Manage Reviews" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

<div class="relative min-h-screen">
    <div class="page-wrapper pt-[calc(theme('spacing.topbar')_*_1.2)] group-data-[layout=horizontal]:pt-[calc(theme('spacing.topbar')_*_1.9)]" x-data="reviewTable()">
        {{> partials/page-heading title="Manage Reviews" sub-title="Ecommerce" }}

        <div class="grid items-center grid-cols-12 gap-5 mb-5">
            <div class="col-span-12 2xl:col-span-8">
                <h6 class="card-title">Reviews</h6>
            </div>
            <div class="col-span-12 2xl:col-span-4">
                <div class="flex flex-wrap items-center gap-3 2xl:justify-end">
                    <div class="relative group/form">
                        <input type="email" class="ltr:pl-9 rtl:pr-9 form-input ltr:group-[&.right]/form:pr-9 rtl:group-[&.right]/form:pl-9 ltr:group-[&.right]/form:pl-4 rtl:group-[&.right]/form:pr-4" placeholder="Search username, date, etc..." x-model="searchTerm" @input="filterReview">
                        <span class="absolute inset-y-0 flex items-center text-gray-500 dark:text-dark-500 ltr:left-3 rtl:right-3 ltr:group-[&.right]/form:right-3 rtl:group-[&.right]/form:left-3 ltr:group-[&.right]/form:left-auto rtl:group-[&.right]/form:right-auto focus:outline-hidden">
                            <i data-lucide="search" class="size-4"></i>
                        </span>
                    </div>
                    <button class="btn btn-primary" data-modal-target="addReviewModal" @click="handleModal('showAddReviewForm')"><i data-lucide="plus" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> New Review</button>
                </div>
            </div>
        </div>

        <div>
            <div class="grid items-center grid-cols-1 mb-4 divide-y divide-gray-200 dark:divide-dark-800 xl:grid-cols-3 xl:divide-y-0 xl:divide-x rtl:xl:divide-x-reverse">
                <div class="p-5 xl:ltr:first:pl-0 xl:rtl:first:pr-0 ltr:last:pr-0 rtl:last:pl-0 last:border-0">
                    <h6 class="mb-5">Total Reviews</h6>
                    <h3><span x-text="totalReviews"></span> <span class="inline-block text-xs align-middle badge badge-green"><span x-text="reviewPercentage"></span>%</span></h3>
                    <p class="mt-2 text-gray-500 dark:text-dark-500">Growth in reviews on this year</p>
                </div>
                <div class="p-5 ltr:first:pl-0 rtl:first:pr-0 ltr:last:pr-0 rtl:last:pl-0 last:border-0">
                    <h6 class="mb-5">Average Rating</h6>
                    <div class="flex items-center gap-3">
                        <h3><span x-text="averageReview"></span></h3>
                        <div class="text-yellow-500">
                            <template x-for="i in 5" :key="i">
                                <i x-bind:class="getStarClass(averageReview, i)"></i>
                            </template>
                        </div>
                    </div>
                    <p class="mt-2 text-gray-500">Average rating on this year</p>
                </div>
                <div class="p-5 flex flex-col gap-1 ltr:first:pl-0 rtl:first:pr-0 ltr:last:pr-0 rtl:last:pl-0 last:border-0">
                    <div class="flex items-center gap-2">
                        <p class="shrink-0">
                            <i class="text-yellow-500 ri-star-fill"></i> 5
                        </p>
                        <div class="!w-[80%] progress-bar progress-1">
                            <div class="w-full text-white bg-green-500 progress-bar-wrap"></div>
                        </div>
                        <h6>4751</h6>
                    </div>
                    <div class="flex items-center gap-2">
                        <p class="shrink-0">
                            <i class="text-yellow-500 ri-star-fill"></i> 4
                        </p>
                        <div class="!w-[64%] progress-bar progress-1">
                            <div class="w-full text-white bg-pink-500 progress-bar-wrap"></div>
                        </div>
                        <h6>3658</h6>
                    </div>
                    <div class="flex items-center gap-2">
                        <p class="shrink-0">
                            <i class="text-yellow-500 ri-star-fill"></i> 3
                        </p>
                        <div class="!w-[51%] progress-bar progress-1">
                            <div class="w-full text-white bg-yellow-500 progress-bar-wrap"></div>
                        </div>
                        <h6>2349</h6>
                    </div>
                    <div class="flex items-center gap-2">
                        <p class="shrink-0">
                            <i class="text-yellow-500 ri-star-fill"></i> 2
                        </p>
                        <div class="!w-[38%] progress-bar progress-1">
                            <div class="w-full text-white bg-sky-500 progress-bar-wrap"></div>
                        </div>
                        <h6>1472</h6>
                    </div>
                    <div class="flex items-center gap-2">
                        <p class="shrink-0">
                            <i class="text-yellow-500 ri-star-fill"></i> 1
                        </p>
                        <div class="!w-[15%] progress-bar progress-1">
                            <div class="w-full text-white bg-red-500 progress-bar-wrap"></div>
                        </div>
                        <h6>120</h6>
                    </div>
                </div>
            </div><!--end grid-->

            <div class="!overflow-x-auto">
                <table class="table flush whitespace-nowrap !overflow-x-auto">
                    <tbody>
                        <template x-if="displayedReviews.length > 0">
                            <template x-for="(review, index) in displayedReviews" :key="index">
                                <tr class="gap-2">
                                    <td class="align-top whitespace-nowrap">
                                        <div class="flex items-center gap-3">
                                            <img :src="review.image" alt="" class="rounded-md shrink-0 size-16">
                                            <div class="overflow-hidden grow">
                                                <h6 class="mb-1 truncate">
                                                    <a href="#!" class="text-current link link-primary" x-text="review.userName"></a>
                                                </h6>
                                                <p class="mb-1 text-sm truncate" x-text="review.date"></p>
                                                <p class="text-sm text-gray-500 truncate dark:text-dark-500">Location: <span x-text="review.location"></span></p>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="whitespace-nowrap">
                                        <div class="max-w-[550px]">
                                            <div class="flex items-center gap-2 mb-3">
                                                <div class="text-yellow-500">
                                                    <template x-for="i in 5" :key="i">
                                                        <i :class="getStarClass(review.star, i)"></i>
                                                    </template>
                                                </div>
                                                <h6>(<span x-text="review.star"></span>)</h6>
                                            </div>
                                            <h6 x-text="review.title" class="mb-1"></h6>
                                            <p class="text-gray-500 whitespace-normal dark:text-dark-500" x-text="review.content"></p>
                                        </div>
                                    </td>
                                    <td class="align-top">
                                        <div class="flex items-center justify-end gap-3">
                                            <button class="btn btn-sub-gray">Direct Message</button>
                                            <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown">
                                                <button x-ref="button" x-on:click="toggle()" :aria-expanded="open.toString()" type="button" class="btn btn-icon-text btn-primary btn-icon">
                                                    <i class="ri-more-2-fill"></i>
                                                </button>
                                                <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden">
                                                    <ul>
                                                        <li>
                                                            <a href="#!" data-modal-target="addReviewModal" @click="editReview(review.userName); close()" class="dropdown-item">
                                                                <i class="align-middle ltr:mr-2 rtl:ml-2 ri-pencil-line"></i> <span>Edit</span>
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a href="#!" data-modal-target="deleteModal" @click=" deleteItem = review.userName" class="dropdown-item">
                                                                <i class="align-middle ltr:mr-2 rtl:ml-2 ri-delete-bin-line"></i> <span>Delete</span>
                                                            </a>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            </template>
                        </template>
                        <template x-if="displayedReviews.length == 0">
                            <td colspan="10" class="!p-8">
                                <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" class="mx-auto size-12" viewBox="0 0 48 48">
                                    <linearGradient id="SVGID_1__h35ynqzIJzH4_gr1" x1="34.598" x2="15.982" y1="15.982" y2="34.598" gradientUnits="userSpaceOnUse">
                                        <stop offset="0" stop-color="#60e8fe"></stop>
                                        <stop offset=".033" stop-color="#6ae9fe"></stop>
                                        <stop offset=".197" stop-color="#97f0fe"></stop>
                                        <stop offset=".362" stop-color="#bdf5ff"></stop>
                                        <stop offset=".525" stop-color="#dafaff"></stop>
                                        <stop offset=".687" stop-color="#eefdff"></stop>
                                        <stop offset=".846" stop-color="#fbfeff"></stop>
                                        <stop offset="1" stop-color="#fff"></stop>
                                    </linearGradient>
                                    <path fill="url(#SVGID_1__h35ynqzIJzH4_gr1)" d="M40.036,33.826L31.68,25.6c0.847-1.739,1.335-3.684,1.335-5.748c0-7.27-5.894-13.164-13.164-13.164	S6.688,12.582,6.688,19.852c0,7.27,5.894,13.164,13.164,13.164c2.056,0,3.995-0.485,5.728-1.326l3.914,4.015l4.331,4.331	c1.715,1.715,4.496,1.715,6.211,0C41.751,38.321,41.751,35.541,40.036,33.826z"></path>
                                    <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M31.95,25.739l8.086,8.086c1.715,1.715,1.715,4.496,0,6.211l0,0c-1.715,1.715-4.496,1.715-6.211,0	l-4.331-4.331"></path>
                                    <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M7.525,24.511c-1.771-4.694-0.767-10.196,3.011-13.975c3.847-3.847,9.48-4.817,14.228-2.912"></path>
                                    <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M30.856,12.603c3.376,5.114,2.814,12.063-1.688,16.565c-4.858,4.858-12.565,5.129-17.741,0.814"></path>
                                </svg>
                                <p class="mt-2 text-center text-gray-500 dark:text-dark-500">No matching records found</p>
                            </td>
                        </template>
                    </tbody>
                </table>
            </div>
            <div class="grid grid-cols-12 gap-5 my-5 items-center" x-show="displayedReviews.length !== 0">
                <div class="col-span-12 md:col-span-6">
                    <p class="text-gray-500 dark:text-dark-500">Showing <b x-text="showingStart"></b> - <b x-text="showingEnd"></b> of <b x-text="filterReviews.length"></b> Results</p>
                </div>
                <div class="col-span-12 md:col-span-6">
                    <div class="flex justify-start md:justify-end pagination pagination-primary" >
                        <button @click="prevPage()" :disabled="currentPage === 1" class="pagination-pre">
                            <i data-lucide="chevron-left" class="mr-1 ltr:inline-block rtl:hidden size-4"></i>  
                            <i data-lucide="chevron-right" class="ml-1 ltr:hidden rtl:inline-block size-4"></i>
                            Prev
                        </button>
                        <template x-for="page in totalPages" :key="page">
                            <button @click="gotoPage(page)" :class="{ 'active': currentPage === page }" class="pagination-item">
                                <span x-text="page"></span>
                            </button>
                        </template>
                        <button @click="nextPage()" :disabled="currentPage === totalPages" class="pagination-next">
                            Next
                            <i data-lucide="chevron-right" class="ml-1 ltr:inline-block rtl:hidden size-4"></i>
                            <i data-lucide="chevron-left" class="mr-1 ltr:hidden rtl:inline-block size-4"></i>
                        </button>
                    </div>
                </div>
            </div>
           

            <!--create review modals-->
            <div id="addReviewModal" class="!hidden modal show" :class="{'show d-block': showAddReviewForm || showEditReviewForm }" x-show="showAddReviewForm || showEditReviewForm">
                <div class="modal-wrap modal-center">
                    <div class="modal-header">
                        <h6 x-text="showAddReviewForm ? 'Add review' : 'Edit review'"></h6>
                        <button data-modal-close="addReviewModal" class="link link-red float-end"><i data-lucide="x" class="size-5"></i></button>
                    </div>
                    <div class="modal-content">
                        <form action="#!">
                            <div class="grid grid-cols-12 gap-5">
                                <div class="col-span-12">
                                    <div class="flex flex-col justify-center gap-5" >
                                        <h6 class="text-center">Your Rating?</h6>
                                        <div class="relative flex justify-center gap-3">
                                            <div class="flex justify-center w-10 h-2 transition-all duration-200 rounded-md cursor-pointer" x-bind:class="rating >= 1 ? 'bg-yellow-500' : 'bg-gray-200 dark:bg-dark-800'" x-on:click="rating = 1" x-on:mouseover="hovering = 1" x-on:mouseleave="hovering = 0">
                                                <p class="mt-4 text-2xl pointer-events-none select-none" x-bind:class="rating == 1 || hovering == 1 ? '' : 'invisible' ">😒</p>
                                            </div>
                                            <div class="flex justify-center w-10 h-2 transition-all duration-200 rounded-md cursor-pointer" x-bind:class="rating >= 2 ? 'bg-yellow-500' : 'bg-gray-200 dark:bg-dark-800'" x-on:click="rating = 2" x-on:mouseover="hovering = 2" x-on:mouseleave="hovering = 0">
                                                <p class="mt-4 text-2xl pointer-events-none select-none" x-bind:class="rating == 2 || hovering == 2 ? '' : 'invisible' ">🤨</p>
                                            </div>
                                            <div class="flex justify-center w-10 h-2 transition-all duration-200 rounded-md cursor-pointer" x-bind:class="rating >= 3 ? 'bg-yellow-500' : 'bg-gray-200 dark:bg-dark-800'" x-on:click="rating = 3" x-on:mouseover="hovering = 3" x-on:mouseleave="hovering=0">
                                                <p class="mt-4 text-2xl pointer-events-none select-none" x-bind:class="rating == 3 || hovering == 3 ? '' : 'invisible' ">😊</p>
                                            </div>
                                            <div class="flex justify-center w-10 h-2 transition-all duration-200 rounded-md cursor-pointer" x-bind:class="rating >= 4 ? 'bg-yellow-500' : 'bg-gray-200 dark:bg-dark-800'" x-on:click="rating = 4" x-on:mouseover="hovering = 4" x-on:mouseleave="hovering = 0">
                                                <p class="mt-4 text-2xl pointer-events-none select-none" x-bind:class="rating == 4 || hovering == 4 ? '' : 'invisible' ">😚</p>
                                            </div>
                                            <div class="flex justify-center w-10 h-2 transition-all duration-200 rounded-md cursor-pointer" x-bind:class="rating >= 5 ? 'bg-yellow-500' : 'bg-gray-200 dark:bg-dark-800'" x-on:click="rating = 5" x-on:mouseover="hovering = 5" x-on:mouseleave="hovering = 0">
                                                <p class="mt-4 text-2xl pointer-events-none select-none" x-bind:class="rating == 5 || hovering == 5 ? '' : 'invisible' ">🥰</p>
                                            </div>
                                        </div>
                                        <div class="mt-5">
                                            <label for="rating" class="form-label">Rating Input:</label>
                                            <input id="rating" x-model="reviewForm.star = rating" type="number" name="rating" class="form-input" @input="validateField('star',reviewForm.star >= 1 || reviewForm.star <= 5, 'Star is required.')" />
                                            <span x-show="errors.star" class="text-sm text-red-500" x-text="errors.star"></span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-span-6">
                                    <label for="userNameInput" class="form-label">User Name</label>
                                    <input type="text" id="userNameInput" class="form-input" placeholder="User name" x-model="reviewForm.userName" @input="validateField('userName', reviewForm.userName, 'User name is required.')">
                                    <span x-show="errors.userName" class="text-sm text-red-500" x-text="errors.userName"></span>
                                </div>
                                <div class="col-span-6">
                                    <label for="createDateInput" class="form-label">Create Date</label>
                                    <input type="text" placeholder="DD-MM-YYYY" id="createDateInput" x-model="reviewForm.date" class="form-input" data-provider="flatpickr" data-date-format="d M, Y"  placeholder="Order ID" @input="validateField('date' , reviewForm.date, 'Date is required.')">
                                    <span x-show="errors.date" class="text-sm text-red-500" x-text="errors.date"></span>
                                </div>
                                <div class="col-span-12">
                                    <label for="locationInput" class="form-label">Location</label>
                                    <input type="text" id="locationInput" class="form-input" placeholder="Location" x-model="reviewForm.location" @input="validateField('location', reviewForm.location, 'Location is required.')">
                                    <span x-show="errors.location" class="text-sm text-red-500" x-text="errors.location"></span>
                                </div>
                                <div class="col-span-12">
                                    <label for="titleInput" class="form-label">Title</label>
                                    <input type="text" id="titleInput" class="form-input" placeholder="Review title" x-model="reviewForm.title" @input="validateField('title', reviewForm.title, 'Title is required.')">
                                    <span x-show="errors.title" class="text-sm text-red-500" x-text="errors.title"></span>
                                </div>
                                <div class="col-span-12">
                                    <label for="writeReviewInput" class="form-label">Write your Content</label>
                                    <textarea name="writeReviewInput" id="writeReviewInput" rows="3" class="h-auto form-input" x-model="reviewForm.content" placeholder="Enter your description" @change="validateField('content', reviewForm.content, 'Content is required.')"></textarea>
                                    <span x-show="errors.content" class="text-sm text-red-500" x-text="errors.content"></span>
                                </div>
                            </div>
                        </form>
                        <div class="flex justify-end gap-2 mt-5">
                            <button type="button" class="btn btn-active-red" data-modal-close="addReviewModal">Cancel</button>
                            <button type="submit" class="btn btn-primary" x-text="showAddReviewForm ? 'Add review' : 'Update review'" @click="submitForm()">Add Review</button>
                        </div>
                    </div>
                </div>
            </div>

            <!--delete modals-->
            <div id="deleteModal" class="!hidden modal show">
                <div class="modal-wrap modal-xs modal-center">
                    <div class="text-center modal-content p-7">
                        <div class="flex items-center justify-center mx-auto mb-4 text-red-500 rounded-full bg-red-500/10 size-14 backdrop-blur-xl">
                            <i data-lucide="trash-2" class="size-6"></i>
                        </div>
                        <h5 class="mb-4">Are you sure you want to delete this Review ?</h5>
                        <div class="flex items-center justify-center gap-2">
                            <button class="btn btn-red" @click="deleteProduct(deleteItem)" data-modal-close="deleteModal">Delete</button>
                            <button data-modal-close="deleteModal" class="btn link link-primary">Cancel</button>
                        </div>
                    </div>
                </div>
            </div><!--end-->
        </div>
    </div>
    {{> partials/footer }}
</div>

{{> partials/vendor-scripts }}

<script type="module" src="assets/js/main.js"></script>

<script type="module" src="assets/js/apps/ecommerce/manage-reviews.init.js"></script>

</body>
</html>