{{> partials/main }}

<head>

    {{> partials/title-meta title="Notification" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}

{{> partials/page-heading title="Notification" sub-title="UI" }}

<div class="grid grid-cols-12 gap-x-space">
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Basic Notification</h6>
        </div>
        <div class="card-body">
            <div class="flex flex-wrap items-center gap-2">
                <button x-data @click="$dispatch('notice', {type: 'success', text: '🔥 Your success message - make it short'})" class="btn btn-green">
                    Success
                </button>
                <button x-data @click="$dispatch('notice', {type: 'info', text: 'Your Info message - make it short'})" class="btn btn-sky">
                    Info
                </button>
                <button x-data @click="$dispatch('notice', {type: 'warning', text: '🪄 Your Warning message - make it short'})" class="btn btn-yellow">
                    Warning
                </button>
                <button x-data x-on:click="$dispatch('notice', {type: 'error', text: '😵 Your critical message - make it short!'})" class="btn btn-red">
                    Error
                </button>
            </div>

            <div x-data="noticesHandler()" class="fixed flex flex-col-reverse items-end justify-start w-screen h-screen gap-3 bottom-10 ltr:right-10 rtl:left-10 z-[1050]" @notice.window="add($event.detail)" style="pointer-events:none">
                <template x-for="notice of notices" :key="notice.id">
                    <div x-show="visible.includes(notice)" x-transition:enter="transition ease-in duration-200" x-transition:enter-start="transform opacity-0 translate-y-2" x-transition:enter-end="transform opacity-100" x-transition:leave="transition ease-out duration-200" x-transition:leave-start="transform ltr:translate-x-0 rtl:-translate-x-0 opacity-100" x-transition:leave-end="transform ltr:translate-x-full rtl:-translate-x-full opacity-0" @click="remove(notice.id)" class="cursor-pointer alert w-82" :class="{
                                    'alert-solid-green': notice.type === 'success',
                                    'alert-solid-sky': notice.type === 'info',
                                    'alert-solid-yellow': notice.type === 'warning',
                                    'alert-solid-red': notice.type === 'error',
                                 }" style="pointer-events:all" x-text="notice.text">
                    </div>
                </template>
            </div>
        </div>
    </div><!--end col-->

    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Position Notification</h6>
        </div>
        <div class="flex flex-wrap items-center gap-2 card-body">
            <div x-data="{ isOpen: false }">
                <button @click="isOpen = true; setTimeout(() => isOpen = false, 1500)" class="btn btn-primary">
                    Top Left
                </button>

                <div x-show="isOpen" x-init="setTimeout(() => isOpen = false, 1500)" class="fixed py-3 text-sm text-white transition-all duration-300 ease-in-out transform rounded-md ltr:pl-5 rtl:pr-5 bg-gradient-to-r from-primary-500 to-primary-600 ltr:pr-9 rtl:pl-9 top-5 z-[1055] ltr:left-5 rtl:right-5 max-w-96" x-transition:enter="transition ease-out duration-300 transform" x-transition:enter-start="opacity-0 translate-y-2" x-transition:enter-end="opacity-100 translate-y-0" x-transition:leave="transition ease-in duration-300 transform" x-transition:leave-start="opacity-100 translate-y-0" x-transition:leave-end="opacity-0 translate-y-2">
                    <span>You have successfully completed this thing!</span>
                    <a href="javascript:void(0);" @click="isOpen = false" class="absolute text-lg transition duration-200 ease-linear text-primary-300 hover:text-primary-50 ltr:right-4 rtl:left-4 top-2"><i class="ri-close-fill"></i></a>
                </div>
            </div>
            <div x-data="{ isOpen: false }">
                <button @click="isOpen = true; setTimeout(() => isOpen = false, 1500)" class="btn btn-primary">
                    Top Center
                </button>

                <div x-show="isOpen" x-init="setTimeout(() => isOpen = false, 1500)" class="fixed py-3 text-sm text-white transition-all duration-300 ease-in-out transform -translate-x-1/2 rounded-md ltr:pl-5 rtl:pr-5 bg-gradient-to-r from-primary-500 to-primary-600 ltr:pr-9 rtl:pl-9 top-5 z-[1055] left-1/2 max-w-96" x-transition:enter="transition ease-out duration-300 transform" x-transition:enter-start="opacity-0 translate-y-2" x-transition:enter-end="opacity-100 translate-y-0" x-transition:leave="transition ease-in duration-300 transform" x-transition:leave-start="opacity-100 translate-y-0" x-transition:leave-end="opacity-0 translate-y-2">
                    <span>You have successfully completed this thing!</span>
                    <a href="javascript:void(0);" @click="isOpen = false" class="absolute text-lg transition duration-200 ease-linear text-primary-300 hover:text-primary-50 ltr:right-4 rtl:left-4 top-2"><i class="ri-close-fill"></i></a>
                </div>
            </div>

            <div x-data="{ isOpen: false }">
                <button @click="isOpen = true; setTimeout(() => isOpen = false, 1500)" class="btn btn-primary">
                    Top Right
                </button>

                <div x-show="isOpen" x-init="setTimeout(() => isOpen = false, 1500)" class="fixed py-3 text-sm text-white transition-all duration-300 ease-in-out transform rounded-md ltr:pl-5 rtl:pr-5 bg-gradient-to-r from-primary-500 to-primary-600 ltr:pr-9 rtl:pl-9 top-5 z-[1055] ltr:right-5 rtl:left-5 max-w-96" x-transition:enter="transition ease-out duration-300 transform" x-transition:enter-start="opacity-0 translate-y-2" x-transition:enter-end="opacity-100 translate-y-0" x-transition:leave="transition ease-in duration-300 transform" x-transition:leave-start="opacity-100 translate-y-0" x-transition:leave-end="opacity-0 translate-y-2">
                    <span>You have successfully completed this thing!</span>
                    <a href="javascript:void(0);" @click="isOpen = false" class="absolute text-lg transition duration-200 ease-linear text-primary-300 hover:text-primary-50 ltr:right-4 rtl:left-4 top-2"><i class="ri-close-fill"></i></a>
                </div>
            </div>

            <div x-data="{ isOpen: false }">
                <button @click="isOpen = true; setTimeout(() => isOpen = false, 1500)" class="btn btn-primary">
                    Bottom Left
                </button>

                <div x-show="isOpen" x-init="setTimeout(() => isOpen = false, 1500)" class="fixed py-3 text-sm text-white transition-all duration-300 ease-in-out transform rounded-md ltr:pl-5 rtl:pr-5 bg-gradient-to-r from-primary-500 to-primary-600 ltr:pr-9 rtl:pl-9 bottom-5 z-[1055] ltr:left-5 rtl:right-5 max-w-96" x-transition:enter="transition ease-out duration-300 transform" x-transition:enter-start="opacity-0 translate-y-2" x-transition:enter-end="opacity-100 translate-y-0" x-transition:leave="transition ease-in duration-300 transform" x-transition:leave-start="opacity-100 translate-y-0" x-transition:leave-end="opacity-0 translate-y-2">
                    <span>You have successfully completed this thing!</span>
                    <a href="javascript:void(0);" @click="isOpen = false" class="absolute text-lg transition duration-200 ease-linear text-primary-300 hover:text-primary-50 ltr:right-4 rtl:left-4 top-2"><i class="ri-close-fill"></i></a>
                </div>
            </div>

            <div x-data="{ isOpen: false }">
                <button @click="isOpen = true; setTimeout(() => isOpen = false, 1500)" class="btn btn-primary">
                    Bottom Center
                </button>

                <div x-show="isOpen" x-init="setTimeout(() => isOpen = false, 1500)" class="fixed py-3 text-sm text-white transition-all duration-300 ease-in-out transform -translate-x-1/2 rounded-md ltr:pl-5 rtl:pr-5 bg-gradient-to-r from-primary-500 to-primary-600 ltr:pr-9 rtl:pl-9 bottom-5 z-[1055] left-1/2 max-w-96" x-transition:enter="transition ease-out duration-300 transform" x-transition:enter-start="opacity-0 translate-y-2" x-transition:enter-end="opacity-100 translate-y-0" x-transition:leave="transition ease-in duration-300 transform" x-transition:leave-start="opacity-100 translate-y-0" x-transition:leave-end="opacity-0 translate-y-2">
                    <span>You have successfully completed this thing!</span>
                    <a href="javascript:void(0);" @click="isOpen = false" class="absolute text-lg transition duration-200 ease-linear text-primary-300 hover:text-primary-50 ltr:right-4 rtl:left-4 top-2"><i class="ri-close-fill"></i></a>
                </div>
            </div>

            <div x-data="{ isOpen: false }">
                <button @click="isOpen = true; setTimeout(() => isOpen = false, 1500)" class="btn btn-primary">
                    Bottom Right
                </button>

                <div x-show="isOpen" x-init="setTimeout(() => isOpen = false, 1500)" class="fixed py-3 text-sm text-white transition-all duration-300 ease-in-out transform rounded-md ltr:pl-5 rtl:pr-5 bg-gradient-to-r from-primary-500 to-primary-600 ltr:pr-9 rtl:pl-9 bottom-5 z-[1055] ltr:right-5 rtl:left-5 max-w-96" x-transition:enter="transition ease-out duration-300 transform" x-transition:enter-start="opacity-0 translate-y-2" x-transition:enter-end="opacity-100 translate-y-0" x-transition:leave="transition ease-in duration-300 transform" x-transition:leave-start="opacity-100 translate-y-0" x-transition:leave-end="opacity-0 translate-y-2">
                    <span>You have successfully completed this thing!</span>
                    <a href="javascript:void(0);" @click="isOpen = false" class="absolute text-lg transition duration-200 ease-linear text-primary-300 hover:text-primary-50 ltr:right-4 rtl:left-4 top-2"><i class="ri-close-fill"></i></a>
                </div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Content Toast</h6>
        </div>
        <div class="flex items-center gap-2 card-body">
            <div x-data="{ isOpen: false }">
                <button @click="isOpen = true; setTimeout(() => isOpen = false, 5000)" class="btn btn-purple">
                    Content Toast
                </button>

                <div x-show="isOpen" x-init="setTimeout(() => isOpen = false, 5000)" class="fixed text-sm transition-all duration-300 ease-in-out transform bg-white rounded-md shadow-lg top-5 z-[1055] ltr:right-5 rtl:left-5 max-w-96 shadow-gray-300 dark:bg-dark-900 dark:shadow-dark-900" x-transition:enter="transition ease-out duration-300 transform" x-transition:enter-start="opacity-0 translate-y-2" x-transition:enter-end="opacity-100 translate-y-0" x-transition:leave="transition ease-in duration-300 transform" x-transition:leave-start="opacity-100 translate-y-0" x-transition:leave-end="opacity-0 translate-y-2">
                    <div class="flex items-center p-3 border-b border-gray-200 dark:border-dark-800 ltr:pr-9 rtl:pl-9">
                        <h6>You have successfully completed this thing!</h6>
                        <a href="javascript:void(0);" @click="isOpen = false" class="absolute text-lg link link-red ltr:right-4 rtl:left-4 top-2"><i class="ri-close-fill"></i></a>
                    </div>
                    <div class="p-3">
                        <p class="text-gray-500 dark:text-dark-500">Those who successfully complete the programme will be awarded a certificate of achievement. You can concentrate and successfully complete a project that just days ago would have looked too difficult.</p>
                    </div>
                </div>
            </div>
        </div>
    </div><!--end col-->
</div><!--end grid-->

</div>
{{> partials/footer }}
</div>

{{> partials/vendor-scripts }}
<script type="module" src="assets/js/ui/notification.init.js"></script>
<script type="module" src="assets/js/main.js"></script>

</body>
</html>