{{> partials/main }}

<head>

    {{> partials/title-meta title="Bar Charts" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}

{{> partials/page-heading title="Bar Charts" sub-title="Echarts" }}

<div class="grid grid-cols-12 gap-x-space">
    <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Basic</h6>
        </div>
        <div class="card-body" x-data="basicBarApp">
            <div class="h-80" x-ref="basicBarChart" data-chart-colors="[bg-sky-500, bg-gray-200, bg-gray-800]" data-chart-dark-colors="[bg-sky-500, bg-dark-800, bg-dark-100]"></div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Axis Align with Tick</h6>
        </div>
        <div class="card-body" x-data="axisAlignBarApp">
            <div class="h-80" x-ref="axisAlignBarChart" id="axisAlignBarChart" data-chart-colors="[bg-primary-500, bg-gray-200, bg-gray-800]" data-chart-dark-colors="[bg-primary-500, bg-dark-800, bg-dark-100]"></div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Bar with Background</h6>
        </div>
        <div class="card-body" x-data="backgroundBarApp">
            <div class="h-80" x-ref="backgroundBarChart" data-chart-colors="[bg-green-500, bg-gray-200, bg-gray-800, bg-gray-100]" data-chart-dark-colors="[bg-green-500, bg-dark-800, bg-dark-100, bg-dark-850]"></div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Set Style of Single Bar</h6>
        </div>
        <div class="card-body" x-data="singleBarApp">
            <div class="h-80" x-ref="singleBarChart" data-chart-colors="[bg-primary-500, bg-purple-500, bg-gray-200, bg-gray-800, bg-gray-100]" data-chart-dark-colors="[bg-primary-500, bg-purple-500, bg-dark-800, bg-dark-100, bg-dark-850]"></div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">World Population</h6>
        </div>
        <div class="card-body" x-data="worldPopulationBarApp">
            <div class="h-80" x-ref="worldPopulationBarChart" data-chart-colors="[bg-primary-500, bg-purple-500, bg-gray-200, bg-gray-800, bg-gray-100]" data-chart-dark-colors="[bg-primary-500, bg-purple-500, bg-dark-800, bg-dark-100, bg-dark-850]"></div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Stacked Bar with borderRadius</h6>
        </div>
        <div class="card-body" x-data="stackedBorderRadiusBarApp">
            <div class="h-80" x-ref="stackedBorderRadiusBarChart" data-chart-colors="[bg-primary-500, bg-purple-500, bg-yellow-500, bg-gray-200, bg-gray-800, bg-gray-100]" data-chart-dark-colors="[bg-primary-500, bg-purple-500, bg-yellow-500, bg-dark-800, bg-dark-100, bg-dark-850]"></div>
        </div>
    </div><!--end col-->
</div><!--end grid-->

<div class="mb-5 text-center">
    <a href="https://echarts.apache.org/examples/en/index.html#chart-type-bar" target="_blank" class="btn btn-primary">More Example <i data-lucide="move-right" class="inline-block ml-1 size-4"></i></a>
</div>
</div>
{{> partials/footer }}
</div>

{{> partials/vendor-scripts }}

<script src="assets/libs/echarts/echarts.js"></script>

<script type="module" src="assets/js/charts/echart-bar.init.js"></script>

<script type="module" src="assets/js/main.js"></script>

</body>
</html>