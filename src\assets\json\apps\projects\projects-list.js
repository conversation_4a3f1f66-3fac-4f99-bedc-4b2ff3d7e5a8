import brand1 from '/assets/images/brands/img-01.png'
import brand2 from '/assets/images/brands/img-02.png'
import brand3 from '/assets/images/brands/img-03.png'
import brand4 from '/assets/images/brands/img-04.png'
import brand5 from '/assets/images/brands/img-05.png'
import brand6 from '/assets/images/brands/img-06.png'
import brand7 from '/assets/images/brands/img-07.png'
import brand8 from '/assets/images/brands/img-08.png'
import brand9 from '/assets/images/brands/img-09.png'
import brand10 from '/assets/images/brands/img-10.png'
import brand11 from '/assets/images/brands/img-11.png'
import brand12 from '/assets/images/brands/img-12.png'
import brand13 from '/assets/images/brands/img-13.png'
import brand14 from '/assets/images/brands/img-14.png'
import brand15 from '/assets/images/brands/img-15.png'
import brand16 from '/assets/images/brands/img-16.png'
import brand17 from '/assets/images/brands/img-17.png'
import brand18 from '/assets/images/brands/img-18.png'
import brand19 from '/assets/images/brands/img-19.png'
import brand20 from '/assets/images/brands/img-20.png'
import brand21 from '/assets/images/brands/img-21.png'
import brand22 from '/assets/images/brands/img-22.png'
import brand23 from '/assets/images/brands/img-23.png'
import brand24 from '/assets/images/brands/img-24.png'
import brand25 from '/assets/images/brands/img-25.png'
import brand26 from '/assets/images/brands/img-26.png'
import brand27 from '/assets/images/brands/img-27.png'
import user1 from '/assets/images/avatar/user-1.png'
import user2 from '/assets/images/avatar/user-2.png'
import user3 from '/assets/images/avatar/user-3.png'
import user4 from '/assets/images/avatar/user-4.png'
import user5 from '/assets/images/avatar/user-5.png'
import user6 from '/assets/images/avatar/user-6.png'
import user7 from '/assets/images/avatar/user-7.png'
import user8 from '/assets/images/avatar/user-8.png'
import user9 from '/assets/images/avatar/user-9.png'
import user10 from '/assets/images/avatar/user-10.png'
import user11 from '/assets/images/avatar/user-11.png'
import user12 from '/assets/images/avatar/user-12.png'
import user13 from '/assets/images/avatar/user-13.png'
import user14 from '/assets/images/avatar/user-14.png'
import user15 from '/assets/images/avatar/user-15.png'
import user16 from '/assets/images/avatar/user-16.png'
import user17 from '/assets/images/avatar/user-17.png'
import user18 from '/assets/images/avatar/user-18.png'
import user19 from '/assets/images/avatar/user-19.png'
import user20 from '/assets/images/avatar/user-20.png'
import user21 from '/assets/images/avatar/user-21.png'
import user22 from '/assets/images/avatar/user-22.png'
import user23 from '/assets/images/avatar/user-23.png'
import user24 from '/assets/images/avatar/user-24.png'
import user25 from '/assets/images/avatar/user-25.png'
import user26 from '/assets/images/avatar/user-26.png'
import user27 from '/assets/images/avatar/user-27.png'


const projectListData = [
    {
        "projectImage": brand1,
        "image": user1,
        "projectName": "Chat Application",
        "clientName": "John Doe",
        "dueDate": "15 June, 2024",
        "totalAmount": "18,000",
        "assignees": [
            {
                "image": user14,
                "name": "Max Boucaut"
            },
            {
                "image": user16,
                "name": "Ethan Zahel"
            }
        ],
        "progress": "18",
        "status": "Active"
    },
    {
        "projectImage": brand2,
        "image": user2,
        "projectName": "E-commerce Platform",
        "clientName": "Jane Smith",
        "dueDate": "25 July, 2024",
        "totalAmount": "25,000",
        "assignees": [
            {
                "image": user17,
                "name": "Poppy Dalley"
            },
            {
                "image": user15,
                "name": "Natasha Tegg"
            },
            {
                "image": user19,
                "name": "Sam Taylor"
            }
        ],
        "progress": "41",
        "status": "On Hold"
    },
    {
        "projectImage": brand3,
        "image": user3,
        "projectName": "Corporate Website",
        "clientName": "ABC Corp",
        "dueDate": "30 May, 2024",
        "totalAmount": "10,000",
        "assignees": [
            {
                "image": user16,
                "name": "Ethan Zahel"
            },
            {
                "image": user12,
                "name": "Julian Marconi"
            }
        ],
        "progress": "75",
        "status": "Pending"
    },
    {
        "projectImage": brand4,
        "image": user4,
        "projectName": "Mobile App",
        "clientName": "XYZ Ltd",
        "dueDate": "10 Aug, 2024",
        "totalAmount": "30,000",
        "assignees": [
            {
                "image": user17,
                "name": "Poppy Dalley"
            },
            {
                "image": user18,
                "name": "Ryan Frazer"
            },
            {
                "image": user14,
                "name": "Max Boucaut"
            }
        ],
        "progress": "59",
        "status": "Active"
    },
    {
        "projectImage": brand5,
        "image": user5,
        "projectName": "Digital Marketing",
        "clientName": "Marketing Inc",
        "dueDate": "5 Sept, 2024",
        "totalAmount": "15,000",
        "assignees": [
            {
                "image": user13,
                "name": "Ava Bennett"
            }
        ],
        "progress": "100",
        "status": "Completed"
    },
    {
        "projectImage": brand6,
        "image": user6,
        "projectName": "SEO Optimization",
        "clientName": "Tech Solutions",
        "dueDate": "20 June, 2024",
        "totalAmount": "12,000",
        "assignees": [
            {
                "image": user15,
                "name": "Natasha Tegg"
            },
            {
                "image": user18,
                "name": "Ryan Frazer"
            }
        ],
        "progress": "50",
        "status": "Active"
    },
    {
        "projectImage": brand7,
        "image": user7,
        "projectName": "Social Media Campaign",
        "clientName": "Brandify",
        "dueDate": "10 July, 2024",
        "totalAmount": "8,000",
        "assignees": [
            {
                "image": user12,
                "name": "Julian Marconi"
            },
            {
                "image": user18,
                "name": "Ryan Frazer"
            },
            {
                "image": user14,
                "name": "Max Boucaut"
            }
        ],
        "progress": "30",
        "status": "On Hold"
    },
    {
        "projectImage": brand8,
        "image": user8,
        "projectName": "API Integration",
        "clientName": "DevHouse",
        "dueDate": "5 Aug, 2024",
        "totalAmount": "20,000",
        "assignees": [
            {
                "image": user17,
                "name": "Poppy Dalley"
            },
            {
                "image": user16,
                "name": "Ethan Zahel"
            }
        ],
        "progress": "60",
        "status": "Pending"
    },
    {
        "projectImage": brand9,
        "image": user9,
        "projectName": "Data Analysis",
        "clientName": "DataWorks",
        "dueDate": "15 May, 2024",
        "totalAmount": "25,000",
        "assignees": [
            {
                "image": user11,
                "name": "Lucas Griffin"
            },
            {
                "image": user18,
                "name": "Ryan Frazer"
            },
            {
                "image": user16,
                "name": "Ethan Zahel"
            }
        ],
        "progress": "80",
        "status": "Active"
    },
    {
        "projectImage": brand10,
        "image": user10,
        "projectName": "AI Model Development",
        "clientName": "AI Innovators",
        "dueDate": "1 Sept, 2024",
        "totalAmount": "50,000",
        "assignees": [
            {
                "image": user11,
                "name": "Lucas Griffin"
            }
        ],
        "progress": "100",
        "status": "Completed"
    },
    {
        "projectImage": brand11,
        "image": user11,
        "projectName": "Cloud Migration",
        "clientName": "Cloudy Inc.",
        "dueDate": "20 Oct, 2024",
        "totalAmount": "35,000",
        "assignees": [
            {
                "image": user12,
                "name": "Julian Marconi"
            },
            {
                "image": user15,
                "name": "Natasha Tegg"
            }
        ],
        "progress": "70",
        "status": "Active"
    },
    {
        "projectImage": brand12,
        "image": user12,
        "projectName": "Cybersecurity Audit",
        "clientName": "SecureIT",
        "dueDate": "30 June, 2024",
        "totalAmount": "22,000",
        "assignees": [
            {
                "image": user18,
                "name": "Ryan Frazer"
            },
            {
                "image": user17,
                "name": "Poppy Dalley"
            },
            {
                "image": user16,
                "name": "Ethan Zahel"
            }
        ],
        "progress": "65",
        "status": "On Hold"
    },
    {
        "projectImage": brand13,
        "image": user13,
        "projectName": "Blockchain Development",
        "clientName": "BlockTech",
        "dueDate": "15 July, 2024",
        "totalAmount": "40,000",
        "assignees": [
            {
                "image": user14,
                "name": "Max Boucaut"
            },
            {
                "image": user19,
                "name": "Sam Taylor"
            }
        ],
        "progress": "50",
        "status": "Active"
    },
    {
        "projectImage": brand14,
        "image": user14,
        "projectName": "VR Training Program",
        "clientName": "Immersive Learning",
        "dueDate": "1 Nov, 2024",
        "totalAmount": "55,000",
        "assignees": [
            {
                 
                 "image" : user11,
                 "name": "Lucas Griffin"
            },
            {
                "image": user13,
                "name": "Ava Bennett"
            }
        ],
        "progress": "40",
        "status": "Active"
    },
    {
        "projectImage": brand15,
        "image": user15,
        "projectName": "Customer Portal",
        "clientName": "CustomerFirst",
        "dueDate": "5 Dec, 2024",
        "totalAmount": "28,000",
        "assignees": [
            {
                "image": user12,
                "name": "Julian Marconi"
            },
            {
                "image": user14,
                "name": "Max Boucaut"
            },
            {
                "image": user18,
                "name": "Ryan Frazer"
            }
        ],
        "progress": "100",
        "status": "Completed"
    },
    {
        "projectImage": brand16,
        "image": user16,
        "projectName": "IoT Implementation",
        "clientName": "SmartHome",
        "dueDate": "15 Jan, 2025",
        "totalAmount": "60,000",
        "assignees": [
            {
                "image": user17,
                "name": "Poppy Dalley"
            },
            {
                "image": user11,
                "name": "Lucas Griffin"
            }
        ],
        "progress": "25",
        "status": "Active"
    },
    {
        "projectImage": brand17,
        "image": user17,
        "projectName": "Marketing Automation",
        "clientName": "MarketPro",
        "dueDate": "25 Feb, 2025",
        "totalAmount": "15,000",
        "assignees": [
            {
                "image": user15,
                "name": "Natasha Tegg"
            }
        ],
        "progress": "55",
        "status": "On Hold"
    },
    {
        "projectImage": brand18,
        "image": user18,
        "projectName": "ERP System",
        "clientName": "Enterprise Solutions",
        "dueDate": "10 March, 2025",
        "totalAmount": "75,000",
        "assignees": [
            {
                "image": user19,
                "name": "Sam Taylor"
            },
            {
                "image": user20,
                "name": "Jordan Fisher"
            }
        ],
        "progress": "60",
        "status": "Pending"
    },
    {
        "projectImage": brand19,
        "image": user19,
        "projectName": "HR Management System",
        "clientName": "HR Innovate",
        "dueDate": "20 April, 2025",
        "totalAmount": "38,000",
        "assignees": [
            {
                "image": user11,
                "name": "Lucas Griffin"
            },
            {
                "image": user12,
                "name": "Julian Marconi"
            },
            {
                "image": user13,
                "name": "Ava Bennett"
            }
        ],
        "progress": "88",
        "status": "Active"
    },
    {
        "projectImage": brand20,
        "image": user20,
        "projectName": "Financial Analysis Tool",
        "clientName": "Finance Experts",
        "dueDate": "1 May, 2025",
        "totalAmount": "29,000",
        "assignees": [
            {
                "image": user16,
                "name": "Ethan Zahel"
            },
            {
                "image": user14,
                "name": "Max Boucaut"
            }
        ],
        "progress": "100",
        "status": "Completed"
    },
    {
        "projectImage": brand21,
        "image": user21,
        "projectName": "Machine Learning Model",
        "clientName": "DataScience Inc.",
        "dueDate": "10 June, 2025",
        "totalAmount": "55,000",
        "assignees": [
            {
                "image": user11,
                "name": "Lucas Griffin"
            },
            {
                "image": user15,
                "name": "Natasha Tegg"
            }
        ],
        "progress": "45",
        "status": "Active"
    },
    {
        "projectImage": brand22,
        "image": user22,
        "projectName": "Website Redesign",
        "clientName": "Creative Design",
        "dueDate": "20 July, 2025",
        "totalAmount": "20,000",
        "assignees": [
            {
                "image": user13,
                "name": "Ava Bennett"
            },
            {
                "image": user16,
                "name": "Ethan Zahel"
            }
        ],
        "progress": "60",
        "status": "Pending"
    },
    {
        "projectImage": brand23,
        "image": user23,
        "projectName": "Mobile Game Development",
        "clientName": "Game Studio",
        "dueDate": "5 Aug, 2025",
        "totalAmount": "70,000",
        "assignees": [
            {
                "image": user19,
                "name": "Sam Taylor"
            },
            {
                "image": user18,
                "name": "Ryan Frazer"
            },
            {
                "image": user14,
                "name": "Max Boucaut"
            }
        ],
        "progress": "75",
        "status": "Active"
    },
    {
        "projectImage": brand24,
        "image": user24,
        "projectName": "Data Migration",
        "clientName": "Cloud Storage",
        "dueDate": "15 Sept, 2025",
        "totalAmount": "25,000",
        "assignees": [
            {
                "image": user12,
                "name": "Julian Marconi"
            },
            {
                "image": user11,
                "name": "Lucas Griffin"
            }
        ],
        "progress": "50",
        "status": "On Hold"
    },
    {
        "projectImage": brand25,
        "image": user25,
        "projectName": "Product Launch",
        "clientName": "Marketing Agency",
        "dueDate": "30 Oct, 2025",
        "totalAmount": "35,000",
        "assignees": [
            {
                "image": user16,
                "name": "Ethan Zahel"
            },
            {
                "image": user14,
                "name": "Max Boucaut"
            },
            {
                "image": user13,
                "name": "Ava Bennett"
            }
        ],
        "progress": "65",
        "status": "Active"
    },
    {
        "projectImage": brand26,
        "image": user26,
        "projectName": "Inventory Management System",
        "clientName": "Retail Solutions",
        "dueDate": "10 Nov, 2025",
        "totalAmount": "40,000",
        "assignees": [
            {
                "image": user19,
                "name": "Sam Taylor"
            },
            {
                "image": user12,
                "name": "Julian Marconi"
            }
        ],
        "progress": "100",
        "status": "Completed"
    },
    {
        "projectImage": brand27,
        "image": user27,
        "projectName": "Custom CRM Development",
        "clientName": "SalesPro",
        "dueDate" :  "25 Dec, 2025",
        "totalAmount": "45,000",
        "assignees": [
            {
                "image": user14,
                "name": "Max Boucaut"
            },
            {
                "image": user15,
                "name": "Natasha Tegg"
            },
            {
                "image": user17,
                "name": "Poppy Dalley"
            }
        ],
        "progress": "80",
        "status": "Active"
    }
]
export default projectListData