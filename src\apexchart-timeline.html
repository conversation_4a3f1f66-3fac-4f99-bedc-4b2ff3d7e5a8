{{> partials/main }}

<head>

    {{> partials/title-meta title="Timeline Charts" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}

{{> partials/page-heading title="Timeline Charts" sub-title="Apexcharts" }}

<div class="grid grid-cols-12 gap-x-space">
    <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Basic</h6>
        </div>
        <div class="card-body">
            <div x-data="basicApp" dir="ltr">
                <div class="!min-h-full" data-chart-colors="[bg-primary-500]" x-ref="basicChart"></div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Advanced</h6>
        </div>
        <div class="card-body">
            <div x-data="advancedTimelineApp" dir="ltr">
                <div class="!min-h-full" data-chart-colors="[bg-primary-500, bg-yellow-500, bg-green-500]" x-ref="advancedTimelineChart"></div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Multiple Series – Group Rows</h6>
        </div>
        <div class="card-body">
            <div x-data="multipleGroupTimelineApp" dir="ltr" >
                <div class="!min-h-full" x-ref="multipleGroupTimelineChart"></div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Dumbbell Chart (Horizontal)</h6>
        </div>
        <div class="card-body">
            <div x-data="dumbbellTimelineApp" dir="ltr">
                <div class="!min-h-full" data-chart-colors="[bg-primary-500, bg-green-500]" x-ref="dumbbellTimelineChart"></div>
            </div>
        </div>
    </div><!--end col-->
</div><!--end grid-->

</div>
{{> partials/footer }}
</div>

{{> partials/vendor-scripts }}

<script src="assets/libs/apexcharts/apexcharts.min.js"></script>

<script type="module" src="assets/js/charts/timeline-chart.init.js"></script>

<script type="module" src="assets/js/main.js"></script>

</body>
</html>