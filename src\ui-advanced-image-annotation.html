{{> partials/main }}

<head>

    {{> partials/title-meta title="Images Annotation" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}

{{> partials/page-heading title="Images Annotation" sub-title="UI Advanced" }}

<div class="grid grid-cols-12 gap-x-space">
    <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Basic</h6>
        </div>
        <div class="card-body">
            <div x-data="annotation()">
                <div class="relative">
                    <img src="assets/images/gallery/img-02.jpg" x-spread="image" class="w-full rounded-sm">

                    <div class="absolute z-20 top-8 ltr:left-1/3 rtl:right-1/3">
                        <div class="absolute top-0 z-0 bg-red-500 rounded-full ltr:right-0 rtl:left-0 size-5 animate-ping"></div>
                        <button class="relative z-10 bg-red-500 border border-red-200 rounded-full shadow-sm shadow-outline size-5" @click="toggleAnnotations"></button>

                        <template x-for="note in notes" :key="note.id">
                            <div class="absolute max-h-52" data-simplebar :style="markerStyle(note)" @click="toggleNote(note)">
                                <div x-show="note.visible" class="relative z-10 flex flex-col w-48 gap-2 overflow-hidden bg-white divide-y divide-gray-200 rounded-sm dark:divide-dark-800 dark:bg-dark-900" @click.stop.away="toggleNote()">
                                    <template x-for="comment in note.comments">
                                        <p x-text="comment.text" class="px-4 pt-2"></p>
                                    </template>

                                    <input x-model="newComment" placeholder="Type comment" class="rounded-none form-input first:pt-2" @keydown.enter="addComment(note)">
                                </div>
                            </div>
                        </template>
                    </div>
                </div>
            </div>
            <p class="mt-2 text-sm italic text-gray-500 dark:text-dark-500">
                Add comments to the existing marker or click anywhere on the image to add new markers.
            </p>
        </div>
    </div><!--end col-->
</div><!--end grid-->

</div>
{{> partials/footer }}
</div>


{{> partials/vendor-scripts }}
<script type="module" src="assets/js/main.js"></script>
<script type="module" src="assets/js/ui/advanced-image-annotation.init.js"></script>

</body>
</html>