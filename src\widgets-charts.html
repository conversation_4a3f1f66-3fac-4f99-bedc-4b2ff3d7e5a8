{{> partials/main }}

<head>

    {{> partials/title-meta title="Widgets Charts" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}
{{> partials/page-heading title="Charts" sub-title="Widgets" }}

<div class="grid grid-cols-12 gap-x-space">
    <div class="col-span-12 xl:col-span-6 2xl:col-span-4 card">
        <div class="card-body">
            <div class="flex gap-3 mb-3">
                <div class="flex items-center justify-center text-red-500 border-2 border-red-400 rounded-full ring-1 ring-offset-2 dark:ring-offset-dark-900 ring-red-500/20 size-12">
                    <i data-lucide="activity" class="fill-red-500/10"></i>
                </div>
                <div>
                    <p class="mb-1 text-gray-500 dark:text-dark-500">Ads Revenue</p>
                    <h5>$<span x-data="animatedCounter(145, 500, 0)" x-init="updateCounter" x-text="Math.round(current)"></span>M</h5>
                </div>
            </div>
            <div x-data="adsRevenueApp" dir="ltr">
                <div class="!min-h-full" data-chart-colors="[bg-red-500]" x-ref="adsRevenueChart"></div>
            </div>
        </div>
    </div><!--end Ads Revenue-->
    <div class="col-span-12 xl:col-span-6 2xl:col-span-4 card">
        <div class="card-body">
            <div class="flex gap-3 mb-3">
                <div class="flex items-center justify-center border-2 rounded-full text-primary-500 ring-1 ring-offset-2 dark:ring-offset-dark-900 ring-primary-500/20 size-12 border-primary-500">
                    <i data-lucide="circle-arrow-up" class="fill-primary-500/10"></i>
                </div>
                <div>
                    <p class="mb-1 text-gray-500 dark:text-dark-500">Sales Revenue</p>
                    <h5>$<span x-data="animatedCounter(145, 500, 0)" x-init="updateCounter" x-text="Math.round(current)"></span>M</h5>
                </div>
            </div>
            <div x-data="salesRevenueApp" dir="ltr">
                <div class="!min-h-full" data-chart-colors="[bg-primary-500, bg-primary-100, bg-primary-50, bg-primary-300]" x-ref="salesRevenueChart"></div>
            </div>
        </div>
    </div><!--end Sales Revenue-->
    <div class="col-span-12 xl:col-span-6 2xl:col-span-4 card">
        <div class="card-body">
            <div class="flex gap-3 mb-3">
                <div class="flex items-center justify-center text-purple-500 border-2 border-purple-400 rounded-full ring-1 ring-offset-2 dark:ring-offset-dark-900 ring-purple-500/20 size-12">
                    <i data-lucide="aperture" class="fill-purple-500/10"></i>
                </div>
                <div>
                    <p class="mb-1 text-gray-500 dark:text-dark-500">Ads Revenue</p>
                    <h5>$<span x-data="animatedCounter(145, 500, 0)" x-init="updateCounter" x-text="Math.round(current)"></span>M</h5>
                </div>
            </div>
            <div x-data="adsRevenueApp" dir="ltr">
                <div class="!min-h-full" data-chart-colors="[bg-purple-500]" x-ref="adsRevenueChart"></div>
            </div>
        </div>
    </div><!--end Ads Revenue-->
    
    <div class="col-span-12 md:col-span-6 2xl:col-span-4">
        <div class="card">
            <div class="flex items-center gap-3 card-header">
                <h6 class="card-title grow">Total Sales</h6>
                <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown">
                    <button x-ref="button" x-on:click="toggle()" :aria-expanded="open.toString()" type="button" class="flex px-2 py-1 text-xs border-gray-200 dark:border-dark-800 link link-red btn">
                        Last Week
                        <svg :class="{ 'transform rotate-180': open }" class="transition-transform duration-300 size-4" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                        </svg>
                    </button>

                    <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden" dropdown-position="right">
                        <a href="#" class="dropdown-item">
                            Last Week
                        </a>

                        <a href="#" class="dropdown-item">
                            Last Month
                        </a>
                        <a href="#" class="dropdown-item">
                            Last Years
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div x-data="labelColumnApp" dir="ltr">
                    <div class="!min-h-full -ml-3" data-chart-colors="[bg-primary-500]" x-ref="labelColumnChart"></div>
                </div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 md:col-span-6 2xl:col-span-3">
        <div class="card">
            <div class="flex items-center gap-3 card-header">
                <h6 class="card-title grow">Total View Performance</h6>
                <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown">
                    <button x-ref="button" x-on:click="toggle()" :aria-expanded="open.toString()" type="button" class="flex link link-primary">
                        <i data-lucide="ellipsis" class="size-5"></i>
                    </button>

                    <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden" dropdown-position="right">
                        <a href="#" class="dropdown-item">
                            Last Week
                        </a>

                        <a href="#" class="dropdown-item">
                            Last Month
                        </a>
                        <a href="#" class="dropdown-item">
                            Last Years
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div x-data="semiDonutApp" dir="ltr">
                    <div class="!min-h-full" data-chart-colors="[bg-primary-500, bg-pink-400]" x-ref="semiDonutChart"></div>
                </div>
                <div class="mt-3 text-center">
                    <p class="mb-3 text-gray-500 dark:text-dark-500">Ensure your information is kept updated to enhance performance.</p>
                    <a href="#!" class="btn btn-primary">Guide Views <i class="ml-1 align-bottom ri-arrow-right-s-line"></i></a>
                </div>
            </div>
            <div class="flex items-center justify-center gap-3 text-sm card-footer">
                <a href="#!" class="text-gray-500 dark:text-dark-500"><i class="align-bottom ri-circle-fill text-primary-500"></i> View Count: <span class="font-medium text-gray-800 dark:text-dark-100">148</span></a>
                <a href="#!" class="text-gray-500 dark:text-dark-500"><i class="text-pink-400 align-bottom ri-circle-fill"></i> Percentage: <span class="font-medium text-gray-800 dark:text-dark-100">59%</span></a>
            </div>
        </div>
    </div><!--end col-->
</div><!--end grid-->

</div>

{{> partials/footer }}
</div>

{{> partials/vendor-scripts }}

<script type="module" src="assets/js/pages/widgets.charts.init.js"></script>

<script type="module" src="assets/js/main.js"></script>


</body>
</html>