{{> partials/main }}

<head>

    {{> partials/title-meta title="Product Overview" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}
{{> partials/page-heading title="Product Overview" sub-title="Ecommerce" }}
<div x-data="reviewTable()">
<div class="grid grid-cols-12 gap-x-5">
    <div class="col-span-12 lg:col-span-8">
        <div class="card">
            <div class="card-body" x-data="{ count: 1, basePrice: 99.99, discount: 0.5 }">
                <div class="flex items-center mb-5">
                    <p class="grow"><span class="badge badge-sub-gray"><i data-lucide="heart" class="inline-block mr-1 text-red-500 size-4 fill-red-500"></i> Wishlist</span></p>
                    <div class="divide-x divide-gray-200 rtl:divide-x-reverse dark:divide-dark-800 shrink-0">
                        <a href="apps-ecommerce-create-products.html" class="ltr:pr-1 rtl:pl-1 link link-primary"><i data-lucide="pencil" class="inline-block size-4"></i> <span class="align-middle">Edit</span></a>
                        <a href="#!" class="ltr:pl-2 rtl:pr-2 link link-red"><i data-lucide="trash-2" class="inline-block size-4"></i> <span class="align-middle">Delete</span></a>
                    </div>
                </div>
                <h5 class="mb-3">Collection Ruffled Cotton Top</h5>
                <div class="flex items-center divide-x divide-gray-200 dark:divide-dark-800 rtl:divide-x-reverse *:px-3 mb-5">
                    <p class="ltr:first:pl-0 rtl:first:pr-0"><i class="text-yellow-500 align-bottom ri-star-half-line"></i> 4.8</p>
                    <p class="ltr:first:pl-0 rtl:first:pr-0">149 Reviews</p>
                    <p class="ltr:first:pl-0 rtl:first:pr-0">4789 Sales</p>
                </div>

                <h4 class="flex items-center gap-2 mt-3">
                    $<span x-text="((basePrice * count) * (1 - discount)).toFixed(2)"></span>
                    <small class="font-normal text-gray-500 line-through dark:text-dark-500">$<span x-text="(basePrice * count).toFixed(2)"></span></small>
                    <span class="text-xs badge badge-red shrink-0">50%</span>
                </h4>

                <div class="grid grid-cols-1 sm:grid-cols-2">
                    <div>
                        <h6 class="mt-5">Select Colors</h6>
                        <div class="flex items-center gap-2 mt-2 grow" x-data="{ activeLink: 'gray' }">
                            <a href="#!" class="flex items-center justify-center text-white bg-blue-500 border-2 border-white rounded-full dark:border-dark-900 outline-1 outline outline-gray-200 dark:outline-dark-800 size-6 group/item" x-bind:class="{ 'active': activeLink === 'blue' }" @click.prevent="activeLink = 'blue'"><i data-lucide="check" class="size-4 hidden group-[&.active]/item:block"></i></a>
                            <a href="#!" class="flex items-center justify-center text-white bg-gray-300 border-2 border-white rounded-full dark:border-dark-900 outline-1 outline outline-gray-200 dark:outline-dark-800 size-6 group/item" x-bind:class="{ 'active': activeLink === 'gray' }" @click.prevent="activeLink = 'gray'"><i data-lucide="check" class="size-4 hidden group-[&.active]/item:block"></i></a>
                            <a href="#!" class="flex items-center justify-center text-white bg-pink-500 border-2 border-white rounded-full dark:border-dark-900 outline-1 outline outline-gray-200 dark:outline-dark-800 size-6 group/item" x-bind:class="{ 'active': activeLink === 'pink' }" @click.prevent="activeLink = 'pink'"><i data-lucide="check" class="size-4 hidden group-[&.active]/item:block"></i></a>
                            <a href="#!" class="flex items-center justify-center text-white bg-green-500 border-2 border-white rounded-full dark:border-dark-900 outline-1 outline outline-gray-200 dark:outline-dark-800 size-6 group/item" x-bind:class="{ 'active': activeLink === 'green' }" @click.prevent="activeLink = 'green'"><i data-lucide="check" class="size-4 hidden group-[&.active]/item:block"></i></a>
                            <a href="#!" class="flex items-center justify-center text-white bg-red-500 border-2 border-white rounded-full dark:border-dark-900 outline-1 outline outline-gray-200 dark:outline-dark-800 size-6 group/item" x-bind:class="{ 'active': activeLink === 'red' }" @click.prevent="activeLink = 'red'"><i data-lucide="check" class="size-4 hidden group-[&.active]/item:block"></i></a>
                        </div>
                    </div>
                    <div>
                        <h6 class="mt-5">Select Size</h6>
                        <div class="flex items-center gap-2 mt-3 font-medium shrink-0" x-data="{ activeLink: 'm' }">
                            <a href="#!" class="text-gray-500 dark:text-dark-500 [&.active]:text-green-500" x-bind:class="{ 'active': activeLink === 's' }" @click.prevent="activeLink = 's'">S</a>
                            <a href="#!" class="text-gray-500 dark:text-dark-500 [&.active]:text-green-500" x-bind:class="{ 'active': activeLink === 'm' }" @click.prevent="activeLink = 'm'">M</a>
                            <a href="#!" class="text-gray-500 dark:text-dark-500 [&.active]:text-green-500" x-bind:class="{ 'active': activeLink === 'l' }" @click.prevent="activeLink = 'l'">L</a>
                            <a href="#!" class="text-gray-500 dark:text-dark-500 [&.active]:text-green-500" x-bind:class="{ 'active': activeLink === 'xl' }" @click.prevent="activeLink = 'xl'">XL</a>
                            <a href="#!" class="text-gray-500 dark:text-dark-500 [&.active]:text-green-500" x-bind:class="{ 'active': activeLink === '2xl' }" @click.prevent="activeLink = '2xl'">2XL</a>
                        </div>
                    </div>
                </div>
                <div class="my-5">
                    <div class="flex items-center w-32 p-1 text-center border border-gray-200 rounded-md dark:border-dark-800">
                        <button @click="if (count > 1 && count--) count--" class="flex items-center justify-center transition duration-200 ease-linear rounded-md text-primary-500 minus size-8 shrink-0 bg-primary-500/20 hover:text-primary-700"><i class="size-4" data-lucide="minus"></i></button>
                        <input type="text" x-model="count" class="h-8 p-0 text-center border-0 rounded-none form-input" readonly>
                        <button @click="count++" class="flex items-center justify-center transition duration-200 ease-linear rounded-md text-primary-500 plus size-8 shrink-0 bg-primary-500/20 hover:text-primary-700"><i class="size-4" data-lucide="plus"></i></button>
                    </div>
                </div>

                <div class="flex flex-wrap items-center gap-2 mb-5">
                    <button type="button" class="btn btn-red w-36">Buy Now</button>
                    <button type="button" class="btn btn-sub-gray w-36">Add to Cart</button>
                </div>

                <h6 class="mb-2">Available offers</h6>
                <div x-data="{ showMore: false }" class="mb-5">
                    <ul class="flex flex-col gap-2 text-gray-500 list-inside dark:text-dark-500 list-circle">
                        <li><span class="font-semibold">Bank Offer</span> Get ₹50 instant discount on first Domiex UPI txn on order of ₹200 and above <a href="#!" class="underline link link-red">T&C</a></li>
                        <li><span class="font-semibold">Bank Offer</span> 5% Cashback on Domiex Axis Bank Card <a href="#!" class="underline link link-red">T&C</a></li>
                        <li><span class="font-semibold">Special Price</span> Get extra ₹7000 off (price inclusive of cashback/coupon) <a href="#!" class="underline link link-red">T&C</a></li>
                        <li><span class="font-semibold">Freebie</span> Flat ₹1000 off on Cleartrip hotels booking along with 300 coins on booking <a href="#!" class="underline link link-red">T&C</a></li>

                        <!-- Additional items to be shown/hidden -->
                        <li x-show="showMore"><span class="font-semibold">Bank Offer</span> Additional Bank Offer details <a href="#!" class="underline link link-red">T&C</a></li>
                        <li x-show="showMore"><span class="font-semibold">Special Price</span> Additional Special Price details <a href="#!" class="underline link link-red">T&C</a></li>
                        <li x-show="showMore"><span class="font-semibold">Freebie</span> Additional Freebie details <a href="#!" class="underline link link-red">T&C</a></li>
                    </ul>

                    <!-- Show More/Show Less link -->
                    <a href="#!" class="inline-block mt-3 link link-red" @click.prevent="showMore = !showMore">
                        <span x-text="showMore ? 'Show Less' : 'Show More'"></span> <i :class="showMore ? 'ri-arrow-up-s-line' : 'ri-arrow-down-s-line'" class="inline-block size-4"></i>
                    </a>
                </div>

                <h6 class="mb-1">Product Overview</h6>
                <p class="mb-4 text-gray-500 dark:text-dark-500">We work with monitoring programmes to ensure compliance with our social, environmental and health and safety standards for our products. To assess compliance, we have developed a programme of audits and continuous improvement plans.</p>

                <div x-data="{ showMore: false }">
                    <table class="table table-sm flush">
                        <tr>
                            <th class="!border-0">Neck</th>
                            <td>U Neck</td>
                        </tr>
                        <tr>
                            <th class="!border-0">Sleeve Style</th>
                            <td>Sleeveless</td>
                        </tr>
                        <tr>
                            <th class="!border-0">Sleeve Length</th>
                            <td>Sleeveless</td>
                        </tr>
                        <tr>
                            <th class="!border-0">Fit</th>
                            <td>Regular</td>
                        </tr>
                        <tr>
                            <th class="!border-0">Fabric</th>
                            <td>Cotton Blend</td>
                        </tr>
                        <tr>
                            <th class="!border-0">Type</th>
                            <td>Cami Top</td>
                        </tr>
                        <tr>
                            <th class="!border-0">Pattern</th>
                            <td>Self Design</td>
                        </tr>
                        <tr x-show="showMore">
                            <th class="!border-0">Color</th>
                            <td>White</td>
                        </tr>
                        <tr x-show="showMore">
                            <th class="!border-0">Pack of</th>
                            <td>1</td>
                        </tr>
                        <tr x-show="showMore">
                            <th class="!border-0">Fabric Care</th>
                            <td>hand wash only</td>
                        </tr>
                        <tr x-show="showMore">
                            <th class="!border-0">Length</th>
                            <td>Crop</td>
                        </tr>
                        <tr x-show="showMore">
                            <th class="!border-0">Net Quantity</th>
                            <td>1</td>
                        </tr>
                    </table>
                    <!-- Show More/Show Less link -->
                    <a href="#!" class="inline-block mt-3 link link-red" @click.prevent="showMore = !showMore">
                        <span x-text="showMore ? 'Show Less' : 'Show More'"></span> <i :class="showMore ? 'ri-arrow-up-s-line' : 'ri-arrow-down-s-line'" class="inline-block size-4"></i>
                    </a>
                </div>
            </div>
        </div><!--end card-->
        <div class="card" >
            <div class="card-header">
                <div class="flex flex-wrap items-center gap-5">
                    <h6 class="card-title grow">Ratings & Reviews</h6>
                    <div class="shrink-0">
                        <button class="btn btn-primary" data-modal-target="addReviewModal" @click="handleModal('showAddReviewForm')"><i data-lucide="plus" class="inline-block mr-1 size-4"></i> New Review</button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div>
                    <div class="overflow-x-auto">
                        <table class="table flush whitespace-nowrap">
                            <tbody>
                                <template x-for="(review, index) in displayedReviews" :key="index">
                                    <tr>
                                        <td class="align-top">
                                            <div class="flex items-center gap-3">
                                                <img :src="review.image" alt="" class="rounded-md shrink-0 size-16">
                                                <div class="overflow-hidden grow">
                                                    <h6 class="mb-1 truncate">
                                                        <a href="#!" class="text-current link link-primary" x-text="review.userName"></a>
                                                    </h6>
                                                    <p class="mb-1 text-sm truncate" x-text="review.date"></p>
                                                    <p class="text-sm text-gray-500 truncate dark:text-dark-500">Location: <span x-text="review.location"></span></p>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="max-w-[550px]">
                                                <div class="flex items-center gap-2 mb-3">
                                                    <div class="text-yellow-500">
                                                        <template x-for="i in 5" :key="i">
                                                            <i :class="getStarClass(review.star, i)"></i>
                                                        </template>
                                                    </div>
                                                    <h6>(<span x-text="review.star"></span>)</h6>
                                                </div>
                                                <h6 x-text="review.title" class="mb-1"></h6>
                                                <p class="text-gray-500 whitespace-normal dark:text-dark-500" x-text="review.content"></p>
                                            </div>
                                        </td>
                                        <td class="align-top">
                                            <div class="flex items-center justify-end gap-3">
                                                <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown">
                                                    <button x-ref="button" x-on:click="toggle()" :aria-expanded="open.toString()" type="button" class="btn btn-icon-text btn-sub-gray btn-icon">
                                                        <i class="ri-more-2-fill"></i>
                                                    </button>
                                                    <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden">
                                                        <ul>
                                                            <li>
                                                                <a href="#!" data-modal-target="addReviewModal" @click="editReview(review.userName); close()" class="dropdown-item">
                                                                    <i class="align-middle ltr:mr-2 rtl:ml-2 ri-pencil-line"></i> <span>Edit</span>
                                                                </a>
                                                            </li>
                                                            <li>
                                                                <a href="#!" data-modal-target="deleteModal" @click=" deleteItem = review.userName" class="dropdown-item">
                                                                    <i class="align-middle ltr:mr-2 rtl:ml-2 ri-delete-bin-line"></i> <span>Delete</span>
                                                                </a>
                                                            </li>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                </template>
                            </tbody>
                        </table>
                    </div>
                    <div class="grid grid-cols-12 gap-5 mt-5 items-center">
                        <div class="col-span-12 sm:col-span-6">
                            <p class="text-gray-500 dark:text-dark-500">Showing <b x-text="showingStart"></b> - <b x-text="showingEnd"></b> of <b x-text="reviews.length"></b> Results</p>
                        </div>
                        <div class="col-span-12 sm:col-span-6">
                            <div class="pagination pagination-primary flex sm:!justify-end">
                                <button @click="prevPage()" :disabled="currentPage === 1" class="pagination-pre">
                                    <i data-lucide="chevron-left" class="mr-1 ltr:inline-block rtl:hidden size-5"></i>
                                    <i data-lucide="chevron-right" class="ml-1 ltr:hidden rtl:inline-block size-5"></i> 
                                    Prev
                                </button>
                                <template x-for="page in totalPages" :key="page">
                                    <button @click="gotoPage(page)" :class="{ 'active': currentPage === page }" class="pagination-item">
                                        <span x-text="page"></span>
                                    </button>
                                </template>
                                <button @click="nextPage()" :disabled="currentPage === totalPages" class="pagination-next">
                                    Next
                                    <i data-lucide="chevron-right" class="ml-1 rtl:hidden size-5 ltr:inline-block"></i>
                                    <i data-lucide="chevron-left" class="mr-1 rtl:inline-block ltr:hidden size-5"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 lg:col-span-4">
        <div class="sticky mb-5 top-24">
            <div class="card">
                <div class="card-body">
                    <div class="bg-gray-100 dark:bg-dark-850">
                        <div class="swiper previewImages" dir="ltr">
                            <div class="swiper-wrapper">
                                <div class="swiper-slide">
                                    <img src="assets/images/products/img-25.png" alt="">
                                </div>
                                <div class="swiper-slide">
                                    <img src="assets/images/products/img-26.png" alt="">
                                </div>
                                <div class="swiper-slide">
                                    <img src="assets/images/products/img-27.png" alt="">
                                </div>
                                <div class="swiper-slide">
                                    <img src="assets/images/products/img-28.png" alt="">
                                </div>
                                <div class="swiper-slide">
                                    <img src="assets/images/products/img-29.png" alt="">
                                </div>
                                <div class="swiper-slide">
                                    <img src="assets/images/products/img-30.png" alt="">
                                </div>
                            </div>
                            <div class="swiper-button-next after:content-['\ea6e'] after:!font-['remixicon'] after:bg-white dark:after:bg-dark-900 size-10 after:text-4xl after:size-10 after:flex after:justify-center after:align-center after:rounded-full"></div>
                            <div class="swiper-button-prev after:content-['\ea64'] after:!font-['remixicon'] after:bg-white dark:after:bg-dark-900 size-10 after:text-4xl after:size-10 after:flex after:justify-center after:align-center after:rounded-full"></div>
                            <div class="swiper-pagination"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card">
                <div class="flex flex-wrap items-center gap-3 card-body">
                    <div class="flex items-center justify-center border border-gray-200 rounded-full dark:border-dark-800 shrink-0 size-14">
                        <i data-lucide="store" class="text-sky-500 fill-sky-500/10"></i>
                    </div>
                    <div class="grow">
                        <h6 class="mb-1"><a href="#!"><span class="align-middle">SRBThemes</span> <i data-lucide="badge-check" class="inline-block ml-1 size-4 text-sky-500 fill-sky-500/10"></i></a></h6>
                        <p><i class="text-yellow-500 align-bottom ri-star-half-line"></i> 4.8</p>
                    </div>
                    <button class="btn btn-purple shrink-0">View Store</button>
                </div>
            </div>
            <div class="flex items-center gap-3 mb-5">
                <h6 class="grow">Recent Product</h6>
                <a href="apps-ecommerce-products-list.html" class="link link-primary">View All</a>
            </div>
            <div class="card">
                <div class="card-body">
                    <div class="relative p-5 bg-gray-100 dark:bg-dark-850">
                        <a href="#!" class="absolute z-10 flex items-center justify-center bg-white rounded-full link link-red size-10 shrink-0 top-5 ltr:right-5 rtl:left-5"><i class="text-lg ri-heart-line"></i></a>
                        <div class="swiper productSlider" dir="ltr">
                            <div class="swiper-wrapper">
                                <div class="swiper-slide">
                                    <img src="assets/images/products/img-06.png" alt="" class="w-3/4 mx-auto">
                                </div>
                                <div class="swiper-slide">
                                    <img src="assets/images/products/img-04.png" alt="" class="w-3/4 mx-auto">
                                </div>
                                <div class="swiper-slide">
                                    <img src="assets/images/products/img-05.png" alt="" class="w-3/4 mx-auto">
                                </div>
                            </div>
                            <div class="swiper-pagination"></div>
                        </div>
                    </div>
                    <div class="mt-5">
                        <h5 class="mb-2">$36.87</h5>
                        <h6 class="mb-1"><a href="#!" class="text-current link link-primary">Bra Lace Crop top</a></h6>
                        <p class="text-gray-500 dark:text-dark-500">Fashion</p>

                        <div class="flex flex-wrap items-center gap-2">
                            <div class="flex items-center gap-2 mt-3 grow" x-data="{ activeLink: 'primary' }">
                                <a href="#!" class="flex items-center justify-center text-white rounded-sm size-5 bg-primary-500 group/item" x-bind:class="{ 'active': activeLink === 'primary' }" @click.prevent="activeLink = 'primary'"><i data-lucide="check" class="size-4 hidden group-[&.active]/item:block"></i></a>
                                <a href="#!" class="flex items-center justify-center text-white bg-pink-500 rounded-sm size-5 group/item" x-bind:class="{ 'active': activeLink === 'pink' }" @click.prevent="activeLink = 'pink'"><i data-lucide="check" class="size-4 hidden group-[&.active]/item:block"></i></a>
                                <a href="#!" class="flex items-center justify-center text-white bg-green-500 rounded-sm size-5 group/item" x-bind:class="{ 'active': activeLink === 'green' }" @click.prevent="activeLink = 'green'"><i data-lucide="check" class="size-4 hidden group-[&.active]/item:block"></i></a>
                                <a href="#!" class="flex items-center justify-center text-white bg-red-500 rounded-sm size-5 group/item" x-bind:class="{ 'active': activeLink === 'red' }" @click.prevent="activeLink = 'red'"><i data-lucide="check" class="size-4 hidden group-[&.active]/item:block"></i></a>
                            </div>

                            <div class="flex flex-row-reverse items-center gap-2 mt-3 font-medium shrink-0" x-data="{ activeLink: 'primary' }">
                                <a href="#!" class="text-gray-500 dark:text-dark-500 [&.active]:text-green-500" x-bind:class="{ 'active': activeLink === 'primary' }" @click.prevent="activeLink = 'primary'">S</a>
                                <a href="#!" class="text-gray-500 dark:text-dark-500 [&.active]:text-green-500" x-bind:class="{ 'active': activeLink === 'pink' }" @click.prevent="activeLink = 'pink'">M</a>
                                <a href="#!" class="text-gray-500 dark:text-dark-500 [&.active]:text-green-500" x-bind:class="{ 'active': activeLink === 'green' }" @click.prevent="activeLink = 'green'">L</a>
                                <a href="#!" class="text-gray-500 dark:text-dark-500 [&.active]:text-green-500" x-bind:class="{ 'active': activeLink === 'sky' }" @click.prevent="activeLink = 'sky'">XL</a>
                                <a href="#!" class="text-gray-500 dark:text-dark-500 [&.active]:text-green-500" x-bind:class="{ 'active': activeLink === 'red' }" @click.prevent="activeLink = 'red'">2XL</a>
                            </div>
                        </div>

                        <div class="flex gap-2 mt-4">
                            <button type="button" class="w-full btn btn-primary">Buy Now</button>
                            <a href="#!" class="btn btn-sub-gray btn-icon shrink-0"><i data-lucide="shopping-basket" class="size-5"></i></a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div><!--end col-->
</div><!--end grid-->

<!--create review modals-->
<div id="addReviewModal" class="!hidden modal show" :class="{'show d-block': showAddReviewForm || showEditReviewForm }" x-show="showAddReviewForm || showEditReviewForm">
    <div class="modal-wrap modal-center">
        <div class="modal-header">
            <h6 x-text="showAddReviewForm ? 'Add review' : 'Edit review'"></h6>
            <button data-modal-close="addReviewModal" class="link link-red float-end"><i data-lucide="x" class="size-5"></i></button>
        </div>
        <div class="modal-content">
            <form action="#!">
                <div class="grid grid-cols-12 gap-5">
                    <div class="col-span-12">
                        <div class="flex flex-col justify-center gap-5">
                            <h6 class="text-center">Your Rating?</h6>
                            <div class="relative flex justify-center gap-3" >
                                <div class="flex justify-center w-10 h-2 transition-all duration-200 rounded-md cursor-pointer" x-bind:class="rating >= 1 ? 'bg-yellow-500' : 'bg-gray-200 dark:bg-dark-800'" x-on:click="rating = 1; " x-on:mouseover="hovering = 1" x-on:mouseleave="hovering = 0">
                                    <p class="mt-4 text-2xl pointer-events-none select-none" x-bind:class="rating == 1 || hovering == 1 ? '' : 'invisible' ">😒</p>
                                </div>
                                <div class="flex justify-center w-10 h-2 transition-all duration-200 rounded-md cursor-pointer" x-bind:class="rating >= 2 ? 'bg-yellow-500' : 'bg-gray-200 dark:bg-dark-800'" x-on:click="rating = 2" x-on:mouseover="hovering = 2" x-on:mouseleave="hovering = 0">
                                    <p class="mt-4 text-2xl pointer-events-none select-none" x-bind:class="rating == 2 || hovering == 2 ? '' : 'invisible' ">🤨</p>
                                </div>
                                <div class="flex justify-center w-10 h-2 transition-all duration-200 rounded-md cursor-pointer" x-bind:class="rating >= 3 ? 'bg-yellow-500' : 'bg-gray-200 dark:bg-dark-800'" x-on:click="rating = 3" x-on:mouseover="hovering = 3" x-on:mouseleave="hovering=0">
                                    <p class="mt-4 text-2xl pointer-events-none select-none" x-bind:class="rating == 3 || hovering == 3 ? '' : 'invisible' ">😊</p>
                                </div>
                                <div class="flex justify-center w-10 h-2 transition-all duration-200 rounded-md cursor-pointer" x-bind:class="rating >= 4 ? 'bg-yellow-500' : 'bg-gray-200 dark:bg-dark-800'" x-on:click="rating = 4" x-on:mouseover="hovering = 4" x-on:mouseleave="hovering = 0">
                                    <p class="mt-4 text-2xl pointer-events-none select-none" x-bind:class="rating == 4 || hovering == 4 ? '' : 'invisible' ">😚</p>
                                </div>
                                <div class="flex justify-center w-10 h-2 transition-all duration-200 rounded-md cursor-pointer" x-bind:class="rating >= 5 ? 'bg-yellow-500' : 'bg-gray-200 dark:bg-dark-800'" x-on:click="rating = 5" x-on:mouseover="hovering = 5" x-on:mouseleave="hovering = 0">
                                    <p class="mt-4 text-2xl pointer-events-none select-none" x-bind:class="rating == 5 || hovering == 5 ? '' : 'invisible' ">🥰</p>
                                </div>
                            </div>
                            <div class="mt-5">
                                <label for="rating" class="form-label">Rating Input:</label>
                                <input id="rating" x-model="reviewForm.star = rating " type="number" min="1" max="5" name="rating" class="form-input" @input="validateField('star', reviewForm.star < 1 || reviewForm.star > 5 , 'rating must be between 1 and 5 is required.');"/>
                                <span x-show="errors.star" class="text-sm text-red-500" x-text="errors.star"></span>
                            </div>
                        </div>
                    </div>
                    <div class="col-span-6">
                        <label for="userNameInput" class="form-label">User Name</label>
                        <input type="text" id="userNameInput" class="form-input" placeholder="User name" x-model="reviewForm.userName" @input="validateField('userName', reviewForm.userName, 'User name is required.')">
                        <span x-show="errors.userName" class="text-sm text-red-500" x-text="errors.userName"></span>
                    </div>
                    <div class="col-span-6">
                        <label for="createDateInput" class="form-label">Create Date</label>
                        <input type="text" placeholder="DD-MM-YYYY" id="createDateInput" x-model="reviewForm.date" class="form-input" data-provider="flatpickr" data-date-format="d M, Y"  placeholder="Order ID" @input="validateField('date' , reviewForm.date, 'Date is required.')">
                        <span x-show="errors.date" class="text-sm text-red-500" x-text="errors.date"></span>
                    </div>
                    <div class="col-span-12">
                        <label for="locationInput" class="form-label">Location</label>
                        <input type="text" id="locationInput" class="form-input" placeholder="Location" x-model="reviewForm.location" @input="validateField('location', reviewForm.location, 'Location is required.')">
                        <span x-show="errors.location" class="text-sm text-red-500" x-text="errors.location"></span>
                    </div>
                    <div class="col-span-12">
                        <label for="titleInput" class="form-label">Title</label>
                        <input type="text" id="titleInput" class="form-input" placeholder="Review title" x-model="reviewForm.title" @input="validateField('title', reviewForm.title, 'Title is required.')">
                        <span x-show="errors.title" class="text-sm text-red-500" x-text="errors.title"></span>
                    </div>
                    <div class="col-span-12">
                        <label for="writeReviewInput" class="form-label">Write your Content</label>
                        <textarea name="writeReviewInput" id="writeReviewInput" rows="3" class="h-auto form-input" x-model="reviewForm.content" placeholder="Enter your description" @change="validateField('content', reviewForm.content, 'Content is required.')"></textarea>
                        <span x-show="errors.content" class="text-sm text-red-500" x-text="errors.content"></span>
                    </div>
                </div>
            </form>
            <div class="flex justify-end gap-2 mt-5">
                <button type="button" class="btn btn-active-red" data-modal-close="addReviewModal">Cancel</button>
                <button type="submit" class="btn btn-primary" x-text="showAddReviewForm ? 'Add review' : 'Update review'" @click="submitForm()">Add Review</button>
            </div>
        </div>
    </div>
</div>

<!--delete modals-->
<div id="deleteModal" class="!hidden modal show">
    <div class="modal-wrap modal-xs modal-center">
        <div class="text-center modal-content p-7">
            <div class="flex items-center justify-center mx-auto mb-4 text-red-500 rounded-full bg-red-500/10 size-14 backdrop-blur-xl">
                <i data-lucide="trash-2" class="size-6"></i>
            </div>
            <h5 class="mb-4">Are you sure you want to delete this Review ?</h5>
            <div class="flex items-center justify-center gap-2">
                <button class="btn btn-red" @click="deleteProduct(deleteItem)" data-modal-close="deleteModal">Delete</button>
                <button data-modal-close="deleteModal" class="btn link link-primary">Cancel</button>
            </div>
        </div>
    </div>
</div><!--end-->
</div>
{{> partials/footer }}
</div>
{{> partials/vendor-scripts }}

<script src="assets/libs/swiper/swiper-bundle.min.js"></script>
<script type="module" src="assets/js/apps/ecommerce/product-overview.init.js"></script>
<script type="module" src="assets/js/main.js"></script>

</body>
</html>