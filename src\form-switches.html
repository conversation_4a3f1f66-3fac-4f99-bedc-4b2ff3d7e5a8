{{> partials/main }}

<head>

    {{> partials/title-meta title="Switches" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}

{{> partials/page-heading title="Switches" sub-title="Forms" }}

<div class="grid grid-cols-12 gap-x-space">
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Default</h6>
        </div>
        <div class="card-body">
            <div class="flex flex-wrap items-center gap-3">
                <label for="togglePrimary" class="switch-group">
                    <div class="relative">
                        <input type="checkbox" id="togglePrimary" class="sr-only peer" />
                        <div class="switch-wrapper"></div>
                        <div class="switch-dot peer-checked:translate-x-full rtl:peer-checked:-translate-x-full switch-primary"></div>
                    </div>
                </label>
                <label for="togglePurple" class="switch-group">
                    <div class="relative">
                        <input type="checkbox" id="togglePurple" class="sr-only peer" />
                        <div class="switch-wrapper"></div>
                        <div class="switch-dot peer-checked:translate-x-full rtl:peer-checked:-translate-x-full switch-purple"></div>
                    </div>
                </label>
                <label for="toggleGreen" class="switch-group">
                    <div class="relative">
                        <input type="checkbox" id="toggleGreen" class="sr-only peer" />
                        <div class="switch-wrapper"></div>
                        <div class="switch-dot peer-checked:translate-x-full rtl:peer-checked:-translate-x-full switch-green"></div>
                    </div>
                </label>
                <label for="toggleRed" class="switch-group">
                    <div class="relative">
                        <input type="checkbox" id="toggleRed" class="sr-only peer" />
                        <div class="switch-wrapper"></div>
                        <div class="switch-dot peer-checked:translate-x-full rtl:peer-checked:-translate-x-full switch-red"></div>
                    </div>
                </label>
                <label for="toggleYellow" class="switch-group">
                    <div class="relative">
                        <input type="checkbox" id="toggleYellow" class="sr-only peer" />
                        <div class="switch-wrapper"></div>
                        <div class="switch-dot peer-checked:translate-x-full rtl:peer-checked:-translate-x-full switch-yellow"></div>
                    </div>
                </label>
                <label for="toggleSky" class="switch-group">
                    <div class="relative">
                        <input type="checkbox" id="toggleSky" class="sr-only peer" />
                        <div class="switch-wrapper"></div>
                        <div class="switch-dot peer-checked:translate-x-full rtl:peer-checked:-translate-x-full switch-sky"></div>
                    </div>
                </label>
                <label for="togglePink" class="switch-group">
                    <div class="relative">
                        <input type="checkbox" id="togglePink" class="sr-only peer" />
                        <div class="switch-wrapper"></div>
                        <div class="switch-dot peer-checked:translate-x-full rtl:peer-checked:-translate-x-full switch-pink"></div>
                    </div>
                </label>
                <label for="toggleGray" class="switch-group">
                    <div class="relative">
                        <input type="checkbox" id="toggleGray" class="sr-only peer" />
                        <div class="switch-wrapper"></div>
                        <div class="switch-dot peer-checked:translate-x-full rtl:peer-checked:-translate-x-full switch-gray"></div>
                    </div>
                </label>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Soft Switches</h6>
        </div>
        <div class="card-body">
            <div class="flex flex-wrap items-center gap-3">
                <label for="togglePrimary1" class="switch-group switch-soft">
                    <div class="relative">
                        <input type="checkbox" id="togglePrimary1" class="sr-only peer" />
                        <div class="switch-wrapper"></div>
                        <div class="switch-dot peer-checked:translate-x-full rtl:peer-checked:-translate-x-full switch-primary"></div>
                    </div>
                </label>
                <label for="togglePurple2" class="switch-group switch-soft">
                    <div class="relative">
                        <input type="checkbox" id="togglePurple2" class="sr-only peer" />
                        <div class="switch-wrapper"></div>
                        <div class="switch-dot peer-checked:translate-x-full rtl:peer-checked:-translate-x-full switch-purple"></div>
                    </div>
                </label>
                <label for="toggleGreen3" class="switch-group switch-soft">
                    <div class="relative">
                        <input type="checkbox" id="toggleGreen3" class="sr-only peer" />
                        <div class="switch-wrapper"></div>
                        <div class="switch-dot peer-checked:translate-x-full rtl:peer-checked:-translate-x-full switch-green"></div>
                    </div>
                </label>
                <label for="toggleRed4" class="switch-group switch-soft">
                    <div class="relative">
                        <input type="checkbox" id="toggleRed4" class="sr-only peer" />
                        <div class="switch-wrapper"></div>
                        <div class="switch-dot peer-checked:translate-x-full rtl:peer-checked:-translate-x-full switch-red"></div>
                    </div>
                </label>
                <label for="toggleYellow5" class="switch-group switch-soft">
                    <div class="relative">
                        <input type="checkbox" id="toggleYellow5" class="sr-only peer" />
                        <div class="switch-wrapper"></div>
                        <div class="switch-dot peer-checked:translate-x-full rtl:peer-checked:-translate-x-full switch-yellow"></div>
                    </div>
                </label>
                <label for="toggleSky6" class="switch-group switch-soft">
                    <div class="relative">
                        <input type="checkbox" id="toggleSky6" class="sr-only peer" />
                        <div class="switch-wrapper"></div>
                        <div class="switch-dot peer-checked:translate-x-full rtl:peer-checked:-translate-x-full switch-sky"></div>
                    </div>
                </label>
                <label for="togglePink7" class="switch-group switch-soft">
                    <div class="relative">
                        <input type="checkbox" id="togglePink7" class="sr-only peer" />
                        <div class="switch-wrapper"></div>
                        <div class="switch-dot peer-checked:translate-x-full rtl:peer-checked:-translate-x-full switch-pink"></div>
                    </div>
                </label>
                <label for="toggleGray8" class="switch-group switch-soft">
                    <div class="relative">
                        <input type="checkbox" id="toggleGray8" class="sr-only peer" />
                        <div class="switch-wrapper"></div>
                        <div class="switch-dot peer-checked:translate-x-full rtl:peer-checked:-translate-x-full switch-gray"></div>
                    </div>
                </label>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Soft Colored Switches</h6>
        </div>
        <div class="card-body">
            <div class="flex flex-wrap items-center gap-3">
                <label for="togglePrimary11" class="switch-group switch-soft">
                    <div class="relative">
                        <input type="checkbox" id="togglePrimary11" class="sr-only peer" />
                        <div class="switch-wrapper peer-checked:!bg-primary-500/15"></div>
                        <div class="switch-dot peer-checked:translate-x-full rtl:peer-checked:-translate-x-full peer-checked:!bg-primary-500"></div>
                    </div>
                </label>
                <label for="togglePurple12" class="switch-group switch-soft">
                    <div class="relative">
                        <input type="checkbox" id="togglePurple12" class="sr-only peer" />
                        <div class="switch-wrapper peer-checked:!bg-purple-500/15"></div>
                        <div class="switch-dot peer-checked:translate-x-full rtl:peer-checked:-translate-x-full peer-checked:!bg-purple-500"></div>
                    </div>
                </label>
                <label for="toggleGreen13" class="switch-group switch-soft">
                    <div class="relative">
                        <input type="checkbox" id="toggleGreen13" class="sr-only peer" />
                        <div class="switch-wrapper peer-checked:!bg-green-500/15"></div>
                        <div class="switch-dot peer-checked:translate-x-full rtl:peer-checked:-translate-x-full peer-checked:!bg-green-500"></div>
                    </div>
                </label>
                <label for="toggleRed14" class="switch-group switch-soft">
                    <div class="relative">
                        <input type="checkbox" id="toggleRed14" class="sr-only peer" />
                        <div class="switch-wrapper peer-checked:!bg-red-500/15"></div>
                        <div class="switch-dot peer-checked:translate-x-full rtl:peer-checked:-translate-x-full peer-checked:!bg-red-500"></div>
                    </div>
                </label>
                <label for="toggleYellow15" class="switch-group switch-soft">
                    <div class="relative">
                        <input type="checkbox" id="toggleYellow15" class="sr-only peer" />
                        <div class="switch-wrapper peer-checked:!bg-yellow-500/15"></div>
                        <div class="switch-dot peer-checked:translate-x-full rtl:peer-checked:-translate-x-full peer-checked:!bg-yellow-500"></div>
                    </div>
                </label>
                <label for="toggleSky16" class="switch-group switch-soft">
                    <div class="relative">
                        <input type="checkbox" id="toggleSky16" class="sr-only peer" />
                        <div class="switch-wrapper peer-checked:!bg-sky-500/15"></div>
                        <div class="switch-dot peer-checked:translate-x-full rtl:peer-checked:-translate-x-full peer-checked:!bg-sky-500"></div>
                    </div>
                </label>
                <label for="togglePink17" class="switch-group switch-soft">
                    <div class="relative">
                        <input type="checkbox" id="togglePink17" class="sr-only peer" />
                        <div class="switch-wrapper peer-checked:!bg-pink-500/15"></div>
                        <div class="switch-dot peer-checked:translate-x-full rtl:peer-checked:-translate-x-full peer-checked:!bg-pink-500"></div>
                    </div>
                </label>
                <label for="toggleOarnge17" class="switch-group switch-soft">
                    <div class="relative">
                        <input type="checkbox" id="toggleOarnge17" class="sr-only peer" />
                        <div class="switch-wrapper peer-checked:!bg-orange-500/15"></div>
                        <div class="switch-dot peer-checked:translate-x-full rtl:peer-checked:-translate-x-full peer-checked:!bg-orange-500"></div>
                    </div>
                </label>
                <label for="toggleIndigo17" class="switch-group switch-soft">
                    <div class="relative">
                        <input type="checkbox" id="toggleIndigo17" class="sr-only peer" />
                        <div class="switch-wrapper peer-checked:!bg-indigo-500/15"></div>
                        <div class="switch-dot peer-checked:translate-x-full rtl:peer-checked:-translate-x-full peer-checked:!bg-indigo-500"></div>
                    </div>
                </label>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Solid Examples</h6>
        </div>
        <div class="card-body">
            <div class="flex flex-wrap items-center gap-3">
                <label for="togglePrimary21" class="switch-group">
                    <div class="relative">
                        <input type="checkbox" id="togglePrimary21" class="sr-only peer" />
                        <div class="switch-wrapper peer-checked:bg-primary-500 peer-checked:border-primary-500"></div>
                        <div class="switch-dot peer-checked:translate-x-full rtl:peer-checked:-translate-x-full peer-checked:bg-primary-50"></div>
                    </div>
                </label>
                <label for="togglePurple22" class="switch-group">
                    <div class="relative">
                        <input type="checkbox" id="togglePurple22" class="sr-only peer" />
                        <div class="switch-wrapper peer-checked:bg-purple-500 peer-checked:border-purple-500"></div>
                        <div class="switch-dot peer-checked:translate-x-full rtl:peer-checked:-translate-x-full peer-checked:bg-purple-50"></div>
                    </div>
                </label>
                <label for="toggleGreen23" class="switch-group">
                    <div class="relative">
                        <input type="checkbox" id="toggleGreen23" class="sr-only peer" />
                        <div class="switch-wrapper peer-checked:border-green-500 peer-checked:bg-green-500"></div>
                        <div class="switch-dot peer-checked:translate-x-full rtl:peer-checked:-translate-x-full peer-checked:bg-green-50"></div>
                    </div>
                </label>
                <label for="toggleRed24" class="switch-group">
                    <div class="relative">
                        <input type="checkbox" id="toggleRed24" class="sr-only peer" />
                        <div class="switch-wrapper peer-checked:border-red-500 peer-checked:bg-red-500"></div>
                        <div class="switch-dot peer-checked:translate-x-full rtl:peer-checked:-translate-x-full peer-checked:bg-red-50"></div>
                    </div>
                </label>
                <label for="toggleYellow25" class="switch-group">
                    <div class="relative">
                        <input type="checkbox" id="toggleYellow25" class="sr-only peer" />
                        <div class="switch-wrapper peer-checked:border-yellow-500 peer-checked:bg-yellow-500"></div>
                        <div class="switch-dot peer-checked:translate-x-full rtl:peer-checked:-translate-x-full peer-checked:bg-yellow-50"></div>
                    </div>
                </label>
                <label for="toggleSky26" class="switch-group">
                    <div class="relative">
                        <input type="checkbox" id="toggleSky26" class="sr-only peer" />
                        <div class="switch-wrapper peer-checked:border-sky-500 peer-checked:bg-sky-500"></div>
                        <div class="switch-dot peer-checked:translate-x-full rtl:peer-checked:-translate-x-full peer-checked:bg-sky-50"></div>
                    </div>
                </label>
                <label for="togglePink27" class="switch-group">
                    <div class="relative">
                        <input type="checkbox" id="togglePink27" class="sr-only peer" />
                        <div class="switch-wrapper peer-checked:border-pink-500 peer-checked:bg-pink-500"></div>
                        <div class="switch-dot peer-checked:translate-x-full rtl:peer-checked:-translate-x-full peer-checked:bg-pink-50"></div>
                    </div>
                </label>
                <label for="toggleOrange28" class="switch-group">
                    <div class="relative">
                        <input type="checkbox" id="toggleOrange28" class="sr-only peer" />
                        <div class="switch-wrapper peer-checked:border-orange-500 peer-checked:bg-orange-500"></div>
                        <div class="switch-dot peer-checked:translate-x-full rtl:peer-checked:-translate-x-full peer-checked:bg-orange-50"></div>
                    </div>
                </label>
                <label for="toggleIndigo29" class="switch-group">
                    <div class="relative">
                        <input type="checkbox" id="toggleIndigo29" class="sr-only peer" />
                        <div class="switch-wrapper peer-checked:border-indigo-500 peer-checked:bg-indigo-500"></div>
                        <div class="switch-dot peer-checked:translate-x-full rtl:peer-checked:-translate-x-full peer-checked:bg-indigo-50"></div>
                    </div>
                </label>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Text or Icon with Switches Examples</h6>
        </div>
        <div class="card-body">
            <div class="flex flex-wrap items-center gap-3">
                <label for="toggleYesNo1" class="switch-group switch-soft switch-text">
                    <div class="relative">
                        <input type="checkbox" id="toggleYesNo1" class="sr-only peer" checked>
                        <div class="switch-wrapper"></div>
                        <div class="switch-dot peer-checked:translate-x-full rtl:peer-checked:-translate-x-full switch-primary peer-checked:bg-primary-500 peer-checked:after:text-primary-50"></div>
                    </div>
                </label>
                <label for="toggleYesNo2" class="switch-group switch-soft switch-text">
                    <div class="relative">
                        <input type="checkbox" id="toggleYesNo2" class="sr-only peer" checked>
                        <div class="switch-wrapper peer-checked:!bg-purple-500/15"></div>
                        <div class="switch-dot peer-checked:translate-x-full rtl:peer-checked:-translate-x-full peer-checked:!bg-purple-500 peer-checked:after:text-purple-50"></div>
                    </div>
                </label>
                <label for="toggleIcons" class="switch-group switch-soft switch-text">
                    <div class="relative">
                        <input type="checkbox" id="toggleIcons" class="sr-only peer" checked>
                        <div class="switch-wrapper peer-checked:!bg-purple-500/15"></div>
                        <div class="switch-dot peer-checked:translate-x-full rtl:peer-checked:-translate-x-full peer-checked:!bg-purple-500 after:font-remix after:!content-['\ea64'] peer-checked:after:!content-['\ea6e'] peer-checked:after:text-purple-50"></div>
                    </div>
                </label>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Square Examples</h6>
        </div>
        <div class="card-body">
            <div class="flex flex-wrap items-center gap-3">
                <label for="toggleSquare" class="switch-group">
                    <div class="relative">
                        <input type="checkbox" id="toggleSquare" class="sr-only peer" checked />
                        <div class="!rounded-md switch-wrapper"></div>
                        <div class="!rounded-md switch-dot peer-checked:translate-x-full rtl:peer-checked:-translate-x-full switch-primary"></div>
                    </div>
                </label>
                <label for="toggleSquare2" class="switch-group switch-soft">
                    <div class="relative">
                        <input type="checkbox" id="toggleSquare2" class="sr-only peer" checked />
                        <div class="switch-wrapper !rounded-md"></div>
                        <div class="switch-dot peer-checked:!bg-primary-500 peer-checked:translate-x-full rtl:peer-checked:-translate-x-full !rounded-md"></div>
                    </div>
                </label>
                <label for="toggleSquare3" class="switch-group switch-soft">
                    <div class="relative">
                        <input type="checkbox" id="toggleSquare3" class="sr-only peer" checked />
                        <div class="switch-wrapper peer-checked:bg-primary-500/15 !rounded-md"></div>
                        <div class="switch-dot peer-checked:translate-x-full rtl:peer-checked:-translate-x-full peer-checked:!bg-primary-500 !rounded-md"></div>
                    </div>
                </label>
                <label for="toggleSquare4" class="switch-group">
                    <div class="relative">
                        <input type="checkbox" id="toggleSquare4" class="sr-only peer" checked />
                        <div class="!rounded-md switch-wrapper peer-checked:bg-primary-500 peer-checked:border-primary-500"></div>
                        <div class="!rounded-md switch-dot peer-checked:translate-x-full rtl:peer-checked:-translate-x-full peer-checked:bg-primary-50"></div>
                    </div>
                </label>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">3D Switches</h6>
        </div>
        <div class="card-body">
            <div class="flex flex-wrap items-center gap-3">
                <label for="toggle3D1" class="switch-group switch-3d">
                    <div class="relative">
                        <input type="checkbox" id="toggle3D1" class="sr-only peer" checked />
                        <div class="switch-wrapper"></div>
                        <div class="switch-dot peer-checked:translate-x-full rtl:peer-checked:-translate-x-full switch-primary"></div>
                    </div>
                </label>
                <label for="toggle3D2" class="switch-group switch-3d">
                    <div class="relative">
                        <input type="checkbox" id="toggle3D2" class="sr-only peer" />
                        <div class="switch-wrapper"></div>
                        <div class="switch-dot peer-checked:translate-x-full rtl:peer-checked:-translate-x-full switch-purple"></div>
                    </div>
                </label>
                <label for="toggle3D3" class="switch-group switch-3d">
                    <div class="relative">
                        <input type="checkbox" id="toggle3D3" class="sr-only peer" />
                        <div class="switch-wrapper"></div>
                        <div class="switch-dot peer-checked:translate-x-full rtl:peer-checked:-translate-x-full switch-green"></div>
                    </div>
                </label>
                <label for="toggle3D4" class="switch-group switch-3d">
                    <div class="relative">
                        <input type="checkbox" id="toggle3D4" class="sr-only peer" />
                        <div class="switch-wrapper"></div>
                        <div class="switch-dot peer-checked:translate-x-full rtl:peer-checked:-translate-x-full switch-red"></div>
                    </div>
                </label>
                <label for="toggle3D5" class="switch-group switch-3d">
                    <div class="relative">
                        <input type="checkbox" id="toggle3D5" class="sr-only peer" />
                        <div class="switch-wrapper"></div>
                        <div class="switch-dot peer-checked:translate-x-full rtl:peer-checked:-translate-x-full switch-yellow"></div>
                    </div>
                </label>
                <label for="toggle3D6" class="switch-group switch-3d">
                    <div class="relative">
                        <input type="checkbox" id="toggle3D6" class="sr-only peer" />
                        <div class="switch-wrapper"></div>
                        <div class="switch-dot peer-checked:translate-x-full rtl:peer-checked:-translate-x-full switch-sky"></div>
                    </div>
                </label>
                <label for="toggle3D7" class="switch-group switch-3d">
                    <div class="relative">
                        <input type="checkbox" id="toggle3D7" class="sr-only peer" />
                        <div class="switch-wrapper"></div>
                        <div class="switch-dot peer-checked:translate-x-full rtl:peer-checked:-translate-x-full switch-pink"></div>
                    </div>
                </label>
                <label for="toggle3D8" class="switch-group switch-3d">
                    <div class="relative">
                        <input type="checkbox" id="toggle3D8" class="sr-only peer" />
                        <div class="switch-wrapper"></div>
                        <div class="switch-dot peer-checked:translate-x-full rtl:peer-checked:-translate-x-full switch-orange"></div>
                    </div>
                </label>
                <label for="toggle3D9" class="switch-group switch-3d">
                    <div class="relative">
                        <input type="checkbox" id="toggle3D9" class="sr-only peer" />
                        <div class="switch-wrapper"></div>
                        <div class="switch-dot peer-checked:translate-x-full rtl:peer-checked:-translate-x-full switch-indigo"></div>
                    </div>
                </label>
            </div>
        </div>
    </div><!--end col-->
</div><!--end grid-->

</div>
{{> partials/footer }}
</div>

{{> partials/vendor-scripts }}
<script type="module" src="assets/js/main.js"></script>

</body>
</html>