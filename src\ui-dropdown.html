{{> partials/main }}

<head>

    {{> partials/title-meta title="Dropdown" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}

{{> partials/page-heading title="Dropdown" sub-title="UI" }}

<div class="grid grid-cols-12 gap-x-space">
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Base Dropdown</h6>
        </div>
        <div class="card-body">
            <div class="flex flex-wrap items-center gap-4">
                <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown">
                    <button x-ref="button" x-on:click="toggle()" :aria-expanded="open.toString()" type="button" class="flex items-center gap-2 btn-sub-gray btn">
                        Dropdown Options
                        <svg :class="{ 'transform rotate-180': open }" class="size-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                        </svg>
                    </button>

                    <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden">
                        <a href="#" class="dropdown-item">
                            New Task
                        </a>

                        <a href="#" class="dropdown-item">
                            Edit Task
                        </a>

                        <a href="#" class="dropdown-item">
                            <span class="text-red-500">Delete Task</span>
                        </a>
                    </div>
                </div>

                <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown">
                    <button x-ref="button" x-on:click="toggle()" :aria-expanded="open.toString()" type="button" class="flex items-center gap-2">
                        Dropdown Link Options
                        <svg :class="{ 'transform rotate-180': open }" class="text-gray-500 transition-transform duration-300 dark:text-dark-500 size-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                        </svg>
                    </button>

                    <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden">
                        <a href="#" class="dropdown-item">
                            New Task
                        </a>

                        <a href="#" class="dropdown-item">
                            Edit Task
                        </a>

                        <a href="#" class="dropdown-item">
                            <span class="text-red-500">Delete Task</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div><!--Base-->

    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Position Dropdown</h6>
        </div>
        <div class="card-body">
            <div class="flex flex-wrap items-center gap-4">
                <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown">
                    <button x-ref="button" x-on:click="toggle()" :aria-expanded="open.toString()" type="button" class="flex items-center gap-2 btn-sub-gray btn">
                        Dropdown Options
                        <svg :class="{ 'transform rotate-180': open }" class="text-gray-400 transition-transform duration-300 size-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                        </svg>
                    </button>

                    <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden">
                        <a href="#" class="dropdown-item">
                            New Task
                        </a>

                        <a href="#" class="dropdown-item">
                            Edit Task
                        </a>

                        <a href="#" class="dropdown-item">
                            <span class="text-red-500">Delete Task</span>
                        </a>
                    </div>
                </div>

                <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown shrink-0" class="dropdown">
                    <button x-ref="button" x-on:click="toggle()" :aria-expanded="open.toString()" type="button" class="flex items-center gap-2 btn-sub-gray btn">
                        Right Dropdown
                        <svg :class="{ 'transform rotate-180': open }" class="text-gray-400 transition-transform duration-300 size-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                        </svg>
                    </button>

                    <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden" dropdown-position="right">
                        <a href="#" class="dropdown-item">
                            New Task
                        </a>

                        <a href="#" class="dropdown-item">
                            Edit Task
                        </a>

                        <a href="#" class="dropdown-item">
                            <span class="text-red-500">Delete Task</span>
                        </a>
                    </div>
                </div>

                <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown">
                    <button x-ref="button" x-on:click="toggle()" :aria-expanded="open.toString()" type="button" class="flex items-center gap-2 btn-sub-gray btn">
                        Right Top Dropdown
                        <svg :class="{ 'transform rotate-180': open }" class="text-gray-400 transition-transform duration-300 size-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                        </svg>
                    </button>

                    <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden" dropdown-position="right-top">
                        <a href="#" class="dropdown-item">
                            New Task
                        </a>

                        <a href="#" class="dropdown-item">
                            Edit Task
                        </a>

                        <a href="#" class="dropdown-item">
                            <span class="text-red-500">Delete Task</span>
                        </a>
                    </div>
                </div>

                <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown">
                    <button x-ref="button" x-on:click="toggle()" :aria-expanded="open.toString()" type="button" class="flex items-center gap-2 btn-sub-gray btn">
                        Left Top Dropdown Options
                        <svg :class="{ 'transform rotate-180': open }" class="text-gray-400 transition-transform duration-300 size-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                        </svg>
                    </button>
                    <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden" dropdown-position="left-top">
                        <a href="#" class="dropdown-item">
                            New Task
                        </a>

                        <a href="#" class="dropdown-item">
                            Edit Task
                        </a>

                        <a href="#" class="dropdown-item">
                            <span class="text-red-500">Delete Task</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div><!--Position-->

    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Colored Dropdown</h6>
        </div>
        <div class="card-body">
            <div class="flex flex-wrap items-center gap-4">
                <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown">
                    <button x-ref="button" x-on:click="toggle()" :aria-expanded="open.toString()" type="button" class="flex items-center gap-2 btn-primary btn">
                        Primary Option
                        <svg :class="{ 'transform rotate-180': open }" class="transition-transform duration-300 size-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                        </svg>
                    </button>

                    <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden" dropdown-position="left">
                        <a href="#" class="dropdown-item dropdown-primary">
                            New Task
                        </a>

                        <a href="#" class="dropdown-item dropdown-primary">
                            Edit Task
                        </a>

                        <a href="#" class="dropdown-item dropdown-primary">
                            Delete Task
                        </a>
                    </div>
                </div>
                <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown">
                    <button x-ref="button" x-on:click="toggle()" :aria-expanded="open.toString()" type="button" class="flex items-center gap-2 btn-green btn">
                        Green Option
                        <svg :class="{ 'transform rotate-180': open }" class="transition-transform duration-300 size-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                        </svg>
                    </button>

                    <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden">
                        <a href="#" class="dropdown-item dropdown-green">
                            New Task
                        </a>

                        <a href="#" class="dropdown-item dropdown-green">
                            Edit Task
                        </a>

                        <a href="#" class="dropdown-item dropdown-green">
                            Delete Task
                        </a>
                    </div>
                </div>
                <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown">
                    <button x-ref="button" x-on:click="toggle()" :aria-expanded="open.toString()" type="button" class="flex items-center gap-2 btn-purple btn">
                        Purple Option
                        <svg :class="{ 'transform rotate-180': open }" class="transition-transform duration-300 size-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                        </svg>
                    </button>

                    <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden">
                        <a href="#" class="dropdown-item dropdown-purple">
                            New Task
                        </a>

                        <a href="#" class="dropdown-item dropdown-purple">
                            Edit Task
                        </a>

                        <a href="#" class="dropdown-item dropdown-purple">
                            Delete Task
                        </a>
                    </div>
                </div>
                <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown">
                    <button x-ref="button" x-on:click="toggle()" :aria-expanded="open.toString()" type="button" class="flex items-center gap-2 btn-yellow btn">
                        Yellow Option
                        <svg :class="{ 'transform rotate-180': open }" class="transition-transform duration-300 size-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                        </svg>
                    </button>

                    <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden">
                        <a href="#" class="dropdown-item dropdown-yellow">
                            New Task
                        </a>

                        <a href="#" class="dropdown-item dropdown-yellow">
                            Edit Task
                        </a>

                        <a href="#" class="dropdown-item dropdown-yellow">
                            Delete Task
                        </a>
                    </div>
                </div>
                <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown">
                    <button x-ref="button" x-on:click="toggle()" :aria-expanded="open.toString()" type="button" class="flex items-center gap-2 btn-sky btn">
                        Sky Option
                        <svg :class="{ 'transform rotate-180': open }" class="transition-transform duration-300 size-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                        </svg>
                    </button>

                    <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden">
                        <a href="#" class="dropdown-item dropdown-sky">
                            New Task
                        </a>

                        <a href="#" class="dropdown-item dropdown-sky">
                            Edit Task
                        </a>

                        <a href="#" class="dropdown-item dropdown-sky">
                            Delete Task
                        </a>
                    </div>
                </div>
                <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown">
                    <button x-ref="button" x-on:click="toggle()" :aria-expanded="open.toString()" type="button" class="flex items-center gap-2 btn-red btn">
                        Red Option
                        <svg :class="{ 'transform rotate-180': open }" class="transition-transform duration-300 size-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                        </svg>
                    </button>

                    <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden">
                        <a href="#" class="dropdown-item dropdown-red">
                            New Task
                        </a>

                        <a href="#" class="dropdown-item dropdown-red">
                            Edit Task
                        </a>

                        <a href="#" class="dropdown-item dropdown-red">
                            Delete Task
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div><!--Colored-->

    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Profile Dropdown</h6>
        </div>
        <div class="card-body">
            <div class="flex flex-wrap items-center gap-4">
                <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown">
                    <button x-ref="button" x-on:click="toggle()" :aria-expanded="open.toString()" type="button" class="flex items-center gap-2 btn btn-primary">
                        Profile
                        <svg :class="{ 'transform rotate-180': open }" class="transition-transform duration-300 size-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                        </svg>
                    </button>

                    <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="dropdown-menu !fixed !w-72" dropdown-position="right">
                        <div class="flex items-center gap-3 p-4 border-b border-gray-200 dark:border-dark-800">
                            <img src="assets/images/avatar/user-17.png" alt="" class="rounded-full size-9 shrink-0">
                            <div class="overflow-hidden grow">
                                <h6>Sophia Mia</h6>
                                <a href="#!" class="truncate link link-primary"><EMAIL></a>
                            </div>
                            <div class="shrink-0">
                                <span class="badge badge-pink">Pro</span>
                            </div>
                        </div>
                        <div class="p-4 *:flex space-y-4 *:items-center">
                            <a href="#!" class="link link-primary"><i data-lucide="user-round" class="size-4 ltr:mr-1 rtl:ml-1"></i> Account Settings</a>
                            <a href="#!" class="link link-primary"><i data-lucide="venetian-mask" class="size-4 ltr:mr-1 rtl:ml-1"></i> Go Incognito</a>
                            <a href="#!" class="link link-primary"><i data-lucide="headset" class="size-4 ltr:mr-1 rtl:ml-1"></i> Help Center</a>
                            <a href="#!" class="link link-primary">
                                <i data-lucide="moon" class="size-4 ltr:mr-1 rtl:ml-1"></i>
                                <span class="grow">Dark Mode</span>
                                <label for="darkModeProfile" class="switch-group shrink-0">
                                    <div class="relative">
                                        <input type="checkbox" id="darkModeProfile" class="sr-only peer" />
                                        <div class="switch-wrapper"></div>
                                        <div class="switch-dot peer-checked:translate-x-full rtl:peer-checked:-translate-x-full switch-primary"></div>
                                    </div>
                                </label>
                            </a>
                            <a href="#!" class="link link-primary"><i data-lucide="phone" class="size-4 ltr:mr-1 rtl:ml-1"></i> Contact Us</a>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div><!--Base-->
</div>

</div>
{{> partials/footer }}
</div>


{{> partials/vendor-scripts }}

<script type="module" src="assets/js/main.js"></script>

</body>
</html>