{{> partials/main }}

<head>

    {{> partials/title-meta title="Create Invoice" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}
{{> partials/page-heading title="Create Invoice" sub-title="Invoices" }}
<div x-data="productTable()">
<div class="card">
    <div class="card-header">
        <div class="items-center gap-3 md:flex">
            <h6 class="grow mb-3 md:mb-0">Create Invoice</h6>
            <div class="flex flex-wrap items-center gap-2 shrink-0">
                <button class="btn btn-sub-gray" @click="resetForm()"><i data-lucide="rotate-ccw" class="inline-block size-4"></i> Reset</button>
                <button class="btn btn-sub-green"><i data-lucide="printer" class="inline-block size-4"></i> Print Invoice</button>
                <button class="btn btn-primary" @click="createInvoice()" >Save Invoice</button>
            </div>
        </div>
    </div>
    <div class="card-body">
        <form action="#!">
            <h6 class="mb-3">Company Information</h6>
            <div class="grid grid-cols-12 gap-5" >
                <div class="col-span-12">
                    <h6 class="form-label">Company Logo</h6>
                    <div>
                        <label for="logo">
                            <span class="inline-flex items-center justify-center w-full h-32 overflow-hidden bg-gray-100 border border-gray-200 rounded-md cursor-pointer dark:bg-dark-850 dark:border-dark-800">
                                <img x-show="imageUrl" :src="imageUrl" class="object-cover h-24">
                                <span x-show="!imageUrl" class="flex flex-col items-center text-gray-500 dark:text-dark-500">
                                    <i data-lucide="upload"></i>
                                    <span class="block mt-3">Upload Your Company Logo</span>
                                </span>
                            </span>
                        </label>
                        <div class="hidden">
                            <label class="block">
                                <span class="sr-only">Choose profile photo</span>
                                <input type="file" name="logo" id="logo" @change="fileChosen" class="block w-full text-sm file:rounded-md focus:outline-0 text-slate-500 file:mr-4 file:py-2 file:px-4 file:border-0 file:text-sm file:font-semibold file:bg-violet-50 file:text-violet-700 hover:file:bg-violet-100 " />
                            </label>
                        </div>
                    </div>
                </div>
                <div class="col-span-12 md:col-span-6 lg:col-span-4">
                    <label for="companyNameInput" class="form-label">Company Name</label>
                    <input type="text" id="companyNameInput" x-model="companyName" @input="validateCompanyName" class="form-input">
                    <p x-show="companyNameError" class="text-red-500" x-text="companyNameError"></p>
                </div>
                <div class="col-span-12 md:col-span-6 lg:col-span-4">
                    <label for="companyEmailInput" class="form-label">Email</label>
                    <input type="email" id="companyEmailInput" x-model="companyEmail" @input="validateEmail" class="form-input">
                    <p x-show="emailError" class="text-red-500" x-text="emailError"></p>
                </div>
                <div class="col-span-12 md:col-span-6 lg:col-span-4">
                    <label for="companyPhoneNumberInput" class="form-label">Phone Number</label>
                    <input type="text" id="companyPhoneNumberInput" x-model="companyPhone" @input="validatePhone" class="form-input">
                    <p x-show="phoneError" class="text-red-500" x-text="phoneError"></p>
                </div>
                <div class="col-span-12 md:col-span-6 lg:col-span-3">
                    <label for="invoiceIDInput" class="form-label">Invoice ID</label>
                    <input type="text" id="invoiceIDInput" class="form-input" disabled value="#PEI-15485">
                </div>
                <div class="col-span-12 md:col-span-6 lg:col-span-3">
                    <label for="invoiceDateInput" class="form-label">Invoice Date</label>
                    <input type="date" id="invoiceDateInput" x-model="invoiceDate" @input="validateInvoiceDate" class="form-input">
                    <p x-show="invoiceDateError" class="text-red-500" x-text="invoiceDateError"></p>
                </div>
                <div class="col-span-12 md:col-span-6 lg:col-span-3">
                    <label for="dueDateInput" class="form-label">Payment Due Date</label>
                    <input type="date" id="dueDateInput" x-model="dueDate" @input="validateDueDate" class="form-input">
                    <p x-show="dueDateError" class="text-red-500" x-text="dueDateError"></p>
                </div>
                <div class="col-span-12 md:col-span-6 lg:col-span-3">
                    <label for="invoiceStatus" class="form-label">Invoice Status</label>
                    <div id="invoiceStatus" placeholder="Invoice Type" x-model="status" @change="validateStatus"></div>
                    <p x-show="statusError" class="text-red-500" x-text="statusError"></p>
                </div>
            </div>
            
            <h6 class="mt-5 mb-3">Products Info</h6>

            <div class="overflow-x-auto">
                <table class="table flush" >
                    <tbody>
                        <tr>
                            <th class="w-12 whitespace-nowrap">#</th>
                            <th class="whitespace-nowrap">Product Name</th>
                            <th class="w-52 whitespace-nowrap">Quantity</th>
                            <th class="w-52 whitespace-nowrap">Unit Price</th>
                            <th class="w-52 whitespace-nowrap">Discount (%)</th>
                            <th class="w-52 whitespace-nowrap">Total Amount</th>
                        </tr>
                        <template x-for="(product, index) in products" :key="index">
                            <tr>
                                <td x-text="index + 1"></td>
                                <td>
                                    <input type="text" x-model="product.name" @input="validateProductName(index)" class="form-input" placeholder="Enter product name">
                                    <p x-show="product.nameError" class="text-red-500" x-text="product.nameError"></p>
                                    <input type="text" x-model="product.category" @input="validateCategory(index)" class="mt-2 mb-3 form-input" placeholder="Category">
                                    <p x-show="product.categoryError" class="text-red-500" x-text="product.categoryError"></p>
                                    <a href="#!" @click="deleteItem(index)" class="link link-red"><i class="align-baseline ri-delete-bin-line"></i> Delete Item</a>
                                </td>
                                <td>
                                    <div x-data="{ count: product.quantity }">
                                        <div class="flex input-spin-group">
                                            <button @click.prevent="if (count > 0) count--; product.quantity = count; calculateTotal(product)" class="input-spin-minus"><i class="ri-subtract-line"></i></button>
                                            <input type="text" x-model="count" class="input-spin form-input" readonly>
                                            <button @click.prevent="count++; product.quantity = count; calculateTotal(product)" class="input-spin-plus"><i class="ri-add-line"></i></button>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <input type="number" x-model="product.unitPrice" @input="validateUnitPrice(index); calculateTotal(product)" class="form-input" placeholder="$0.00">
                                    <p x-show="product.unitPriceError" class="text-red-500" x-text="product.unitPriceError"></p>
                                </td>
                                <td>
                                    <input type="number" x-model="product.discountPercentage" @input="validateDiscountPercentage(index); calculateTotal(product)" class="form-input" placeholder="0%">
                                    <p x-show="product.discountError" class="text-red-500" x-text="product.discountError"></p>
                                </td>
                                <td>
                                    <input type="text" x-model="product.totalAmount" class="form-input" readonly placeholder="$0.00">
                                </td>
                            </tr>
                        </template>
                        <tr>
                            <td colspan="6">
                                <button @click="addItem" class="btn btn-primary"><i class="align-bottom ri-add-line"></i> Add Item</button>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="5" class="text-right">Sub Total</td>
                            <td>
                                <input type="text" x-model="subtotal" class="form-input" readonly placeholder="$0.00">
                            </td>
                        </tr>
                        <tr>
                            <td colspan="5" class="text-right">Vat Amount (6%)</td>
                            <td>
                                <input type="text" x-model="vatAmount" class="form-input" readonly placeholder="$0.00">
                            </td>
                        </tr>
                        <tr>
                            <td colspan="5" class="text-right">Discount (10%)</td>
                            <td>
                                <input type="number" x-model="additionalDiscount" @input="validateAdditionalDiscount" class="form-input" placeholder="10%">
                            </td>
                        </tr>
                        <tr>
                            <td colspan="5" class="text-right">Shipping Charge</td>
                            <td>
                                <input type="text" x-model="shippingCharge" @input="calculateGrandTotal" class="form-input" placeholder="$0.00">
                            </td>
                        </tr>
                        <tr>
                            <td colspan="5" class="text-right">Total Amount</td>
                            <td>
                                <input type="text" x-model="grandTotal" class="form-input" readonly placeholder="$0.00">
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <h6 class="mt-5 mb-3">Payment Method</h6>
            <div class="grid grid-cols-12 gap-5">
                <div class="col-span-12">
                    <label for="cardHolderNameInput" class="form-label">Card Holder Name</label>
                    <input type="text" id="cardHolderNameInput" x-model="cardHolderName" @input="validateCardHolderName" class="form-input" placeholder="Card holder name">
                    <p x-show="cardHolderNameError" class="text-red-500" x-text="cardHolderNameError"></p>
                </div>
                <div class="col-span-12 md:col-span-6 lg:col-span-4">
                    <label for="cardNumberInput" class="form-label">Card Number</label>
                    <input type="text" id="cardNumberInput" x-model="cardNumber" @input="formatCardNumber()" class="form-input" placeholder="0000 0000 0000 0000" maxlength="19">
                    <p x-show="cardNumberError" class="text-red-500" x-text="cardNumberError"></p>
                </div>
                <div class="col-span-12 md:col-span-6 lg:col-span-4">
                    <label for="expiryInput" class="form-label">Expiry Date</label>
                    <input type="text" id="expiryInput" x-model="expiryDate" @input="formatExpiryDate" class="form-input" placeholder="MM/YYYY" maxlength="7">
                    <p x-show="expiryDateError" class="text-red-500" x-text="expiryDateError"></p>
                </div>
                <div class="col-span-12 md:col-span-6 lg:col-span-4">
                    <label for="cvvInput" class="form-label">CVV</label>
                    <input type="text" id="cvvInput" x-model="cvv" @input="validateCVV" class="form-input" placeholder="000" maxlength="3">
                    <p x-show="cvvError" class="text-red-500" x-text="cvvError"></p>
                </div>
            </div>
            <div class="mt-5">
                <label for="textareaInput2" class="form-label">Terms & Conditions</label>
                <textarea name="textareaInput2" id="textareaInput2" rows="3" class="h-auto form-input" placeholder="Enter your terms"></textarea>
            </div>

            <div class="mt-5">
                <div class="flex flex-wrap items-center justify-end gap-2">
                    <button class="btn btn-sub-gray" @click="resetForm()"><i data-lucide="rotate-ccw" class="inline-block size-4"></i> Reset</button>
                    <button @click="createInvoice()" class="btn btn-primary" >Save Invoice</button>
                </div>
            </div>
        </form>
    </div>
</div>
</div>
</div>
{{> partials/footer }}
</div>

{{> partials/vendor-scripts }}

<script type="module" src="assets/js/apps/invoices/create.init.js"></script>

<script type="module" src="assets/js/main.js"></script>

</body>
</html>