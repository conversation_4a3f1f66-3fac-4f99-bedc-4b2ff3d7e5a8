{{> partials/main }}

<head>

    {{> partials/title-meta title="Reset Password" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

<div class="relative">
    <div class="grid grid-cols-12">
        <div class="relative col-span-12 py-8 overflow-hidden bg-gray-100 dark:bg-dark-850 lg:min-h-screen lg:col-span-6 md:p-9 xl:p-12">
            <div class="absolute bottom-0 w-32 -rotate-45 -top-64 -right-8 bg-gray-200/20 dark:bg-dark-800/20"></div>
            <div class="p-4">
                <a href="index.html">
                    <img src="assets/images/main-logo.png" alt="" class="h-8 dark:hidden">
                    <img src="assets/images/logo-white.png" alt="" class="hidden h-8 dark:inline-block">
                </a>
                <h1 class="max-w-lg mt-8 text-2xl font-normal leading-tight capitalize md:leading-tight md:text-4xl">The most straightforward way to manage your projects</h1>

                <img src="assets/images/others/auth-creative.png" alt="" class="mt-9 xl:mt-0 relative xl:absolute xl:scale-110 rounded-lg shadow-lg xl:top-[315px] xl:left-[115px]">
            </div>
        </div>
        <div class="flex items-center col-span-12 lg:min-h-screen lg:col-span-6 py-9 md:py-12">
            <div class="grid w-full grid-cols-12">
                <div class="col-span-12 mx-4 mb-0 2xl:col-span-8 2xl:col-start-3 md:mx-12 card">
                    <div class="md:p-10 card-body">
                        <h4 class="mb-2 font-bold leading-relaxed text-center text-transparent drop-shadow-lg ltr:bg-gradient-to-r rtl:bg-gradient-to-l from-primary-500 vie-purple-500 to-pink-500 bg-clip-text">Set your new password</h4>
                        <p class="mb-5 text-center text-gray-500 dark:text-dark-500">Ensure that your new password is different from any passwords you've previously used.</p>
                        <form @submit.prevent="validateForm" x-data="passwordForm()" >
                            <div  class="grid grid-cols-12 gap-4 mt-5">
                                <div class="col-span-12">
                                    <div x-data="{ show: false }">
                                        <label for="passwordInput" class="form-label">Password</label>
                                        <div class="relative">
                                            <input type="password" id="passwordInput" x-bind:type="show ? 'text' : 'password'" class="ltr:pr-8 rtl:pl-8 form-input" placeholder="Enter your password"
                                                   x-model="password">
                                            <button type="button" @click="show = !show" class="absolute inset-y-0 flex items-center text-gray-500 dark:text-dark-500 ltr:right-3 rtl:left-3 focus:outline-hidden">
                                                <i data-lucide="eye" x-show="show" class="size-5"></i>
                                                <i data-lucide="eye-off" x-show="!show" class="size-5"></i>
                                            </button>
                                        </div>
                                        <p x-show="errors.password" class="text-sm text-red-500" x-text="errors.password"></p>
                                    </div>
                                </div>
                                <div class="col-span-12">
                                    <div x-data="{ show: false }">
                                        <label for="confirmPasswordInput" class="form-label">Confirm Password</label>
                                        <div class="relative">
                                            <input type="password" id="confirmPasswordInput" x-bind:type="show ? 'text' : 'password'" class="ltr:pr-8 rtl:pl-8 form-input" placeholder="Enter your confirm password"
                                                   x-model="confirmPassword">
                                            <button type="button" @click="show = !show" class="absolute inset-y-0 flex items-center text-gray-500 dark:text-dark-500 ltr:right-3 rtl:left-3 focus:outline-hidden">
                                                <i data-lucide="eye" x-show="show" class="size-5"></i>
                                                <i data-lucide="eye-off" x-show="!show" class="size-5"></i>
                                            </button>
                                        </div>
                                        <p x-show="errors.confirmPassword" class="text-sm text-red-500" x-text="errors.confirmPassword"></p>
                                    </div>
                                </div>
                                <div class="col-span-12">
                                    <button type="submit" class="w-full px-4 py-2 text-white rounded-md bg-primary-500 hover:bg-primary-600">Set Password</button>
                                    <p class="mt-3 text-center text-gray-500 dark:text-dark-500">Return to the <a href="auth-signin-basic.html" class="font-medium underline link link-primary"><span class="align-middle">Sign In</span> <i data-lucide="move-right" class="inline-block ml-1 size-4"></i></a></p>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{{> partials/vendor-scripts }}

<script type="module" src="assets/js/main.js"></script>
<script type="module" src="assets/js/auth/reset-password-validation.js"></script>

</body>
</html>