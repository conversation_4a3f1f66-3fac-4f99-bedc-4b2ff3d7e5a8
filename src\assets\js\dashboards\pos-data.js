// POS System Data and State Management
export const posData = {
    // Categories data
    categories: [
        {
            id: 'burger',
            name: 'Burger',
            icon: 'assets/images/pos/vegetable.png',
            itemCount: 10
        },
        {
            id: 'coffee',
            name: 'Coffee & Tea',
            icon: 'assets/images/pos/coffee.png',
            itemCount: 19
        },
        {
            id: 'drink',
            name: 'Drinks',
            icon: 'assets/images/pos/beer.png',
            itemCount: 14
        },
        {
            id: 'rice',
            name: 'Rice Dish',
            icon: 'assets/images/pos/rice-dish.png',
            itemCount: 26
        },
        {
            id: 'pasta',
            name: 'Pasta',
            icon: 'assets/images/pos/noodles.png',
            itemCount: 8
        },
        {
            id: 'pizza',
            name: 'Pizza',
            icon: 'assets/images/pos/pizza.png',
            itemCount: 33
        },
        {
            id: 'salad',
            name: 'Salad',
            icon: 'assets/images/pos/greek-salad.png',
            itemCount: 11
        }
    ],

    // Products data
    products: [
        {
            id: 1,
            category: 'coffee',
            name: 'Caramel Macchiato Dream Swirl',
            image: 'assets/images/pos/img-01.png',
            price: 11.99,
            available: 44,
            sold: 84,
            hasSize: true,
            hasSugarLevel: true
        },
        {
            id: 2,
            category: 'coffee',
            name: 'Coffee milk Latte Tea Cappuccino',
            image: 'assets/images/pos/img-02.png',
            price: 14.98,
            available: 48,
            sold: 27,
            hasSize: true,
            hasSugarLevel: true
        },
        {
            id: 3,
            category: 'coffee',
            name: 'Pumpkin Spice Autumn Classic',
            image: 'assets/images/pos/img-03.png',
            price: 4.99,
            available: 35,
            sold: 42,
            hasSize: true,
            hasSugarLevel: false
        },
        {
            id: 4,
            category: 'coffee',
            name: 'Peppermint Mocha Holiday Cheer',
            image: 'assets/images/pos/img-04.png',
            price: 12.99,
            available: 30,
            sold: 56,
            hasSize: true,
            hasSugarLevel: true
        },
        {
            id: 5,
            category: 'pizza',
            name: 'Garden Veggie Harvest Delight',
            image: 'assets/images/pos/img-05.png',
            price: 13.99,
            available: 25,
            sold: 38,
            hasSize: true,
            hasSugarLevel: false
        },
        {
            id: 6,
            category: 'burger',
            name: 'Triple Cheese Bacon Supreme',
            image: 'assets/images/pos/img-06.png',
            price: 15.99,
            available: 20,
            sold: 45,
            hasSize: false,
            hasSugarLevel: false
        },
        {
            id: 7,
            category: 'rice',
            name: 'Persian Jeweled Saffron Rice',
            image: 'assets/images/pos/img-07.png',
            price: 8.99,
            available: 40,
            sold: 32,
            hasSize: false,
            hasSugarLevel: false
        },
        {
            id: 8,
            category: 'rice',
            name: 'Wild Mushroom Truffle Rice',
            image: 'assets/images/pos/img-08.png',
            price: 10.99,
            available: 38,
            sold: 62,
            hasSize: false,
            hasSugarLevel: false
        },
        {
            id: 9,
            category: 'rice',
            name: 'Tropical Mango Coconut Rice',
            image: 'assets/images/pos/img-09.png',
            price: 3.99,
            available: 50,
            sold: 75,
            hasSize: false,
            hasSugarLevel: false
        },
        {
            id: 10,
            category: 'rice',
            name: 'Savory Beef Stroganoff Rice',
            image: 'assets/images/pos/img-10.png',
            price: 6.99,
            available: 28,
            sold: 48,
            hasSize: false,
            hasSugarLevel: false
        },
        {
            id: 11,
            category: 'rice',
            name: 'Smoky Chipotle Cilantro Rice',
            image: 'assets/images/pos/img-11.png',
            price: 9.99,
            available: 32,
            sold: 45,
            hasSize: false,
            hasSugarLevel: false
        },
        {
            id: 12,
            category: 'pasta',
            name: 'Spicy Arrabbiata Pepper Penne',
            image: 'assets/images/pos/img-12.png',
            price: 5.99,
            available: 42,
            sold: 58,
            hasSize: false,
            hasSugarLevel: false
        },
        {
            id: 13,
            category: 'pizza',
            name: 'Spinach Artichoke Alfredo Delight',
            image: 'assets/images/pos/img-13.png',
            price: 7.99,
            available: 15,
            sold: 35,
            hasSize: true,
            hasSugarLevel: false
        },
        {
            id: 14,
            category: 'pizza',
            name: 'Prosciutto Fig Balsamic Glaze',
            image: 'assets/images/pos/img-14.png',
            price: 11.99,
            available: 28,
            sold: 42,
            hasSize: true,
            hasSugarLevel: false
        },
        {
            id: 15,
            category: 'burger',
            name: 'Spicy Southwest Heat Wave',
            image: 'assets/images/pos/img-15.png',
            price: 14.99,
            available: 22,
            sold: 33,
            hasSize: false,
            hasSugarLevel: false
        },
        {
            id: 16,
            category: 'burger',
            name: 'Classic Americana Burger Dream',
            image: 'assets/images/pos/img-16.png',
            price: 16.99,
            available: 18,
            sold: 52,
            hasSize: false,
            hasSugarLevel: false
        },
        {
            id: 17,
            category: 'burger',
            name: 'Gourmet Truffle Umami Explosion',
            image: 'assets/images/pos/img-17.png',
            price: 9.99,
            available: 35,
            sold: 28,
            hasSize: false,
            hasSugarLevel: false
        },
        {
            id: 18,
            category: 'burger',
            name: 'Black Bean Southwest Fiesta',
            image: 'assets/images/pos/img-18.png',
            price: 13.99,
            available: 30,
            sold: 48,
            hasSize: false,
            hasSugarLevel: false
        },
        {
            id: 19,
            category: 'burger',
            name: 'Beyond Vegan Deluxe Supreme',
            image: 'assets/images/pos/img-19.png',
            price: 4.99,
            available: 45,
            sold: 65,
            hasSize: false,
            hasSugarLevel: false
        },
        {
            id: 20,
            category: 'drink',
            name: 'Classic Lime Mint Refresher',
            image: 'assets/images/pos/img-20.png',
            price: 7.99,
            available: 22,
            sold: 38,
            hasSize: false,
            hasSugarLevel: false
        },
        {
            id: 21,
            category: 'drink',
            name: 'Honey Ginger Spice Mojito',
            image: 'assets/images/pos/img-21.png',
            price: 7.99,
            available: 22,
            sold: 38,
            hasSize: false,
            hasSugarLevel: false
        },
        {
            id: 22,
            category: 'drink',
            name: 'Grapefruit Rosemary Citrus Fizz',
            image: 'assets/images/pos/img-22.png',
            price: 7.99,
            available: 22,
            sold: 38,
            hasSize: false,
            hasSugarLevel: false
        },
        {
            id: 23,
            category: 'drink',
            name: 'Elderflower Cucumber Mint Dream',
            image: 'assets/images/pos/img-23.png',
            price: 7.99,
            available: 22,
            sold: 38,
            hasSize: false,
            hasSugarLevel: false
        },
        {
            id: 24,
            category: 'burger',
            name: 'Seasoned Garlic Parmesan Fries',
            image: 'assets/images/pos/img-24.png',
            price: 7.99,
            available: 22,
            sold: 38,
            hasSize: true,
            hasSugarLevel: false
        }
    ],

    // Tables data
    tables: [
        { id: 'A1', label: 'Table A1', value: 'Table A1' },
        { id: 'A2', label: 'Table A2', value: 'Table A2' },
        { id: 'A3', label: 'Table A3', value: 'Table A3' },
        { id: 'B1', label: 'Table B1', value: 'Table B1' },
        { id: 'B2', label: 'Table B2', value: 'Table B2' },
        { id: 'B3', label: 'Table B3', value: 'Table B3' },
        { id: 'C1', label: 'Table C1', value: 'Table C1' },
        { id: 'C2', label: 'Table C2', value: 'Table C2' },
        { id: 'C3', label: 'Table C3', value: 'Table C3' },
        { id: 'D1', label: 'Table D1', value: 'Table D1' },
        { id: 'D2', label: 'Table D2', value: 'Table D2' },
        { id: 'D3', label: 'Table D3', value: 'Table D3' }
    ]
};

// Alpine.js Store
export const posStore = {
    currentCategory: 'burger',
    cart: [],
    orderType: 'dine-in', // 'dine-in', 'takeaway', 'delivery'
    customerInfo: {
        name: '',
        contact: '',
        table: '',
        address: '',
        deliveryTime: '',
        notes: ''
    },
    paymentMethod: 'cash', // 'cash', 'card', 'scan', 'online'

    // Methods
    setCategory(category) {
        this.currentCategory = category;
    },

    addToCart(product, quantity = 1, options = {}) {
        const cartItem = {
            id: Date.now(), // Unique ID for cart item
            productId: product.id,
            name: product.name,
            price: product.price,
            quantity: quantity,
            options: options, // Size, sugar level, etc.
            total: product.price * quantity
        };
        this.cart.push(cartItem);
    },

    removeFromCart(cartItemId) {
        this.cart = this.cart.filter(item => item.id !== cartItemId);
    },

    clearCart() {
        this.cart = [];
    },

    updateCustomerInfo(info) {
        this.customerInfo = { ...this.customerInfo, ...info };
    },

    setOrderType(type) {
        this.orderType = type;
    },

    setPaymentMethod(method) {
        this.paymentMethod = method;
    },

    // Computed properties
    get cartTotal() {
        return this.cart.reduce((total, item) => total + item.total, 0);
    },

    get cartItemCount() {
        return this.cart.reduce((count, item) => count + item.quantity, 0);
    },

    get tax() {
        return this.cartTotal * 0.056; // 5.6% tax
    },

    get grandTotal() {
        const deliveryFee = this.orderType === 'delivery' ? 3.50 : 0;
        return this.cartTotal + this.tax + deliveryFee;
    }
}; 