{{> partials/main }}

<head>

    {{> partials/title-meta title="Range Area Charts" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}

{{> partials/page-heading title="Range Area Charts" sub-title="Apexcharts" }}

<div class="grid grid-cols-12 gap-x-space">
    <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Basic</h6>
        </div>
        <div class="card-body">
            <div x-data="rangeBasicApp" dir="ltr">
                <div class="!min-h-full" data-chart-colors="[bg-primary-500]" x-ref="rangeBasicChart"></div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Combo</h6>
        </div>
        <div class="card-body">
            <div x-data="rangeComboApp" dir="ltr">
                <div class="!min-h-full" data-chart-colors="[bg-sky-500, bg-red-500]" x-ref="rangeComboChart"></div>
            </div>
        </div>
    </div><!--end col-->
</div><!--end grid-->

</div>
{{> partials/footer }}
</div>

{{> partials/vendor-scripts }}

<script type="module" src="assets/js/charts/range-area-chart.init.js"></script>

<script type="module" src="assets/js/main.js"></script>

</body>
</html>