{{> partials/main }}

<head>

    {{> partials/title-meta title="List View" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

<div class="relative min-h-screen">
    <div class="page-wrapper pt-[calc(theme('spacing.topbar')_*_1.2)] group-data-[layout=horizontal]:pt-[calc(theme('spacing.topbar')_*_1.9)]" x-data="patientsTable()">
        {{> partials/page-heading title="List View" sub-title="Patients" }}

        <div class="justify-between sm:flex">
            <div>
                <div class="relative group/form">
                    <input type="email" class="ltr:pl-9 rtl:pr-9 form-input ltr:group-[&.right]/form:pr-9 rtl:group-[&.right]/form:pl-9 ltr:group-[&.right]/form:pl-4 rtl:group-[&.right]/form:pr-4" placeholder="Search for patients..." x-model="searchTerm" @input="SearchPatients()">
                    <div class="absolute inset-y-0 flex items-center text-gray-500 dark:text-dark-500 ltr:left-3 rtl:right-3 ltr:group-[&.right]/form:right-3 rtl:group-[&.right]/form:left-3 ltr:group-[&.right]/form:left-auto rtl:group-[&.right]/form:right-auto focus:outline-hidden">
                        <i data-lucide="search" class="size-4"></i>
                    </div>
                </div>
            </div>
            <div>
                <div class="flex flex-wrap items-center justify-end gap-2 mt-2 sm:mt-0">
                    <button type="button" class="btn btn-sub-gray" data-drawer-target="filterSidebar"><i data-lucide="sliders-horizontal" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> Filters</button>
                    <a href="apps-hospital-patients-create.html" class="btn btn-primary"><i data-lucide="circle-plus" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> Add Patient</a>
                </div>
            </div>
        </div>

        <div>
            <div class="grid grid-cols-1 md:grid-cols-2 2xl:grid-cols-4 gap-x-space mt-space">
                <template x-for="(patient, index) in displayedPatients" :key="index">
                    <div class="relative card">
                        <div class="card-body">
                            <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown dropdown-right ltr:float-right rtl:float-left">
                                <button x-ref="button" x-on:click="toggle()" :aria-expanded="open.toString()" title="dropdown-button" type="button" class="flex items-center text-gray-500 dark:text-dark-500">
                                    <i class="ri-more-fill"></i>
                                </button>
                                <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed dropdown-menu p-2">
                                    <ul>
                                        <li>
                                            <a href="#!" data-modal-target="patientOverviewModal" class="dropdown-item" @click="overviewItem = patient">
                                                <i class="align-middle ltr:mr-2 rtl:ml-2 ri-eye-line"></i> <span>Overview</span>
                                            </a>
                                        </li>
                                        <li>
                                            <a href="apps-hospital-patients-create.html" class="dropdown-item">
                                                <i class="align-middle ltr:mr-2 rtl:ml-2 ri-pencil-line"></i> <span>Edit</span>
                                            </a>
                                        </li>
                                        <li>
                                            <a href="#!" data-modal-target="deleteModal" class="dropdown-item" @click="deleteItem = patient">
                                                <i class="align-middle ltr:mr-2 rtl:ml-2 ri-delete-bin-line"></i> <span>Delete</span>
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                            <div class="flex items-center gap-3">
                                <img :src="patient.image" alt="" class="rounded-md size-20 shrink-0">
                                <div class="grow">
                                    <h6 class="mb-1.5"><a href="javascript:void(0);" data-modal-target="patientOverviewModal" x-text="patient.name" @click="overviewItem = patient"></a></h6>
                                    <p class="mb-1 text-gray-500 dark:text-dark-500"><i class="ltr:mr-1 rtl:ml-1 ri-mail-line"></i> <span class="align-middle" x-text="patient.email"></span></p>
                                    <p class="text-gray-500 dark:text-dark-500"><i class="ltr:mr-1 rtl:ml-1 ri-phone-line"></i> <span class="align-middle" x-text="patient.phoneNumber"></span></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </template>
                <template x-if="displayedPatients.length == 0">
                    <div class="col-span-12 p-8">
                        <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" class="mx-auto size-12" viewBox="0 0 48 48">
                            <linearGradient id="SVGID_1__h35ynqzIJzH4_gr1" x1="34.598" x2="15.982" y1="15.982" y2="34.598" gradientUnits="userSpaceOnUse">
                                <stop offset="0" stop-color="#60e8fe"></stop>
                                <stop offset=".033" stop-color="#6ae9fe"></stop>
                                <stop offset=".197" stop-color="#97f0fe"></stop>
                                <stop offset=".362" stop-color="#bdf5ff"></stop>
                                <stop offset=".525" stop-color="#dafaff"></stop>
                                <stop offset=".687" stop-color="#eefdff"></stop>
                                <stop offset=".846" stop-color="#fbfeff"></stop>
                                <stop offset="1" stop-color="#fff"></stop>
                            </linearGradient>
                            <path fill="url(#SVGID_1__h35ynqzIJzH4_gr1)" d="M40.036,33.826L31.68,25.6c0.847-1.739,1.335-3.684,1.335-5.748c0-7.27-5.894-13.164-13.164-13.164	S6.688,12.582,6.688,19.852c0,7.27,5.894,13.164,13.164,13.164c2.056,0,3.995-0.485,5.728-1.326l3.914,4.015l4.331,4.331	c1.715,1.715,4.496,1.715,6.211,0C41.751,38.321,41.751,35.541,40.036,33.826z"></path>
                            <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M31.95,25.739l8.086,8.086c1.715,1.715,1.715,4.496,0,6.211l0,0c-1.715,1.715-4.496,1.715-6.211,0	l-4.331-4.331"></path>
                            <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M7.525,24.511c-1.771-4.694-0.767-10.196,3.011-13.975c3.847-3.847,9.48-4.817,14.228-2.912"></path>
                            <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M30.856,12.603c3.376,5.114,2.814,12.063-1.688,16.565c-4.858,4.858-12.565,5.129-17.741,0.814"></path>
                        </svg>
                        <p class="mt-2 text-center text-gray-500 dark:text-dark-500">No matching records found</p>
                    </div>
                </template>
            </div>
            <div class="grid grid-cols-12 gap-5 mb-5 items-center" x-show="displayedPatients.length !== 0">
                <div class="col-span-12 md:col-span-6 justify-center md:justify-start flex flex-wrap">
                    <p class="text-gray-500 dark:text-dark-500">Showing <b x-text="showingStart"></b> - <b x-text="showingEnd"></b> of <b x-text="filteredPatients.length"></b> Results</p>
                </div>
                <div class="col-span-12 md:col-span-6">
                    <div class="flex justify-center md:justify-end pagination pagination-primary">
                        <button @click="prevPage()" :disabled="currentPage === 1" class="pagination-pre">
                            <i data-lucide="chevron-left" class="mr-1 ltr:inline-block rtl:hidden size-4"></i>
                            <i data-lucide="chevron-right" class="ml-1 ltr:hidden rtl:inline-block size-4"></i>
                            Prev
                        </button>
                        <template x-for="page in totalPages" :key="page">
                            <button @click="gotoPage(page)" :class="{ 'active': currentPage === page }" class="pagination-item">
                                <span x-text="page"></span>
                            </button>
                        </template>
                        <button @click="nextPage()" :disabled="currentPage === totalPages" class="pagination-next">
                            Next
                            <i data-lucide="chevron-right" class="ml-1 ltr:inline-block rtl:hidden size-4"></i>
                            <i data-lucide="chevron-left" class="mr-1 ltr:hidden rtl:inline-block size-4"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!--patient overview-->
        <div id="patientOverviewModal" class="!hidden modal show">
            <div class="modal-wrap modal-center">
                <div class="p-2 modal-content">
                    <div class="h-24 p-5 rounded-t-sm ltr:bg-gradient-to-r rtl:bg-gradient-to-l from-primary-500/20 via-pink-500/20 to-green-500/20">
                    </div>
                    <div class="p-4">
                        <div class="flex">
                            <div class="relative inline-block -mt-16 rounded-md ltr:mr-auto rtl:ml-auto shrink-0">
                                <img :src="overviewItem.image" alt="" class="rounded-md size-24">
                            </div>
                            <div x-data="{ loadingButton: false, isActive: false }" class="shrink-0">
                                <button type="button" class="btn btn-red">Book Appointment</button>
                            </div>
                        </div>
                        <h6 class="mt-3 mb-1" x-text="overviewItem.name">Alice Johnson</h6>
                        <p class="mb-3 text-gray-500 dark:text-dark-500">Appointment Date: <span x-text="overviewItem.date">19 June, 2024</span></p>
                        <p class="mb-3 text-gray-500 dark:text-dark-500">Overview</p>

                        <div class="flex flex-col gap-3">
                            <div class="flex items-center gap-2">
                                <div class="w-48 font-medium shrink-0">
                                    <i data-lucide="briefcase-medical" class="inline-block text-gray-500 dark:text-dark-500 size-4"></i> <span class="align-baseline">Doctor Name</span>
                                </div>
                                <p x-text="overviewItem.doctorName">Dr. Robert</p>
                            </div>
                            <div class="flex items-center gap-2">
                                <div class="w-48 font-medium shrink-0">
                                    <i data-lucide="building-2" class="inline-block text-gray-500 dark:text-dark-500 size-4"></i> <span class="align-baseline">Treatment Type</span>
                                </div>
                                <p x-text="overviewItem.treatmentType">Neurology</p>
                            </div>
                            <div class="flex items-center gap-2">
                                <div class="w-48 font-medium shrink-0">
                                    <i data-lucide="mail" class="inline-block text-gray-500 dark:text-dark-500 size-4"></i> <span class="align-baseline">Email</span>
                                </div>
                                <p><a href="mailto:<EMAIL>" x-text="overviewItem.email"><EMAIL></a></p>
                            </div>
                            <div class="flex items-center gap-2">
                                <div class="w-48 font-medium shrink-0">
                                    <i data-lucide="phone" class="inline-block text-gray-500 dark:text-dark-500 size-4"></i> <span class="align-baseline">Phone Number</span>
                                </div>
                                <p><a href="#!" x-text="overviewItem.phoneNumber">+61 2 9374 4000</a></p>
                            </div>
                            <div class="flex items-center gap-2">
                                <div class="w-48 font-medium shrink-0">
                                    <i data-lucide="ambulance" class="inline-block text-gray-500 dark:text-dark-500 size-4"></i> <span class="align-baseline">Insurance</span>
                                </div>
                                <p><a href="#!" x-text="overviewItem.insurance">Yes</a></p>
                            </div>
                            <div class="flex items-center gap-2">
                                <div class="w-48 font-medium shrink-0"> 
                                    <i data-lucide="gem" class="inline-block text-gray-500 dark:text-dark-500 size-4"></i> <span class="align-baseline">Status</span>
                                </div>
                                <p class="badge badge-primary" x-text="overviewItem.status">New</p>
                            </div>
                        </div>
                        <div class="flex items-center justify-end gap-2 mt-6">
                            <button type="button" class="btn btn-active-red" data-modal-close="patientOverviewModal"><i data-lucide="x" class="inline-block size-4"></i> <span class="align-baseline">Close</span></button>
                            <a href="apps-hospital-patients-create.html" class="btn btn-primary"><i data-lucide="pencil" class="inline-block mr-1 size-4"></i> Edit Patient</a>
                        </div>
                    </div>
                </div>
            </div>
        </div><!--end-->

        <!--delete modal-->
        <div id="deleteModal" class="!hidden modal show">
            <div class="modal-wrap modal-xs modal-center">
                <div class="text-center modal-content p-7">
                    <div class="flex items-center justify-center mx-auto mb-4 text-red-500 rounded-full bg-red-500/10 size-14 backdrop-blur-xl">
                        <i data-lucide="trash-2" class="size-6"></i>
                    </div>
                    <h5 class="mb-4">Are you sure you want to delete this Contact ?</h5>
                    <div class="flex items-center justify-center gap-2">
                        <button class="btn btn-red" @click="deletePatients()" data-modal-close="deleteModal">Delete</button>
                        <button data-modal-close="deleteModal" class="btn link link-primary">Cancel</button>
                    </div>
                </div>
            </div>
        </div><!--end-->

        <!--filter sidebar-->
        <div id="filterSidebar" drawer-end class="drawer show">
            <div class="drawer-header">
                <h6>Patients Filters</h6>
                <button data-drawer-close="filterSidebar" title="drawer-close"><i data-lucide="x" class="link link-red size-5"></i></button>
            </div>
            <div class="drawer-content">
                <div class="relative mb-5 group/form">
                    <input type="text" class="ltr:pl-9 rtl:pr-9 form-input" placeholder="Search for patients..." x-model="searchQuery">
                    <div class="absolute inset-y-0 flex items-center text-gray-500 dark:text-dark-500 ltr:left-3 rtl:right-3 focus:outline-hidden">
                        <i data-lucide="search" class="size-4"></i>
                    </div>
                </div>
                <div class="mb-5">
                    <p class="mb-1.5">Doctors</p>
                    <div id="doctorsSelect" placeholder="Select Doctor"></div>
                </div>
                <div class="mb-5">
                    <p class="mb-1.5">Patient Status</p>
                    <div id="patientStatusSelect" placeholder="Select Status"></div>
                </div>
                <div class="mb-5">
                    <p class="mb-1.5">Insurance</p>
                    <div id="insuranceSelect" placeholder="Select Insurance"></div>
                </div>
                <div class="mb-5">
                    <p class="mb-1.5">City</p>
                    <div id="citySelect" placeholder="Select City"></div>
                </div>
                <div>
                    <p class="mb-1.5">Gender</p>
                    <div id="genderSelect" placeholder="Select Gender"></div>
                </div>
            </div>
            <div class="flex items-center justify-between gap-2 p-4 border-t border-gray-200 dark:border-dark-800">
                <button type="button" class="btn btn-sub-gray" @click="clearFilters()">Reset</button>
                <button type="button" class="btn btn-primary" @click="filterPatients()" data-drawer-close="filterSidebar">
                    Filters
                    <i data-lucide="move-right" class="ml-1 ltr:inline-block rtl:hidden size-4"></i>
                    <i data-lucide="move-left" class="mr-1 ltr:hidden rtl:inline-block size-4"></i>
                </button>
            </div>
        </div>
    </div>
    {{> partials/footer }}
</div>



{{> partials/vendor-scripts }}

<script type="module" src="assets/js/apps/hospital/patients/list-view.init.js"></script>

<script type="module" src="assets/js/main.js"></script>

</body>
</html>