import user1 from "/assets/images/avatar/user-1.png"
import user5 from "/assets/images/avatar/user-5.png"
import user8 from "/assets/images/avatar/user-8.png"
import user14 from "/assets/images/avatar/user-14.png"
import user15 from "/assets/images/avatar/user-15.png"
import user17 from "/assets/images/avatar/user-17.png"
import user4 from "/assets/images/avatar/user-4.png"
import user19 from "/assets/images/avatar/user-19.png"
import user20 from "/assets/images/avatar/user-20.png"
import user21 from "/assets/images/avatar/user-21.png"
import user22 from "/assets/images/avatar/user-22.png"
import user23 from "/assets/images/avatar/user-23.png"
import user24 from "/assets/images/avatar/user-24.png"
import user25 from "/assets/images/avatar/user-25.png"
import user28 from "/assets/images/avatar/user-28.png"
const parentsData = [
    {
        "parentsName": "<PERSON>",
        "studentName": "<PERSON>",
        "image": user1,
        "relation": "Mother",
        "occupation": "Nurse    ",
        "gender": "Female",
        "email": "<EMAIL>",
        "phone": "1541151542",
        "address": "Belgium"
    },
    {
        "parentsName": "Elmer McPeek",
        "studentName": "Dorothy Daley",
        "image": user1,
        "relation": "Father",
        "occupation": "Director",
        "gender": "Male",
        "email": "<EMAIL>",
        "phone": "1541151542",
        "address": "Belgium"
    },
    {
        "parentsName": "Paul Balls",
        "studentName": "Silvia Balls",
        "image": user5,
        "relation": "Father",
        "occupation": "Office Manager",
        "gender": "Male",
        "email": "<EMAIL>",
        "phone": "*********",
        "address": "Poland"
    },
    {
        "parentsName": "Judy Soto",
        "studentName": "Robert Soto",
        "relation": "Mother",
        "occupation": "Assistant Manager",
        "gender": "Female",
        "email": "<EMAIL>",
        "phone": "**********",
        "address": "Romania"
    },
    {
        "parentsName": "John Harper",
        "studentName": "Emily Harper",
        "image": user8,
        "relation": "Father",
        "occupation": "Account Manager",
        "gender": "Male",
        "email": "<EMAIL>",
        "phone": "**********",
        "address": "Mexico"
    },
    {
        "parentsName": "Amanda Hanson",
        "studentName": "Michael Hanson",
        "image": user14,
        "relation": "Mother",
        "occupation": "Coordinator",
        "gender": "Female",
        "email": "<EMAIL>",
        "phone": "**********",
        "address": "Ukraine"
    },
    {
        "parentsName": "Clarissa Lee",
        "studentName": "Jessica Lee",
        "image": user15,
        "relation": "Mother",
        "occupation": "Nurse",
        "gender": "Female",
        "email": "<EMAIL>",
        "phone": "**********",
        "address": "Ukraine"
    },
    {
        "parentsName": "Samuel Ellis",
        "studentName": "Daniel Ellis",
        "relation": "Father",
        "occupation": "Principal",
        "gender": "Male",
        "email": "<EMAIL>",
        "phone": "**********",
        "address": "Italy"
    },
    {
        "parentsName": "Jacque Carle",
        "studentName": "Olivia Carle",
        "image": user17,
        "relation": "Mother",
        "occupation": "Financial Analyst",
        "gender": "Female",
        "email": "<EMAIL>",
        "phone": "1741251823",
        "address": "Hungary"
    },
    {
        "parentsName": "Joseph Wilson",
        "studentName": "Ethan Wilson",
        "image": user4,
        "relation": "Father",
        "occupation": "Human Resources",
        "gender": "Male",
        "email": "<EMAIL>",
        "phone": "1815351923",
        "address": "Spain"
    },
    {
        "parentsName": "Nancy Smith",
        "studentName": "Charles Smith",
        "image": user19,
        "relation": "Mother",
        "occupation": "Teacher",
        "gender": "Female",
        "email": "<EMAIL>",
        "phone": "1952362024",
        "address": "Germany"
    },
    {
        "parentsName": "David Brown",
        "studentName": "Sophia Brown",
        "image": user20,
        "relation": "Father",
        "occupation": "Software Engineer",
        "gender": "Male",
        "email": "<EMAIL>",
        "phone": "1963472125",
        "address": "France"
    },
    {
        "parentsName": "Patricia Davis",
        "studentName": "Benjamin Davis",
        "image": user21,
        "relation": "Mother",
        "occupation": "Marketing Manager",
        "gender": "Female",
        "email": "<EMAIL>",
        "phone": "1974582226",
        "address": "Netherlands"
    },
    {
        "parentsName": "Michael Martinez",
        "studentName": "Emma Martinez",
        "image": user22,
        "relation": "Father",
        "occupation": "Doctor",
        "gender": "Male",
        "email": "<EMAIL>",
        "phone": "1985692327",
        "address": "Sweden"
    },
    {
        "parentsName": "Barbara Taylor",
        "studentName": "Mason Taylor",
        "image": user23,
        "relation": "Mother",
        "occupation": "Graphic Designer",
        "gender": "Female",
        "email": "<EMAIL>",
        "phone": "1996702428",
        "address": "Norway"
    },
    {
        "parentsName": "Christopher Thomas",
        "studentName": "Ava Thomas",
        "image": user24,
        "relation": "Father",
        "occupation": "Chef",
        "gender": "Male",
        "email": "<EMAIL>",
        "phone": "1907812529",
        "address": "Finland"
    },
    {
        "parentsName": "Lisa Lee",
        "studentName": "Isabella Lee",
        "image": user25,
        "relation": "Mother",
        "occupation": "Project Manager",
        "gender": "Female",
        "email": "<EMAIL>",
        "phone": "1918922630",
        "address": "Denmark"
    },
    {
        "parentsName": "Joshua Clark",
        "studentName": "Lucas Clark",
        "image": user28,
        "relation": "Father",
        "occupation": "Dentist",
        "gender": "Male",
        "email": "<EMAIL>",
        "phone": "1941252933",
        "address": "Ireland"
    }
]

export default parentsData