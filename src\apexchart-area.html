{{> partials/main }}

<head>

    {{> partials/title-meta title="Area Charts" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}

{{> partials/page-heading title="Area Charts" sub-title="Apexcharts" }}

<div class="grid grid-cols-12 gap-x-space">
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Basic</h6>
        </div>
        <div class="card-body">
            <div x-data="basicAreaApp" dir="ltr">
                <div class="!min-h-full" data-chart-colors="[bg-primary-500]" x-ref="basicAreaChart"></div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Spline Area</h6>
        </div>
        <div class="card-body">
            <div x-data="splineAreaApp" dir="ltr">
                <div class="!min-h-full" data-chart-colors="[bg-sky-500, bg-green-500]" x-ref="splineAreaChart"></div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Negative</h6>
        </div>
        <div class="card-body">
            <div x-data="negativeAreaApp" dir="ltr">
                <div class="!min-h-full" data-chart-colors="[bg-primary-500, bg-gray-300]" data-chart-dark-colors="[bg-primary-500, bg-dark-700]" x-ref="negativeAreaChart"></div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Stacked</h6>
        </div>
        <div class="card-body">
            <div x-data="stackedAreaApp" dir="ltr">
                <div class="!min-h-full" data-chart-colors="[bg-primary-500, bg-green-500, bg-gray-200]" data-chart-dark-colors="[bg-primary-500, bg-green-500, bg-dark-700]" x-ref="stackedAreaChart"></div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Missing / Null values</h6>
        </div>
        <div class="card-body">
            <div x-data="missingAreaApp" dir="ltr">
                <div class="!min-h-full" data-chart-colors="[bg-primary-500]" x-ref="missingAreaChart"></div>
            </div>
        </div>
    </div><!--end col-->
</div><!--end grid-->

</div>
{{> partials/footer }}
</div>

{{> partials/vendor-scripts }}

<script type="module" src="assets/js/charts/area-chart.init.js"></script>

<script type="module" src="assets/js/main.js"></script>

</body>
</html>