{{> partials/main }}

<head>

    {{> partials/title-meta title="Accordion & Collapse" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}

{{> partials/page-heading title="Accordion & Collapse" sub-title="UI" }}

<div class="grid grid-cols-12 gap-x-space">
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Basic Accordion</h6>
        </div>
        <div class="card-body">
            <div class="grid grid-cols-12 gap-5">
                <div class="col-span-12 md:col-span-6" x-data="{selected:1}">
                    <div class="accordion">
                        <button type="button" class="accordion-button accordion-primary" @click="selected !== 1 ? selected = 1 : selected = null" x-bind:class="{ 'active': selected === 1 }">
                            <span class="flex items-center justify-between">
                                <span>Why do we use Tailwind CSS?</span>
                                <span class="arrow-icon" x-bind:class="{ 'active': selected === 1 }" x-show="selected !== 1"><i data-lucide="chevron-down"></i></span>
                                <span class="arrow-icon" x-bind:class="{ 'active': selected === 1 }" x-show="selected === 1"><i data-lucide="chevron-up"></i></span>
                            </span>
                        </button>
                        <div class="accordion-main-content" x-ref="container1" x-bind:style="selected == 1 ? 'max-height: ' + $refs.container1.scrollHeight + 'px' : ''">
                            <div class="content">
                                <p>Tailwind CSS is used to design and style web pages fast and Responsive. Rapid Development, Highly Customizable, Reduced CSS File Size, Great Documentation, and Community Support are the main reasons for using Tailwind CSS.</p>
                            </div>
                        </div>
                    </div>
                    <div class="accordion">
                        <button type="button" class="accordion-button accordion-primary" @click="selected !== 2 ? selected = 2 : selected = null" x-bind:class="{ 'active': selected === 2 }">
                            <span class="flex items-center justify-between">
                                <span>Can we change the base font-family in Tailwind config?</span>
                                <span class="arrow-icon" x-bind:class="{ 'active': selected === 2 }" x-show="selected !== 2"><i data-lucide="chevron-down"></i></span>
                                <span class="arrow-icon" x-bind:class="{ 'active': selected === 2 }" x-show="selected === 2"><i data-lucide="chevron-up"></i></span>
                            </span>
                        </button>
                        <div class="accordion-main-content" x-ref="container2" x-bind:style="selected == 2 ? 'max-height: ' + $refs.container2.scrollHeight + 'px' : ''">
                            <div class="content">
                                <p>Yes, we can change the base <code class="text-pink-500">font-family</code> in Tailwind <code class="text-pink-500">config.</code> To adjust the main font style in Tailwind CSS, you can modify it by making changes in the “theme” part of your configuration file (<code class="text-pink-500">tailwind.config.js</code>). Just open that file, find the theme section, and add or update the fontFamily setting. We can also Change the font-family in the Tailwind config with different techniques Changing base font-family, Adding a new font family, Removing font family.</p>
                            </div>
                        </div>
                    </div>
                    <div class="accordion">
                        <button type="button" class="accordion-button accordion-primary" @click="selected !== 3 ? selected = 3 : selected = null" x-bind:class="{ 'active': selected === 3 }">
                            <span class="flex items-center justify-between">
                                <span>How to create a form with Tailwind CSS?</span>
                                <span class="arrow-icon" x-bind:class="{ 'active': selected === 3 }" x-show="selected !== 3"><i data-lucide="chevron-down"></i></span>
                                <span class="arrow-icon" x-bind:class="{ 'active': selected === 3 }" x-show="selected === 3"><i data-lucide="chevron-up"></i></span>
                            </span>
                        </button>
                        <div class="accordion-main-content" x-ref="container3" x-bind:style="selected == 3 ? 'max-height: ' + $refs.container3.scrollHeight + 'px' : ''">
                            <div class="content">
                                <p>Tailwind CSS, offers Tailwind forms as plugins that provide a foundational reset for form styles. Install TailwindCSS by writing the following command. We can also use utility classes to make a form with Tailwind CSS, use the easy-to-apply classes for backgrounds, borders, shadows, etc. Start by creating the form element and use the space-y-{n} class to add vertical spacing between the form controls.”</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-span-12 md:col-span-6" x-data="{selected:1}">
                    <div class="accordion">
                        <button type="button" class="accordion-button accordion-green" @click="selected !== 1 ? selected = 1 : selected = null" x-bind:class="{ 'active': selected === 1 }">
                            <span class="flex items-center justify-between">
                                <span>Why do we use Tailwind CSS?</span>
                                <span class="arrow-icon" x-bind:class="{ 'active': selected === 1 }" x-show="selected !== 1"><i data-lucide="chevron-down"></i></span>
                                <span class="arrow-icon" x-bind:class="{ 'active': selected === 1 }" x-show="selected === 1"><i data-lucide="chevron-up"></i></span>
                            </span>
                        </button>
                        <div class="accordion-main-content" x-ref="container1" x-bind:style="selected == 1 ? 'max-height: ' + $refs.container1.scrollHeight + 'px' : ''">
                            <div class="content">
                                <p>Tailwind CSS is used to design and style web pages fast and Responsive. Rapid Development, Highly Customizable, Reduced CSS File Size, Great Documentation, and Community Support are the main reasons for using Tailwind CSS.</p>
                            </div>
                        </div>
                    </div>
                    <div class="accordion">
                        <button type="button" class="accordion-button accordion-green" @click="selected !== 2 ? selected = 2 : selected = null" x-bind:class="{ 'active': selected === 2 }">
                            <span class="flex items-center justify-between">
                                <span>Can we change the base font-family in Tailwind config?</span>
                                <span class="arrow-icon" x-bind:class="{ 'active': selected === 2 }" x-show="selected !== 2"><i data-lucide="chevron-down"></i></span>
                                <span class="arrow-icon" x-bind:class="{ 'active': selected === 2 }" x-show="selected === 2"><i data-lucide="chevron-up"></i></span>
                            </span>
                        </button>
                        <div class="accordion-main-content" x-ref="container2" x-bind:style="selected == 2 ? 'max-height: ' + $refs.container2.scrollHeight + 'px' : ''">
                            <div class="content">
                                <p>Yes, we can change the base <code class="text-pink-500">font-family</code> in Tailwind <code class="text-pink-500">config.</code> To adjust the main font style in Tailwind CSS, you can modify it by making changes in the “theme” part of your configuration file (<code class="text-pink-500">tailwind.config.js</code>). Just open that file, find the theme section, and add or update the fontFamily setting. We can also Change the font-family in the Tailwind config with different techniques Changing base font-family, Adding a new font family, Removing font family.</p>
                            </div>
                        </div>
                    </div>
                    <div class="accordion">
                        <button type="button" class="accordion-button accordion-green" @click="selected !== 3 ? selected = 3 : selected = null" x-bind:class="{ 'active': selected === 3 }">
                            <span class="flex items-center justify-between">
                                <span>How to create a form with Tailwind CSS?</span>
                                <span class="arrow-icon" x-bind:class="{ 'active': selected === 3 }" x-show="selected !== 3"><i data-lucide="chevron-down"></i></span>
                                <span class="arrow-icon" x-bind:class="{ 'active': selected === 3 }" x-show="selected === 3"><i data-lucide="chevron-up"></i></span>
                            </span>
                        </button>
                        <div class="accordion-main-content" x-ref="container3" x-bind:style="selected == 3 ? 'max-height: ' + $refs.container3.scrollHeight + 'px' : ''">
                            <div class="content">
                                <p>Tailwind CSS, offers Tailwind forms as plugins that provide a foundational reset for form styles. Install TailwindCSS by writing the following command. We can also use utility classes to make a form with Tailwind CSS, use the easy-to-apply classes for backgrounds, borders, shadows, etc. Start by creating the form element and use the space-y-{n} class to add vertical spacing between the form controls.”</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Boxed Accordion</h6>
        </div>
        <div class="card-body">
            <div class="grid grid-cols-12 gap-5">
                <div class="col-span-12 md:col-span-6" x-data="{selected:1}">
                    <div class="flex flex-col gap-3">
                        <div class="accordion-boxed">
                            <button type="button" class="accordion-button accordion-green" @click="selected !== 1 ? selected = 1 : selected = null" x-bind:class="{ 'active': selected === 1 }">
                                <span class="flex items-center justify-between">
                                    <span>Why do we use Tailwind CSS?</span>
                                    <span class="arrow-icon" x-bind:class="{ 'active': selected === 1 }" x-show="selected !== 1"><i data-lucide="chevron-down"></i></span>
                                    <span class="arrow-icon" x-bind:class="{ 'active': selected === 1 }" x-show="selected === 1"><i data-lucide="chevron-up"></i></span>
                                </span>
                            </button>
                            <div class="accordion-main-content" x-ref="container1" x-bind:style="selected == 1 ? 'max-height: ' + $refs.container1.scrollHeight + 'px' : ''">
                                <div class="content">
                                    <p>Tailwind CSS is used to design and style web pages fast and Responsive. Rapid Development, Highly Customizable, Reduced CSS File Size, Great Documentation, and Community Support are the main reasons for using Tailwind CSS.</p>
                                </div>
                            </div>
                        </div>
                        <div class="accordion-boxed">
                            <button type="button" class="accordion-button accordion-green" @click="selected !== 2 ? selected = 2 : selected = null" x-bind:class="{ 'active': selected === 2}">
                                <span class="flex items-center justify-between">
                                    <span>Can we change the base font-family in Tailwind config?</span>
                                    <span class="arrow-icon" x-bind:class="{ 'active': selected === 2 }" x-show="selected !== 2"><i data-lucide="chevron-down"></i></span>
                                    <span class="arrow-icon" x-bind:class="{ 'active': selected === 2 }" x-show="selected === 2"><i data-lucide="chevron-up"></i></span>
                                </span>
                            </button>
                            <div class="accordion-main-content" x-ref="container2" x-bind:style="selected == 2 ? 'max-height: ' + $refs.container2.scrollHeight + 'px' : ''">
                                <div class="content">
                                    <p>Yes, we can change the base <code class="text-pink-500">font-family</code> in Tailwind <code class="text-pink-500">config.</code> To adjust the main font style in Tailwind CSS, you can modify it by making changes in the “theme” part of your configuration file (<code class="text-pink-500">tailwind.config.js</code>). Just open that file, find the theme section, and add or update the fontFamily setting. We can also Change the font-family in the Tailwind config with different techniques Changing base font-family, Adding a new font family, Removing font family.</p>
                                </div>
                            </div>
                        </div>
                        <div class="accordion-boxed">
                            <button type="button" class="accordion-button accordion-green" @click="selected !== 3 ? selected = 3 : selected = null" x-bind:class="{ 'active': selected === 3 }">
                                <span class="flex items-center justify-between">
                                    <span>How to create a form with Tailwind CSS?</span>
                                    <span class="arrow-icon" x-bind:class="{ 'active': selected === 3 }" x-show="selected !== 3"><i data-lucide="chevron-down"></i></span>
                                    <span class="arrow-icon" x-bind:class="{ 'active': selected === 3 }" x-show="selected === 3"><i data-lucide="chevron-up"></i></span>
                                </span>
                            </button>
                            <div class="accordion-main-content" x-ref="container3" x-bind:style="selected == 3 ? 'max-height: ' + $refs.container3.scrollHeight + 'px' : ''">
                                <div class="content">
                                    <p>Tailwind CSS, offers Tailwind forms as plugins that provide a foundational reset for form styles. Install TailwindCSS by writing the following command. We can also use utility classes to make a form with Tailwind CSS, use the easy-to-apply classes for backgrounds, borders, shadows, etc. Start by creating the form element and use the space-y-{n} class to add vertical spacing between the form controls.”</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div><!--end col-->
                <div class="col-span-12 md:col-span-6" x-data="{selected:1}">
                    <div class="flex flex-col gap-3">
                        <div class="accordion-boxed">
                            <button type="button" class="accordion-button accordion-solid-purple" @click="selected !== 1 ? selected = 1 : selected = null" x-bind:class="{ 'active': selected === 1 }">
                                <div class="flex items-center justify-between">
                                    <span>Why do we use Tailwind CSS?</span>
                                    <span class="arrow-icon" x-bind:class="{ 'active': selected === 1 }" x-show="selected !== 1"><i data-lucide="chevron-down"></i></span>
                                    <span class="arrow-icon" x-bind:class="{ 'active': selected === 1 }" x-show="selected === 1"><i data-lucide="chevron-up"></i></span>
                                </div>
                            </button>
                            <div class="accordion-main-content" x-ref="container1" x-bind:style="selected == 1 ? 'max-height: ' + $refs.container1.scrollHeight + 'px' : ''">
                                <div class="content">
                                    <p>Tailwind CSS is used to design and style web pages fast and Responsive. Rapid Development, Highly Customizable, Reduced CSS File Size, Great Documentation, and Community Support are the main reasons for using Tailwind CSS.</p>
                                </div>
                            </div>
                        </div>
                        <div class="accordion-boxed">
                            <button type="button" class="accordion-button accordion-solid-purple" @click="selected !== 2 ? selected = 2 : selected = null" x-bind:class="{ 'active': selected === 2 }">
                                <div class="flex items-center justify-between">
                                    <span>Can we change the base font-family in Tailwind config?</span>
                                    <span class="arrow-icon" x-bind:class="{ 'active': selected === 2 }" x-show="selected !== 2"><i data-lucide="chevron-down"></i></span>
                                    <span class="arrow-icon" x-bind:class="{ 'active': selected === 2 }" x-show="selected === 2"><i data-lucide="chevron-up"></i></span>
                                </div>
                            </button>
                            <div class="accordion-main-content" x-ref="container2" x-bind:style="selected == 2 ? 'max-height: ' + $refs.container2.scrollHeight + 'px' : ''">
                                <div class="content">
                                    <p>Yes, we can change the base <code class="text-pink-500">font-family</code> in Tailwind <code class="text-pink-500">config.</code> To adjust the main font style in Tailwind CSS, you can modify it by making changes in the “theme” part of your configuration file (<code class="text-pink-500">tailwind.config.js</code>). Just open that file, find the theme section, and add or update the fontFamily setting. We can also Change the font-family in the Tailwind config with different techniques Changing base font-family, Adding a new font family, Removing font family.</p>
                                </div>
                            </div>
                        </div>
                        <div class="accordion-boxed">
                            <button type="button" class="accordion-button accordion-solid-purple" @click="selected !== 3 ? selected = 3 : selected = null" x-bind:class="{ 'active': selected === 3 }">
                                <div class="flex items-center justify-between">
                                    <span>How to create a form with Tailwind CSS?</span>
                                    <span class="arrow-icon" x-bind:class="{ 'active': selected === 3 }" x-show="selected !== 3"><i data-lucide="chevron-down"></i></span>
                                    <span class="arrow-icon" x-bind:class="{ 'active': selected === 3 }" x-show="selected === 3"><i data-lucide="chevron-up"></i></span>
                                </div>
                            </button>
                            <div class="accordion-main-content" x-ref="container3" x-bind:style="selected == 3 ? 'max-height: ' + $refs.container3.scrollHeight + 'px' : ''">
                                <div class="content">
                                    <p>Tailwind CSS, offers Tailwind forms as plugins that provide a foundational reset for form styles. Install TailwindCSS by writing the following command. We can also use utility classes to make a form with Tailwind CSS, use the easy-to-apply classes for backgrounds, borders, shadows, etc. Start by creating the form element and use the space-y-{n} class to add vertical spacing between the form controls.”</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div><!--end col-->
            </div><!--end grid-->
        </div>
    </div><!--end col-->
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Collapse</h6>
        </div>
        <div class="card-body">
            <div class="relative" x-data="{selected:null}">
                <button type="button" class="text-white btn bg-primary-500 border-primary-500 hover:bg-primary-600 hover:text-white hover:border-primary-600 focus:bg-primary-600 focus:text-white focus:border-primary-600" @click="selected !== 1 ? selected = 1 : selected = null">
                    <div class="flex items-center justify-between">
                        <span class="ltr:mr-1 rtl:ml-1">Buttons Collapse</span>
                        <span class="ico-down" x-show="selected !== 1"><i data-lucide="chevron-down"></i></span>
                        <span class="ico-up" x-show="selected === 1"><i data-lucide="chevron-up"></i></span>
                    </div>
                </button>
                <div class="relative overflow-hidden transition-all duration-700 max-h-0" x-ref="container1" x-bind:style="selected == 1 ? 'max-height: ' + $refs.container1.scrollHeight + 'px' : ''">
                    <div class="pt-3">
                        <p>Tailwind CSS is an open-source project, available for free usage and utility-first CSS framework that provides responsiveness.</p>
                    </div>
                </div>
            </div>
        </div>
    </div><!--end col-->
</div><!--end grid-->

</div>
{{> partials/footer }}
</div>

{{> partials/vendor-scripts }}
<script type="module" src="assets/js/main.js"></script>

</body>
</html>