{{> partials/main }}

<head>

    {{> partials/title-meta title="Line Charts" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}

{{> partials/page-heading title="Line Charts" sub-title="Echarts" }}

<div class="grid grid-cols-12 gap-x-space">
    <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Basic</h6>
        </div>
        <div class="card-body" x-data="basicLineApp">
            <div class="h-80" x-ref="basicLineChart" data-chart-colors="[bg-primary-500, bg-gray-200, bg-gray-800]" data-chart-dark-colors="[bg-primary-500, bg-dark-800, bg-dark-100]"></div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Smooth Line</h6>
        </div>
        <div class="card-body" x-data="smoothLineApp">
            <div class="h-80" x-ref="smoothLineChart" data-chart-colors="[bg-purple-500, bg-gray-200, bg-gray-800]" data-chart-dark-colors="[bg-purple-500, bg-dark-800, bg-dark-100]"></div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Stacked Line Chart</h6>
        </div>
        <div class="card-body" x-data="stackedLineApp">
            <div class="h-80" x-ref="stackedLineChart" data-chart-colors="[bg-purple-500, bg-yellow-500, bg-sky-500, bg-green-500, bg-orange-500, bg-gray-200, bg-gray-800]" data-chart-dark-colors="[bg-purple-500, bg-yellow-500, bg-sky-500, bg-green-500, bg-orange-500 bg-dark-800, bg-dark-100]"></div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Line Y Category</h6>
        </div>
        <div class="card-body" x-data="categoryLineApp">
            <div class="h-80" x-ref="categoryLineChart" data-chart-colors="[bg-purple-500, bg-gray-200, bg-gray-800]" data-chart-dark-colors="[bg-purple-500, bg-dark-800, bg-dark-100]"></div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Step Line</h6>
        </div>
        <div class="card-body" x-data="stepLineApp">
            <div class="h-80" x-ref="stepLineChart" data-chart-colors="[bg-primary-500, bg-purple-500, bg-green-500, bg-gray-200, bg-gray-800]" data-chart-dark-colors="[bg-primary-500, bg-purple-500, bg-green-500, bg-dark-800, bg-dark-100]"></div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Style Line</h6>
        </div>
        <div class="card-body" x-data="styleLineApp">
            <div class="h-80" x-ref="styleLineChart" data-chart-colors="[bg-primary-500, bg-gray-200, bg-gray-800, bg-red-500]" data-chart-dark-colors="[bg-primary-500, bg-dark-800, bg-dark-100, bg-red-500]"></div>
        </div>
    </div><!--end col-->
</div><!--end grid-->

<div class="mb-5 text-center">
    <a href="https://echarts.apache.org/examples/en/index.html#chart-type-line" target="_blank" class="btn btn-primary">More Example <i data-lucide="move-right" class="inline-block ml-1 size-4"></i></a>
</div>

</div>
{{> partials/footer }}
</div>

{{> partials/vendor-scripts }}

<script src="assets/libs/echarts/echarts.js"></script>
<script type="module" src="assets/js/charts/echart-line.init.js"></script>

<script type="module" src="assets/js/main.js"></script>

</body>
</html>