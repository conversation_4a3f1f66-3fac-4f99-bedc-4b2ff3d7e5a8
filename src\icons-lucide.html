{{> partials/main }}

<head>

    {{> partials/title-meta title="Lucide" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}

{{> partials/page-heading title="Lucide" sub-title="Icons" }}

<div class="grid grid-cols-12 gap-x-space">
    <div class="col-span-12 card">
        <div class="flex items-center card-header">
            <h6 class="text-15 grow">Lucide Icons</h6>
            <a href="https://lucide.dev/icons" target="_blank" class="font-medium text-red-500 underline transition duration-200 ease-linear hover:text-red-600 shrink-0">View All Icons</a>
        </div>
        <div class="card-body">
            <p class="mb-3 text-gray-500 dark:text-dark-500">Lucide is an open-source icon library that provides <code class="text-pink-500">1000+</code> vector (svg) files for displaying icons and symbols in digital and non-digital projects. The library aims to make it easier for designers and developers to incorporate icons into their projects by providing several official packages to make it easier to use these icons in your project.</p>

            <h6 class="mb-2 text-16">Installation</h6>

            <h6 class="mb-1">Package Managers</h6>
            <p class="text-gray-500 dark:text-dark-500 mb-2">Implementation of the lucide icon library for web applications.</p>

            <pre><deckgo-highlight-code lang="js">
                        <code slot="code">npm install lucide</code>
                    </deckgo-highlight-code></pre>

            <h6 class="mb-1 mt-2">CDN</h6>
            <p class="text-gray-500 mb-2">Implementation of the lucide icon library for web applications.</p>

            <pre>
<deckgo-highlight-code lang="js">
<code slot="code">&lt;!-- Development version --&gt;
&lt;script src=&quot;https://unpkg.com/lucide@latest/dist/umd/lucide.js&quot;&gt;&lt;/script&gt;

&lt;!-- Production version --&gt;
&lt;script src=&quot;https://unpkg.com/lucide@latest&quot;&gt;&lt;/script&gt;
</code>
</deckgo-highlight-code></pre>

            <h6 class="mb-1 mt-2">Usage</h6>
            <p class="text-gray-500 dark:text-dark-500 mb-2">Here is a complete example with unpkg</p>

            <pre>
<deckgo-highlight-code lang="js"><code slot="code">&lt;body&gt;
    &lt;i data-lucide=&quot;volume-2&quot; class=&quot;my-class&quot;&gt;&lt;/i&gt;
    &lt;i data-lucide=&quot;x&quot;&gt;&lt;/i&gt;
    &lt;i data-lucide=&quot;menu&quot;&gt;&lt;/i&gt;

    &lt;script src=&quot;assets/libs/lucide/umd/lucide.min.js&quot;&gt;&lt;/script&gt;
    &lt;script&gt;
        lucide.createIcons();
    &lt;/script&gt;
&lt;/body&gt;
</code>
</deckgo-highlight-code>
</pre>

            <p class="mb-0 text-gray-500 dark:text-dark-500">For more details, see the <a href="#!" class="link hover:text-primary-600 text-primary-500">documentation</a>.</p>
        </div>
    </div><!--end col-->
    <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Color Icons</h6>
        </div>
        <div class="card-body">
            <div class="*:size-10 *:flex *:items-center *:justify-center flex items-center *:border *:border-gray-200 dark:*:border-dark-800 gap-2 *:rounded-md flex-wrap">
                <div><i data-lucide="award" class="text-gray-500 dark:text-dark-500 size-5"></i></div>
                <div><i data-lucide="box" class="size-5 text-primary-500"></i></div>
                <div><i data-lucide="bot" class="text-green-500 size-5"></i></div>
                <div><i data-lucide="chef-hat" class="text-yellow-500 size-5"></i></div>
                <div><i data-lucide="camera" class="text-purple-500 size-5"></i></div>
                <div><i data-lucide="file-text" class="text-red-500 size-5"></i></div>
                <div><i data-lucide="globe" class="text-sky-500 size-5"></i></div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Stroke Width</h6>
        </div>
        <div class="card-body">
            <div class="*:size-10 *:flex *:items-center *:justify-center flex items-center *:border *:border-gray-200 dark:*:border-dark-800 gap-2 *:rounded-md">
                <div><i data-lucide="box" class="stroke-1 size-5 text-primary-500"></i></div>
                <div><i data-lucide="box" class="stroke-2 size-5 text-primary-500"></i></div>
                <div><i data-lucide="box" class="stroke-3 size-5 text-primary-500"></i></div>
                <div><i data-lucide="box" class="stroke-4 size-5 text-primary-500"></i></div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Sizes Icons</h6>
        </div>
        <div class="card-body">
            <div class="flex flex-wrap items-center gap-3">
                <div><i data-lucide="box" class="size-2 text-primary-500"></i></div>
                <div><i data-lucide="box" class="size-2.5 text-primary-500"></i></div>
                <div><i data-lucide="box" class="size-3 text-primary-500"></i></div>
                <div><i data-lucide="box" class="size-3.5 text-primary-500"></i></div>
                <div><i data-lucide="box" class="size-4 text-primary-500"></i></div>
                <div><i data-lucide="box" class="size-5 text-primary-500"></i></div>
                <div><i data-lucide="box" class="size-6 text-primary-500"></i></div>
                <div><i data-lucide="box" class="size-7 text-primary-500"></i></div>
                <div><i data-lucide="box" class="size-8 text-primary-500"></i></div>
                <div><i data-lucide="box" class="size-9 text-primary-500"></i></div>
                <div><i data-lucide="box" class="size-10 text-primary-500"></i></div>
                <div><i data-lucide="box" class="size-11 text-primary-500"></i></div>
                <div><i data-lucide="box" class="size-12 text-primary-500"></i></div>
                <div><i data-lucide="box" class="size-14 text-primary-500"></i></div>
                <div><i data-lucide="box" class="size-16 text-primary-500"></i></div>
                <div><i data-lucide="box" class="size-20 text-primary-500"></i></div>
                <div><i data-lucide="box" class="size-24 text-primary-500"></i></div>
                <div><i data-lucide="box" class="size-28 text-primary-500"></i></div>
                <div><i data-lucide="box" class="size-32 text-primary-500"></i></div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Duotune Icons</h6>
        </div>
        <div class="card-body">
            <div class="flex flex-wrap items-center gap-5">
                <div><i data-lucide="apple" class="size-6 text-primary-500 fill-primary-500/20"></i></div>
                <div><i data-lucide="heart" class="text-red-500 size-6 fill-red-500/20"></i></div>
                <div><i data-lucide="shopping-cart" class="text-green-500 size-6 fill-green-500/20"></i></div>
                <div><i data-lucide="bell" class="size-6 text-sky-500 fill-sky-500/20"></i></div>
                <div><i data-lucide="shopping-bag" class="text-purple-500 size-6 fill-purple-500/20"></i></div>
                <div><i data-lucide="calendar-days" class="text-yellow-500 size-6 fill-yellow-500/20"></i></div>
                <div><i data-lucide="building-2" class="text-pink-500 size-6 fill-pink-500/20"></i></div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Hover Duotune Icons</h6>
        </div>
        <div class="card-body">
            <div class="flex flex-wrap items-center gap-5">
                <a href="#!"><i data-lucide="apple" class="transition duration-300 ease-linear size-6 text-primary-500 hover:fill-primary-500/20"></i></a>
                <a href="#!"><i data-lucide="heart" class="text-red-500 transition duration-300 ease-linear size-6 hover:fill-red-500/20"></i></a>
                <a href="#!"><i data-lucide="shopping-cart" class="text-green-500 transition duration-300 ease-linear size-6 hover:fill-green-500/20"></i></a>
                <a href="#!"><i data-lucide="bell" class="transition duration-300 ease-linear size-6 text-sky-500 hover:fill-sky-500/20"></i></a>
                <a href="#!"><i data-lucide="shopping-bag" class="text-purple-500 transition duration-300 ease-linear size-6 hover:fill-purple-500/20"></i></a>
                <a href="#!"><i data-lucide="calendar-days" class="text-yellow-500 transition duration-300 ease-linear size-6 hover:fill-yellow-500/20"></i></a>
                <a href="#!"><i data-lucide="building-2" class="text-pink-500 transition duration-300 ease-linear size-6 hover:fill-pink-500/20"></i></a>
            </div>
        </div>
    </div><!--end col-->
</div><!--end grid-->



</div>
{{> partials/footer }}
</div>

{{> partials/vendor-scripts }}

<script type="module" src="assets/js/ui/advanced-highlight-code.init.js"></script>
<script type="module" src="assets/js/main.js"></script>

</body>
</html>