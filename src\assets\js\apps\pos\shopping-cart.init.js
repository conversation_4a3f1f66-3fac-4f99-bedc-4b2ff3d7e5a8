document.addEventListener('alpine:init', () => {
    Alpine.data('shoppingCart', () => ({
        items: [
            {
                id: 1,
                name: 'Peppermint Mocha Holiday Cheer',
                price: 14.99,
                quantity: 1,
                size: 'Large',
                temperature: 'Hot',
                image: 'assets/images/pos/img-04.png',
                category: 'coffee',
                customizations: {
                    extraShot: false,
                    whippedCream: false,
                    caramelDrizzle: false
                },
                instructions: ''
            },
            {
                id: 2,
                name: 'Caramel Macchiato Delight',
                price: 16.99,
                quantity: 1,
                size: 'Medium',
                temperature: 'Iced',
                image: 'assets/images/pos/img-16.png',
                category: 'coffee',
                customizations: {
                    extraShot: false,
                    whippedCream: false,
                    caramelDrizzle: false
                },
                instructions: ''
            },
            {
                id: 3,
                name: 'Prosciutto Fig Balsamic Glaze',
                price: 33.99,
                quantity: 1,
                size: 'Large',
                temperature: 'Hot',
                image: 'assets/images/pos/img-14.png',
                category: 'coffee',
                customizations: {
                    extraShot: false,
                    whippedCream: false,
                    caramelDrizzle: false
                },
                instructions: ''
            }
        ],
        discountCode: '',
        discountAmount: 0,
        subtotal: 0,
        vat: 0,
        total: 0,
        notification: {
            show: false,
            message: '',
            type: 'success'
        },

        init() {
            this.calculateTotals();
        },

        calculateTotals() {
            this.subtotal = this.items.reduce((sum, item) => {
                let itemTotal = item.price * item.quantity;
                // Add customization costs
                if (item.customizations.extraShot) itemTotal += 1.00 * item.quantity;
                if (item.customizations.whippedCream) itemTotal += 0.50 * item.quantity;
                if (item.customizations.caramelDrizzle) itemTotal += 0.50 * item.quantity;
                if (item.customizations.extraCheese) itemTotal += 1.00 * item.quantity;
                if (item.customizations.extraPatty) itemTotal += 2.00 * item.quantity;
                if (item.customizations.bacon) itemTotal += 1.50 * item.quantity;
                if (item.customizations.extraToppings) itemTotal += 2.00 * item.quantity;
                if (item.customizations.glutenFree) itemTotal += 2.00 * item.quantity;
                return sum + itemTotal;
            }, 0);
            
            this.vat = this.subtotal * 0.15;
            this.total = this.subtotal + this.vat - this.discountAmount;
        },

        getItemSubtotal(item) {
            let itemTotal = item.price * item.quantity;
            if (item.customizations.extraShot) itemTotal += 1.00 * item.quantity;
            if (item.customizations.whippedCream) itemTotal += 0.50 * item.quantity;
            if (item.customizations.caramelDrizzle) itemTotal += 0.50 * item.quantity;
            if (item.customizations.extraCheese) itemTotal += 1.00 * item.quantity;
            if (item.customizations.extraPatty) itemTotal += 2.00 * item.quantity;
            if (item.customizations.bacon) itemTotal += 1.50 * item.quantity;
            if (item.customizations.extraToppings) itemTotal += 2.00 * item.quantity;
            if (item.customizations.glutenFree) itemTotal += 2.00 * item.quantity;
            return itemTotal.toFixed(2);
        },

        updateQuantity(item, change) {
            const newQuantity = item.quantity + change;
            if (newQuantity > 0) {
                item.quantity = newQuantity;
                this.calculateTotals();
            }
        },

        removeItem(id) {
            this.items = this.items.filter(item => item.id !== id);
            this.calculateTotals();
        },

        applyDiscount() {
            const discountCodes = {
                'WELCOME10': 0.10,
                'SUMMER20': 0.20,
                'HOLIDAY25': 0.25
            };

            const code = this.discountCode.toLowerCase();
            if (discountCodes[code]) {
                this.discountAmount = this.subtotal * discountCodes[code];
                this.calculateTotals();
                this.showNotification('Discount applied successfully!', 'success');
            } else {
                this.discountAmount = 0;
                this.calculateTotals();
                this.showNotification('Invalid discount code', 'error');
            }
        },

        updateCustomization(item, type) {
            item.customizations[type] = !item.customizations[type];
            this.calculateTotals();
        },

        updateInstructions(item, value) {
            item.instructions = value;
        },

        showNotification(message, type) {
            this.notification = {
                show: true,
                message,
                type
            };
            setTimeout(() => {
                this.notification.show = false;
            }, 3000);
        },

        proceedToCheckout() {
            console.log('Proceeding to checkout with items:', this.items);
            console.log('Total amount:', this.total);
        }
    }));
});
