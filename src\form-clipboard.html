{{> partials/main }}

<head>

    {{> partials/title-meta title="Editors" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}

{{> partials/page-heading title="Clipboard" sub-title="Forms & Tables" }}

<div class="grid grid-cols-12">
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Input with Clipboard</h6>
        </div>
        <div class="card-body">
            <div class="relative">
                <input type="text" id="basicClipboard" class="ltr:pr-8 rtl:pl-8 form-input" value="https://github.com/zenorocha/clipboard.js.git">
                <button data-clipboard-target="#basicClipboard" class="absolute inset-y-0 flex items-center p-0 text-gray-500 dark:text-dark-500 ltr:right-3 dark:hover:text-primary-500 hover:text-primary-500 focus:text-primary-500 dark:focus:text-primary-500 rtl:left-3 focus:outline-hidden btn focus:outline-0">
                    <i data-lucide="clipboard" class="size-5"></i>
                </button>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Textarea with Clipboard</h6>
        </div>
        <div class="card-body">
            <div class="relative">
                <textarea id="bar" class="h-auto form-input">Utility classes help you work within the constraints of a system instead of littering your stylesheets with arbitrary values. They make it easy to be consistent with color choices, spacing, typography, shadows, and everything else that makes up a well-engineered design system.</textarea>
                <div class="ltr:text-right rtl:text-left">
                    <button class="mt-4 btn btn-primary" data-clipboard-action="cut" data-clipboard-target="#bar">
                        Cut to clipboard
                    </button>
                </div>
            </div>
        </div>
    </div><!--end col-->
</div><!--end grid-->

</div>
{{> partials/footer }}
</div>


{{> partials/vendor-scripts }}

<script type="module" src="assets/js/form/clipboard.init.js"></script>

<script type="module" src="assets/js/main.js"></script>

</body>
</html>