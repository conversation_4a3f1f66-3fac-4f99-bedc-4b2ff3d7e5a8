{{> partials/main }}

<head>

    {{> partials/title-meta title="Bordered Datatables" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}

{{> partials/page-heading title="Bordered" sub-title="Datatables" }}

<div class="grid grid-cols-12 gap-x-space">
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Bordered</h6>
        </div>
        <div class="card-body">
            <div>
                <div class="overflow-x-auto">
                    <table id="example" class="display group bordered" style="width:100%">
                        <thead>
                            <tr>
                                <th class="ltr:text-left rtl:text-right">Name</th>
                                <th class="ltr:text-left rtl:text-right">Position</th>
                                <th class="ltr:text-left rtl:text-right">Office</th>
                                <th class="ltr:text-left rtl:text-right">Age</th>
                                <th class="ltr:text-left rtl:text-right">Start date</th>
                                <th class="ltr:text-left rtl:text-right">Salary</th>
                            </tr>
                        </thead>
                        <tbody>
                        </tbody>
                        <tfoot>
                            <tr>
                                <th class="ltr:text-left rtl:text-right">Name</th>
                                <th class="ltr:text-left rtl:text-right">Position</th>
                                <th class="ltr:text-left rtl:text-right">Office</th>
                                <th class="ltr:text-left rtl:text-right">Age</th>
                                <th class="ltr:text-left rtl:text-right">Start date</th>
                                <th class="ltr:text-left rtl:text-right">Salary</th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
    </div><!--end col-->
</div><!--end grid-->

</div>
{{> partials/footer }}
</div>

{{> partials/vendor-scripts }}

<script src="assets/js/table/jquery-3.7.1.js"></script>
<script src="assets/js/table/dataTables.js"></script>
<script src="assets/js/table/dataTables.responsive.js"></script>
<script src="assets/js/table/dataTables.tailwindcss.js"></script>
<script type="module" src="assets/js/table/datatables-basic.init.js"></script>
<script type="module" src="assets/js/main.js"></script>

</body>
</html>