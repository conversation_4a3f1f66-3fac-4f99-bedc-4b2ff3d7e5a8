<div id="settingsModal" class="!hidden modal show">
    <div class="modal-wrap modal-center modal-xl">
        <div class="modal-header">
            <h6>Domiex Customize </h6>
            <button data-modal-close="settingsModal" class="link link-red"><i data-lucide="x" class="size-5"></i></button>
        </div>
        <div class="modal-content">
            <div>
                <h6 class="mb-3">Select Layout:</h6>
                <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-space">
                    <div class="flex-col gap-0 input-radio-group">
                        <input @change="setLayout('default')" id="defaultLayout" name="layout" type="radio" x-model="layout" value="default" class="hidden input-radio peer">
                        <label for="defaultLayout" class="block w-full mb-3 overflow-hidden cursor-pointer card h-28 peer-checked:border-primary-500">
                            <span class="block h-full">
                                <span class="flex gap-1 px-4 py-1.5 bg-gray-200/50 dark:bg-dark-800/50">
                                    <span class="inline-block bg-red-500 rounded-full size-1.5"></span>
                                    <span class="inline-block bg-green-500 rounded-full size-1.5"></span>
                                    <span class="inline-block rounded-full bg-yellow-500 size-1.5"></span>
                                </span>
                                <span class="grid h-[calc(100%_-_8px)] grid-cols-12">
                                    <span class="h-[calc(100%_-_8px)] col-span-2 bg-gray-50 dark:bg-dark-850"></span>
                                    <span class="h-[calc(100%_-_8px)] col-span-10 p-1.5 inline-block">
                                        <span class="block w-1/3 h-1.5 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                                        <span class="block w-1/2 h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                                        <span class="block w-full h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                                        <span class="block w-1/3 h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                                        <span class="block w-2/3 h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                                    </span>
                                </span>
                            </span>
                        </label>
                        <label for="defaultLayout" class="cursor-pointer form-label">Default</label>
                    </div>
                    <div class="flex-col gap-0 input-radio-group">
                        <input @change="setLayout('horizontal')" id="horizontalLayout" name="layout" type="radio" x-model="layout" value="horizontal" class="hidden input-radio peer">
                        <label for="horizontalLayout" class="block w-full mb-3 overflow-hidden cursor-pointer card h-28 peer-checked:border-primary-500">
                            <span class="block h-full">
                                <span class="flex gap-1 px-4 py-1.5 bg-gray-200/50 dark:bg-dark-800/50">
                                    <span class="inline-block bg-red-500 rounded-full size-1.5"></span>
                                    <span class="inline-block bg-green-500 rounded-full size-1.5"></span>
                                    <span class="inline-block rounded-full bg-yellow-500 size-1.5"></span>
                                </span>
                                <span class="block h-2 bg-gray-50 dark:bg-dark-850"></span>
                                <span class="grid grid-cols-12 gap-0">
                                    <span class="inline-block col-span-12 p-2">
                                        <span class="block w-1/3 h-1.5 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                                        <span class="block w-1/2 h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                                        <span class="block w-full h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                                        <span class="block w-1/3 h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                                        <span class="block w-2/3 h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                                    </span>
                                </span>
                            </span>
                        </label>
                        <label for="horizontalLayout" class="cursor-pointer form-label">Horizontal</label>
                    </div>
                    <div class="flex-col hidden gap-0 input-radio-group lg:flex">
                        <input @change="setLayout('modern')" id="modernLayout" name="layout" type="radio" x-model="layout" value="modern" class="hidden input-radio peer">
                        <label for="modernLayout" class="block w-full mb-3 overflow-hidden cursor-pointer card h-28 peer-checked:border-primary-500">
                            <span class="flex h-full">
                                <span class="w-3 h-full shrink-0 bg-gray-50 dark:bg-dark-850"></span>
                                <span class="grow">
                                    <span class="flex gap-1 px-4 py-1.5 bg-gray-200/50 dark:bg-dark-800/50">
                                        <span class="inline-block bg-red-500 rounded-full size-1.5"></span>
                                        <span class="inline-block bg-green-500 rounded-full size-1.5"></span>
                                        <span class="inline-block rounded-full bg-yellow-500 size-1.5"></span>
                                    </span>
                                    <span class="p-1.5 block">
                                        <span class="block w-1/3 h-1.5 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                                        <span class="block w-1/2 h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                                        <span class="block w-full h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                                        <span class="block w-1/3 h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                                        <span class="block w-2/3 h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                                    </span>
                                </span>
                            </span>
                        </label>
                        <label for="modernLayout" class="cursor-pointer form-label">Modern</label>
                    </div>
                    <div class="flex-col gap-0 input-radio-group">
                        <input @change="setLayout('boxed')" id="boxedLayout" name="layout" type="radio" x-model="layout" value="boxed" class="hidden input-radio peer">
                        <label for="boxedLayout" class="block w-full p-1.5 mb-3 overflow-hidden cursor-pointer card h-28 peer-checked:border-primary-500">
                            <span class="block h-full">
                                <span class="flex gap-1 px-4 py-1.5 bg-gray-200/50 dark:bg-dark-800/50">
                                    <span class="inline-block bg-red-500 rounded-full size-1.5"></span>
                                    <span class="inline-block bg-green-500 rounded-full size-1.5"></span>
                                    <span class="inline-block rounded-full bg-yellow-500 size-1.5"></span>
                                </span>
                                <span class="grid h-[calc(100%_-_8px)] grid-cols-12">
                                    <span class="h-[calc(100%_-_8px)] col-span-2 bg-gray-50 dark:bg-dark-850"></span>
                                    <span class="h-[calc(100%_-_8px)] col-span-10 p-1.5 inline-block">
                                        <span class="block w-1/3 h-1.5 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                                        <span class="block w-1/2 h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                                        <span class="block w-full h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                                        <span class="block w-1/3 h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                                        <span class="block w-2/3 h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                                    </span>
                                </span>
                            </span>
                        </label>
                        <label for="boxedLayout" class="cursor-pointer form-label">Boxed</label>
                    </div>
                    <div class="flex-col hidden gap-0 input-radio-group lg:flex">
                        <input @change="setLayout('semibox')" id="semiboxLayout" name="layout" type="radio" x-model="layout" value="semibox" class="hidden input-radio peer">
                        <label for="semiboxLayout" class="block w-full p-1.5 mb-3 overflow-hidden cursor-pointer card h-28 peer-checked:border-primary-500">
                            <span class="block h-full">
                                <span class="flex gap-1 px-4 py-1.5 bg-gray-200/50 dark:bg-dark-800/50">
                                    <span class="inline-block bg-red-500 rounded-full size-1.5"></span>
                                    <span class="inline-block bg-green-500 rounded-full size-1.5"></span>
                                    <span class="inline-block rounded-full bg-yellow-500 size-1.5"></span>
                                </span>
                                <span class="grid h-[calc(100%_-_8px)] grid-cols-12">
                                    <span class="h-[calc(100%_-_8px)] col-span-2 bg-gray-50 dark:bg-dark-850"></span>
                                    <span class="h-[calc(100%_-_8px)] col-span-10 p-1.5 inline-block">
                                        <span class="block w-1/3 h-1.5 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                                        <span class="block w-1/2 h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                                        <span class="block w-full h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                                        <span class="block w-1/3 h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                                        <span class="block w-2/3 h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                                    </span>
                                </span>
                            </span>
                        </label>
                        <label for="semiboxLayout" class="cursor-pointer form-label">Semibox</label>
                    </div>
                </div>

                <template x-if="layout === 'modern'">
                    <div class="hidden lg:block">
                        <h6 class="my-3">Navigation Types</h6>
                        <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-space">
                            <div class="input-radio-group">
                                <input @change="setNavigation('default')" id="defaultType" name="navType" type="radio" x-model="navType" value="default" class="input-radio input-radio-primary">
                                <label for="defaultType" class="input-radio-label">Default</label>
                            </div>
                            <div class="input-radio-group">
                                <input @change="setNavigation('floating')" id="floatingType" name="navType" type="radio" x-model="navType" value="floating" class="input-radio input-radio-primary">
                                <label for="floatingType" class="input-radio-label">Floating</label>
                            </div>
                            <div class="input-radio-group">
                                <input @change="setNavigation('boxed')" id="boxedType" name="navType" type="radio" x-model="navType" value="boxed" class="input-radio input-radio-primary">
                                <label for="boxedType" class="input-radio-label">Boxed</label>
                            </div>
                            <div class="input-radio-group">
                                <input @change="setNavigation('pattern')" id="patternType" name="navType" type="radio" x-model="navType" value="pattern" class="input-radio input-radio-primary">
                                <label for="patternType" class="input-radio-label">Pattern</label>
                            </div>
                        </div>
                    </div>
                </template>

                <div class="hidden xl:block">
                    <h6 class="my-4">Content Widths:</h6>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-space">
                        <div class="flex-col gap-0 input-radio-group">
                            <input @change="setContentWidth('default')" id="defaultContent" name="contentWidth" type="radio" x-model="contentWidth" value="default" class="hidden input-radio peer">
                            <label for="defaultContent" class="block w-full mb-3 overflow-hidden cursor-pointer card h-28 peer-checked:border-primary-500">
                                <span class="block h-full">
                                    <span class="flex gap-1 px-4 py-1.5 bg-gray-200/50 dark:bg-dark-800/50">
                                        <span class="inline-block bg-red-500 rounded-full size-1.5"></span>
                                        <span class="inline-block bg-green-500 rounded-full size-1.5"></span>
                                        <span class="inline-block rounded-full bg-yellow-500 size-1.5"></span>
                                    </span>
                                    <span class="grid h-[calc(100%_-_8px)] grid-cols-12">
                                        <span class="h-[calc(100%_-_8px)] col-span-2 bg-gray-50 dark:bg-dark-850"></span>
                                        <span class="h-[calc(100%_-_8px)] col-span-10 py-1.5 px-4 inline-block">
                                            <span class="block w-1/3 h-1.5 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                                            <span class="block w-1/2 h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                                            <span class="block w-full h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                                            <span class="block w-1/3 h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                                            <span class="block w-2/3 h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                                        </span>
                                    </span>
                                </span>
                            </label>
                            <label for="defaultContent" class="cursor-pointer form-label">Default</label>
                        </div>
                        <div class="flex-col gap-0 input-radio-group">
                            <input @change="setContentWidth('fluid')" id="fluidLayout" name="contentWidth" type="radio" x-model="contentWidth" value="fluid" class="hidden input-radio peer">
                            <label for="fluidLayout" class="block w-full mb-3 overflow-hidden cursor-pointer card h-28 peer-checked:border-primary-500">
                                <span class="block h-full">
                                    <span class="flex gap-1 px-4 py-1.5 bg-gray-200/50 dark:bg-dark-800/50">
                                        <span class="inline-block bg-red-500 rounded-full size-1.5"></span>
                                        <span class="inline-block bg-green-500 rounded-full size-1.5"></span>
                                        <span class="inline-block rounded-full bg-yellow-500 size-1.5"></span>
                                    </span>
                                    <span class="grid h-[calc(100%_-_8px)] grid-cols-12">
                                        <span class="h-[calc(100%_-_8px)] col-span-2 bg-gray-50 dark:bg-dark-850"></span>
                                        <span class="h-[calc(100%_-_8px)] col-span-10 p-1.5 inline-block">
                                            <span class="block w-1/3 h-1.5 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                                            <span class="block w-1/2 h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                                            <span class="block w-full h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                                            <span class="block w-1/3 h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                                            <span class="block w-2/3 h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                                        </span>
                                    </span>
                                </span>
                            </label>
                            <label for="fluidLayout" class="cursor-pointer form-label">Fluid</label>
                        </div>
                    </div>
                </div>

                <template x-if="layout !== 'horizontal'">
                    <div class="hidden lg:block">
                        <h6 class="my-4">Sidebar Sizes:</h6>
                        <div class="grid grid-cols-2 md:grid-cols-5 gap-space">
                            <div class="flex-col gap-0 input-radio-group">
                                <input @change="setSidebar('large')" id="defaultSidebar" name="sidebar" type="radio" x-model="sidebar" value="large" class="hidden input-radio peer">
                                <label for="defaultSidebar" class="block w-full h-24 mb-3 overflow-hidden cursor-pointer card peer-checked:border-primary-500">
                                    <span class="block h-full">
                                        <span class="flex gap-1 px-4 py-1.5 bg-gray-200/50 dark:bg-dark-800/50">
                                            <span class="inline-block bg-red-500 rounded-full size-1.5"></span>
                                            <span class="inline-block bg-green-500 rounded-full size-1.5"></span>
                                            <span class="inline-block rounded-full bg-yellow-500 size-1.5"></span>
                                        </span>
                                        <span class="grid h-[calc(100%_-_8px)] grid-cols-12">
                                            <span class="h-[calc(100%_-_8px)] col-span-3 bg-gray-50 dark:bg-dark-850"></span>
                                            <span class="h-[calc(100%_-_8px)] col-span-9 py-1.5 px-4 inline-block">
                                                <span class="block w-1/3 h-1.5 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                                                <span class="block w-1/2 h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                                                <span class="block w-full h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                                                <span class="block w-1/3 h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                                                <span class="block w-2/3 h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                                            </span>
                                        </span>
                                    </span>
                                </label>
                                <label for="defaultSidebar" class="cursor-pointer form-label">Default (Large)</label>
                            </div>
                            <div class="flex-col gap-0 input-radio-group">
                                <input @change="setSidebar('medium')" id="mediumSidebar" name="sidebar" type="radio" x-model="sidebar" value="medium" class="hidden input-radio peer">
                                <label for="mediumSidebar" class="block w-full h-24 mb-3 overflow-hidden cursor-pointer card peer-checked:border-primary-500">
                                    <span class="block h-full">
                                        <span class="flex gap-1 px-4 py-1.5 bg-gray-200/50 dark:bg-dark-800/50">
                                            <span class="inline-block bg-red-500 rounded-full size-1.5"></span>
                                            <span class="inline-block bg-green-500 rounded-full size-1.5"></span>
                                            <span class="inline-block rounded-full bg-yellow-500 size-1.5"></span>
                                        </span>
                                        <span class="grid h-[calc(100%_-_8px)] grid-cols-12">
                                            <span class="h-[calc(100%_-_8px)] col-span-2 bg-gray-50 dark:bg-dark-850"></span>
                                            <span class="h-[calc(100%_-_8px)] col-span-10 py-1.5 px-4 inline-block">
                                                <span class="block w-1/3 h-1.5 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                                                <span class="block w-1/2 h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                                                <span class="block w-full h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                                                <span class="block w-1/3 h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                                                <span class="block w-2/3 h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                                            </span>
                                        </span>
                                    </span>
                                </label>
                                <label for="mediumSidebar" class="cursor-pointer form-label">Medium</label>
                            </div>
                            <div class="flex-col gap-0 input-radio-group">
                                <input @change="setSidebar('small')" id="smallSidebar" name="sidebar" type="radio" x-model="sidebar" value="small" class="hidden input-radio peer">
                                <label for="smallSidebar" class="block w-full h-24 mb-3 overflow-hidden cursor-pointer card peer-checked:border-primary-500">
                                    <span class="block h-full">
                                        <span class="flex gap-1 px-4 py-1.5 bg-gray-200/50 dark:bg-dark-800/50">
                                            <span class="inline-block bg-red-500 rounded-full size-1.5"></span>
                                            <span class="inline-block bg-green-500 rounded-full size-1.5"></span>
                                            <span class="inline-block rounded-full bg-yellow-500 size-1.5"></span>
                                        </span>
                                        <span class="grid h-[calc(100%_-_8px)] grid-cols-12">
                                            <span class="h-[calc(100%_-_8px)] col-span-1 bg-gray-50 dark:bg-dark-850"></span>
                                            <span class="h-[calc(100%_-_8px)] col-span-11 py-1.5 px-4 inline-block">
                                                <span class="block w-1/3 h-1.5 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                                                <span class="block w-1/2 h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                                                <span class="block w-full h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                                                <span class="block w-1/3 h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                                                <span class="block w-2/3 h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                                            </span>
                                        </span>
                                    </span>
                                </label>
                                <label for="smallSidebar" class="cursor-pointer form-label">Small</label>
                            </div>
                        </div>
                    </div>
                </template>

                <h6 class="my-4">Layout Directions:</h6>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-space">
                    <div class="flex-col gap-0 input-radio-group">
                        <input id="ltrMode" @change="setDirection('ltr')" name="layoutDirection" type="radio" x-model="layoutDir" value="ltr" class="hidden input-radio peer">
                        <label for="ltrMode" class="block w-full mb-3 overflow-hidden cursor-pointer card h-28 peer-checked:border-primary-500">
                            <span class="block h-full">
                                <span class="flex gap-1 px-4 py-1.5 bg-gray-200/50 dark:bg-dark-800/50">
                                    <span class="inline-block bg-red-500 rounded-full size-1.5"></span>
                                    <span class="inline-block bg-green-500 rounded-full size-1.5"></span>
                                    <span class="inline-block rounded-full bg-yellow-500 size-1.5"></span>
                                </span>
                                <span class="grid h-[calc(100%_-_8px)] grid-cols-12">
                                    <span class="h-[calc(100%_-_8px)] col-span-2 bg-gray-50 dark:bg-dark-850"></span>
                                    <span class="h-[calc(100%_-_8px)] col-span-10 py-1.5 px-4 inline-block">
                                        <span class="block w-1/3 h-1.5 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                                        <span class="block w-1/2 h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                                        <span class="block w-full h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                                        <span class="block w-1/3 h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                                        <span class="block w-2/3 h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                                    </span>
                                </span>
                            </span>
                        </label>
                        <label for="ltrMode" class="cursor-pointer form-label">LTR</label>
                    </div>
                    <div class="flex-col gap-0 input-radio-group">
                        <input @change="setDirection('rtl')" id="rtlMode" name="layoutDirection" type="radio" x-model="layoutDir" value="rtl" class="hidden input-radio peer">
                        <label for="rtlMode" class="block w-full mb-3 overflow-hidden cursor-pointer card h-28 peer-checked:border-primary-500">
                            <span class="block h-full">
                                <span class="flex gap-1 px-4 py-1.5 bg-gray-200/50 justify-end dark:bg-dark-800/50">
                                    <span class="inline-block bg-red-500 rounded-full size-1.5"></span>
                                    <span class="inline-block bg-green-500 rounded-full size-1.5"></span>
                                    <span class="inline-block rounded-full bg-yellow-500 size-1.5"></span>
                                </span>
                                <span class="grid h-[calc(100%_-_8px)] grid-cols-12">
                                    <span class="h-[calc(100%_-_8px)] col-span-10 p-1.5 inline-block">
                                        <span class="block w-1/3 h-1.5 bg-gray-100 rounded-md dark:bg-dark-850 ml-auto"></span>
                                        <span class="block w-1/2 h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850 ml-auto"></span>
                                        <span class="block w-full h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850 ml-auto"></span>
                                        <span class="block w-1/3 h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850 ml-auto"></span>
                                        <span class="block w-2/3 h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850 ml-auto"></span>
                                    </span>
                                    <span class="h-[calc(100%_-_8px)] col-span-2 bg-gray-50 dark:bg-dark-850"></span>
                                </span>
                            </span>
                        </label>
                        <label for="rtlMode" class="cursor-pointer form-label">RTL</label>
                    </div>
                </div>

                <h6 class="my-4">Layout Modes:</h6>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-space">
                    <div class="flex-col gap-0 input-radio-group">
                        <input @change="setLayoutMode('light')" id="lightMode" name="layoutMode" type="radio" x-model="curLayoutMode" value="light" class="hidden input-radio peer">
                        <label for="lightMode" class="block w-full mb-3 overflow-hidden cursor-pointer card h-28 peer-checked:border-primary-500">
                            <span class="block h-full">
                                <span class="flex gap-1 px-4 py-1.5 bg-gray-200/50 dark:bg-dark-800/50">
                                    <span class="inline-block bg-red-500 rounded-full size-1.5"></span>
                                    <span class="inline-block bg-green-500 rounded-full size-1.5"></span>
                                    <span class="inline-block rounded-full bg-yellow-500 size-1.5"></span>
                                </span>
                                <span class="grid h-[calc(100%_-_8px)] grid-cols-12">
                                    <span class="h-[calc(100%_-_8px)] col-span-2 bg-gray-50 dark:bg-dark-850"></span>
                                    <span class="h-[calc(100%_-_8px)] col-span-10 p-1.5 inline-block">
                                        <span class="block w-1/3 h-1.5 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                                        <span class="block w-1/2 h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                                        <span class="block w-full h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                                        <span class="block w-1/3 h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                                        <span class="block w-2/3 h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                                    </span>
                                </span>
                            </span>
                        </label>
                        <label for="lightMode" class="cursor-pointer form-label">Light</label>
                    </div>
                    <div class="flex-col gap-0 input-radio-group">
                        <input @change="setLayoutMode('dark')" id="darkMode" name="layoutMode" type="radio" x-model="curLayoutMode" value="dark" class="hidden input-radio peer">
                        <label for="darkMode" class="block w-full mb-3 overflow-hidden cursor-pointer border-dark-700 bg-dark-950 card h-28 peer-checked:border-primary-500">
                            <span class="block h-full">
                                <span class="flex gap-1 px-4 py-1.5 bg-dark-700/50">
                                    <span class="inline-block bg-red-500 rounded-full size-1.5"></span>
                                    <span class="inline-block bg-green-500 rounded-full size-1.5"></span>
                                    <span class="inline-block rounded-full bg-yellow-500 size-1.5"></span>
                                </span>
                                <span class="grid h-[calc(100%_-_8px)] grid-cols-12">
                                    <span class="h-[calc(100%_-_8px)] col-span-2 bg-dark-900 dark:bg-dark-850"></span>
                                    <span class="h-[calc(100%_-_8px)] col-span-10 p-1.5 inline-block">
                                        <span class="block w-1/3 h-1.5 bg-dark-900 dark:bg-dark-850 rounded-md"></span>
                                        <span class="block w-1/2 h-1.5 mt-1 bg-dark-900 dark:bg-dark-850 rounded-md"></span>
                                        <span class="block w-full h-1.5 mt-1 bg-dark-900 dark:bg-dark-850 rounded-md"></span>
                                        <span class="block w-1/3 h-1.5 mt-1 bg-dark-900 dark:bg-dark-850 rounded-md"></span>
                                        <span class="block w-2/3 h-1.5 mt-1 bg-dark-900 dark:bg-dark-850 rounded-md"></span>
                                    </span>
                                </span>
                            </span>
                        </label>
                        <label for="darkMode" class="cursor-pointer form-label">Dark</label>
                    </div>
                    <div class="flex-col gap-0 input-radio-group">
                        <input @change="setLayoutMode('auto')" id="autoMode" name="layoutMode" type="radio" x-model="curLayoutMode" value="auto" class="hidden input-radio peer">
                        <label for="autoMode" class="relative block w-full mb-3 overflow-hidden cursor-pointer card h-28 peer-checked:border-primary-500 before:absolute before:bg-gray-950 before:w-1/2 before:inset-y-0 before:right-0">
                            <span class="relative block h-full">
                                <span class="flex gap-1 px-4 py-1.5 bg-gray-200/50 dark:bg-dark-800/50">
                                    <span class="inline-block bg-red-500 rounded-full size-1.5"></span>
                                    <span class="inline-block bg-green-500 rounded-full size-1.5"></span>
                                    <span class="inline-block rounded-full bg-yellow-500 size-1.5"></span>
                                </span>
                                <span class="grid h-[calc(100%_-_8px)] grid-cols-12">
                                    <span class="h-[calc(100%_-_8px)] col-span-2 bg-gray-50 dark:bg-dark-850"></span>
                                    <span class="h-[calc(100%_-_8px)] col-span-10 p-1.5 inline-block">
                                        <span class="block w-1/3 h-1.5 bg-gray-100/50 dark:bg-dark-850 rounded-md"></span>
                                        <span class="block w-1/2 h-1.5 mt-1 bg-gray-100/50 dark:bg-dark-850 rounded-md"></span>
                                        <span class="block w-full h-1.5 mt-1 bg-gray-100/50 dark:bg-dark-850 rounded-md"></span>
                                        <span class="block w-1/3 h-1.5 mt-1 bg-gray-100/50 dark:bg-dark-850 rounded-md"></span>
                                        <span class="block w-2/3 h-1.5 mt-1 bg-gray-100/50 dark:bg-dark-850 rounded-md"></span>
                                    </span>
                                </span>
                            </span>
                        </label>
                        <label for="autoMode" class="cursor-pointer form-label">Default System</label>
                    </div>
                    <div class="flex-col gap-0 input-radio-group">
                        <input @change="setLayoutMode('black-white')" id="blackWhiteMode" name="layoutMode" type="radio" x-model="curLayoutMode" value="black-white" class="hidden input-radio peer">
                        <label for="blackWhiteMode" class="block w-full mb-3 overflow-hidden cursor-pointer card h-28 peer-checked:border-primary-500 grayscale">
                            <span class="block h-full">
                                <span class="flex gap-1 px-4 py-1.5 bg-gray-200/50 dark:bg-dark-800/50">
                                    <span class="inline-block bg-red-500 rounded-full size-1.5"></span>
                                    <span class="inline-block bg-green-500 rounded-full size-1.5"></span>
                                    <span class="inline-block rounded-full bg-yellow-500 size-1.5"></span>
                                </span>
                                <span class="grid h-[calc(100%_-_8px)] grid-cols-12">
                                    <span class="h-[calc(100%_-_8px)] col-span-2 bg-gray-50 dark:bg-dark-850"></span>
                                    <span class="h-[calc(100%_-_8px)] col-span-10 p-1.5 inline-block">
                                        <span class="block w-1/3 h-1.5 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                                        <span class="block w-1/2 h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                                        <span class="block w-full h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                                        <span class="block w-1/3 h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                                        <span class="block w-2/3 h-1.5 mt-1 bg-gray-100 rounded-md dark:bg-dark-850"></span>
                                    </span>
                                </span>
                            </span>
                        </label>
                        <label for="blackWhiteMode" class="cursor-pointer form-label">Black & White</label>
                    </div>
                </div>

                <template x-if="layoutMode === 'dark'">
                    <div>
                        <h6 class="my-4 darkModeColors">Dark Mode Colors:</h6>
                        <div class="flex flex-wrap items-center gap-3 darkModeColors">
                            <div class="input-radio-group">
                                <input @change="setDarkModeColors('default')" id="noneColors" name="darkModeColors" x-model="darkModeColors" type="radio" value="default" class="hidden input-radio peer">
                                <label for="noneColors" class="flex items-center justify-center border border-gray-200 rounded-full dark:border-dark-800 input-radio-label size-10 peer-checked:ring-1 peer-checked:ring-offset-2 dark:peer-checked:ring-offset-dark-900 peer-checked:ring-primary-400">
                                    <i data-lucide="x" class="size-4"></i>
                                </label>
                            </div>
                            <div class="input-radio-group">
                                <input @change="setDarkModeColors('zinc')" id="zincColors" name="darkModeColors" x-model="darkModeColors" type="radio" value="zinc" class="hidden input-radio peer">
                                <label for="zincColors" class="rounded-full bg-zinc-950 input-radio-label size-10 peer-checked:ring-1 peer-checked:ring-offset-2 dark:peer-checked:ring-offset-dark-900 peer-checked:ring-primary-400"></label>
                            </div>
                            <div class="input-radio-group">
                                <input @change="setDarkModeColors('stone')" id="stoneColors" name="darkModeColors" x-model="darkModeColors" type="radio" value="stone" class="hidden input-radio peer">
                                <label for="stoneColors" class="rounded-full bg-stone-950 input-radio-label size-10 peer-checked:ring-1 peer-checked:ring-offset-2 dark:peer-checked:ring-offset-dark-900 peer-checked:ring-primary-400"></label>
                            </div>
                            <div class="input-radio-group">
                                <input @change="setDarkModeColors('neutral')" id="neutralColors" name="darkModeColors" x-model="darkModeColors" type="radio" value="neutral" class="hidden input-radio peer">
                                <label for="neutralColors" class="rounded-full bg-neutral-950 input-radio-label size-10 peer-checked:ring-1 peer-checked:ring-offset-2 dark:peer-checked:ring-offset-dark-900 peer-checked:ring-primary-400"></label>
                            </div>
                            <div class="input-radio-group">
                                <input @change="setDarkModeColors('gray')" id="grayColors" name="darkModeColors" x-model="darkModeColors" type="radio" value="gray" class="hidden input-radio peer">
                                <label for="grayColors" class="rounded-full bg-gray-950 input-radio-label size-10 peer-checked:ring-1 peer-checked:ring-offset-2 dark:peer-checked:ring-offset-dark-900 peer-checked:ring-primary-400"></label>
                            </div>
                        </div>
                    </div>
                </template>

                <template x-if="layout !== 'horizontal'">
                    <div>
                        <h6 class="my-4">Sidebar Asset Colors:</h6>
                        <div class="flex flex-wrap items-center gap-3">
                            <div class="input-radio-group">
                                <input @change="setSidebarColors('light')" id="lightSidebarColors" name="sidebarColors" x-model="sidebarColors" type="radio" value="light" class="hidden input-radio peer">
                                <label for="lightSidebarColors" class="bg-gray-100 rounded-full input-radio-label size-10 peer-checked:ring-1 peer-checked:ring-offset-2 dark:peer-checked:ring-offset-dark-900 peer-checked:ring-primary-400"></label>
                            </div>
                            <div class="input-radio-group">
                                <input @change="setSidebarColors('dark')" id="darkSidebarColors" name="sidebarColors" x-model="sidebarColors" type="radio" value="dark" class="hidden input-radio peer">
                                <label for="darkSidebarColors" class="bg-gray-800 rounded-full input-radio-label size-10 peer-checked:ring-1 peer-checked:ring-offset-2 dark:peer-checked:ring-offset-dark-900 peer-checked:ring-primary-400"></label>
                            </div>
                            <div class="input-radio-group">
                                <input @change="setSidebarColors('brand')" id="brandSidebarColors" name="sidebarColors" x-model="sidebarColors" type="radio" value="brand" class="hidden input-radio peer">
                                <label for="brandSidebarColors" class="rounded-full bg-primary-900 input-radio-label size-10 peer-checked:ring-1 peer-checked:ring-offset-2 dark:peer-checked:ring-offset-dark-900 peer-checked:ring-primary-400"></label>
                            </div>
                            <div class="input-radio-group">
                                <input @change="setSidebarColors('purple')" id="purpleSidebarColors" name="sidebarColors" x-model="sidebarColors" type="radio" value="purple" class="hidden input-radio peer">
                                <label for="purpleSidebarColors" class="rounded-full bg-purple-950 input-radio-label size-10 peer-checked:ring-1 peer-checked:ring-offset-2 dark:peer-checked:ring-offset-dark-900 peer-checked:ring-primary-400"></label>
                            </div>
                            <div class="input-radio-group">
                                <input @change="setSidebarColors('sky')" id="skySidebarColors" name="sidebarColors" x-model="sidebarColors" type="radio" value="sky" class="hidden input-radio peer">
                                <label for="skySidebarColors" class="rounded-full bg-sky-950 input-radio-label size-10 peer-checked:ring-1 peer-checked:ring-offset-2 dark:peer-checked:ring-offset-dark-900 peer-checked:ring-primary-400"></label>
                            </div>
                        </div>
                    </div>
                </template>

                <h6 class="my-4">Primary Asset Colors:</h6>
                <div class="flex flex-wrap items-center gap-3">
                    <div class="input-radio-group">
                        <input @change="setPrimaryColors('default')" id="defaultPrimaryColors" name="primaryColors" x-model="primaryColors" type="radio" value="default" class="hidden input-radio peer">
                        <label for="defaultPrimaryColors" class="rounded-full bg-[#358ffc] input-radio-label size-10 peer-checked:ring-1 peer-checked:ring-offset-2 dark:peer-checked:ring-offset-dark-900 peer-checked:ring-primary-400"></label>
                    </div>
                    <div class="input-radio-group">
                        <input @change="setPrimaryColors('green')" id="greenPrimaryColors" name="primaryColors" x-model="primaryColors" type="radio" value="green" class="hidden input-radio peer">
                        <label for="greenPrimaryColors" class="bg-[#1acd81] rounded-full input-radio-label size-10 peer-checked:ring-1 peer-checked:ring-offset-2 dark:peer-checked:ring-offset-dark-900 peer-checked:ring-primary-400"></label>
                    </div>
                    <div class="input-radio-group">
                        <input @change="setPrimaryColors('violet')" id="violetPrimaryColors" name="primaryColors" x-model="primaryColors" type="radio" value="violet" class="hidden input-radio peer">
                        <label for="violetPrimaryColors" class="rounded-full bg-violet-500 input-radio-label size-10 peer-checked:ring-1 peer-checked:ring-offset-2 dark:peer-checked:ring-offset-dark-900 peer-checked:ring-primary-400"></label>
                    </div>
                    <div class="input-radio-group">
                        <input @change="setPrimaryColors('orange')" id="orangePrimaryColors" name="primaryColors" x-model="primaryColors" type="radio" value="orange" class="hidden input-radio peer">
                        <label for="orangePrimaryColors" class="rounded-full bg-[#f04b1f] input-radio-label size-10 peer-checked:ring-1 peer-checked:ring-offset-2 dark:peer-checked:ring-offset-dark-900 peer-checked:ring-primary-400"></label>
                    </div>
                    <div class="input-radio-group">
                        <input @change="setPrimaryColors('teal')" id="tealPrimaryColors" name="primaryColors" x-model="primaryColors" type="radio" value="teal" class="hidden input-radio peer">
                        <label for="tealPrimaryColors" class="bg-teal-500 rounded-full input-radio-label size-10 peer-checked:ring-1 peer-checked:ring-offset-2 dark:peer-checked:ring-offset-dark-900 peer-checked:ring-primary-400"></label>
                    </div>
                    <div class="input-radio-group">
                        <input @change="setPrimaryColors('fuchsia')" id="fuchsiaPrimaryColors" name="primaryColors" x-model="primaryColors" type="radio" value="fuchsia" class="hidden input-radio peer">
                        <label for="fuchsiaPrimaryColors" class="rounded-full bg-fuchsia-500 input-radio-label size-10 peer-checked:ring-1 peer-checked:ring-offset-2 dark:peer-checked:ring-offset-dark-900 peer-checked:ring-primary-400"></label>
                    </div>
                    <div class="input-radio-group">
                        <input @change="setPrimaryColors('lime')" id="limePrimaryColors" name="primaryColors" x-model="primaryColors" type="radio" value="lime" class="hidden input-radio peer">
                        <label for="limePrimaryColors" class="rounded-full bg-lime-500 input-radio-label size-10 peer-checked:ring-1 peer-checked:ring-offset-2 dark:peer-checked:ring-offset-dark-900 peer-checked:ring-primary-400"></label>
                    </div>
                    <div class="input-radio-group">
                        <input @change="setPrimaryColors('amber')" id="amberPrimaryColors" name="primaryColors" x-model="primaryColors" type="radio" value="amber" class="hidden input-radio peer">
                        <label for="amberPrimaryColors" class="rounded-full bg-amber-500 input-radio-label size-10 peer-checked:ring-1 peer-checked:ring-offset-2 dark:peer-checked:ring-offset-dark-900 peer-checked:ring-primary-400"></label>
                    </div>
                </div>
            </div>
        </div>
        <div class="flex items-center justify-end gap-2 modal-footer">
            <button type="button" class="btn btn-sub-gray" @click="resetAttributes()"><i data-lucide="rotate-ccw" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> Reset Layouts</button>
            <button type="button" class="btn btn-red"><i data-lucide="shopping-bag" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> Buy Now</button>
        </div>
    </div>
</div>