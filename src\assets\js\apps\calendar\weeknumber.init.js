/*
Template Name: Domiex - Admin & Dashboard Template
Author: SRBThemes
Version: 1.0.0
File: week number calendar init Js File
*/

import { Calendar } from '@fullcalendar/core'
import interactionPlugin from '@fullcalendar/interaction'
import dayGridPlugin from '@fullcalendar/daygrid'

let cal = null;
function initCalendar(calendar = null) {
    // Destroy the existing calendar instance if it exists
    if (calendar !== null) {
        calendar.destroy();
    }
    const calendarEl = document.getElementById('weekNumberCalendar')
    calendar = new Calendar(calendarEl, {
        timeZone: 'America/New_York',
        buttonText: {
            today: 'Today',
            year: 'Year',
            month: 'Month',
            week: 'Week',
            day: 'Day',
            list: 'List'
        },
        plugins: [
            interactionPlugin,
            dayGridPlugin
        ],
        weekNumbers: true,
    });
    return calendar.render();
}

cal = initCalendar(cal);
function handleResize() {
    cal = initCalendar(cal); // Reinitialize the calendar on resize
}

window.addEventListener('resize', handleResize);