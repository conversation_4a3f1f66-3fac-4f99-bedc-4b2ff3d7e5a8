{{> partials/main }}

<head>

    {{> partials/title-meta title="UI Tooltip" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}

{{> partials/page-heading title="UI Tooltip" sub-title="UI" }}

<div class="grid grid-cols-12 gap-x-space">
    <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Default Behaviour Tooltip</h6>
        </div>
        <div class="card-body">
            <div class="flex flex-wrap items-center gap-2">
                <div x-data="{ tooltip: 'Hover Tooltip' }">
                    <button class="btn btn-primary" x-tooltip="tooltip">Hover for Button!</button>
                </div>
                <div x-data="{ tooltip: 'Click Tooltip' }">
                    <button class="btn btn-primary" x-tooltip.on.click="tooltip">Click Me</button>
                </div>
                <div x-data="{ tooltip: 'Mouseenter Tooltip' }">
                    <button class="btn btn-primary" x-tooltip.on.mouseenter="tooltip">Mouseenter</button>
                </div>
            </div>
        </div>
    </div><!--Behaviour-->

    <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Follow Cursor</h6>
        </div>
        <div class="card-body">
            <div class="flex flex-wrap items-center gap-2">
                <div x-data="{ tooltip: 'I am a Tooltip' }">
                    <button class="btn btn-green" x-tooltip.cursor="tooltip">Default</button>
                </div>
                <div x-data="{ tooltip: 'I am a Tooltip' }">
                    <button class="btn btn-green" x-tooltip.cursor.x="tooltip">Horizontal</button>
                </div>
                <div x-data="{ tooltip: 'I am a Tooltip' }">
                    <button class="btn btn-green" x-tooltip.cursor.initial="tooltip">Initial</button>
                </div>
            </div>
        </div>
    </div><!--Cursor-->

    <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Arrowless Tooltip</h6>
        </div>
        <div class="card-body">
            <div class="flex flex-wrap items-center gap-2">
                <div x-data="{ tooltip: 'I am a Tooltip' }">
                    <button class="btn btn-purple" x-tooltip.arrowless="tooltip">Arrowless</button>
                </div>
            </div>
        </div>
    </div><!--Arrow-->

    <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">No Flip Tooltip</h6>
        </div>
        <div class="card-body">
            <div class="flex flex-wrap items-center gap-2">
                <div x-data="{ tooltip: 'I am a Tooltip' }">
                    <button class="btn btn-primary" x-tooltip.placement.left.no-flip="tooltip">No Flip</button>
                </div>
            </div>
        </div>
    </div><!--No Flip-->

    <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Custom HTML Input with Tooltip</h6>
        </div>
        <div class="card-body">
            <div x-data="{ message: 'Hello, world!' }">
                <input type="text" class="form-input" x-model="message">

                <template x-ref="content">
                    <p x-text="message" class="text-red-500"></p>
                </template>

                <button class="mt-3 btn btn-primary" x-tooltip="{ content: () => $refs.content.innerHTML, allowHTML: true, appendTo: $root }">
                    Dynamic HTML Content
                </button>
            </div>
        </div>
    </div><!--No Flip-->

    <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Animation Tooltip</h6>
        </div>
        <div class="card-body">
            <div class="flex flex-wrap items-center gap-2">
                <div x-data="{ tooltip: 'I am a Tooltip' }">
                    <button class="btn btn-purple" x-tooltip.animation.scale="tooltip">Scale</button>
                </div>
                <div x-data="{ tooltip: 'I am a Tooltip' }">
                    <button class="btn btn-purple" x-tooltip.animation.scale-subtle="tooltip">Scale-subtle</button>
                </div>
                <div x-data="{ tooltip: 'I am a Tooltip' }">
                    <button class="btn btn-purple" x-tooltip.animation.scale-extreme="tooltip">scale-extreme</button>
                </div>
            </div>
        </div>
    </div><!--Animation-->

    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Placement Tooltip</h6>
        </div>
        <div class="card-body">
            <div class="grid grid-cols-1 gap-2 sm:grid-cols-2 md:grid-cols-3">
                <div x-data="{ tooltip: 'I am a Tooltip' }">
                    <button class="w-full btn btn-purple" x-tooltip.placement.top="tooltip">Top</button>
                </div>
                <div x-data="{ tooltip: 'I am a Tooltip' }">
                    <button class="w-full btn btn-purple" x-tooltip.placement.top-start="tooltip">Top Start</button>
                </div>
                <div x-data="{ tooltip: 'I am a Tooltip' }">
                    <button class="w-full btn btn-purple" x-tooltip.placement.top-end="tooltip">Top End</button>
                </div>
                <div x-data="{ tooltip: 'I am a Tooltip' }">
                    <button class="w-full btn btn-purple" x-tooltip.placement.right="tooltip">Right</button>
                </div>
                <div x-data="{ tooltip: 'I am a Tooltip' }">
                    <button class="w-full btn btn-purple" x-tooltip.placement.right-start="tooltip">Right Start</button>
                </div>
                <div x-data="{ tooltip: 'I am a Tooltip' }">
                    <button class="w-full btn btn-purple" x-tooltip.placement.right-end="tooltip">Right End</button>
                </div>
                <div x-data="{ tooltip: 'I am a Tooltip' }">
                    <button class="w-full btn btn-purple" x-tooltip.placement.bottom="tooltip">Bottom</button>
                </div>
                <div x-data="{ tooltip: 'I am a Tooltip' }">
                    <button class="w-full btn btn-purple" x-tooltip.placement.bottom-start="tooltip">Bottom Start</button>
                </div>
                <div x-data="{ tooltip: 'I am a Tooltip' }">
                    <button class="w-full btn btn-purple" x-tooltip.placement.bottom-end="tooltip">Bottom End</button>
                </div>
                <div x-data="{ tooltip: 'I am a Tooltip' }">
                    <button class="w-full btn btn-purple" x-tooltip.placement.left="tooltip">Left</button>
                </div>
                <div x-data="{ tooltip: 'I am a Tooltip' }">
                    <button class="w-full btn btn-purple" x-tooltip.placement.left-start="tooltip">Left Start</button>
                </div>
                <div x-data="{ tooltip: 'I am a Tooltip' }">
                    <button class="w-full btn btn-purple" x-tooltip.placement.left-end="tooltip">Left End</button>
                </div>
            </div>
        </div>
    </div><!--Animation-->
</div>

</div>
{{> partials/footer }}
</div>


{{> partials/vendor-scripts }}
<script type="module" src="assets/js/main.js"></script>

</body>
</html>