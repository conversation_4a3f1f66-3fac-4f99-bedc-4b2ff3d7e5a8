{{> partials/main }}

<head>

    {{> partials/title-meta title="Bot" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}

{{> partials/page-heading title="Bot" sub-title="UI Advanced" }}

<div class="grid grid-cols-12 gap-x-space">
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Default ChatBot</h6>
        </div>
        <div class="grid items-center grid-cols-1 gap-5 md:grid-cols-2 xl:grid-cols-3 card-body">
            <div x-data="chatBot()">
                <div class="flex flex-col rounded-md shadow-lg shadow-gray-200 dark:shadow-dark-800 dark:bg-dark-900">
                    <div class="p-4 text-white bg-gradient-to-tr from-primary-500 to-purple-500 rounded-t-md">
                        <div>
                            <h6 class="mb-1 text-16">ChatBot</h6>
                            <p class="text-xs text-white/75">Online</p>
                        </div>
                    </div>
                    <div class="p-4 h-80" data-simplebar>
                        <div id="messages" class="flex flex-col gap-y-4">
                            <template x-for="(message, key) in messages">
                                <div>
                                    <div class="flex items-end" :class="message.from=='bot'?'':'justify-end'">
                                        <div class="flex flex-col max-w-sm space-y-2 text-sm" :class="message.from=='bot'?'order-2 items-start rtl:mr-2 ltr:ml-2':'order-1 items-end ltr:mr-2 rtl:ml-2'">
                                            <div>
                                                <span class="inline-block px-3 py-2 rounded-md" :class="message.from=='bot'?'ltr:rounded-bl-none rtl:rounded-br-none bg-gray-100 dark:bg-dark-850':'ltr:rounded-br-none rtl:rounded-bl-none bg-primary-500 text-white'" x-html="message.text"></span>
                                            </div>
                                        </div>
                                        <img x-show="message.from=='bot'" :src='botImg' alt="" class="order-1 rounded-full size-6" /> 
                                        <img x-show="message.from!=='bot'" :src='userImg' alt="" class="order-2 rounded-full size-6" />
                                    </div>
                                </div>
                            </template>
                            <div x-show="botTyping" style="display: none;">
                                <div class="flex items-end">
                                    <div class="flex flex-col items-start order-2 mx-2 space-y-2 text-sm">
                                        <div class="flex items-end">
                                            <img :src="botImg" alt="" class="rounded-full size-6">
                                            <div class="flex items-center justify-center space-x-1">
                                                <span class="sr-only">Loading...</span>
                                                <div class="size-2 bg-primary-500 rounded-full animate-bounce [animation-delay:-0.3s]"></div>
                                                <div class="size-2 bg-primary-500 rounded-full animate-bounce [animation-delay:-0.15s]"></div>
                                                <div class="rounded-full bg-primary-500 size-2 animate-bounce"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="p-4">
                        <div class="relative flex">
                            <input type="text" placeholder="Say something..." autocomplete="off" autofocus="true" @keydown.enter="updateChat($event.target)" class="rounded-full ltr:pr-10 rtl:pl-10 form-input" x-ref="input" />
                            <div class="absolute inset-y-0 items-center hidden ltr:right-1 rtl:left-1 sm:flex">
                                <button type="button" class="inline-flex items-center justify-center text-white transition duration-200 ease-in-out rounded-full size-8 bg-primary-500 hover:bg-primary-600 focus:outline-hidden" @click.prevent="updateChat($refs.input)">
                                    <i data-lucide="send-horizontal" class="size-4"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div x-data="chatBot()">
                <div class="flex flex-col rounded-md shadow-lg shadow-gray-200 dark:shadow-dark-800 dark:bg-dark-900">
                    <div class="p-4 text-white bg-gradient-to-br from-green-500 to-sky-500 rounded-t-md">
                        <div>
                            <h6 class="mb-1 text-16">ChatBot</h6>
                            <p class="text-xs text-white/75">Online</p>
                        </div>
                    </div>
                    <div class="p-4 h-80" data-simplebar>
                        <div id="messages" class="flex flex-col gap-y-4">
                            <template x-for="(message, key) in messages">
                                <div>
                                    <div class="flex items-end" :class="message.from=='bot'?'':'justify-end'">
                                        <div class="flex flex-col max-w-sm space-y-2 text-sm" :class="message.from=='bot'?'order-2 items-start rtl:mr-2 ltr:ml-2':'order-1 items-end mr-2'">
                                            <div>
                                                <span class="inline-block px-3 py-2 rounded-md" :class="message.from=='bot'?'ltr:rounded-bl-none rtl:rounded-br-none bg-gray-100 dark:bg-dark-850':'ltr:rounded-br-none rtl:rounded-bl-none bg-primary-500 text-white'" x-html="message.text"></span>
                                            </div>
                                        </div>
                                        <img x-show="message.from=='bot'" :src='botImg' alt="" class="order-1 rounded-full size-6" /> 
                                        <img x-show="message.from!=='bot'" :src='userImg' alt="" class="order-2 rounded-full size-6" />
                                    </div>
                                </div>
                            </template>
                            <div x-show="botTyping" style="display: none;">
                                <div class="flex items-end">
                                    <div class="flex flex-col items-start order-2 mx-2 space-y-2 text-sm">
                                        <div class="flex items-end">
                                            <img :src="botImg" alt="" class="rounded-full size-6">
                                            <div class="flex items-center justify-center space-x-1">
                                                <span class="sr-only">Loading...</span>
                                                <div class="size-2 bg-primary-500 rounded-full animate-bounce [animation-delay:-0.3s]"></div>
                                                <div class="size-2 bg-primary-500 rounded-full animate-bounce [animation-delay:-0.15s]"></div>
                                                <div class="rounded-full bg-primary-500 size-2 animate-bounce"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="p-4">
                        <div class="relative flex">
                            <input type="text" placeholder="Say something..." autocomplete="off" autofocus="true" @keydown.enter="updateChat($event.target)" class="rounded-full form-input ltr:pr-10 rtl:pl-10" x-ref="input" />
                            <div class="absolute inset-y-0 items-center hidden ltr:right-1 rtl:left-1 sm:flex">
                                <button type="button" class="inline-flex items-center justify-center text-white transition duration-200 ease-in-out rounded-full size-8 bg-primary-500 hover:bg-primary-600 focus:outline-hidden" @click.prevent="updateChat($refs.input)">
                                    <i data-lucide="send-horizontal" class="size-4"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div x-data="chatBot()">
                <div class="flex flex-col bg-white rounded-md shadow-lg shadow-gray-200 dark:shadow-dark-800 dark:bg-dark-900">
                    <div class="p-4 bg-gradient-to-br from-primary-500/20 via-green-500/20 to-purple-500/20 rounded-t-md">
                        <div>
                            <h6 class="mb-1 text-16">ChatBot</h6>
                            <p class="text-xs text-gray-500 dark:text-dark-500">Online</p>
                        </div>
                    </div>
                    <div class="p-4 h-80" data-simplebar>
                        <div id="messages" class="flex flex-col gap-y-4">
                            <template x-for="(message, key) in messages">
                                <div>
                                    <div class="flex items-end" :class="message.from=='bot'?'':'justify-end'">
                                        <div class="flex flex-col max-w-sm space-y-2 text-sm" :class="message.from=='bot'?'order-2 items-start rtl:mr-2 ltr:ml-2':'order-1 items-end mr-2'">
                                            <div>
                                                <span class="inline-block px-3 py-2 rounded-md" :class="message.from=='bot'?'ltr:rounded-bl-none rtl:rounded-br-none bg-gray-100 dark:bg-dark-850':'ltr:rounded-br-none rtl:rounded-bl-none bg-primary-500 text-white'" x-html="message.text"></span>
                                            </div>
                                        </div>
                                        <img x-show="message.from=='bot'" :src='botImg' alt="" class="order-1 rounded-full size-6" /> 
                                        <img x-show="message.from!=='bot'" :src='userImg' alt="" class="order-2 rounded-full size-6" />
                                    </div>
                                </div>
                            </template>
                            <div x-show="botTyping" style="display: none;">
                                <div class="flex items-end">
                                    <div class="flex flex-col items-start order-2 mx-2 space-y-2 text-sm">
                                        <div class="flex items-end">
                                            <img :src="botImg" alt="" class="rounded-full size-6">
                                            <div class="flex items-center justify-center space-x-1">
                                                <span class="sr-only">Loading...</span>
                                                <div class="size-2 bg-primary-500 rounded-full animate-bounce [animation-delay:-0.3s]"></div>
                                                <div class="size-2 bg-primary-500 rounded-full animate-bounce [animation-delay:-0.15s]"></div>
                                                <div class="rounded-full bg-primary-500 size-2 animate-bounce"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="p-4">
                        <div class="relative flex">
                            <input type="text" placeholder="Say something..." autocomplete="off" autofocus="true" @keydown.enter="updateChat($event.target)" class="rounded-full form-input ltr:pr-10 rtl:pl-10" x-ref="input" />
                            <div class="absolute inset-y-0 items-center hidden ltr:right-1 rtl:left-1 sm:flex">
                                <button type="button" class="inline-flex items-center justify-center text-white transition duration-200 ease-in-out rounded-full size-8 bg-primary-500 hover:bg-primary-600 focus:outline-hidden" @click.prevent="updateChat($refs.input)">
                                    <i data-lucide="send-horizontal" class="size-4"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Live ChatBot</h6>
        </div>
        <div class="flex items-center gap-5 card-body">
            <button type="button" class="text-white btn bg-primary-500 border-primary-500 hover:bg-primary-600 hover:text-white hover:border-primary-600 focus:bg-primary-600 focus:text-white focus:border-primary-600" @click="document.querySelector('#modal-bot').classList.remove('hidden')">Live Chatbox</button>
            <div class="fixed hidden z-[1050] ltr:right-5 rtl:left-5 ltr:md:right-8 rtl:md:left-8 bottom-8 md:w-96" id="modal-bot">
                <div x-data="chatBot()" class="flex flex-col bg-white rounded-md shadow-lg shadow-gray-200 dark:shadow-dark-800 dark:bg-dark-900">
                    <div class="p-4 text-white bg-gradient-to-tr from-primary-500 to-purple-500 rounded-t-md">
                        <div>
                            <h6 class="mb-1 text-16">ChatBot</h6>
                            <p class="text-xs text-white/75">Online</p>
                        </div>
                    </div>
                    <div class="p-4 h-80" data-simplebar>
                        <div id="messages" class="flex flex-col gap-y-4">
                            <template x-for="(message, key) in messages">
                                <div>
                                    <div class="flex items-end" :class="message.from=='bot'?'':'justify-end'">
                                        <div class="flex flex-col max-w-sm space-y-2 text-sm" :class="message.from=='bot'?'order-2 items-start rtl:mr-2 ltr:ml-2':'order-1 items-end mr-2'">
                                            <div>
                                                <span class="inline-block px-3 py-2 rounded-md" :class="message.from=='bot'?'ltr:rounded-bl-none rtl:rounded-br-none bg-gray-100 dark:bg-dark-850':'ltr:rounded-br-none rtl:rounded-bl-none bg-primary-500 text-white'" x-html="message.text"></span>
                                            </div>
                                        </div>
                                        <img :src="message.from=='bot'? botImg : userImg" alt="" class="rounded-full size-6" :class="message.from=='bot'?'order-1':'order-2'">
                                    </div>
                                </div>
                            </template>
                            <div x-show="botTyping" style="display: none;">
                                <div class="flex items-end">
                                    <div class="flex flex-col items-start order-2 mx-2 space-y-2 text-sm">
                                        <div class="flex items-end">
                                            <img :src="botImg" alt="" class="rounded-full size-6">
                                            <div class="flex items-center justify-center space-x-1">
                                                <span class="sr-only">Loading...</span>
                                                <div class="size-2 bg-primary-500 rounded-full animate-bounce [animation-delay:-0.3s]"></div>
                                                <div class="size-2 bg-primary-500 rounded-full animate-bounce [animation-delay:-0.15s]"></div>
                                                <div class="rounded-full bg-primary-500 size-2 animate-bounce"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="p-4">
                        <div class="relative flex">
                            <input type="text" placeholder="Say something..." autocomplete="off" autofocus="true" @keydown.enter="updateChat($event.target)" class="rounded-full form-input ltr:pr-10 rtl:pl-10" x-ref="input" />
                            <div class="absolute inset-y-0 items-center hidden ltr:right-1 rtl:left-1 sm:flex">
                                <button type="button" class="inline-flex items-center justify-center text-white transition duration-200 ease-in-out rounded-full size-8 bg-primary-500 hover:bg-primary-600 focus:outline-hidden" @click.prevent="updateChat($refs.input)">
                                    <i data-lucide="send-horizontal" class="size-4"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div><!--end col-->
</div><!--end grid-->

</div>
{{> partials/footer }}
</div>

{{> partials/vendor-scripts }}
<script type="module" src="assets/js/ui/advanced-bot.init.js"></script>
<script type="module" src="assets/js/main.js"></script>

</body>
</html>