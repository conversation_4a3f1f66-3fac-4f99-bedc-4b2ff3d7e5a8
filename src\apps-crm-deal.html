{{> partials/main }}

<head>

    {{> partials/title-meta title="Deal" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}
{{> partials/page-heading title="Deal" sub-title="CRM" }}
<div x-data="dealsTable()">
    <div x-data="callModal()">
        <div x-data="{ isCardView: false }">
            <div class="flex flex-wrap justify-between gap-5 mb-5">
                <div>
                    <div class="relative group/form">
                        <input type="email" class="ltr:pl-9 rtl:pr-9 form-input ltr:group-[&.right]/form:pr-9 rtl:group-[&.right]/form:pl-9 ltr:group-[&.right]/form:pl-4 rtl:group-[&.right]/form:pr-4" placeholder="Search for ..." @input="filteredDeals" x-model="searchTerm">
                        <div class="absolute inset-y-0 flex items-center text-gray-500 dark:text-dark-500 ltr:left-3 rtl:right-3 ltr:group-[&.right]/form:right-3 rtl:group-[&.right]/form:left-3 ltr:group-[&.right]/form:left-auto rtl:group-[&.right]/form:right-auto focus:outline-hidden">
                            <i data-lucide="search" class="size-4"></i>
                        </div>
                    </div>
                </div>
                <div>
                    <div class="flex flex-wrap gap-2">
                        <button class="btn btn-icon btn-icon-text" :class="isCardView ? 'btn btn-primary' : 'btn btn-sub-gray'" @click="isCardView = true" title="card"><i data-lucide="gallery-vertical-end" class="size-5"></i></button>
                        <button class="btn btn-icon btn-icon-text" :class="!isCardView ? 'btn btn-primary' : 'btn btn-sub-gray'" @click="isCardView = false" title="list"><i data-lucide="list" class="size-5"></i></button>
                        <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown shrink-0">
                            <button x-ref="button" x-on:click="toggle()" :aria-expanded="open.toString()" title="dropdown-button" type="button" class="btn btn-sub-gray">
                                <i data-lucide="filter" class="inline-block ltr:mr-1 rtl:ml-1 align-center size-4"></i> Sort
                                By
                            </button>

                            <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden !w-64" dropdown-position="right">
                                <ul>
                                    <li>
                                        <a href="#!" x-on:click="init , sort(null) " class="dropdown-item">
                                            No Sorting
                                        </a>
                                    </li>
                                    <li>
                                        <a href="#!" x-on:click="sort('projectName')" class="dropdown-item">
                                            Alphabetical (A -> Z)
                                        </a>
                                    </li>
                                    <li>
                                        <a href="#!" x-on:click="sort('projectName')" class="dropdown-item">
                                            Reverse Alphabetical (Z -> A)
                                        </a>
                                    </li>
                                    <li>
                                        <a href="#!" x-on:click="sort('status')" class="dropdown-item">
                                            Status
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div>
                <div :class="{' group card-view gap-x-5 grid grid-cols-12': isCardView, 'list-view group': !isCardView}" x-transition>
                    <template x-if="displayedDeals.length > 0">
                        <template x-for="(deal, index) in displayedDeals" :key="index">
                            <div class="group-[&.card-view]:2xl:col-span-3 group-[&.card-view]:md:col-span-6 group-[&.card-view]:col-span-12">
                                <div class="card">
                                    <div class="card-body">
                                        <div class="group-[&.list-view]:flex group-[&.list-view]:justify-between gap-4 group-[&.list-view]:overflow-x-auto group-[&.list-view]:whitespace-nowrap group-[&.card-view]:grid group-[&.card-view]:grid-cols-12">
                                            <div class="flex items-center group-[&.card-view]:col-span-12 gap-3">
                                                <div class="p-2 border border-gray-200 rounded-md dark:border-dark-800 size-12 shrink-0">
                                                    <img :src="deal.image" alt="">
                                                </div>
                                                <div class="overflow-hidden grow">
                                                    <h6 class="mb-1 truncate"><a href="#!" x-text="deal.projectName"></a>
                                                    </h6>
                                                    <p class="text-gray-500 dark:text-dark-500"><span x-text="deal.amount"></span> <span class="group-[&.card-view]:inline-block hidden">- <span x-text="formatDate(deal.createDate)"></span></span></p>
                                                </div>
                                            </div>
                                            <div class="group-[&.list-view]:w-28 group-[&.card-view]:hidden">
                                                <p class="text-gray-500 dark:text-dark-500" x-text="formatDate(deal.createDate)"></p>
                                            </div>
                                            <div class="group-[&.list-view]:w-28 group-[&.card-view]:col-span-12">
                                                <p class="text-gray-500 dark:text-dark-500 truncate group-[&.card-view]:text-gray-800 dark:group-[&.card-view]:text-dark-50 group-[&.card-view]:font-medium" x-text="deal.company"></p>
                                                <p class="group-[&.card-view]:block hidden mt-1 text-gray-500 dark:text-dark-500 line-clamp-2" x-text="deal.content"></p>
                                            </div>
                                            <div class="w-28 group-[&.card-view]:col-span-12">
                                                <div class="flex items-center gap-2">
                                                    <p class="badge badge-pink" x-text="typeof deal.daysLeft === 'number' ? `${deal.daysLeft} Days left` : deal.daysLeft">
                                                    </p>
                                                    <span x-text="deal.status" :class="{
                                                        'badge badge-green': deal.status === 'Active',
                                                        'badge badge-gray': deal.status === 'Unactive',
                                                    }"></span>
                                                </div>
                                            </div>
                                            <div class="group-[&.card-view]:col-span-12">
                                                <div class="flex flex-wrap items-center group-[&.list-view]:justify-end gap-2">
                                                    <button type="button" data-modal-target="messageModal" @click="openChat(deal)" class="border-dashed btn btn-dashed-yellow"><i class="align-baseline ltr:mr-1 rtl:ml-1 ri-message-2-line"></i> Message</button>
                                                    <button type="button" data-modal-target="callModal" @click="reviewDeal(deal.projectName); callModalOpen = true " class="border-dashed btn btn-dashed-primary shrink-0"><i class="align-baseline ltr:mr-1 rtl:ml-1 ri-phone-line"></i> Call</button>
                                                    <button type="button" class="btn btn-sub-gray shrink-0 btn-icon-text btn-icon" title="delete" @click="deleteDeals(deal)"><i data-lucide="trash" class="inline-block size-4"></i></button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </template>
                    </template>
                    <template x-if="displayedDeals.length == 0">
                        <div class="p-8">
                            <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" class="mx-auto size-12" viewBox="0 0 48 48">
                                <linearGradient id="SVGID_1__h35ynqzIJzH4_gr1" x1="34.598" x2="15.982" y1="15.982" y2="34.598" gradientUnits="userSpaceOnUse">
                                    <stop offset="0" stop-color="#60e8fe"></stop>
                                    <stop offset=".033" stop-color="#6ae9fe"></stop>
                                    <stop offset=".197" stop-color="#97f0fe"></stop>
                                    <stop offset=".362" stop-color="#bdf5ff"></stop>
                                    <stop offset=".525" stop-color="#dafaff"></stop>
                                    <stop offset=".687" stop-color="#eefdff"></stop>
                                    <stop offset=".846" stop-color="#fbfeff"></stop>
                                    <stop offset="1" stop-color="#fff"></stop>
                                </linearGradient>
                                <path fill="url(#SVGID_1__h35ynqzIJzH4_gr1)" d="M40.036,33.826L31.68,25.6c0.847-1.739,1.335-3.684,1.335-5.748c0-7.27-5.894-13.164-13.164-13.164	S6.688,12.582,6.688,19.852c0,7.27,5.894,13.164,13.164,13.164c2.056,0,3.995-0.485,5.728-1.326l3.914,4.015l4.331,4.331	c1.715,1.715,4.496,1.715,6.211,0C41.751,38.321,41.751,35.541,40.036,33.826z">
                                </path>
                                <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M31.95,25.739l8.086,8.086c1.715,1.715,1.715,4.496,0,6.211l0,0c-1.715,1.715-4.496,1.715-6.211,0	l-4.331-4.331">
                                </path>
                                <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M7.525,24.511c-1.771-4.694-0.767-10.196,3.011-13.975c3.847-3.847,9.48-4.817,14.228-2.912">
                                </path>
                                <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M30.856,12.603c3.376,5.114,2.814,12.063-1.688,16.565c-4.858,4.858-12.565,5.129-17.741,0.814">
                                </path>
                            </svg>
                            <p class="mt-2 text-center text-gray-500 dark:text-dark-500">No matching records found</p>
                        </div>
                    </template>
                </div>
                <!-- <template x-if="displayedDeals.length !== 0"> -->
                <div class="grid grid-cols-12 gap-5 mb-5 items-center" x-show="displayedDeals.length !== 0">
                    <div class="col-span-12 md:col-span-6 justify-center md:justify-start flex flex-wrap">
                        <p class="text-gray-500 dark:text-dark-500">Showing <b x-text="showingStart"></b> - <b x-text="showingEnd"></b> of <b x-text="filterDeals.length"></b> Results</p>
                    </div>
                    <div class="col-span-12 md:col-span-6">
                        <div class="flex justify-center md:justify-end pagination pagination-primary">
                            <button @click="prevPage()" :disabled="currentPage === 1" class="pagination-pre">
                                <i data-lucide="chevron-left" class="mr-1 ltr:inline-block rtl:hidden size-4"></i>
                                <i data-lucide="chevron-right" class="ml-1 ltr:hidden rtl:inline-block size-4"></i>
                                Prev
                            </button>
                            <template x-for="page in totalPages" :key="page">
                                <button @click="gotoPage(page)" :class="{ 'active': currentPage === page }" class="pagination-item">
                                    <span x-text="page"></span>
                                </button>
                            </template>
                            <button @click="nextPage()" :disabled="currentPage === totalPages" class="pagination-next">
                                Next
                                <i data-lucide="chevron-right" class="ml-1 ltr:inline-block rtl:hidden size-4"></i>
                                <i data-lucide="chevron-left" class="mr-1 ltr:hidden rtl:inline-block size-4"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <!-- </template> -->
            </div>
        </div>

        <!--call modal-->
        <div id="callModal" class="!hidden modal show" x-show="callModalOpen">
            <div class="modal-wrap modal-xs modal-br" @click.outside="stopCall()">
                <template x-if="callModalOpen? startCall() : stopCall()"></template>
                <div class="modal-content">
                    <div>
                        <div class="flex items-center gap-2">
                            <div class="p-2 border border-gray-200 rounded-md dark:border-dark-800 size-12 shrink-0">
                                <img alt="" :src="selectedDeal.userImage">
                            </div>
                            <div>
                                <h6 x-text="selectedDeal.projectName"></h6>
                                <p class="text-sm text-gray-500 dark:text-dark-500" x-text="isCalling ? 'Calling ...' : formatDuration(callDuration)"></p>
                            </div>
                        </div>
                        <div class="flex items-center gap-3 mt-5">
                            <button type="button" @click="toggleMute" class="btn btn-active-gray shrink-0 btn-icon-text btn-icon">
                                <i x-show="isMuted" data-lucide="mic-off" class="size-5"></i>
                                <i x-show="!isMuted" data-lucide="mic" class="size-5"></i>
                            </button>
                            <button type="button" class="btn btn-active-gray shrink-0 btn-icon-text btn-icon">
                                <i data-lucide="pause" class="size-5"></i>
                            </button>
                            <button type="button" class="btn btn-active-gray shrink-0 btn-icon-text btn-icon">
                                <i data-lucide="disc" class="size-5"></i>
                            </button>
                            <button type="button" data-modal-close="callModal" @click="stopCall()" class="btn btn-active-red shrink-0 btn-icon-text btn-icon">
                                <i data-lucide="phone" class="size-5"></i>
                            </button>
                            <button type="button" class="btn btn-active-gray shrink-0 btn-icon-text btn-icon">
                                <i data-lucide="settings" class="size-5"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--message modal-->
        <div id="messageModal" class="!hidden modal show">
            <div class="modal-wrap modal-sm modal-br">
                <div class="modal-content">
                    <div class="flex items-center gap-2 p-2 bg-gray-100 rounded-md dark:bg-dark-850">
                        <button data-modal-close="messageModal" class="p-0 text-gray-500 dark:text-dark-500 btn btn-icon-text size-10 hover:text-gray-800 dark:hover:text-dark-50 shrink-0"><i data-lucide="chevron-left"></i></button>
                        <img :src="currentChat.userImage" alt="" class="rounded-full size-10">
                        <div class="grow">
                            <h6>Sophia Mia</h6>
                            <p class="text-xs text-gray-500 dark:text-dark-500" x-text="currentChat.status"> <span class="inline-block bg-green-500 size-1.5 rounded-full ltr:mr-0.5 rtl:ml-0.5 align-middle"></span>
                                Active</p>
                        </div>
                        <button type="button" data-modal-close="messageModal" data-modal-target="callModal" @click="callModalOpen = true" class="p-0 btn btn-sub-red shrink-0 btn-icon-text size-10">
                            <i data-lucide="phone" class="size-5"></i>
                        </button>
                    </div>
                    <div data-simplebar class="px-5 mt-4 -mx-5 min-h-72 max-h-72">
                        <div class="flex flex-col gap-3">
                            <template x-for="(message, index) in currentChat.messages" :key="index">
                                <div>
                                    <div class="flex gap-2 group [&.right]:justify-end" x-show="message.sender == 'user'">
                                        <div class="rounded-full size-9 group-[&.right]:order-2">
                                            <img :src="currentChat.userImage" alt="" class="rounded-full">
                                        </div>
                                        <div class="py-2 px-3 text-sm font-medium rounded-md bg-gray-100 dark:bg-dark-850 max-w-64 text-gray-500 dark:text-dark-500 ltr:rounded-bl-none rtl:rounded-br-none group-[&.right]:bg-primary-100 group-[&.right]:text-primary-500 group-[&.right]:order-1 ltr:group-[&.right]:rounded-br-none rtl:group-[&.right]:rounded-bl-none ltr:group-[&.right]:rounded-bl-md rtl:group-[&.right]:rounded-br-md" x-text="message.text">
                                        </div>
                                    </div>
                                    <div class="flex gap-2 right group [&.right]:justify-end" x-show="message.sender == 'agent'">
                                        <div class="rounded-full size-9 group-[&.right]:order-2">
                                            <img src="assets/images/avatar/user-14.png" alt="" class="rounded-full">
                                        </div>
                                        <div class="py-2 px-3 text-sm font-medium rounded-md bg-gray-100 dark:bg-dark-850 max-w-64 text-gray-500 dark:text-dark-500 ltr:rounded-bl-none rtl:rounded-br-none group-[&.right]:bg-primary-100 group-[&.right]:text-primary-500 group-[&.right]:order-1 ltr:group-[&.right]:rounded-br-none rtl:group-[&.right]:rounded-bl-none ltr:group-[&.right]:rounded-bl-md rtl:group-[&.right]:rounded-br-md" x-text="message.text">
                                        </div>
                                    </div>
                                </div>
                            </template>
                        </div>
                    </div>
                    <div class="relative flex mt-4">
                        <input type="text" placeholder="Say something..." x-model="newMessage" @keydown.enter="sendMessage()" autocomplete="off" autofocus="true" class="ltr:pr-12 rtl:pl-12 form-input" x-ref="input" />
                        <div class="absolute inset-y-0 items-center hidden ltr:right-1 rtl:left-1 sm:flex">
                            <button type="button" @click="sendMessage()" class="inline-flex items-center justify-center text-white transition duration-200 ease-in-out rounded-md size-8 bg-primary-500 hover:bg-primary-600 focus:outline-hidden">
                                <i data-lucide="send-horizontal" class="size-4"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</div>
{{> partials/footer }}
</div>

{{> partials/vendor-scripts }}

<script type="module" src="assets/js/apps/crm/deal.init.js"></script>
<script type="module" src="assets/js/main.js"></script>

</body>
</html>