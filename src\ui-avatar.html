{{> partials/main }}

<head>

    {{> partials/title-meta title="Avatar" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}

{{> partials/page-heading title="Avatar" sub-title="UI" }}

<div class="grid grid-cols-12 gap-x-space">
    <div class="col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Avatar Images</h6>
        </div>
        <div class="flex flex-wrap gap-3 card-body">
            <img src="assets/images/avatar/user-44.png" alt="" class="rounded-xs size-5">
            <img src="assets/images/avatar/user-44.png" alt="" class="rounded-xs size-6">
            <img src="assets/images/avatar/user-44.png" alt="" class="rounded-xs size-7">
            <img src="assets/images/avatar/user-44.png" alt="" class="rounded-xs size-8">
            <img src="assets/images/avatar/user-44.png" alt="" class="rounded-xs size-9">
            <img src="assets/images/avatar/user-44.png" alt="" class="rounded-xs size-10">
            <img src="assets/images/avatar/user-44.png" alt="" class="rounded-xs size-12">
            <img src="assets/images/avatar/user-44.png" alt="" class="rounded-xs size-14">
            <img src="assets/images/avatar/user-44.png" alt="" class="rounded-xs size-16">
            <img src="assets/images/avatar/user-44.png" alt="" class="rounded-xs size-20">
            <img src="assets/images/avatar/user-44.png" alt="" class="rounded-xs size-24">
        </div>
    </div>
    <div class="col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Rounded Images</h6>
        </div>
        <div class="flex flex-wrap gap-3 card-body">
            <img src="assets/images/avatar/user-44.png" alt="" class="rounded-full size-5">
            <img src="assets/images/avatar/user-44.png" alt="" class="rounded-full size-6">
            <img src="assets/images/avatar/user-44.png" alt="" class="rounded-full size-7">
            <img src="assets/images/avatar/user-44.png" alt="" class="rounded-full size-8">
            <img src="assets/images/avatar/user-44.png" alt="" class="rounded-full size-9">
            <img src="assets/images/avatar/user-44.png" alt="" class="rounded-full size-10">
            <img src="assets/images/avatar/user-44.png" alt="" class="rounded-full size-12">
            <img src="assets/images/avatar/user-44.png" alt="" class="rounded-full size-14">
            <img src="assets/images/avatar/user-44.png" alt="" class="rounded-full size-16">
            <img src="assets/images/avatar/user-44.png" alt="" class="rounded-full size-20">
            <img src="assets/images/avatar/user-44.png" alt="" class="rounded-full size-24">
        </div>
    </div>

    <div class="col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Rounded Text Avatar</h6>
        </div>
        <div class="flex flex-wrap gap-3 card-body">
            <div class="flex items-center justify-center rounded-xs size-5 bg-primary-100 text-primary-500 text-11">0</div>
            <div class="flex items-center justify-center rounded-xs size-6 bg-primary-100 text-primary-500 text-11">1</div>
            <div class="flex items-center justify-center rounded-xs size-7 bg-primary-100 text-primary-500 text-11">2</div>
            <div class="flex items-center justify-center text-xs rounded-xs size-8 bg-primary-100 text-primary-500">3</div>
            <div class="flex items-center justify-center text-xs rounded-xs size-9 bg-primary-100 text-primary-500">4</div>
            <div class="flex items-center justify-center text-sm rounded-xs size-10 bg-primary-100 text-primary-500">5</div>
            <div class="flex items-center justify-center rounded-xs text-15 size-12 bg-primary-100 text-primary-500">6</div>
            <div class="flex items-center justify-center text-lg rounded-xs size-14 bg-primary-100 text-primary-500">7</div>
            <div class="flex items-center justify-center text-xl rounded-xs size-16 bg-primary-100 text-primary-500">8</div>
            <div class="flex items-center justify-center text-2xl rounded-xs size-20 bg-primary-100 text-primary-500">9</div>
            <div class="flex items-center justify-center text-3xl rounded-xs size-24 bg-primary-100 text-primary-500">10</div>
        </div>
    </div>
    <div class="col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Text Avatar</h6>
        </div>
        <div class="flex flex-wrap gap-3 card-body">
            <div class="flex items-center justify-center rounded-full size-5 bg-primary-100 text-primary-500 text-11">0</div>
            <div class="flex items-center justify-center rounded-full size-6 bg-primary-100 text-primary-500 text-11">1</div>
            <div class="flex items-center justify-center rounded-full size-7 bg-primary-100 text-primary-500 text-11">2</div>
            <div class="flex items-center justify-center text-xs rounded-full size-8 bg-primary-100 text-primary-500">3</div>
            <div class="flex items-center justify-center text-xs rounded-full size-9 bg-primary-100 text-primary-500">4</div>
            <div class="flex items-center justify-center text-sm rounded-full size-10 bg-primary-100 text-primary-500">5</div>
            <div class="flex items-center justify-center rounded-full text-15 size-12 bg-primary-100 text-primary-500">6</div>
            <div class="flex items-center justify-center text-lg rounded-full size-14 bg-primary-100 text-primary-500">7</div>
            <div class="flex items-center justify-center text-xl rounded-full size-16 bg-primary-100 text-primary-500">8</div>
            <div class="flex items-center justify-center text-2xl rounded-full size-20 bg-primary-100 text-primary-500">9</div>
            <div class="flex items-center justify-center text-3xl rounded-full size-24 bg-primary-100 text-primary-500">10</div>
        </div>
    </div>

    <div class="col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Icon Avatar</h6>
        </div>
        <div class="flex flex-wrap gap-3 card-body">
            <div class="flex items-center justify-center rounded-xs size-5 bg-primary-100 text-primary-500 text-11"><i data-lucide="home" class="size-2.5"></i></div>
            <div class="flex items-center justify-center rounded-xs size-6 bg-primary-100 text-primary-500 text-11"><i data-lucide="home" class="size-3"></i></div>
            <div class="flex items-center justify-center rounded-xs size-7 bg-primary-100 text-primary-500 text-11"><i data-lucide="home" class="size-3.5"></i></div>
            <div class="flex items-center justify-center text-xs rounded-xs size-8 bg-primary-100 text-primary-500"><i data-lucide="home" class="size-3.5"></i></div>
            <div class="flex items-center justify-center text-xs rounded-xs size-9 bg-primary-100 text-primary-500"><i data-lucide="home" class="size-3.5"></i></div>
            <div class="flex items-center justify-center text-sm rounded-xs size-10 bg-primary-100 text-primary-500"><i data-lucide="home" class="size-4"></i></div>
            <div class="flex items-center justify-center rounded-xs text-15 size-12 bg-primary-100 text-primary-500"><i data-lucide="home" class="size-4"></i></div>
            <div class="flex items-center justify-center text-lg rounded-xs size-14 bg-primary-100 text-primary-500"><i data-lucide="home" class="size-5"></i></div>
            <div class="flex items-center justify-center text-xl rounded-xs size-16 bg-primary-100 text-primary-500"><i data-lucide="home" class="size-5"></i></div>
            <div class="flex items-center justify-center text-2xl rounded-xs size-20 bg-primary-100 text-primary-500"><i data-lucide="home" class="size-6"></i></div>
            <div class="flex items-center justify-center text-3xl rounded-xs size-24 bg-primary-100 text-primary-500"><i data-lucide="home" class="size-7"></i></div>
        </div>
    </div>
    <div class="col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Rounded Icon Avatar</h6>
        </div>
        <div class="flex flex-wrap gap-3 card-body">
            <div class="flex items-center justify-center rounded-full size-5 bg-primary-100 text-primary-500 text-11"><i data-lucide="home" class="size-2.5"></i></div>
            <div class="flex items-center justify-center rounded-full size-6 bg-primary-100 text-primary-500 text-11"><i data-lucide="home" class="size-3"></i></div>
            <div class="flex items-center justify-center rounded-full size-7 bg-primary-100 text-primary-500 text-11"><i data-lucide="home" class="size-3.5"></i></div>
            <div class="flex items-center justify-center text-xs rounded-full size-8 bg-primary-100 text-primary-500"><i data-lucide="home" class="size-3.5"></i></div>
            <div class="flex items-center justify-center text-xs rounded-full size-9 bg-primary-100 text-primary-500"><i data-lucide="home" class="size-3.5"></i></div>
            <div class="flex items-center justify-center text-sm rounded-full size-10 bg-primary-100 text-primary-500"><i data-lucide="home" class="size-4"></i></div>
            <div class="flex items-center justify-center rounded-full text-15 size-12 bg-primary-100 text-primary-500"><i data-lucide="home" class="size-4"></i></div>
            <div class="flex items-center justify-center text-lg rounded-full size-14 bg-primary-100 text-primary-500"><i data-lucide="home" class="size-5"></i></div>
            <div class="flex items-center justify-center text-xl rounded-full size-16 bg-primary-100 text-primary-500"><i data-lucide="home" class="size-5"></i></div>
            <div class="flex items-center justify-center text-2xl rounded-full size-20 bg-primary-100 text-primary-500"><i data-lucide="home" class="size-6"></i></div>
            <div class="flex items-center justify-center text-3xl rounded-full size-24 bg-primary-100 text-primary-500"><i data-lucide="home" class="size-7"></i></div>
        </div>
    </div>
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Colored Avatar</h6>
        </div>
        <div class="flex flex-wrap gap-3 card-body">
            <div class="flex items-center justify-center text-xs rounded-md size-9 bg-primary-100 text-primary-500"><i data-lucide="home" class="size-3.5 fill-primary-200"></i></div>
            <div class="flex items-center justify-center text-xs text-green-500 bg-green-100 rounded-md size-9"><i data-lucide="badge-check" class="size-3.5 fill-green-200"></i></div>
            <div class="flex items-center justify-center text-xs text-pink-500 bg-pink-100 rounded-md size-9"><i data-lucide="dribbble" class="size-3.5 fill-pink-200"></i></div>
            <div class="flex items-center justify-center text-xs rounded-md text-primary-500 bg-primary-100 size-9"><i data-lucide="facebook" class="size-3.5 fill-primary-200"></i></div>
            <div class="flex items-center justify-center text-xs text-purple-500 bg-purple-100 rounded-md size-9"><i data-lucide="twitch" class="size-3.5 fill-purple-200"></i></div>
            <div class="flex items-center justify-center text-xs rounded-md text-sky-500 bg-sky-100 size-9"><i data-lucide="twitter" class="size-3.5 fill-sky-200"></i></div>
            <div class="flex items-center justify-center text-xs text-yellow-500 bg-yellow-100 rounded-md size-9"><i data-lucide="alert-triangle" class="size-3.5 fill-yellow-200"></i></div>
            <div class="flex items-center justify-center text-xs text-white rounded-md size-9 bg-primary-500"><i data-lucide="home" class="size-3.5"></i></div>
            <div class="flex items-center justify-center text-xs text-white bg-yellow-500 rounded-md size-9"><i data-lucide="alert-triangle" class="size-3.5"></i></div>
        </div>
    </div>
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Group Avatar</h6>
        </div>
        <div class="card-body">
            <div class="grid grid-cols-2 gap-5">
                <div class="flex -space-x-2">
                    <img class="z-40 inline-block rounded-full size-9 ring-2 ring-white" src="assets/images/avatar/user-1.png" alt="">
                    <img class="z-30 inline-block rounded-full size-9 ring-2 ring-white" src="assets/images/avatar/user-2.png" alt="">
                    <img class="z-20 inline-block rounded-full size-9 ring-2 ring-white" src="assets/images/avatar/user-3.png" alt="">
                    <img class="z-10 inline-block rounded-full size-9 ring-2 ring-white" src="assets/images/avatar/user-4.png" alt="">
                </div>
                <div class="flex -space-x-2">
                    <a href="#!" class="z-40"><img class="inline-block rounded-full size-9 ring-2 ring-white" src="assets/images/avatar/user-5.png" alt=""></a>
                    <a href="#!" class="z-30"><img class="inline-block rounded-full size-9 ring-2 ring-white" src="assets/images/avatar/user-6.png" alt=""></a>
                    <a href="#!" class="z-20"><span class="flex items-center justify-center font-semibold text-white bg-green-500 border-2 border-white rounded-full text-11 size-9">UT</span></a>
                    <a href="#!" class="z-10"><img class="inline-block rounded-full size-9 ring-2 ring-white" src="assets/images/avatar/user-7.png" alt=""></a>
                </div>
                <div class="flex -space-x-4">
                    <img class="border-2 border-white rounded-full size-9" src="assets/images/avatar/user-8.png" alt="">
                    <img class="border-2 border-white rounded-full size-9" src="assets/images/avatar/user-9.png" alt="">
                    <img class="border-2 border-white rounded-full size-9" src="assets/images/avatar/user-10.png" alt="">
                    <img class="border-2 border-white rounded-full size-9" src="https://randomuser.me/api/portraits/women/32.jpg" alt="">
                    <img class="border-2 border-white rounded-full size-9" src="https://randomuser.me/api/portraits/men/44.jpg" alt="">
                    <img class="border-2 border-white rounded-full size-9" src="https://randomuser.me/api/portraits/women/42.jpg" alt="">
                    <span class="flex items-center justify-center font-semibold text-white border-2 border-white rounded-full bg-primary-500 text-11 size-9">+999</span>
                </div>
            </div>
        </div>
    </div>

    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Group Avatar</h6>
        </div>
        <div class="card-body">
            <div class="grid grid-cols-2 gap-5">
                <div class="flex items-center space-x-2 duration-200 delay-300 animate-out zoom-in">
                    <div>
                        <h6 class="text-center md:text-left">Trusted by</h6>
                        <div class="flex flex-col items-center gap-3 md:flex-row">
                            <div class="flex p-2 -space-x-2 overflow-hidden">
                                <img class="inline-block duration-100 rounded-full size-9 ring-2 ring-white hover:scale-105 tranform" src="assets/images/avatar/user-1.png" alt="">
                                <img class="inline-block duration-100 rounded-full size-9 ring-2 ring-white hover:scale-105 tranform" src="assets/images/avatar/user-2.png" alt="">
                                <img class="inline-block duration-100 rounded-full size-9 ring-2 ring-white hover:scale-105 tranform" src="assets/images/avatar/user-3.png" alt="">
                                <img class="inline-block duration-100 rounded-full size-9 ring-2 ring-white hover:scale-105 tranform" src="assets/images/avatar/user-4.png" alt="">
                                <img class="inline-block duration-100 rounded-full size-9 ring-2 ring-white hover:scale-105 tranform" src="assets/images/avatar/user-5.png" alt="">
                                <img class="inline-block duration-100 rounded-full size-9 ring-2 ring-white hover:scale-105 tranform" src="assets/images/avatar/user-6.png" alt="">
                            </div>
                            <h6>Join 5,000+ other members</h6>
                        </div>
                    </div>
                </div>

                <div>
                    <div class="inline-flex items-end justify-center w-full mx-auto text-center">
                        <img src="assets/images/avatar/user-15.png" class="absolute ml-6 transform translate-x-24 border-4 border-white rounded-full size-12 md:w-16 md:h-16">
                        <img src="assets/images/avatar/user-16.png" class="absolute -ml-6 transform -translate-x-24 border-4 border-white rounded-full size-12 md:w-16 md:h-16">
                        <img src="assets/images/avatar/user-17.png" class="absolute transform -translate-x-16 border-4 border-white rounded-full size-16 md:w-20 md:h-20">
                        <img src="assets/images/avatar/user-19.png" class="absolute transform translate-x-16 border-4 border-white rounded-full size-16 md:w-20 md:h-20">
                        <img src="assets/images/avatar/user-18.png" class="relative border-4 border-white rounded-full size-20 md:w-24 md:h-24">
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</div>
{{> partials/footer }}
</div>


{{> partials/vendor-scripts }}
<script type="module" src="assets/js/main.js"></script>

</body>
</html>