{{> partials/main }}

<head>

    {{> partials/title-meta title="User Projects" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}

<div class="relative mb-6">
    <div class="relative overflow-hidden rounded-md h-44 bg-primary-500/10">
        <div class="border-[60px] border-t-primary-500 border-l-primary-500 absolute opacity-10 -top-2 left-0 rotate-45 size-96"></div>
        <div class="border-[60px] border-green-500 absolute opacity-10 top-20 left-8 rotate-45 size-80"></div>
        <div class="border-[60px] border-pink-500 absolute opacity-10 top-36 left-28 rotate-45 size-40"></div>
    </div>
    <div class="text-center">
        <div class="relative inline-block mx-auto">
            <div class="relative p-1 rounded-full bg-gradient-to-tr from-primary-300 via-red-300 to-green-300 -mt-14">
                <img src="assets/images/avatar/user-17.png" alt="" class="mx-auto border-4 border-white rounded-full dark:border-dark-900 size-28">
            </div>
            <div class="absolute border-2 border-white dark:border-dark-900 rounded-full size-4 bg-green-500 bottom-2.5 ltr:right-2.5 rtl:left-2.5"></div>
        </div>
        <h5 class="mt-2 mb-1">Sophia Mia <i data-lucide="badge-check" class="inline-block text-primary-500 fill-primary-500/20 size-5"></i></h5>
        <ul class="flex flex-wrap items-center justify-center gap-2 text-gray-500 dark:text-dark-500 text-14">
            <li><i data-lucide="building-2" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-middle">UI / UX Designer</span></li>
            <li><i data-lucide="map-pin" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-middle">Argentina</span></li>
            <li><i data-lucide="calendar-days" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-middle">24 April, 2024</span></li>
        </ul>
    </div>
</div>

<ul class="pb-2 overflow-x-auto tabs-pills lg:pb-0">
    <li>
        <a href="pages-user.html" class="nav-item text-gray-500 dark:text-dark-500 [&.active]:bg-primary-500 [&.active]:text-primary-50">
            <i data-lucide="eye" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-middle whitespace-nowrap">Overview</span>
        </a>
    </li>
    <li>
        <a href="pages-user-activity.html" class="nav-item text-gray-500 dark:text-dark-500 [&.active]:bg-primary-500 [&.active]:text-primary-50">
            <i data-lucide="sparkles" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-middle whitespace-nowrap">Activity</span>
        </a>
    </li>
    <li>
        <a href="pages-user-followers.html" class="nav-item text-gray-500 dark:text-dark-500 [&.active]:bg-primary-500 [&.active]:text-primary-50">
            <i data-lucide="user-round" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-middle whitespace-nowrap">Followers</span>
        </a>
    </li>
    <li>
        <a href="pages-user-documents.html" class="nav-item text-gray-500 dark:text-dark-500 [&.active]:bg-primary-500 [&.active]:text-primary-50">
            <i data-lucide="file-text" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-middle whitespace-nowrap">Documents</span>
        </a>
    </li>
    <li>
        <a href="pages-user-notes.html" class="nav-item text-gray-500 dark:text-dark-500 [&.active]:bg-primary-500 [&.active]:text-primary-50">
            <i data-lucide="list" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-middle whitespace-nowrap">Notes</span>
        </a>
    </li>
    <li>
        <a href="pages-user-projects.html" class="nav-item text-gray-500 dark:text-dark-500 [&.active]:bg-primary-500 [&.active]:text-primary-50 active">
            <i data-lucide="monitor" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-middle whitespace-nowrap">Projects</span>
        </a>
    </li>
</ul>

<h5 class="mt-4 mb-5 text-16">Projects</h5>

<div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-x-5">
    <div class="card">
        <div class="card-body">
            <div class="flex gap-3">
                <div class="flex items-center justify-center size-12 bg-gradient-to-t from-sky-500/10 rounded-modern shrink-0">
                    <i data-lucide="messages-square" class="relative stroke-1 text-sky-500 size-7 fill-sky-500/10"></i>
                </div>
                <div class="grow">
                    <h6 class="mb-1"><a href="#!" class="transition duration-200 ease-linear hover:text-primary-500">Chat App Templates</a></h6>
                    <p class="text-gray-500 dark:text-dark-500 line-clamp-5">Chat applications typically run on centralized networks that are served by platform operator servers as opposed to peer-to-peer protocols such as XMPP. This allows two people to talk to each other in real time.</p>
                </div>
            </div>
            <div class="flex items-center gap-3 mt-3">
                <a href="#!" class="ltr:mr-auto rtl:ml-auto badge badge-gray">#Features</a>
                <div class="text-gray-500 dark:text-dark-500 shrink-0 text-15">
                    <p><i data-lucide="eye" class="inline-block size-4"></i> <span class="align-middle">148</span></p>
                </div>
            </div>
        </div>
        <div class="flex items-center gap-3 border-t border-gray-200 card-body dark:border-dark-800">
            <div class="flex -space-x-3 grow rtl:space-x-reverse">
                <a href="#!" class="transition duration-300 ease-linear hover:z-10" title="avatar-link"><img class="border-2 border-white rounded-full dark:border-dark-900 size-7" src="assets/images/avatar/user-2.png" alt=""></a>
                <a href="#!" class="transition duration-300 ease-linear hover:z-10" title="avatar-link"><img class="border-2 border-white rounded-full dark:border-dark-900 size-7" src="assets/images/avatar/user-3.png" alt=""></a>
            </div>
            <div x-data="{
                        open: false,
                        toggle() {
                            if (this.open) {
                                return this.close();
                            }
                    
                            this.$refs.button.focus();
                    
                            this.open = true;
                        },
                        close(focusAfter) {
                            if (!this.open) return;
                    
                            this.open = false;
                    
                            focusAfter && focusAfter.focus();
                        }
                    }" x-on:keydown.escape.prevent.stop="close($refs.button)" x-on:focusin.window="!$refs.panel.contains($event.target) && close()" x-id="['dropdown-button']" class="dropdown">
                <button x-ref="button" x-on:click="toggle()" :aria-expanded="open" :aria-controls="$id('dropdown-button')" type="button" title="dropdown-button" class="link link-primary">
                    <i data-lucide="ellipsis" class="size-5"></i>
                </button>

                <div x-ref="panel" x-show="open" x-transition.origin.top.right x-on:click.outside="close($refs.button)" :id="$id('dropdown-button')" style="display: none;" class="p-2 dropdown-menu dropdown-right">
                    <a href="#" class="dropdown-item">
                        Overview
                    </a>

                    <a href="#" class="dropdown-item">
                        Edit
                    </a>

                    <a href="#" class="dropdown-item">
                        Delete
                    </a>
                </div>
            </div>
        </div>
    </div><!--end card-->
    <div class="card">
        <div class="card-body">
            <div class="flex gap-3">
                <div class="flex items-center justify-center size-12 bg-gradient-to-t from-pink-500/10 rounded-modern shrink-0">
                    <i data-lucide="box" class="relative text-pink-500 stroke-1 size-7 fill-pink-500/10"></i>
                </div>
                <div class="grow">
                    <h6 class="mb-1"><a href="#!" class="transition duration-200 ease-linear hover:text-primary-500">Domiex - Admin & Dashboards Templates</a></h6>
                    <p class="text-gray-500 dark:text-dark-500 line-clamp-5">An admin dashboard template is a powerful tool that streamlines the process of building a robust and user-friendly admin panel for web applications. Image by ThemeMakker. It provides a pre-designed interface with various components and features to manage and monitor the backend of an application.</p>
                </div>
            </div>
            <div class="flex items-center gap-3 mt-3">
                <a href="#!" class="ltr:mr-auto rtl:ml-auto badge badge-gray">#Admin</a>
                <div class="text-gray-500 dark:text-dark-500 shrink-0 text-15">
                    <p><i data-lucide="eye" class="inline-block size-4"></i> <span class="align-middle">74</span></p>
                </div>
            </div>
        </div>
        <div class="flex items-center gap-3 border-t border-gray-200 card-body dark:border-dark-800">
            <div class="flex -space-x-3 grow rtl:space-x-reverse">
                <a href="#!" class="transition duration-300 ease-linear hover:z-10" title="avatar-link"><img class="border-2 border-white rounded-full dark:border-dark-900 size-7" src="assets/images/avatar/user-5.png" alt=""></a>
                <a href="#!" class="transition duration-300 ease-linear hover:z-10" title="avatar-link"><img class="border-2 border-white rounded-full dark:border-dark-900 size-7" src="assets/images/avatar/user-20.png" alt=""></a>
                <a href="#!" class="transition duration-300 ease-linear hover:z-10" title="avatar-link"><img class="border-2 border-white rounded-full dark:border-dark-900 size-7" src="assets/images/avatar/user-13.png" alt=""></a>
            </div>
            <div x-data="{
                        open: false,
                        toggle() {
                            if (this.open) {
                                return this.close();
                            }
                    
                            this.$refs.button.focus();
                    
                            this.open = true;
                        },
                        close(focusAfter) {
                            if (!this.open) return;
                    
                            this.open = false;
                    
                            focusAfter && focusAfter.focus();
                        }
                    }" x-on:keydown.escape.prevent.stop="close($refs.button)" x-on:focusin.window="!$refs.panel.contains($event.target) && close()" x-id="['dropdown-button']" class="dropdown">
                <button x-ref="button" x-on:click="toggle()" :aria-expanded="open" :aria-controls="$id('dropdown-button')" type="button" title="dropdown-button" class="link link-primary">
                    <i data-lucide="ellipsis" class="size-5"></i>
                </button>

                <div x-ref="panel" x-show="open" x-transition.origin.top.right x-on:click.outside="close($refs.button)" :id="$id('dropdown-button')" style="display: none;" class="p-2 dropdown-menu dropdown-right">
                    <a href="#" class="dropdown-item">
                        Overview
                    </a>

                    <a href="#" class="dropdown-item">
                        Edit
                    </a>

                    <a href="#" class="dropdown-item">
                        Delete
                    </a>
                </div>
            </div>
        </div>
    </div><!--end card-->
    <div class="card">
        <div class="card-body">
            <div class="flex gap-3">
                <div class="flex items-center justify-center size-12 bg-gradient-to-t from-green-500/10 rounded-modern shrink-0">
                    <i data-lucide="users" class="relative text-green-500 stroke-1 size-7 fill-green-500/10"></i>
                </div>
                <div class="grow">
                    <h6 class="mb-1"><a href="#!" class="transition duration-200 ease-linear hover:text-primary-500">Employee Management System</a></h6>
                    <p class="text-gray-500 dark:text-dark-500 line-clamp-5">Employee management is the process by which employers ensure workers perform their jobs to the best of their abilities so as to achieve business goals. It typically entails building and maintaining healthy relationships with employees, as well as monitoring their daily labor and measuring progress.</p>
                </div>
            </div>
            <div class="flex items-center gap-3 mt-3">
                <a href="#!" class="ltr:mr-auto rtl:ml-auto badge badge-gray">#Management</a>
                <div class="text-gray-500 dark:text-dark-500 shrink-0 text-15">
                    <p><i data-lucide="eye" class="inline-block size-4"></i> <span class="align-middle">179</span></p>
                </div>
            </div>
        </div>
        <div class="flex items-center gap-3 border-t border-gray-200 card-body dark:border-dark-800">
            <div class="flex -space-x-3 grow rtl:space-x-reverse">
                <a href="#!" class="transition duration-300 ease-linear hover:z-10" title="avatar-link"><img class="border-2 border-white rounded-full dark:border-dark-900 size-7" src="assets/images/avatar/user-15.png" alt=""></a>
                <a href="#!" class="transition duration-300 ease-linear hover:z-10" title="avatar-link"><img class="border-2 border-white rounded-full dark:border-dark-900 size-7" src="assets/images/avatar/user-16.png" alt=""></a>
                <a href="#!" class="transition duration-300 ease-linear hover:z-10" title="avatar-link"><img class="border-2 border-white rounded-full dark:border-dark-900 size-7" src="assets/images/avatar/user-17.png" alt=""></a>
                <a href="#!" class="transition duration-300 ease-linear hover:z-10" title="avatar-link"><img class="border-2 border-white rounded-full dark:border-dark-900 size-7" src="assets/images/avatar/user-18.png" alt=""></a>
                <a href="#!" class="transition duration-300 ease-linear hover:z-10" title="avatar-link"><img class="border-2 border-white rounded-full dark:border-dark-900 size-7" src="assets/images/avatar/user-22.png" alt=""></a>
            </div>
            <div x-data="{
                        open: false,
                        toggle() {
                            if (this.open) {
                                return this.close();
                            }
                    
                            this.$refs.button.focus();
                    
                            this.open = true;
                        },
                        close(focusAfter) {
                            if (!this.open) return;
                    
                            this.open = false;
                    
                            focusAfter && focusAfter.focus();
                        }
                    }" x-on:keydown.escape.prevent.stop="close($refs.button)" x-on:focusin.window="!$refs.panel.contains($event.target) && close()" x-id="['dropdown-button']" class="dropdown">
                <button x-ref="button" x-on:click="toggle()" :aria-expanded="open" :aria-controls="$id('dropdown-button')" type="button" title="dropdown-button" class="link link-primary">
                    <i data-lucide="ellipsis" class="size-5"></i>
                </button>

                <div x-ref="panel" x-show="open" x-transition.origin.top.right x-on:click.outside="close($refs.button)" :id="$id('dropdown-button')" style="display: none;" class="p-2 dropdown-menu dropdown-right">
                    <a href="#" class="dropdown-item">
                        Overview
                    </a>

                    <a href="#" class="dropdown-item">
                        Edit
                    </a>

                    <a href="#" class="dropdown-item">
                        Delete
                    </a>
                </div>
            </div>
        </div>
    </div><!--end card-->
    <div class="card">
        <div class="card-body">
            <div class="flex gap-3">
                <div class="flex items-center justify-center size-12 bg-gradient-to-t from-purple-500/10 rounded-modern shrink-0">
                    <i data-lucide="globe" class="relative text-purple-500 stroke-1 size-7 fill-purple-500/10"></i>
                </div>
                <div class="grow">
                    <h6 class="mb-1"><a href="#!" class="transition duration-200 ease-linear hover:text-primary-500">Create Business Website</a></h6>
                    <p class="text-gray-500 dark:text-dark-500 line-clamp-5">If all you need is a basic one-page website with an email address, phone number and maybe your business address, you can absolutely do that yourself. There are many website builders and one-page templates you can use to get up and running quickly.</p>
                </div>
            </div>
            <div class="flex items-center gap-3 mt-3">
                <a href="#!" class="ltr:mr-auto rtl:ml-auto badge badge-gray">#Design</a>
                <div class="text-gray-500 dark:text-dark-500 shrink-0 text-15">
                    <p><i data-lucide="eye" class="inline-block size-4"></i> <span class="align-middle">132</span></p>
                </div>
            </div>
        </div>
        <div class="flex items-center gap-3 border-t border-gray-200 card-body dark:border-dark-800">
            <div class="flex -space-x-3 grow rtl:space-x-reverse">
                <a href="#!" class="transition duration-300 ease-linear hover:z-10" title="avatar-link"><img class="border-2 border-white rounded-full dark:border-dark-900 size-7" src="assets/images/avatar/user-10.png" alt=""></a>
            </div>
            <div x-data="{
                        open: false,
                        toggle() {
                            if (this.open) {
                                return this.close();
                            }
                    
                            this.$refs.button.focus();
                    
                            this.open = true;
                        },
                        close(focusAfter) {
                            if (!this.open) return;
                    
                            this.open = false;
                    
                            focusAfter && focusAfter.focus();
                        }
                    }" x-on:keydown.escape.prevent.stop="close($refs.button)" x-on:focusin.window="!$refs.panel.contains($event.target) && close()" x-id="['dropdown-button']" class="dropdown">
                <button x-ref="button" x-on:click="toggle()" :aria-expanded="open" :aria-controls="$id('dropdown-button')" type="button" title="dropdown-button" class="link link-primary">
                    <i data-lucide="ellipsis" class="size-5"></i>
                </button>

                <div x-ref="panel" x-show="open" x-transition.origin.top.right x-on:click.outside="close($refs.button)" :id="$id('dropdown-button')" style="display: none;" class="p-2 dropdown-menu dropdown-right">
                    <a href="#" class="dropdown-item">
                        Overview
                    </a>

                    <a href="#" class="dropdown-item">
                        Edit
                    </a>

                    <a href="#" class="dropdown-item">
                        Delete
                    </a>
                </div>
            </div>
        </div>
    </div><!--end card-->
    <div class="card">
        <div class="card-body">
            <div class="flex gap-3">
                <div class="flex items-center justify-center size-12 bg-gradient-to-t from-primary-500/10 rounded-modern shrink-0">
                    <i data-lucide="square-user" class="relative stroke-1 text-primary-500 size-7 fill-primary-500/10"></i>
                </div>
                <div class="grow">
                    <h6 class="mb-1"><a href="#!" class="transition duration-200 ease-linear hover:text-primary-500">Contact Page Prototype</a></h6>
                    <p class="text-gray-500 dark:text-dark-500 line-clamp-5">A contact page prototype is a draft of a contact page. It can be used to test the design and functionality of the page before it is launched. There are many different ways to create a contact page prototype. Some popular methods include using a wireframe tool, a prototyping tool, or a content management system.</p>
                </div>
            </div>
            <div class="flex items-center gap-3 mt-3">
                <a href="#!" class="ltr:mr-auto rtl:ml-auto badge badge-gray">#Figma</a>
                <div class="text-gray-500 dark:text-dark-500 shrink-0 text-15">
                    <p><i data-lucide="eye" class="inline-block size-4"></i> <span class="align-middle">163</span></p>
                </div>
            </div>
        </div>
        <div class="flex items-center gap-3 border-t border-gray-200 card-body dark:border-dark-800">
            <div class="flex -space-x-3 grow rtl:space-x-reverse">
                <a href="#!" class="transition duration-300 ease-linear hover:z-10" title="avatar-link"><img class="border-2 border-white rounded-full dark:border-dark-900 size-7" src="assets/images/avatar/user-20.png" alt=""></a>
                <a href="#!" class="transition duration-300 ease-linear hover:z-10" title="avatar-link"><img class="border-2 border-white rounded-full dark:border-dark-900 size-7" src="assets/images/avatar/user-18.png" alt=""></a>
                <a href="#!" class="transition duration-300 ease-linear hover:z-10" title="avatar-link"><img class="border-2 border-white rounded-full dark:border-dark-900 size-7" src="assets/images/avatar/user-22.png" alt=""></a>
                <a href="#!" class="transition duration-300 ease-linear hover:z-10" title="avatar-link"><img class="border-2 border-white rounded-full dark:border-dark-900 size-7" src="assets/images/avatar/user-23.png" alt=""></a>
            </div>
            <div x-data="{
                        open: false,
                        toggle() {
                            if (this.open) {
                                return this.close();
                            }
                    
                            this.$refs.button.focus();
                    
                            this.open = true;
                        },
                        close(focusAfter) {
                            if (!this.open) return;
                    
                            this.open = false;
                    
                            focusAfter && focusAfter.focus();
                        }
                    }" x-on:keydown.escape.prevent.stop="close($refs.button)" x-on:focusin.window="!$refs.panel.contains($event.target) && close()" x-id="['dropdown-button']" class="dropdown">
                <button x-ref="button" x-on:click="toggle()" :aria-expanded="open" :aria-controls="$id('dropdown-button')" type="button" title="dropdown-button" class="link link-primary">
                    <i data-lucide="ellipsis" class="size-5"></i>
                </button>

                <div x-ref="panel" x-show="open" x-transition.origin.top.right x-on:click.outside="close($refs.button)" :id="$id('dropdown-button')" style="display: none;" class="p-2 dropdown-menu dropdown-right">
                    <a href="#" class="dropdown-item">
                        Overview
                    </a>

                    <a href="#" class="dropdown-item">
                        Edit
                    </a>

                    <a href="#" class="dropdown-item">
                        Delete
                    </a>
                </div>
            </div>
        </div>
    </div><!--end card-->
    <div class="card">
        <div class="card-body">
            <div class="flex gap-3">
                <div class="flex items-center justify-center size-12 bg-gradient-to-t from-red-500/10 rounded-modern shrink-0">
                    <i data-lucide="ruler" class="relative text-red-500 stroke-1 size-7 fill-red-500/10"></i>
                </div>
                <div class="grow">
                    <h6 class="mb-1"><a href="#!" class="transition duration-200 ease-linear hover:text-primary-500">Design System - Create Components</a></h6>
                    <p class="text-gray-500 dark:text-dark-500 line-clamp-5">A design system defines reusable components and their usage, and explains why and when designers should choose a component. This helps designers and developers learn design concepts and best practices for different components.</p>
                </div>
            </div>
            <div class="flex items-center gap-3 mt-3">
                <a href="#!" class="ltr:mr-auto rtl:ml-auto badge badge-gray">#Components</a>
                <div class="text-gray-500 dark:text-dark-500 shrink-0 text-15">
                    <p><i data-lucide="eye" class="inline-block size-4"></i> <span class="align-middle">245</span></p>
                </div>
            </div>
        </div>
        <div class="flex items-center gap-3 border-t border-gray-200 card-body dark:border-dark-800">
            <div class="flex -space-x-3 grow rtl:space-x-reverse">
                <a href="#!" class="transition duration-300 ease-linear hover:z-10" title="avatar-link"><img class="border-2 border-white rounded-full dark:border-dark-900 size-7" src="assets/images/avatar/user-10.png" alt=""></a>
                <a href="#!" class="transition duration-300 ease-linear hover:z-10" title="avatar-link"><img class="border-2 border-white rounded-full dark:border-dark-900 size-7" src="assets/images/avatar/user-14.png" alt=""></a>
            </div>
            <div x-data="{
                        open: false,
                        toggle() {
                            if (this.open) {
                                return this.close();
                            }
                    
                            this.$refs.button.focus();
                    
                            this.open = true;
                        },
                        close(focusAfter) {
                            if (!this.open) return;
                    
                            this.open = false;
                    
                            focusAfter && focusAfter.focus();
                        }
                    }" x-on:keydown.escape.prevent.stop="close($refs.button)" x-on:focusin.window="!$refs.panel.contains($event.target) && close()" x-id="['dropdown-button']" class="dropdown">
                <button x-ref="button" x-on:click="toggle()" :aria-expanded="open" :aria-controls="$id('dropdown-button')" type="button" title="dropdown-button" class="link link-primary">
                    <i data-lucide="ellipsis" class="size-5"></i>
                </button>

                <div x-ref="panel" x-show="open" x-transition.origin.top.right x-on:click.outside="close($refs.button)" :id="$id('dropdown-button')" style="display: none;" class="p-2 dropdown-menu dropdown-right">
                    <a href="#" class="dropdown-item">
                        Overview
                    </a>

                    <a href="#" class="dropdown-item">
                        Edit
                    </a>

                    <a href="#" class="dropdown-item">
                        Delete
                    </a>
                </div>
            </div>
        </div>
    </div><!--end card-->
</div><!--end grid-->

<div class="grid items-center grid-cols-12 gap-5 mb-5">
    <div class="col-span-12 lg:col-span-5 justify-center lg:justify-start flex flex-wrap">
        <p class="text-gray-500 dark:text-dark-500">Showing <b>6</b> of <b>76</b> Results</p>
    </div><!--end col-->
    <div class="col-span-12 lg:col-span-7">
        <div class="flex justify-center lg:justify-end pagination pagination-primary">
            <button type="button" class="pagination-pre" disabled>
                <i data-lucide="chevron-left" class="mr-1 ltr:inline-block rtl:hidden size-5"></i>
                <i data-lucide="chevron-right" class="ml-1 ltr:hidden rtl:inline-block size-5"></i>
                Prev
            </button>
            <button type="button" class="pagination-item active">1</button>
            <button type="button" class="pagination-item">2</button>
            <button type="button" class="pagination-item">3</button>
            <button type="button" class="pagination-item">...</button>
            <button type="button" class="pagination-item">10</button>
            <button type="button" class="pagination-next">
                Next
                <i data-lucide="chevron-right" class="ml-1 rtl:hidden size-5 ltr:inline-block"></i>
                <i data-lucide="chevron-left" class="mr-1 rtl:inline-block ltr:hidden size-5"></i>
            </button>
        </div>
    </div><!--end col-->
</div><!--end grid-->

</div>
{{> partials/footer }}
</div>

{{> partials/vendor-scripts }}

<script type="module" src="assets/js/main.js"></script>

</body>
</html>