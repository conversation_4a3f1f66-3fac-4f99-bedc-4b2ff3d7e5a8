{{> partials/main }}

<head>

    {{> partials/title-meta title="Departments" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}
{{> partials/page-heading title="Departments" sub-title="Hospital" }}

<div class="grid grid-cols-12 gap-x-space">
    <div class="col-span-12 card">
        <div class="flex items-center gap-3 card-header flex-wrap">
            <h6 class="card-title grow">Employee</h6>
            <p class="text-gray-500 shrink-0 dark:text-dark-500">Number of Staff according to department</p>
        </div>
        <div class="card-body">
            <div x-data="distributedColumnApp" dir="ltr">
                <div class="!min-h-full" data-chart-colors="[bg-primary-500, bg-pink-500, bg-sky-500, bg-green-300, bg-yellow-200, bg-orange-200, bg-purple-500, bg-red-500]" x-ref="distributedColumnChart"></div>
            </div>
        </div>
    </div>
    <div class="col-span-12 card" x-data="departmentTable()">
        <div class="flex md:items-center gap-3 card-header flex-col md:flex-row">
            <h6 class="card-title grow">Department List</h6>
            <button type="button" class="btn btn-primary shrink-0" @click="handleModal('showAddDepartmentForm')" data-modal-target="addDepartmentModal"><i data-lucide="circle-plus" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> Add Department</button>
        </div>
        <div class="pt-0 card-body">
            <div>
                <div class="overflow-x-auto table-box whitespace-nowrap">
                    <table class="table flush">
                        <tbody>
                            <tr class="text-gray-500 bg-gray-100 dark:text-dark-500 dark:bg-dark-850">
                                <th x-on:click="sort('departmentID')" class="!font-medium cursor-pointer">ID <span x-show="sortBy === 'departmentID'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th x-on:click="sort('departmentName')" class="!font-medium cursor-pointer">Department Name <span x-show="sortBy === 'departmentName'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th x-on:click="sort('doctor')" class="!font-medium cursor-pointer">Doctor <span x-show="sortBy === 'doctor'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th x-on:click="sort('totalEmployee')" class="!font-medium cursor-pointer">Total Employee <span x-show="sortBy === 'totalEmployee'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th x-on:click="sort('headOfDepartment')" class="!font-medium cursor-pointer">Head of Dept. <span x-show="sortBy === 'headOfDepartment'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th x-on:click="sort('status')" class="!font-medium cursor-pointer">Status <span x-show="sortBy === 'status'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th class="!font-medium">Action</th>
                            </tr>
                            <template x-for="(department, index) in displayedDepartment" :key="index">
                                <tr class="*:px-3 *:py-2.5">
                                    <td x-text="department.departmentID"></td>
                                    <td x-text="department.departmentName"></td>
                                    <td>
                                        <div class="flex">
                                            <div class="flex items-center gap-3">
                                                <div class="relative text-gray-500 bg-gray-100 rounded-full dark:text-dark-500 dark:bg-dark-850 size-10">
                                                    <img x-show="department.image" :src="department.image" alt="" class="rounded-full" @error="removeImage($event)">
                                                    <span x-show="!department.image" x-text="department.avatarText" class="absolute inset-0 flex items-center justify-center text-sm font-semibold text-gray-500 bg-gray-100 rounded-full dark:text-dark-500 dark:bg-dark-850"></span>
                                                </div>
                                                <div>
                                                    <h6><a href="#!" x-text="department.doctor"></a></h6>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td x-text="department.totalEmployee"></td>
                                    <td><a href="#!" class="link link-primary" x-text="department.headOfDepartment"></a>
                                    </td>
                                    <td>
                                        <span x-text="department.status" :class="{
                                            'badge badge-green': department.status === 'Active',
                                            'badge badge-red': department.status === 'Unactive',
                                        }"></span>
                                    </td>
                                    <td>
                                        <div class="flex items-center gap-2">
                                            <button class="btn btn-sub-gray btn-icon !size-8" title="Edit" @click="editDepartment(department.departmentID)" data-modal-target="addDepartmentModal"><i class="ri-pencil-line"></i></button>
                                            <button class="btn btn-sub-red btn-icon !size-8" title="delete" @click="deleteItem = department.departmentID" data-modal-target="deleteModal"><i class="ri-delete-bin-line"></i></button>
                                        </div>
                                    </td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                </div>
                <div class="grid items-center grid-cols-12 gap-5 mt-5">
                    <div class="col-span-12 text-center lg:col-span-6 ltr:lg:text-left rtl:lg:text-right">
                        <p class="text-gray-500 dark:text-dark-500">Showing <b x-text="showingStart"></b> - <b x-text="showingEnd"></b> of <b x-text="departments.length"></b> Results</p>
                    </div>
                    <div class="col-span-12 lg:col-span-6">
                        <div class="flex justify-center lg:justify-end pagination pagination-primary">
                            <button @click="prevPage()" :disabled="currentPage === 1" class="pagination-pre">
                                <i data-lucide="chevron-left" class="mr-1 ltr:inline-block rtl:hidden size-4"></i>
                                <i data-lucide="chevron-right" class="ml-1 ltr:hidden rtl:inline-block size-4"></i>
                                Prev
                            </button>
                            <template x-for="page in totalPages" :key="page">
                                <button @click="gotoPage(page)" :class="{ 'active': currentPage === page }" class="pagination-item">
                                    <span x-text="page"></span>
                                </button>
                            </template>
                            <button @click="nextPage()" :disabled="currentPage === totalPages" class="pagination-next">
                                Next
                                <i data-lucide="chevron-right" class="ml-1 ltr:inline-block rtl:hidden size-4"></i>
                                <i data-lucide="chevron-left" class="mr-1 ltr:hidden rtl:inline-block size-4"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!--create modals-->
        <div id="addDepartmentModal" class="!hidden modal show" :class="{'show d-block': showAddDepartmentForm || showEditDepartmentForm}" x-show="showAddDepartmentForm || showEditDepartmentForm">
            <div class="modal-wrap modal-center">
                <div class="p-2 modal-content">
                    <div class="h-24 p-5 rounded-t-sm bg-gradient-to-r from-primary-500/20 via-pink-500/20 to-green-500/20">
                    </div>
                    <div class="p-4">
                        <div class="-mt-16">
                            <label for="logo">
                                <div class="inline-flex items-center justify-center overflow-hidden bg-gray-100 border-2 border-white border-solid rounded-full cursor-pointer dark:bg-dark-850 dark:border-dark-900 size-24">
                                    <template x-if="showAddDepartmentForm != true">
                                        <div>
                                            <img x-show="departmentForm.image" :src="departmentForm.image" class="object-cover w-full h-full rounded-full">
                                            <span x-show="!departmentForm.image" x-text="departmentForm.avatarText" class="flex items-center justify-center text-lg font-semibold text-gray-500 bg-gray-100 rounded-full dark:text-dark-500 dark:bg-dark-850"></span>
                                        </div>
                                    </template>
                                    <div x-show="showAddDepartmentForm" class="flex flex-col items-center text-gray-500 dark:text-dark-500">
                                        <i data-lucide="upload"></i>
                                    </div>
                                </div>
                            </label>
                            <div class="hidden mt-4">
                                <label class="block">
                                    <span class="sr-only">Choose profile photo</span>
                                    <input type="file" name="logo" id="logo" @change="fileChosen" class="block w-full text-sm file:rounded-md focus:outline-0 text-slate-500 file:mr-4 file:py-2 file:px-4 file:border-0 file:text-sm file:font-semibold file:bg-violet-50 file:text-violet-700 hover:file:bg-violet-100 " />
                                </label>
                            </div>
                        </div>
                        <div class="grid grid-cols-12 gap-4 mt-4">
                            <div class="col-span-12">
                                <label for="departmentNameInput" class="form-label">Department Name</label>
                                <input type="text" id="departmentNameInput" class="form-input" placeholder="Department Name" x-model="departmentForm.departmentName" @input="validateField('departmentName', departmentForm.departmentName, 'Department Name is required.')">
                                <span x-show="errors.departmentName" class="text-red-500" x-text="errors.departmentName"></span>
                            </div>
                            <div class="col-span-12">
                                <label for="doctorInput" class="form-label">Doctor</label>
                                <input type="text" id="doctorInput" class="form-input" placeholder="Enter doctor name" x-model="departmentForm.doctor" @input="validateField('doctor', departmentForm.doctor, 'Doctor name is required.')">
                                <span x-show="errors.doctor" class="text-red-500" x-text="errors.doctor"></span>
                            </div>
                            <div class="col-span-12">
                                <label for="totalEmployeeInput" class="form-label">Total Employee</label>
                                <input type="number" id="totalEmployeeInput" class="form-input" placeholder="0" x-model="departmentForm.totalEmployee" @input="validateField('totalEmployee', departmentForm.totalEmployee, 'Total employee is required.')">
                                <span x-show="errors.totalEmployee" class="text-red-500" x-text="errors.totalEmployee"></span>
                            </div>
                            <div class="col-span-12">
                                <label for="headOfDeptInput" class="form-label">Head of Dept.</label>
                                <input type="text" id="headOfDeptInput" class="form-input" placeholder="Head of dept." x-model="departmentForm.headOfDepartment" @input="validateField('headOfDepartment', departmentForm.headOfDepartment, 'Head of department is required.')">
                                <span x-show="errors.headOfDepartment" class="text-red-500" x-text="errors.headOfDepartment"></span>
                            </div>
                            <div class="col-span-12">
                                <label for="statusSelect" class="form-label">Status</label>
                                <div id="statusSelect" placeholder="Select Status" x-model="departmentForm.status" @change="validateField('status', document , 'Status is required.')"></div>
                                <span x-show="errors.status" class="text-red-500" x-text="errors.status"></span>
                            </div>
                        </div>
                        <div class="flex items-center justify-end gap-2 mt-5">
                            <button type="button" class="btn btn-active-red" data-modal-close="addDepartmentModal"><i data-lucide="x" class="inline-block size-4"></i> <span class="align-baseline">Close</span></button>
                            <button type="button" class="btn btn-primary" @click="submitForm()" x-text="showAddDepartmentForm ? 'Add Department' : 'Update Department'"><i data-lucide="plus" class="inline-block mr-1 size-4"></i> Add Department</button>
                        </div>
                    </div>
                </div>
            </div>
        </div><!--end-->

        <!--delete modal-->
        <div id="deleteModal" class="!hidden modal show">
            <div class="modal-wrap modal-xs modal-center">
                <div class="text-center modal-content p-7">
                    <div class="flex items-center justify-center mx-auto mb-4 text-red-500 rounded-full bg-red-500/10 size-14 backdrop-blur-xl">
                        <i data-lucide="trash-2" class="size-6"></i>
                    </div>
                    <h5 class="mb-4">Are you sure you want to delete this department ?</h5>
                    <div class="flex items-center justify-center gap-2">
                        <button class="btn btn-red" @click="deleteDepartment()" data-modal-close="deleteModal">Delete</button>
                        <button data-modal-close="deleteModal" class="btn link link-primary">Cancel</button>
                    </div>
                </div>
            </div>
        </div><!--end-->
    </div><!--end col-->
</div><!--end grid-->
</div>
{{> partials/footer }}
</div>


{{> partials/vendor-scripts }}

<!--apexcharts URL-->
<script src="assets/libs/dayjs/dayjs.min.js"></script>
<script src="assets/libs/dayjs/plugin/quarterOfYear.js"></script>

<script type="module" src="assets/js/apps/hospital/departments.init.js"></script>

<script type="module" src="assets/js/main.js"></script>

</body>
</html>