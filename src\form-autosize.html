{{> partials/main }}

<head>

    {{> partials/title-meta title="Autosize" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}

{{> partials/page-heading title="Autosize" sub-title="Forms" }}

<div class="grid grid-cols-12">
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Autosize Textarea</h6>
        </div>
        <div class="card-body">
            <div x-data="app()" x-init="resizeObserver($refs.container,$refs.textarea)">
                <div x-ref="container">
                    <div>
                        <textarea class="h-auto form-input" rows="3" placeholder="Message..." maxlength="255" @input="updateTextarea($refs.textarea)" x-ref="textarea"></textarea>
                    </div>
                    <div class="mt-1 leading-none ltr:text-right rtl:text-left">
                        <span class="text-xs text-gray-500 dark:text-dark-500"><span x-text="charCount"></span> / 255</span>
                    </div>
                </div>
            </div>
        </div>
    </div><!--end col-->
</div><!--end grid-->

</div>
{{> partials/footer }}
</div>


{{> partials/vendor-scripts }}

<script type="module" src="assets/js/form/autosize.init.js"></script>

<script type="module" src="assets/js/main.js"></script>

</body>
</html>