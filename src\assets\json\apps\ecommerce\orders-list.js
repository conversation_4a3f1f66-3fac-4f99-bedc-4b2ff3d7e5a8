import product1 from "/assets/images/products/img-01.png";
import product2 from "/assets/images/products/img-02.png";
import product3 from "/assets/images/products/img-03.png";
import product4 from "/assets/images/products/img-04.png";
import product5 from "/assets/images/products/img-05.png";
import product6 from "/assets/images/products/img-06.png";
import product7 from "/assets/images/products/img-07.png";
import product8 from "/assets/images/products/img-08.png";
import product9 from "/assets/images/products/img-09.png";
import product10 from "/assets/images/products/img-10.png";
import product11 from "/assets/images/products/img-11.png";
import product12 from "/assets/images/products/img-12.png";
import product13 from "/assets/images/products/img-13.png";
import product14 from "/assets/images/products/img-14.png";
import product15 from "/assets/images/products/img-15.png";
import product16 from "/assets/images/products/img-16.png";
import product17 from "/assets/images/products/img-17.png";
import product18 from "/assets/images/products/img-18.png";
import product19 from "/assets/images/products/img-19.png";
import product20 from "/assets/images/products/img-20.png";
import product21 from "/assets/images/products/img-21.png";
import product22 from "/assets/images/products/img-22.png";
import product23 from "/assets/images/products/img-23.png";
import product24 from "/assets/images/products/img-24.png";
import product25 from "/assets/images/products/img-25.png";

const products =[
    {
        "ordersDate": "15 Mar, 2022",
        "deliveredDate": "21 Mar, 2022",
        "customersName": "Ella Patel",
        "productName": "Denim Jacket",
        "payment": "Paid",
        "price": 45.99,
        "total": 45.99,
        "qty": 1,
        "status": "Delivered",
        "image": product1
    },
    {
        "ordersDate": "02 Apr, 2022",
        "deliveredDate": "09 Apr, 2022",
        "customersName": "Lucas Nguyen",
        "productName": "Leather Wallet",
        "payment": "COD",
        "price": 35.50,
        "total": 35.50,
        "qty": 1,
        "status": "Pending",
        "image": product2
    },
    {
        "ordersDate": "18 Jun, 2022",
        "deliveredDate": "26 Jun, 2022",
        "customersName": "Isabella Thomas",
        "productName": "Summer Dress",
        "payment": "Unpaid",
        "price": 28.75,
        "total": 28.75,
        "qty": 2,
        "status": "New",
        "image": product3
    },
    {
        "ordersDate": "30 Jul, 2022",
        "deliveredDate": "07 Aug, 2022",
        "customersName": "Mason Wilson",
        "productName": "Wireless Headphones",
        "payment": "Paid",
        "price": 79.99,
        "total": 79.99,
        "qty": 1,
        "status": "Delivered",
        "image": product4
    },
    {
        "ordersDate": "12 Sep, 2022",
        "deliveredDate": "19 Sep, 2022",
        "customersName": "Olivia Brown",
        "productName": "Sunglasses",
        "payment": "COD",
        "price": 19.95,
        "total": 19.95,
        "qty": 1,
        "status": "Shipping",
        "image": product5
    },
    {
        "ordersDate": "24 Oct, 2022",
        "deliveredDate": "31 Oct, 2022",
        "customersName": "William Garcia",
        "productName": "Sports Watch",
        "payment": "Paid",
        "price": 55.00,
        "total": 55.00,
        "qty": 1,
        "status": "Delivered",
        "image": product6
    },
    {
        "ordersDate": "05 Nov, 2022",
        "deliveredDate": "12 Nov, 2022",
        "customersName": "Ava Martinez",
        "productName": "Backpack",
        "payment": "COD",
        "price": 42.75,
        "total": 42.75,
        "qty": 1,
        "status": "Shipping",
        "image": product7
    },
    {
        "ordersDate": "14 Dec, 2022",
        "deliveredDate": "22 Dec, 2022",
        "customersName": "Liam Clark",
        "productName": "Winter Coat",
        "payment": "Unpaid",
        "price": 89.99,
        "total": 89.99,
        "qty": 1,
        "status": "New",
        "image": product8
    },
    {
        "ordersDate": "01 Jan, 2023",
        "deliveredDate": "09 Jan, 2023",
        "customersName": "Charlotte Lewis",
        "productName": "Scarf",
        "payment": "Paid",
        "price": 12.50,
        "total": 12.50,
        "qty": 2,
        "status": "Pending",
        "image": product9
    },
    {
        "ordersDate": "10 Feb, 2023",
        "deliveredDate": "18 Feb, 2023",
        "customersName": "James Taylor",
        "productName": "Smartphone Case",
        "payment": "COD",
        "price": 15.99,
        "total": 15.99,
        "qty": 1,
        "status": "Shipping",
        "image": product10
    },
    {
        "ordersDate": "20 Mar, 2023",
        "deliveredDate": "27 Mar, 2023",
        "customersName": "Emma Hernandez",
        "productName": "Fitness Tracker",
        "payment": "Paid",
        "price": 69.00,
        "total": 69.00,
        "qty": 1,
        "status": "Delivered",
        "image": product11
    },
    {
        "ordersDate": "05 Apr, 2023",
        "deliveredDate": "12 Apr, 2023",
        "customersName": "Noah Young",
        "productName": "Sneakers",
        "payment": "COD",
        "price": 49.95,
        "total": 49.95,
        "qty": 1,
        "status": "Shipping",
        "image": product12
    },
    {
        "ordersDate": "15 May, 2023",
        "deliveredDate": "22 May, 2023",
        "customersName": "Sophia Martinez",
        "productName": "Handbag",
        "payment": "Unpaid",
        "price": 39.99,
        "total": 39.99,
        "qty": 1,
        "status": "New",
        "image": product13
    },
    {
        "ordersDate": "25 Jun, 2023",
        "deliveredDate": "02 Jul, 2023",
        "customersName": "Alexander Perez",
        "productName": "Tie",
        "payment": "Paid",
        "price": 9.50,
        "total": 9.50,
        "qty": 1,
        "status": "Delivered",
        "image": product14
    },
    {
        "ordersDate": "04 Aug, 2023",
        "deliveredDate": "11 Aug, 2023",
        "customersName": "Avery Scott",
        "productName": "Earrings",
        "payment": "COD",
        "price": 29.75,
        "total": 29.75,
        "qty": 1,
        "status": "Shipping",
        "image": product15
    },
    {
        "ordersDate": "16 Sep, 2023",
        "deliveredDate": "23 Sep, 2023",
        "customersName": "Harper Green",
        "productName": "Sweater",
        "payment": "Paid",
        "price": 35.99,
        "total": 35.99,
        "qty": 1,
        "status": "Delivered",
        "image": product16
    },
    {
        "ordersDate": "27 Oct, 2023",
        "deliveredDate": "03 Nov, 2023",
        "customersName": "Benjamin Adams",
        "productName": "Cufflinks",
        "payment": "COD",
        "price": 19.50,
        "total": 19.50,
        "qty": 1,
        "status": "Shipping",
        "image": product17
    },
    {
        "ordersDate": "07 Dec, 2023",
        "deliveredDate": "15 Dec, 2023",
        "customersName": "Evelyn Baker",
        "productName": "Winter Boots",
        "payment": "Unpaid",
        "price": 79.95,
        "total": 79.95,
        "qty": 1,
        "status": "New",
        "image": product18
    },
    {
        "ordersDate": "20 Jan, 2024",
        "deliveredDate": "27 Jan, 2024",
        "customersName": "Logan Hall",
        "productName": "Beanie",
        "payment": "Paid",
        "price": 8.99,
        "total": 8.99,
        "qty": 1,
        "status": "Delivered",
        "image": product19
    },
    {
        "ordersDate": "03 Mar, 2024",
        "deliveredDate": "10 Mar, 2024",
        "customersName": "Mia Morris",
        "productName": "Necklace",
        "payment": "COD",
        "price": 39.50,
        "total": 39.50,
        "qty": 1,
        "status": "Shipping",
        "image": product20
    },
    {
        "ordersDate": "15 Apr, 2024",
        "deliveredDate": "22 Apr, 2024",
        "customersName": "Ethan Walker",
        "productName": "Baseball Cap",
        "payment": "Paid",
        "price": 14.99,
        "total": 14.99,
        "qty": 1,
        "status": "Delivered",
        "image": product21
    },
    {
        "ordersDate": "25 May, 2024",
        "deliveredDate": "01 Jun, 2024",
        "customersName": "Amelia King",
        "productName": "Belt",
        "payment": "COD",
        "price": 22.50,
        "total": 22.50,
        "qty": 1,
        "status": "Shipping",
        "image": product22
    },
    {
        "ordersDate": "05 Jul, 2024",
        "deliveredDate": "12 Jul, 2024",
        "customersName": "Jack Harris",
        "productName": "Wallet",
        "payment": "Paid",
        "price": 24.75,
        "total": 24.75,
        "qty": 1,
        "status": "Delivered",
        "image": product23
    },
    {
        "ordersDate": "15 Aug, 2024",
        "deliveredDate": "22 Aug, 2024",
        "customersName": "Avery Rodriguez",
        "productName": "Watch",
        "payment": "COD",
        "price": 59.95,
        "total": 59.95,
        "qty": 1,
        "status": "Shipping",
        "image": product24
    },
    {
        "ordersDate": "25 Sep, 2024",
        "deliveredDate": "02 Oct, 2024",
        "customersName": "Sofia Wilson",
        "productName": "Handkerchief",
        "payment": "Unpaid",
        "price": 7.99,
        "total": 7.99,
        "qty": 1,
        "status": "New",
        "image": product25
    },
    {
        "ordersDate": "10 Oct, 2024",
        "deliveredDate": "17 Oct, 2024",
        "customersName": "Henry Thompson",
        "productName": "Jacket",
        "payment": "Paid",
        "price": 79.99,
        "total": 79.99,
        "qty": 1,
        "status": "Delivered",
        "image": product1
    },
    {
        "ordersDate": "20 Nov, 2024",
        "deliveredDate": "27 Nov, 2024",
        "customersName": "Scarlett Lee",
        "productName": "Shoes",
        "payment": "COD",
        "price": 49.95,
        "total": 49.95,
        "qty": 1,
        "status": "Shipping",
        "image": product2
    },
    {
        "ordersDate": "30 Dec, 2024",
        "deliveredDate": "06 Jan, 2025",
        "customersName": "David White",
        "productName": "Gloves",
        "payment": "Paid",
        "price": 29.99,
        "total": 29.99,
        "qty": 1,
        "status": "Delivered",
        "image": product3
    },
    {
        "ordersDate": "10 Feb, 2025",
        "deliveredDate": "17 Feb, 2025",
        "customersName": "Grace Harris",
        "productName": "Socks",
        "payment": "COD",
        "price": 12.50,
        "total": 12.50,
        "qty": 1,
        "status": "Shipping",
        "image": product4
    },
    {
        "ordersDate": "20 Mar, 2025",
        "deliveredDate": "27 Mar, 2025",
        "customersName": "Daniel Martin",
        "productName": "Hat",
        "payment": "Paid",
        "price": 19.99,
        "total": 19.99,
        "qty": 1,
        "status": "Delivered",
        "image": product5
    }
]
export default products