{{> partials/main }}

<head>

    {{> partials/title-meta title="Button Navigation" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}

{{> partials/page-heading title="Button Navigation" sub-title="UI" }}

<div class="grid grid-cols-12 gap-x-space">
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Bottom Navigation</h6>
        </div>
        <div class="card-body">
            <div class="grid grid-cols-1 gap-space lg:grid-cols-2">
                <div class="btn-navigation">
                    <a href="#!" class="navigation-primary"><i data-lucide="home" class="mx-auto"></i></a>
                    <a href="#!" class="navigation-primary active"><i data-lucide="zap" class="mx-auto"></i></a>
                    <a href="#!" class="navigation-primary"><i data-lucide="bar-chart-big" class="mx-auto"></i></a>
                    <a href="#!" class="navigation-primary"><i data-lucide="settings" class="mx-auto"></i></a>
                </div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Animation Navigation</h6>
        </div>
        <div class="card-body">
            <div class="grid grid-cols-1 gap-space lg:grid-cols-2">
                <div class="btn-navigation animate-navigation">
                    <a href="#!" class="navigation-animation-purple"><i data-lucide="home" class="mx-auto"></i></a>
                    <a href="#!" class="navigation-animation-purple active"><i data-lucide="zap" class="mx-auto"></i></a>
                    <a href="#!" class="navigation-animation-purple"><i data-lucide="bar-chart-big" class="mx-auto"></i></a>
                    <a href="#!" class="navigation-animation-purple"><i data-lucide="settings" class="mx-auto"></i></a>
                </div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Border Navigation</h6>
        </div>
        <div class="card-body">
            <div class="grid grid-cols-1 gap-space lg:grid-cols-2">
                <div class="btn-navigation navigation-border">
                    <a href="#!" class="navigation-animation-green"><i data-lucide="home" class="mx-auto"></i></a>
                    <a href="#!" class="navigation-animation-green active"><i data-lucide="calendar-days" class="mx-auto"></i></a>
                    <a href="#!" class="navigation-animation-green"><i data-lucide="bell" class="mx-auto"></i></a>
                    <a href="#!" class="navigation-animation-green"><i data-lucide="settings" class="mx-auto"></i></a>
                </div>
                <div class="btn-navigation navigation-border border-top">
                    <a href="#!" class="navigation-animation-sky"><i data-lucide="user" class="mx-auto"></i></a>
                    <a href="#!" class="navigation-animation-sky active"><i data-lucide="box" class="mx-auto"></i></a>
                    <a href="#!" class="navigation-animation-sky"><i data-lucide="clapperboard" class="mx-auto"></i></a>
                    <a href="#!" class="navigation-animation-sky"><i data-lucide="pencil-ruler" class="mx-auto"></i></a>
                </div>
            </div>
        </div>
    </div><!--end col-->
</div><!--end grid-->

</div>
{{> partials/footer }}
</div>

{{> partials/vendor-scripts }}

<script type="module" src="assets/js/main.js"></script>

</body>
</html>