{{> partials/main }}

<head>

    {{> partials/title-meta title="Tabs" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}

{{> partials/page-heading title="Tabs" sub-title="UI" }}

<div class="grid grid-cols-12 gap-x-space">
    <div class="col-span-12 md:col-span-6">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title">Basic</h6>
            </div>
            <div class="card-body">
                <div x-data="{ 
                            openTab: 1,
                            activeClasses: 'active',
                            inactiveClasses: 'text-gray-500 hover:text-primary-500 dark:text-dark-500 dark:hover:text-primary-500'
                        }">
                    <ul class="tabs">
                        <li @click="openTab = 1">
                            <a href="javascript:void(0)" :class="openTab === 1 ? activeClasses : inactiveClasses" class="nav-item [&.active]:after:opacity-100 [&.active]:after:w-full [&.active]:text-primary-500">
                                Home
                            </a>
                        </li>
                        <li @click="openTab = 2">
                            <a href="javascript:void(0)" :class="openTab === 2 ? activeClasses : inactiveClasses" class="nav-item [&.active]:after:opacity-100 [&.active]:after:w-full [&.active]:text-primary-500">
                                Service
                            </a>
                        </li>
                        <li @click="openTab = 3">
                            <a href="javascript:void(0)" :class="openTab === 3 ? activeClasses : inactiveClasses" class="nav-item [&.active]:after:opacity-100 [&.active]:after:w-full [&.active]:text-primary-500">
                                Contact Us
                            </a>
                        </li>
                    </ul>
                    <div class="mt-4">
                        <div x-show="openTab === 1">
                            <p class="text-gray-500 dark:text-dark-500">The Admin Dashboard displays tabs for multiple pages that provide a personalized view of BI performance, data correctness, required cube maintenance and required administrative actions. These pages contain the results of detailed analyses, represented by links, images, graphs, pie charts and BI reports.</p>
                        </div>
                        <div x-show="openTab === 2">
                            <p class="text-gray-500 dark:text-dark-500">A service administrator is responsible for overseeing all aspects of the work order for a service department. Your job duties include coordinating with the service manager on the completion of customer work orders, putting together billing for work completed, and reviewing time cards for service employees.</p>
                        </div>
                        <div x-show="openTab === 3">
                            <p class="text-gray-500 dark:text-dark-500">The official business definition of a Contact Administrator is an individual responsible for managing the contact database of an organization. This includes the maintenance of contact information, the creation of contact groups, and the organization of contact information into categories.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 md:col-span-6">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title">Basic Justify</h6>
            </div>
            <div class="card-body">
                <div x-data="{ 
                            openTab: 1,
                            activeClasses: 'active',
                            inactiveClasses: 'text-gray-500 hover:text-primary-500 dark:text-dark-500 dark:hover:text-primary-500'
                        }">
                    <ul class="tabs *:grow">
                        <li @click="openTab = 1">
                            <a href="javascript:void(0)" :class="openTab === 1 ? activeClasses : inactiveClasses" class="nav-item [&.active]:after:opacity-100 [&.active]:after:w-full [&.active]:text-primary-500">
                                Home
                            </a>
                        </li>
                        <li @click="openTab = 2">
                            <a href="javascript:void(0)" :class="openTab === 2 ? activeClasses : inactiveClasses" class="nav-item [&.active]:after:opacity-100 [&.active]:after:w-full [&.active]:text-primary-500">
                                Service
                            </a>
                        </li>
                        <li @click="openTab = 3">
                            <a href="javascript:void(0)" :class="openTab === 3 ? activeClasses : inactiveClasses" class="nav-item [&.active]:after:opacity-100 [&.active]:after:w-full [&.active]:text-primary-500">
                                Contact Us
                            </a>
                        </li>
                    </ul>
                    <div class="mt-4">
                        <div x-show="openTab === 1">
                            <p class="text-gray-500 dark:text-dark-500">The Admin Dashboard displays tabs for multiple pages that provide a personalized view of BI performance, data correctness, required cube maintenance and required administrative actions. These pages contain the results of detailed analyses, represented by links, images, graphs, pie charts and BI reports.</p>
                        </div>
                        <div x-show="openTab === 2">
                            <p class="text-gray-500 dark:text-dark-500">A service administrator is responsible for overseeing all aspects of the work order for a service department. Your job duties include coordinating with the service manager on the completion of customer work orders, putting together billing for work completed, and reviewing time cards for service employees.</p>
                        </div>
                        <div x-show="openTab === 3">
                            <p class="text-gray-500 dark:text-dark-500">The official business definition of a Contact Administrator is an individual responsible for managing the contact database of an organization. This includes the maintenance of contact information, the creation of contact groups, and the organization of contact information into categories.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 md:col-span-6">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title">Pill Tabs</h6>
            </div>
            <div class="card-body">
                <div x-data="{ 
                            openTab: 1,
                            activeClasses: 'active',
                            inactiveClasses: 'text-gray-500 hover:text-primary-500 dark:text-dark-500 dark:hover:text-primary-500'
                        }">
                    <ul class="tabs-pills">
                        <li @click="openTab = 1">
                            <a href="javascript:void(0)" :class="openTab === 1 ? activeClasses : inactiveClasses" class="nav-item [&.active]:bg-primary-500 [&.active]:text-primary-50">
                                Home
                            </a>
                        </li>
                        <li @click="openTab = 2">
                            <a href="javascript:void(0)" :class="openTab === 2 ? activeClasses : inactiveClasses" class="nav-item [&.active]:bg-primary-500 [&.active]:text-primary-50">
                                Service
                            </a>
                        </li>
                        <li @click="openTab = 3">
                            <a href="javascript:void(0)" :class="openTab === 3 ? activeClasses : inactiveClasses" class="nav-item [&.active]:bg-primary-500 [&.active]:text-primary-50">
                                Contact Us
                            </a>
                        </li>
                    </ul>
                    <div class="mt-4">
                        <div x-show="openTab === 1">
                            <p class="text-gray-500 dark:text-dark-500">The Admin Dashboard displays tabs for multiple pages that provide a personalized view of BI performance, data correctness, required cube maintenance and required administrative actions. These pages contain the results of detailed analyses, represented by links, images, graphs, pie charts and BI reports.</p>
                        </div>
                        <div x-show="openTab === 2">
                            <p class="text-gray-500 dark:text-dark-500">A service administrator is responsible for overseeing all aspects of the work order for a service department. Your job duties include coordinating with the service manager on the completion of customer work orders, putting together billing for work completed, and reviewing time cards for service employees.</p>
                        </div>
                        <div x-show="openTab === 3">
                            <p class="text-gray-500 dark:text-dark-500">The official business definition of a Contact Administrator is an individual responsible for managing the contact database of an organization. This includes the maintenance of contact information, the creation of contact groups, and the organization of contact information into categories.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 md:col-span-6">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title">Pill Justify Tabs</h6>
            </div>
            <div class="card-body">
                <div x-data="{ 
                            openTab: 1,
                            activeClasses: 'active',
                            inactiveClasses: 'text-gray-500 hover:text-primary-500 dark:text-dark-500 dark:hover:text-primary-500'
                        }">
                    <ul class="tabs-pills *:grow">
                        <li @click="openTab = 1">
                            <a href="javascript:void(0)" :class="openTab === 1 ? activeClasses : inactiveClasses" class="nav-item [&.active]:bg-primary-500 [&.active]:text-primary-50">
                                Home
                            </a>
                        </li>
                        <li @click="openTab = 2">
                            <a href="javascript:void(0)" :class="openTab === 2 ? activeClasses : inactiveClasses" class="nav-item [&.active]:bg-primary-500 [&.active]:text-primary-50">
                                Service
                            </a>
                        </li>
                        <li @click="openTab = 3">
                            <a href="javascript:void(0)" :class="openTab === 3 ? activeClasses : inactiveClasses" class="nav-item [&.active]:bg-primary-500 [&.active]:text-primary-50">
                                Contact Us
                            </a>
                        </li>
                    </ul>
                    <div class="mt-4">
                        <div x-show="openTab === 1">
                            <p class="text-gray-500 dark:text-dark-500">The Admin Dashboard displays tabs for multiple pages that provide a personalized view of BI performance, data correctness, required cube maintenance and required administrative actions. These pages contain the results of detailed analyses, represented by links, images, graphs, pie charts and BI reports.</p>
                        </div>
                        <div x-show="openTab === 2">
                            <p class="text-gray-500 dark:text-dark-500">A service administrator is responsible for overseeing all aspects of the work order for a service department. Your job duties include coordinating with the service manager on the completion of customer work orders, putting together billing for work completed, and reviewing time cards for service employees.</p>
                        </div>
                        <div x-show="openTab === 3">
                            <p class="text-gray-500 dark:text-dark-500">The official business definition of a Contact Administrator is an individual responsible for managing the contact database of an organization. This includes the maintenance of contact information, the creation of contact groups, and the organization of contact information into categories.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 md:col-span-6">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title">Icon with Animation Tabs</h6>
            </div>
            <div class="card-body">
                <div x-data="{ 
                            openTab: 2,
                            activeClasses: 'active',
                            inactiveClasses: 'text-gray-500 hover:text-primary-500 dark:text-dark-500 dark:hover:text-primary-500'
                        }">
                    <ul class="tabs-pills tabs-icon">
                        <li @click="openTab = 1">
                            <a href="javascript:void(0)" :class="openTab === 1 ? activeClasses : inactiveClasses" class="nav-item [&.active]:bg-primary-500 [&.active]:text-primary-50 group/item">
                                <i data-lucide="home" class="transition duration-200 ease-linear size-4 group-hover/item:animate-pulse"></i>
                            </a>
                        </li>
                        <li @click="openTab = 2">
                            <a href="javascript:void(0)" :class="openTab === 2 ? activeClasses : inactiveClasses" class="nav-item [&.active]:bg-primary-500 [&.active]:text-primary-50 group/item">
                                <i data-lucide="airplay" class="transition duration-200 ease-linear size-4 group-hover/item:animate-pulse"></i>
                            </a>
                        </li>
                        <li @click="openTab = 3">
                            <a href="javascript:void(0)" :class="openTab === 3 ? activeClasses : inactiveClasses" class="nav-item [&.active]:bg-primary-500 [&.active]:text-primary-50 group/item">
                                <i data-lucide="square-user" class="transition duration-200 ease-linear size-4 group-hover/item:animate-pulse"></i>
                            </a>
                        </li>
                    </ul>
                    <div class="mt-4">
                        <div x-show="openTab === 1">
                            <p class="text-gray-500 dark:text-dark-500">The Admin Dashboard displays tabs for multiple pages that provide a personalized view of BI performance, data correctness, required cube maintenance and required administrative actions. These pages contain the results of detailed analyses, represented by links, images, graphs, pie charts and BI reports.</p>
                        </div>
                        <div x-show="openTab === 2">
                            <p class="text-gray-500 dark:text-dark-500">A service administrator is responsible for overseeing all aspects of the work order for a service department. Your job duties include coordinating with the service manager on the completion of customer work orders, putting together billing for work completed, and reviewing time cards for service employees.</p>
                        </div>
                        <div x-show="openTab === 3">
                            <p class="text-gray-500 dark:text-dark-500">The official business definition of a Contact Administrator is an individual responsible for managing the contact database of an organization. This includes the maintenance of contact information, the creation of contact groups, and the organization of contact information into categories.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 md:col-span-6">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title">Icon with Text Tabs</h6>
            </div>
            <div class="card-body">
                <div x-data="{ 
                            openTab: 2,
                            activeClasses: 'active',
                            inactiveClasses: 'text-gray-500 hover:text-primary-500 dark:text-dark-500 dark:hover:text-primary-500'
                        }">
                    <ul class="tabs-pills">
                        <li @click="openTab = 1">
                            <a href="javascript:void(0)" :class="openTab === 1 ? activeClasses : inactiveClasses" class="nav-item [&.active]:bg-primary-500 [&.active]:text-primary-50">
                                <i data-lucide="home" class="inline-block size-4 ltr:mr-1 rtl:ml-1"></i> <span class="align-middle">Home</span>
                            </a>
                        </li>
                        <li @click="openTab = 2">
                            <a href="javascript:void(0)" :class="openTab === 2 ? activeClasses : inactiveClasses" class="nav-item [&.active]:bg-primary-500 [&.active]:text-primary-50">
                                <i data-lucide="tangent" class="inline-block size-4 ltr:mr-1 rtl:ml-1"></i> <span class="align-middle">Service</span>
                            </a>
                        </li>
                        <li @click="openTab = 3">
                            <a href="javascript:void(0)" :class="openTab === 3 ? activeClasses : inactiveClasses" class="nav-item [&.active]:bg-primary-500 [&.active]:text-primary-50">
                                <i data-lucide="home" class="inline-block size-4 ltr:mr-1 rtl:ml-1"></i> <span class="align-middle">Contact Us</span>
                            </a>
                        </li>
                    </ul>
                    <div class="mt-4">
                        <div x-show="openTab === 1">
                            <p class="text-gray-500 dark:text-dark-500">The Admin Dashboard displays tabs for multiple pages that provide a personalized view of BI performance, data correctness, required cube maintenance and required administrative actions. These pages contain the results of detailed analyses, represented by links, images, graphs, pie charts and BI reports.</p>
                        </div>
                        <div x-show="openTab === 2">
                            <p class="text-gray-500 dark:text-dark-500">A service administrator is responsible for overseeing all aspects of the work order for a service department. Your job duties include coordinating with the service manager on the completion of customer work orders, putting together billing for work completed, and reviewing time cards for service employees.</p>
                        </div>
                        <div x-show="openTab === 3">
                            <p class="text-gray-500 dark:text-dark-500">The official business definition of a Contact Administrator is an individual responsible for managing the contact database of an organization. This includes the maintenance of contact information, the creation of contact groups, and the organization of contact information into categories.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 md:col-span-6">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title">Animation Tabs</h6>
            </div>
            <div class="card-body">
                <div x-data="{ 
                            openTab: 2,
                            activeClasses: 'active',
                            inactiveClasses: 'text-gray-500 hover:text-green-500 dark:text-dark-500 dark:hover:text-green-500'
                        }">
                    <ul class="tabs-animation">
                        <li class="w-32" @click="openTab = 1">
                            <a href="javascript:void(0)" :class="openTab === 1 ? activeClasses : inactiveClasses" class="nav-item group/item [&.active]:bg-green-500 [&.active]:text-green-50">
                                <i data-lucide="home" class="icon group-hover/item:translate-y-11"></i> <span class="content group-hover/item:visible group-hover/item:-translate-y-3">Home</span>
                            </a>
                        </li>
                        <li class="w-32" @click="openTab = 2">
                            <a href="javascript:void(0)" :class="openTab === 2 ? activeClasses : inactiveClasses" class="nav-item group/item [&.active]:bg-green-500 [&.active]:text-green-50">
                                <i data-lucide="tangent" class="icon group-hover/item:translate-y-11"></i> <span class="content group-hover/item:visible group-hover/item:-translate-y-3">Service</span>
                            </a>
                        </li>
                        <li class="w-32" @click="openTab = 3">
                            <a href="javascript:void(0)" :class="openTab === 3 ? activeClasses : inactiveClasses" class="nav-item group/item [&.active]:bg-green-500 [&.active]:text-green-50">
                                <i data-lucide="home" class="icon group-hover/item:translate-y-11"></i> <span class="content group-hover/item:visible group-hover/item:-translate-y-3">Contact Us</span>
                            </a>
                        </li>
                    </ul>
                    <div class="mt-4">
                        <div x-show="openTab === 1">
                            <p class="text-gray-500 dark:text-dark-500">The Admin Dashboard displays tabs for multiple pages that provide a personalized view of BI performance, data correctness, required cube maintenance and required administrative actions. These pages contain the results of detailed analyses, represented by links, images, graphs, pie charts and BI reports.</p>
                        </div>
                        <div x-show="openTab === 2">
                            <p class="text-gray-500 dark:text-dark-500">A service administrator is responsible for overseeing all aspects of the work order for a service department. Your job duties include coordinating with the service manager on the completion of customer work orders, putting together billing for work completed, and reviewing time cards for service employees.</p>
                        </div>
                        <div x-show="openTab === 3">
                            <p class="text-gray-500 dark:text-dark-500">The official business definition of a Contact Administrator is an individual responsible for managing the contact database of an organization. This includes the maintenance of contact information, the creation of contact groups, and the organization of contact information into categories.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div><!--end col-->
</div><!--end grid-->

</div>
{{> partials/footer }}
</div>


{{> partials/vendor-scripts }}
<script type="module" src="assets/js/main.js"></script>

</body>
</html>