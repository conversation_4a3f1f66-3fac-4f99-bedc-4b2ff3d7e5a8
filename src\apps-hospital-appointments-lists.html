{{> partials/main }}

<head>

    {{> partials/title-meta title="List View" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}
{{> partials/page-heading title="List View" sub-title="Appointments" }}

<div class="grid grid-cols-1 md:grid-cols-2 2xl:grid-cols-4 gap-x-space">
    <div class="card">
        <div class="flex items-center gap-3 card-body">
            <div class="flex items-center justify-center text-green-500 rounded-md size-12 shrink-0 bg-gradient-to-r from-yellow-500/15 to-green-500/15">
                <i data-lucide="calendar-plus-2" class="size-6"></i>
            </div>
            <div class="grow">
                <p class="mb-1 text-gray-500 dark:text-dark-500">Today Appointment</p>
                <h6>10</h6>
            </div>
        </div>
    </div>
    <div class="card">
        <div class="flex items-center gap-3 card-body">
            <div class="flex items-center justify-center rounded-md text-sky-500 size-12 shrink-0 bg-gradient-to-r from-sky-500/15 to-green-500/15">
                <i data-lucide="calendar-plus" class="size-6"></i>
            </div>
            <div class="grow">
                <p class="mb-1 text-gray-500 dark:text-dark-500">New Appointment</p>
                <h6>21</h6>
            </div>
        </div>
    </div>
    <div class="card">
        <div class="flex items-center gap-3 card-body">
            <div class="flex items-center justify-center text-red-500 rounded-md size-12 shrink-0 bg-gradient-to-r from-red-500/15 to-pink-500/15">
                <i data-lucide="calendar-x-2" class="size-6"></i>
            </div>
            <div class="grow">
                <p class="mb-1 text-gray-500 dark:text-dark-500">Cancel Appointment</p>
                <h6>09</h6>
            </div>
        </div>
    </div>
    <div class="card">
        <div class="flex items-center gap-3 card-body">
            <div class="flex items-center justify-center text-purple-500 rounded-md size-12 shrink-0 bg-gradient-to-r from-purple-500/15 to-pink-500/15">
                <i data-lucide="calendar-days" class="size-6"></i>
            </div>
            <div class="grow">
                <p class="mb-1 text-gray-500 dark:text-dark-500">Total Appointment</p>
                <h6>153</h6>
            </div>
        </div>
    </div>
</div>

<div x-data="todayAppointmentTable()" x-init="init()">
    <div class="flex mb-3">
        <h6 class="grow">Today Appointments</h6>
        <a href="apps-hospital-appointments-book.html" class="underline link link-primary shrink-0" @click.prevent="toggleShowAll()" x-text="showAll ? 'Show Less' : 'Show All'"></a>
    </div>
    <div class="grid grid-cols-1 md:grid-cols-2 2xl:grid-cols-4 gap-x-space">
        <template x-for="(appointment, index) in displayedAppointments" :key="index">
            <div class="card">
                <div class="card-body">
                    <div class="flex">
                        <div class="grow">
                            <img :src="appointment.image" alt="" class="rounded-lg size-12">
                        </div>
                        <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown">
                            <button x-ref="button" x-on:click="toggle()" title="dropdown-button" :aria-expanded="open.toString()" title="dropdown-button" type="button" class="link link-primary">
                                <i class="ri-more-fill"></i>
                            </button>

                            <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden" dropdown-position="right">
                                <a href="apps-hospital-patients-overview.html" class="dropdown-item">
                                    Overview
                                </a>

                                <a href="#" class="dropdown-item">
                                    Edit
                                </a>
                                <a href="#" class="dropdown-item">
                                    Delete
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="mt-4">
                        <h6 class="mb-1"><a href="#!" x-text="appointment.patientName"></a></h6>
                        <p class="text-gray-500 dark:text-dark-500" x-text="appointment.treatment"></p>
                    </div>
                    <div class="flex mt-5 text-gray-500 dark:text-dark-500">
                        <p class="grow"><i class="align-baseline ltr:mr-1 rtl:ml-1 ri-calendar-event-line"></i> <span x-text="appointment.date"></span></p>
                        <p><i class="align-baseline ltr:mr-1 rtl:ml-1 ri-time-line"></i> <span x-text="appointment.time"></span></p>
                    </div>
                </div>
            </div>
        </template>
    </div>
</div>
<div x-data="callModal()">
    <div class="card" x-data="appointmentsTable()">
        <div class="flex md:items-center gap-5 card-header flex-col md:flex-row">
            <h6 class="card-title grow">Appointments (<span x-text="totalAppointments"></span>)</h6>
            <button type="button" class="btn btn-primary shrink-0"><a href="apps-hospital-appointments-book.html">Book Appointment</a></button>
        </div>
        <div class="pt-0 card-body">
            <div>
                <div class="overflow-x-auto table-box">
                    <table class="table whitespace-nowrap">
                        <tbody>
                            <tr class="text-gray-500 bg-gray-100 dark:bg-dark-850 dark:text-dark-500">
                                <th x-on:click="sort('patientName')" class="!font-medium cursor-pointer">Patient Name <span x-show="sortBy === 'patientName'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th x-on:click="sort('date')" class="!font-medium cursor-pointer">Date <span x-show="sortBy === 'date'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th x-on:click="sort('time')" class="!font-medium cursor-pointer">Time <span x-show="sortBy === 'time'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th x-on:click="sort('treatmentType')" class="!font-medium cursor-pointer">Treatment <span x-show="sortBy === 'differenceTime'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th x-on:click="sort('doctor')" class="!font-medium cursor-pointer">Doctor <span x-show="sortBy === 'doctor'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th x-on:click="sort('status')" class="!font-medium cursor-pointer">Status <span x-show="sortBy === 'status'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th class="!font-medium">Action</th>
                            </tr>
                            <template x-for="(appointment, index) in displayedAppointments" :key="index">
                                <tr class="*:px-3 *:py-2.5">
                                    <td>
                                        <div class="flex items-center gap-3">
                                            <div class="relative text-gray-500 bg-gray-100 rounded-full dark:bg-dark-850 dark:text-dark-500 size-8">
                                                <img x-show="appointment.image" :src="appointment.image" alt="" class="rounded-full" @error="removeImage($event)">
                                                <span x-show="!appointment.image" x-text="appointment.avatarText" class="absolute inset-0 flex items-center justify-center text-xs font-semibold text-gray-500 bg-gray-100 rounded-full dark:bg-dark-850 dark:text-dark-500"></span>
                                            </div>
                                            <div>
                                                <h6><a href="apps-hospital-patients-overview.html" x-text="appointment.patientName"></a></h6>
                                            </div>
                                        </div>
                                    </td>
                                    <td x-text="appointment.date"></td>
                                    <td x-text="appointment.time"></td>
                                    <td x-text="appointment.treatmentType"></td>
                                    <td x-text="appointment.doctor"></td>
                                    <td>
                                        <span x-text="appointment.status" :class="{
                                            'badge badge-primary': appointment.status === 'New',
                                            'badge badge-red': appointment.status === 'Cancel',
                                            'badge badge-green': appointment.status === 'Confirmed',
                                            'badge badge-purple': appointment.status === 'Completed',
                                            'badge badge-gray': appointment.status === 'Pending'
                                        }"></span>
                                    </td>
                                    <td>
                                        <div class="flex items-center gap-2">
                                            <button class="btn btn-sub-gray btn-icon !size-8" title="overview" @click="reviewAppointment(appointment)" data-modal-target="overviewModal"><i class="ri-eye-line"></i></button>
                                            <button class="btn btn-sub-gray btn-icon !size-8" title="edit"><i class="ri-pencil-line"></i></button>
                                            <button class="btn btn-sub-red btn-icon !size-8" title="delete" @click="deleteAppoint = appointment" data-modal-target="deleteModal"><i class="ri-delete-bin-line"></i></button>
                                        </div>
                                    </td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                </div>
                <div class="grid items-center grid-cols-12 gap-5 mt-5">
                    <div class="col-span-12 text-center lg:col-span-6 ltr:lg:text-left rtl:lg:text-right">
                        <p class="text-gray-500 dark:text-dark-500">Showing <b x-text="showingStart"></b> - <b x-text="showingEnd"></b> of <b x-text="appointments.length"></b> Results</p>
                    </div>
                    <div class="col-span-12 lg:col-span-6">
                        <div class="flex justify-center lg:justify-end pagination pagination-primary">
                            <button @click="prevPage()" :disabled="currentPage === 1" class="pagination-pre">
                                <i data-lucide="chevron-left" class="mr-1 ltr:inline-block rtl:hidden size-4"></i>
                                <i data-lucide="chevron-right" class="ml-1 ltr:hidden rtl:inline-block size-4"></i>
                                Prev
                            </button>
                            <template x-for="page in totalPages" :key="page">
                                <button @click="gotoPage(page)" :class="{ 'active': currentPage === page }" class="pagination-item">
                                    <span x-text="page"></span>
                                </button>
                            </template>
                            <button @click="nextPage()" :disabled="currentPage === totalPages" class="pagination-next">
                                Next
                                <i data-lucide="chevron-right" class="ml-1 ltr:inline-block rtl:hidden size-4"></i>
                                <i data-lucide="chevron-left" class="mr-1 ltr:hidden rtl:inline-block size-4"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!--Overview-->
        <div id="overviewModal" class="!hidden modal show">
            <div class="modal-wrap modal-center">
                <div class="modal-header">
                    <h6>Appointment Overview</h6>
                    <span x-text="selectedAppointment.status" :class="{
                'badge badge-primary': selectedAppointment.status === 'New',
                'badge badge-red': selectedAppointment.status === 'Cancel',
                'badge badge-green': selectedAppointment.status === 'Confirmed',
                'badge badge-purple': selectedAppointment.status === 'Completed',
                'badge badge-gray': selectedAppointment.status === 'Pending'
            }"></span>
                </div>
                <div class="modal-content">
                    <p class="mb-2 text-gray-500 dark:text-dark-500">Patient Info</p>
                    <div class="flex gap-3 mb-5">
                        <div class="relative items-center justify-center overflow-hidden text-gray-500 bg-gray-100 rounded-full dark:bg-dark-850 dark:text-dark-500 size-10">
                            <img :src="selectedAppointment.image" alt="" class="rounded-full">
                            <span x-show="!selectedAppointment.image" x-text="selectedAppointment.avatarText" class="absolute inset-0 flex items-center justify-center text-xs font-semibold text-gray-500 bg-gray-100 rounded-full dark:bg-dark-850 dark:text-dark-500"></span>
                        </div>
                        <div>
                            <h6 x-text="selectedAppointment.patientName">Daniel Adams</h6>
                            <p class="text-gray-500 dark:text-dark-500" x-text="selectedAppointment.treatmentType">Skin condition evaluation</p>
                        </div>
                    </div>
                    <p class="mb-2 text-gray-500 dark:text-dark-500">Date & Time</p>
                    <div class="flex gap-3 mb-5">
                        <div class="flex items-center justify-center overflow-hidden text-gray-500 bg-gray-100 rounded-full dark:bg-dark-850 dark:text-dark-500 size-10">
                            <i data-lucide="calendar-plus-2" class="size-5"></i>
                        </div>
                        <div>
                            <h6 x-text="selectedAppointment.date">08 June, 2024</h6>
                            <p class="text-gray-500 dark:text-dark-500" x-text="selectedAppointment.time">03:00PM - 04:00PM</p>
                        </div>
                    </div>
                    <div class="flex gap-3">
                        <p class="text-gray-500 dark:text-dark-500">Doctor Name:</p>
                        <div>
                            <h6 x-text="selectedAppointment.doctor">Dr. Sarah Evans</h6>
                        </div>
                    </div>
                </div>
                <div class="gap-2 modal-footer">
                    <button type="button" class="w-full btn btn-primary" data-modal-close="overviewModal" @click="callModalOpen = true" data-modal-target="callModal"><i class="ri-phone-line"></i> Call Patient</button>
                    <button type="button" class="w-full btn btn-red" @click="cancelAppointment()" data-modal-close="overviewModal"><i class="ri-close-line"></i> Cancel Appointment</button>
                </div>
            </div>
        </div>

        <!--call modal-->
        <div id="callModal" class="!hidden modal show" x-show="callModalOpen">
            <div class="modal-wrap modal-xs modal-br" @click.outside="stopCall()">
                <template  x-if="callModalOpen? startCall() : stopCall()"></template>
                <div class="modal-content" >
                    <div>
                        <div class="flex items-center gap-2">
                            <div class="relative items-center justify-center overflow-hidden text-gray-500 bg-gray-100 rounded-full dark:bg-dark-850 dark:text-dark-500 size-12 shrink-0">
                                <img :src="selectedAppointment.image" alt="">
                                <span x-show="!selectedAppointment.image" x-text="selectedAppointment.avatarText" class="absolute inset-0 flex items-center justify-center text-xs font-semibold text-gray-500 bg-gray-100 rounded-full dark:bg-dark-850 dark:text-dark-500"></span>
                            </div>
                            <div>
                                <h6 x-text="selectedAppointment.patientName">Daniel Adams</h6>
                                <p class="text-sm text-gray-500 dark:text-dark-500" x-text="isCalling ? 'Calling ...' : formatDuration(callDuration)">Calling ...</p>
                            </div>
                        </div>
                        <div class="flex items-center gap-3 mt-5">
                            <button type="button" class="btn btn-active-gray shrink-0 btn-icon-text btn-icon" @click="toggleMute">
                                <i x-show="isMuted" data-lucide="mic-off" class="size-5"></i>
                                <i x-show="!isMuted" data-lucide="mic" class="size-5"></i>
                            </button>
                            <button type="button" class="btn btn-active-gray shrink-0 btn-icon-text btn-icon">
                                <i data-lucide="pause" class="size-5"></i>
                            </button>
                            <button type="button" class="btn btn-active-gray shrink-0 btn-icon-text btn-icon">
                                <i data-lucide="disc" class="size-5"></i>
                            </button>
                            <button type="button" data-modal-target="overviewModal" @click="stopCall()" data-modal-close="callModal" class="btn btn-active-red shrink-0 btn-icon-text btn-icon">
                                <i data-lucide="phone" class="size-5"></i>
                            </button>
                            <button type="button" class="btn btn-active-gray shrink-0 btn-icon-text btn-icon">
                                <i data-lucide="settings" class="size-5"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!--delete modal-->
        <div id="deleteModal" class="!hidden modal show">
            <div class="modal-wrap modal-xs modal-center">
                <div class="text-center modal-content p-7">
                    <div class="flex items-center justify-center mx-auto mb-4 text-red-500 rounded-full bg-red-500/10 size-14 backdrop-blur-xl">
                        <i data-lucide="trash-2" class="size-6"></i>
                    </div>
                    <h5 class="mb-4">Are you sure you want to delete this appointment ?</h5>
                    <div class="flex items-center justify-center gap-2">
                        <button class="btn btn-red" @click="deleteAppointment() " data-modal-close="deleteModal">Delete</button>
                        <button data-modal-close="deleteModal" class="btn link link-primary">Cancel</button>
                    </div>
                </div>
            </div>
        </div><!--end-->
    </div>
</div>
</div>
{{> partials/footer }}
</div>

{{> partials/vendor-scripts }}

<script type="module" src="assets/js/apps/hospital/appointments/list.init.js"></script>

<script type="module" src="assets/js/main.js"></script>

</body>
</html>