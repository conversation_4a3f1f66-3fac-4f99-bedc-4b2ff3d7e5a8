{{> partials/main }}

<head>

    {{> partials/title-meta title="Grid View" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

<div class="relative min-h-screen">
    <div class="page-wrapper pt-[calc(theme('spacing.topbar')_*_1.2)] group-data-[layout=horizontal]:pt-[calc(theme('spacing.topbar')_*_1.9)]" x-data="projectsTable()">
        {{> partials/page-heading title="Grid View" sub-title="Projects" }}
        <div>
            <div class="flex flex-wrap items-center gap-5 mb-5">
                <div class="shrink-0">
                    <h6 class="card-title">My Projects (<span x-text="totalProjects"></span>)</h6>
                </div>
                <div class="md:mx-auto">
                    <div class="relative w-full md:!w-80 group/form">
                        <input type="text" x-model="searchQuery" @input="filterProjects" class="ltr:pl-9 rtl:pr-9 form-input ltr:group-[&.right]/form:pr-9 rtl:group-[&.right]/form:pl-9 ltr:group-[&.right]/form:pl-4 rtl:group-[&.right]/form:pr-4" placeholder="Search for projects...">
                        <div class="absolute inset-y-0 flex items-center text-gray-500 dark:text-dark-500 ltr:left-3 rtl:right-3 ltr:group-[&.right]/form:right-3 rtl:group-[&.right]/form:left-3 ltr:group-[&.right]/form:left-auto rtl:group-[&.right]/form:right-auto focus:outline-hidden">
                            <i data-lucide="search" class="size-4"></i>
                        </div>
                    </div>
                </div>
                <div class="shrink-0">
                    <button type="button" data-modal-target="addProjectModal" @click="handleModal('showAddProjectForm')" class="btn btn-primary"><i data-lucide="circle-plus" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-middle">Add Project</span></button>
                </div>
            </div>
            <ul class="overflow-x-auto tabs">
                <li>
                    <a href="#!" @click="setFilterStatus(''); activeTab = ''" :class="{ 'active': activeTab === '' }" class="nav-item [&.active]:after:opacity-100 [&.active]:after:w-full [&.active]:text-primary-500">
                        All Projects
                    </a>
                </li>
                <li>
                    <a href="#!" @click="setFilterStatus('Active'); activeTab = 'Active'" :class="{ 'active': activeTab === 'Active' }" class="nav-item [&.active]:after:opacity-100 [&.active]:after:w-full [&.active]:text-primary-500">
                        Active
                    </a>
                </li>
                <li>
                    <a href="#!" @click="setFilterStatus('Pending'); activeTab = 'Pending'" :class="{ 'active': activeTab === 'Pending' }" class="nav-item [&.active]:after:opacity-100 [&.active]:after:w-full [&.active]:text-primary-500">
                        Pending
                    </a>
                </li>
                <li>
                    <a href="#!" @click="setFilterStatus('On Hold'); activeTab = 'On Hold'" :class="{ 'active': activeTab === 'On Hold' }" class="nav-item [&.active]:after:opacity-100 [&.active]:after:w-full [&.active]:text-primary-500">
                        On Hold
                    </a>
                </li>
                <li>
                    <a href="#!" @click="setFilterStatus('Completed'); activeTab = 'Completed'" :class="{ 'active': activeTab === 'Completed' }" class="nav-item [&.active]:after:opacity-100 [&.active]:after:w-full [&.active]:text-primary-500">
                        Completed
                    </a>
                </li>
            </ul>

            <div class="grid grid-cols-1 mt-5 md:grid-cols-2 2xl:grid-cols-4 gap-x-5">
                <template x-if="displayedProjects.length > 0">
                    <template x-for="(project, index) in displayedProjects" :key="index">
                        <div class="card">
                            <div class="card-body">
                                <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown float-end">
                                    <button x-ref="button" x-on:click="toggle()" :aria-expanded="open.toString()" type="button" title="dropdown-button" class="flex items-center text-gray-500 dark:text-dark-500">
                                        <i class="ri-more-fill"></i>
                                    </button>
                                    <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden" dropdown-position="right">
                                        <ul>
                                            <li>
                                                <a href="apps-projects-overview.html" class="dropdown-item">
                                                    <i class="align-middle ltr:mr-2 rtl:ml-2 ri-eye-line"></i>
                                                    <span>Overview</span>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="#!" data-modal-target="addProjectModal" @click=" editProject(project.projectName)" class="dropdown-item">
                                                    <i class="align-middle ltr:mr-2 rtl:ml-2 ri-pencil-line"></i>
                                                    <span>Edit</span>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="#!" @click="deleteItem = project.projectName" data-modal-target="deleteModal" class="dropdown-item">
                                                    <i class="align-middle ltr:mr-2 rtl:ml-2 ri-delete-bin-line"></i>
                                                    <span>Delete</span>
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="p-2 mb-3 border border-gray-200 rounded-md dark:border-dark-800 size-12">
                                    <img :src="project.projectImage" alt="">
                                </div>
                                <h6 x-text="project.projectName" class="mb-1"></h6>
                                <p class="text-gray-500 dark:text-dark-500" x-text="project.clientName"></p>
                                <div class="grid grid-cols-2 mt-3 divide-x divide-gray-200 rtl:divide-x-reverse dark:divide-dark-800">
                                    <div class="p-2 text-center">
                                        <h6 x-text="project.dueDate" class="mb-1"></h6>
                                        <p class="text-gray-500 dark:text-dark-500">Due Date</p>
                                    </div>
                                    <div class="p-2 text-center">
                                        <h6 x-text="`$${project.totalAmount}`" class="mb-1"></h6>
                                        <p class="text-gray-500 dark:text-dark-500">Total Amount</p>
                                    </div>
                                </div>
                                <div class="mt-5">
                                    <p class="mb-2 text-gray-500 dark:text-dark-500">Project <span x-text="`${project.progress}%`"></span> completed</p>
                                    <div class="progress-bar progress-1">
                                        <div class="text-white progress-bar-wrap ltr:bg-gradient-to-r rtl:bg-gradient-to-l from-primary-500 to-pink-500 via-purple-500" :style="`width: ${project.progress}%`"></div>
                                    </div>
                                </div>
                                <div class="flex items-center gap-2 mt-5">
                                    <p class="text-gray-500 dark:text-dark-500">Assigned To:</p>
                                    <div class="flex -space-x-3 grow">
                                        <template x-for="assignee in project.assignees">
                                            <a href="#!" class="transition duration-300 ease-linear hover:z-10" title="avatar link">
                                                <img class="border-2 border-white rounded-full dark:border-dark-900 size-8" :src="assignee.image" alt="">
                                            </a>
                                        </template>
                                    </div>
                                    <div class="shrink-0">
                                        <span x-text="project.status" :class="{
                                            'badge badge-purple': project.status === 'Active',
                                            'badge badge-orange': project.status === 'On Hold',
                                            'badge badge-yellow': project.status === 'Pending',
                                            'badge badge-green': project.status === 'Completed'
                                        }"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </template>
                </template>
            </div><!--end grid-->

            <template x-if="filteredProjects.length == 0">
                <div class="p-8">
                    <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" class="mx-auto size-12" viewBox="0 0 48 48">
                        <linearGradient id="SVGID_1__h35ynqzIJzH4_gr1" x1="34.598" x2="15.982" y1="15.982" y2="34.598" gradientUnits="userSpaceOnUse">
                            <stop offset="0" stop-color="#60e8fe"></stop>
                            <stop offset=".033" stop-color="#6ae9fe"></stop>
                            <stop offset=".197" stop-color="#97f0fe"></stop>
                            <stop offset=".362" stop-color="#bdf5ff"></stop>
                            <stop offset=".525" stop-color="#dafaff"></stop>
                            <stop offset=".687" stop-color="#eefdff"></stop>
                            <stop offset=".846" stop-color="#fbfeff"></stop>
                            <stop offset="1" stop-color="#fff"></stop>
                        </linearGradient>
                        <path fill="url(#SVGID_1__h35ynqzIJzH4_gr1)" d="M40.036,33.826L31.68,25.6c0.847-1.739,1.335-3.684,1.335-5.748c0-7.27-5.894-13.164-13.164-13.164	S6.688,12.582,6.688,19.852c0,7.27,5.894,13.164,13.164,13.164c2.056,0,3.995-0.485,5.728-1.326l3.914,4.015l4.331,4.331	c1.715,1.715,4.496,1.715,6.211,0C41.751,38.321,41.751,35.541,40.036,33.826z">
                        </path>
                        <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M31.95,25.739l8.086,8.086c1.715,1.715,1.715,4.496,0,6.211l0,0c-1.715,1.715-4.496,1.715-6.211,0	l-4.331-4.331">
                        </path>
                        <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M7.525,24.511c-1.771-4.694-0.767-10.196,3.011-13.975c3.847-3.847,9.48-4.817,14.228-2.912">
                        </path>
                        <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M30.856,12.603c3.376,5.114,2.814,12.063-1.688,16.565c-4.858,4.858-12.565,5.129-17.741,0.814">
                        </path>
                    </svg>
                    <p class="mt-2 text-center text-gray-500 dark:text-dark-500">No matching records found</p>
                </div>
            </template>
                <div class="grid grid-cols-12 gap-5 mb-5 items-center" x-show="filteredProjects.length !== 0">
                    <div class="col-span-12 md:col-span-6">
                        <p class="text-gray-500 dark:text-dark-500">Showing <b x-text="showingStart"></b> - <b x-text="showingEnd"></b> of <b x-text="filteredProjects.length"></b> Results</p>
                    </div>
                    <div class="col-span-12 md:col-span-6">
                        <div class="flex justify-start md:justify-end pagination pagination-primary">
                            <button @click="prevPage()" :disabled="currentPage === 1" class="pagination-pre">
                                <i data-lucide="chevron-left" class="mr-1 ltr:inline-block rtl:hidden size-4"></i>
                                <i data-lucide="chevron-right" class="ml-1 ltr:hidden rtl:inline-block size-4"></i>
                                Prev
                            </button>
                            <template x-for="page in totalPages" :key="page">
                                <button @click="gotoPage(page)" :class="{ 'active': currentPage === page }" class="pagination-item">
                                    <span x-text="page"></span>
                                </button>
                            </template>
                            <button @click="nextPage()" :disabled="currentPage === totalPages" class="pagination-next">
                                Next
                                <i data-lucide="chevron-right" class="ml-1 ltr:inline-block rtl:hidden size-4"></i>
                                <i data-lucide="chevron-left" class="mr-1 ltr:hidden rtl:inline-block size-4"></i>
                            </button>
                        </div>
                    </div>
                </div>
        </div>

        <div id="addProjectModal" class="!hidden modal show" :class="{'show d-block': showAddProjectForm || showEditProjectForm}" x-show="showAddProjectForm || showEditProjectForm">
            <div class="modal-wrap modal-center">
                <div class="modal-header">
                    <h6 class="modal-title" x-text="showAddProjectForm ? 'Add project' : 'Edit project'">Add Project
                    </h6>
                    <button data-modal-close="addProjectModal" class="link link-red"><i data-lucide="x" class="size-5"></i></button>
                </div>
                <div class="modal-content">
                    <div class="grid grid-cols-12 gap-4">
                        <div class="col-span-12">
                            <label for="projectTitleInput" class="form-label">Project Title</label>
                            <input type="text" id="projectTitleInput" class="form-input" placeholder="Project title" x-model="projectForm.projectName" @input="validateField('projectName', projectForm.projectName, 'Project name is required.')">
                            <span x-show="errors.projectName" class="text-red-500" x-text="errors.projectName"></span>
                        </div>
                        <div class="col-span-12">
                            <label for="clientName" class="form-label">Client Name</label>
                            <input type="text" id="clientName" class="form-input" placeholder="Enter name" x-model="projectForm.clientName" @input="validateField('clientName', projectForm.clientName, 'Client name is required.')">
                            <span x-show="errors.clientName" class="text-red-500" x-text="errors.clientName"></span>
                        </div>
                        <div class="col-span-6">
                            <label for="dueDateInput" class="form-label">Due Date</label>
                            <input type="text" id="dueDateInput" class="form-input" placeholder="Select due date" data-provider="flatpickr" data-date-format="d M, Y" x-model="projectForm.dueDate" @input="validateField('dueDate', projectForm.dueDate, 'Due date is required.')">
                            <span x-show="errors.dueDate" class="text-red-500" x-text="errors.dueDate"></span>
                        </div>
                        <div class="col-span-6">
                            <label for="totalAmountInput" class="form-label">Total Amount ($)</label>
                            <input type="number" id="totalAmountInput" class="form-input" placeholder="$00.00" x-model="projectForm.totalAmount" @input="validateField('totalAmount', projectForm.totalAmount, 'Total amount is required.')">
                            <span x-show="errors.totalAmount" class="text-red-500" x-text="errors.totalAmount"></span>
                        </div>
                        <div class="col-span-12">
                            <label for="progressInput" class="form-label">% Complete</label>
                            <input type="number" id="progressInput" class="form-input" placeholder="0" x-model="projectForm.progress" @input="validateField('progress', projectForm.progress, 'Progress is required.')">
                            <span x-show="errors.progress" class="text-red-500" x-text="errors.progress"></span>
                            <div class="mt-3 progress-bar progress-1">
                                <div class="text-white progress-bar-wrap ltr:bg-gradient-to-r rtl:bg-gradient-to-l from-primary-500 to-pink-500 via-purple-500" x-bind:style="`width: ${projectForm.progress}%`"></div>
                            </div>
                        </div>
                        <div class="col-span-12">
                            <label for="assignedSelect" class="form-label">Assignee To</label>
                            <div id="assignedSelect" placeholder="Select Assignee To" x-model="projectForm.assignee" @change="validateField('assignee',  document.querySelector('#assignedSelect') , 'Assignee is required.')">
                            </div>
                            <span x-show="errors.assignee" class="text-red-500" x-text="errors.assignee"></span>
                        </div>
                        <div class="col-span-12">
                            <label for="statusSelect2" class="form-label">Status</label>
                            <div id="statusSelect2" placeholder="Select Status" x-model="projectForm.status" @change="validateField('status', document.querySelector('#statusSelect2') , 'Status is required.')">
                            </div>
                            <span x-show="errors.status" class="text-red-500" x-text="errors.status"></span>
                        </div>
                    </div>
                    <div class="flex items-center justify-end gap-2 mt-5">
                        <button type="button" class="btn btn-active-red" data-modal-close="addProjectModal" @click="resetForm()">
                            <i data-lucide="x" class="inline-block size-4"></i>
                            <span class="align-baseline">Close</span>
                        </button>
                        <button type="button" class="btn btn-primary" x-text="showAddProjectForm ? 'Add project' : 'Update project'" @click="submitForm()"></button>
                    </div>
                </div>
            </div>
        </div>

        <!--delete modal-->
        <div id="deleteModal" class="!hidden modal show">
            <div class="modal-wrap modal-xs modal-center">
                <div class="text-center modal-content p-7">
                    <div class="flex items-center justify-center mx-auto mb-4 text-red-500 rounded-full bg-red-500/10 size-14 backdrop-blur-xl">
                        <i data-lucide="trash-2" class="size-6"></i>
                    </div>
                    <h5 class="mb-4">Are you sure you want to delete this project ?</h5>
                    <div class="flex items-center justify-center gap-2">
                        <button class="btn btn-red" @click="deleteProject()" data-modal-close="deleteModal">Delete</button>
                        <button data-modal-close="deleteModal" class="btn link link-primary">Cancel</button>
                    </div>
                </div>
            </div>
        </div><!--end-->

    </div>
    {{> partials/footer }}
</div>


{{> partials/vendor-scripts }}

<script type="module" src="assets/js/apps/projects/grid-view.init.js"></script>

<script type="module" src="assets/js/main.js"></script>

</body>
</html>