{{> partials/main }}

<head>

    {{> partials/title-meta title="File Input"}}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar}}
{{> partials/sidebar}}

{{> partials/page-wrapper }}
        {{> partials/page-heading title="File Input" sub-title="Forms"}}

        <div class="grid grid-cols-12 gap-x-space">
            <div class="col-span-12 card">
                <div class="card-header">
                    <h6 class="card-title">Basic</h6>
                </div>
                <div class="card-body">
                    <div class="grid grid-cols-12 gap-5">
                        <div class="col-span-12 md:col-span-6">
                            <label for="basicInput1" class="form-label">Light File Input</label>
                            <input type="file" id="basicInput1" class="form-file form-file-light">
                        </div><!--end col-->
                        <div class="col-span-12 md:col-span-6">
                            <label for="basicInput2" class="form-label">Dark File Input</label>
                            <input type="file" id="basicInput2" class="form-file">
                        </div><!--end col-->
                    </div><!--end grid-->
                </div>
            </div><!--end col-->
            <div class="col-span-12 card">
                <div class="card-header">
                    <h6 class="card-title">Basic</h6>
                </div>
                <div class="card-body">
                    <div class="grid grid-cols-12 gap-5">
                        <div class="col-span-12 md:col-span-6">
                            <label for="basicInput3" class="form-label">Small File Input</label>
                            <input type="file" id="basicInput3" class="form-file form-file-sm form-file-light">
                        </div><!--end col-->
                        <div class="col-span-12 md:col-span-6">
                            <label for="basicInput4" class="form-label">Medium File Input</label>
                            <input type="file" id="basicInput4" class="form-file form-file-md form-file-light">
                        </div><!--end col-->
                        <div class="col-span-12 md:col-span-6">
                            <label for="basicInput4" class="form-label">Default File Input</label>
                            <input type="file" id="basicInput4" class="form-file form-file-light">
                        </div><!--end col-->
                        <div class="col-span-12 md:col-span-6">
                            <label for="basicInput4" class="form-label">Large File Input</label>
                            <input type="file" id="basicInput4" class="form-file form-file-lg form-file-light">
                        </div><!--end col-->
                    </div><!--end grid-->
                </div>
            </div><!--end col-->
            <div class="col-span-12 md:col-span-6 card">
                <div class="card-header">
                    <h6 class="card-title">Upload User Profile</h6>
                </div>
                <div class="card-body">
                    <div class="text-sm">
                        <div x-data="previewImage()">
                            <label for="logo">
                                <div class="inline-flex items-center justify-center overflow-hidden bg-gray-100 border border-gray-200 rounded-sm cursor-pointer dark:bg-dark-850 dark:border-dark-800 size-32">
                                    <img x-show="imageUrl" :src="imageUrl" class="object-cover w-full h-full">
                                    <div x-show="!imageUrl" class="flex flex-col items-center text-gray-500 dark:text-dark-500">
                                        <i data-lucide="upload"></i>
                                        <div class="mt-2">User Profile</div>
                                    </div>
                                </div>
                            </label>
                            <div class="mt-4">
                                <label class="block">
                                    <span class="sr-only">Choose profile photo</span>
                                    <input type="file" name="logo" id="logo" @change="fileChosen" class="block w-full text-sm file:cursor-pointer file:rounded-md focus:outline-0 text-slate-500 dark:text-dark-500 file:mr-4 file:py-2 file:px-4 file:border-0 file:text-sm file:font-semibold file:bg-violet-500/10 file:text-violet-700 hover:file:bg-violet-500/15 " />
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div><!--end col-->
            <div class="col-span-12 md:col-span-6 card" >
                <div class="card-header">
                    <h6 class="card-title">Upload User Profile</h6>
                </div>
                <div class="card-body" x-data="previewImage()">
                    <form class="flex items-center space-x-6">
                        <div class="shrink-0">
                            <img x-show="imageUrl" class="object-cover w-16 h-16 rounded-full" :src="imageUrl" alt="Current profile photo" />
                            <div x-show="!imageUrl">
                                <img class="w-16 h-16 rounded-full bject-cover " src="assets/images/avatar/user-13.png" alt="Current profile photo" />
                            </div>
                        </div>
                        <label class="block">
                            <span class="sr-only">Choose profile photo</span>
                            <input type="file" @change="fileChosen"  class="block w-full text-sm file:cursor-pointer text-slate-500 dark:text-dark-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-violet-500/10 file:text-violet-700 hover:file:bg-violet-500/15 " />
                        </label>
                    </form>
                </div>
            </div><!--end col-->
        </div><!--end grid-->

    </div>
    {{> partials/footer }}
</div>

{{> partials/vendor-scripts }}

<script type="module" src="assets/js/form/file-input.init.js"></script>

<script type="module" src="assets/js/main.js"></script>

</body>
</html>