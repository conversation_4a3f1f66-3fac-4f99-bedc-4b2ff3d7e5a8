{{> partials/main }}

<head>

    {{> partials/title-meta title="Word Counter" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}

{{> partials/page-heading title="Word Counter" sub-title="UI Advanced" }}

<div class="grid grid-cols-12 gap-x-space">
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Simple Word Counter</h6>
        </div>
        <div class="card-body">
            <div x-data="data()" x-init="{...countWords(), ...countChars()}">
                <textarea name="search" id="words" rows="10" x-model="search" @keyup="countWords(); countChars();" class="h-auto form-input" x-text="search"></textarea>
                <p class="mt-4">Word counts: <span class="font-semibold text-blue-500" x-text="wordCount"></span></p>
                <p>Character count: <span class="font-semibold text-blue-500" x-text="charCount"></span> (without space)</p>
            </div>
        </div>
    </div><!--end col-->
</div><!--end grid-->

</div>
{{> partials/footer }}
</div>


{{> partials/vendor-scripts }}
<script type="module" src="assets/js/main.js"></script>
<script type="module" src="assets/js/ui/advanced-word-counter.init.js"></script>

</body>
</html>