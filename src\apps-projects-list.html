{{> partials/main }}

<head>

    {{> partials/title-meta title="List View" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}
{{> partials/page-heading title="List View" sub-title="Projects" }}
<div x-data="projectsTable()">
    <div class="card">
        <div class="flex flex-wrap items-center gap-5 card-header">
            <div class="grow">
                <h6 class="mb-1 card-title">All Projects (264)</h6>
                <p class="text-gray-500">Manage your construction projects from start to finish with complete control.</p>
            </div>
            <button type="button" data-modal-target="addProjectModal" @click="handleModal('showAddProjectForm')" class="btn btn-primary"><i data-lucide="circle-plus" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-middle">Add Project</span></button>
        </div>
        <div class="card-header">

            <div class="grid grid-cols-12 gap-5">
                <div class="col-span-12 xl:col-span-4">
                    <div class="relative group/form">
                        <input type="text" class="ltr:pl-9 rtl:pr-9 form-input group-[&.right]/form:pr-9 group-[&.right]/form:pl-4" placeholder="Search for projects..." @input="filterProjects" x-model="searchTerm">
                        <div class="absolute inset-y-0 flex items-center text-gray-500 dark:text-dark-500 ltr:left-3 rtl:right-3 group-[&.right]/form:right-3 group-[&.right]/form:left-auto focus:outline-hidden">
                            <i data-lucide="search" class="size-4"></i>
                        </div>
                    </div>
                </div><!--end col-->
                <div class="flex flex-wrap col-span-12 gap-2 xl:col-span-8 xl:justify-end">
                    <button class="btn btn-red btn-icon " x-show="selectedItems.length > 0" @click="deleteSelectedItems()">
                        <i data-lucide="trash" class="inline-block size-4"></i>
                    </button>
                    <div>
                        <div id="filterSelect" placeholder="Filter Date Select" @change="updateFilterDateSelect"></div>
                    </div>
                    <div>
                        <div id="filterStatusSelect" placeholder="Status Select" @change="updateFilterStatus"></div>
                    </div>
                    <div>
                        <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown shrink-0">
                            <button x-ref="button" x-on:click="toggle()" :aria-expanded="open.toString()" type="button" class="w-full btn btn-sub-gray whitespace-nowrap">
                                Filter By Assignee
                            </button>

                            <div x-ref="dropdown" x-show="open" @change="updateFilterAssignees" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden !w-52" dropdown-position="right">
                                <ul>
                                    <li>
                                        <p class="mb-1 text-gray-500 dark:text-dark-500">Filter by Assignee</p>
                                    </li>
                                    <li>
                                        <div class="py-2 input-check-group">
                                            <input id="assigneeToMax" class="shrink-0 input-check input-check-primary" type="checkbox" value="Max Boucaut" />
                                            <label for="assigneeToMax" value="Max Boucaut" class="flex items-center gap-2 font-medium input-check-label grow">
                                                <img src="assets/images/avatar/user-14.png" alt="" class="rounded-full size-6">
                                                Max Boucaut
                                            </label>
                                        </div>
                                    </li>
                                    <li>
                                        <div class="py-2 input-check-group">
                                            <input id="assigneeTonatasha" class="shrink-0 input-check input-check-primary" type="checkbox" value="Natasha Tegg" />
                                            <label for="assigneeTonatasha" class="flex items-center gap-2 font-medium input-check-label grow">
                                                <img src="assets/images/avatar/user-15.png" alt="" class="rounded-full size-6">
                                                Natasha Tegg
                                            </label>
                                        </div>
                                    </li>
                                    <li>
                                        <div class="py-2 input-check-group">
                                            <input id="assigneeToEthan" class="shrink-0 input-check input-check-primary" type="checkbox" value="Ethan Zahel" />
                                            <label for="assigneeToEthan" class="flex items-center gap-2 font-medium input-check-label grow">
                                                <img src="assets/images/avatar/user-16.png" alt="" class="rounded-full size-6">
                                                Ethan Zahel
                                            </label>
                                        </div>
                                    </li>
                                    <li>
                                        <div class="py-2 input-check-group">
                                            <input id="assigneeToPoppy" class="shrink-0 input-check input-check-primary" type="checkbox" value="Poppy Dalley" />
                                            <label for="assigneeToPoppy" class="flex items-center gap-2 font-medium input-check-label grow">
                                                <img src="assets/images/avatar/user-17.png" alt="" class="rounded-full size-6">
                                                Poppy Dalley
                                            </label>
                                        </div>
                                    </li>
                                    <li>
                                        <div class="py-2 input-check-group">
                                            <input id="assigneeToRyan" class="shrink-0 input-check input-check-primary" type="checkbox" value="Ryan Frazer" />
                                            <label for="assigneeToRyan" class="flex items-center gap-2 font-medium input-check-label grow">
                                                <img src="assets/images/avatar/user-18.png" alt="" class="rounded-full size-6">
                                                Ryan Frazer
                                            </label>
                                        </div>
                                    </li>
                                    <li>
                                        <div class="py-2 input-check-group">
                                            <input id="assigneeToJulian" class="shrink-0 input-check input-check-primary" type="checkbox" value="Julian Marconi" />
                                            <label for="assigneeToJulian" class="flex items-center gap-2 font-medium input-check-label grow">
                                                <img src="assets/images/avatar/user-12.png" alt="" class="rounded-full size-6">
                                                Julian Marconi
                                            </label>
                                        </div>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div><!--end grid-->
        </div>
        <div class="pt-0 card-body">
            <div>
                <div class="overflow-x-auto table-box">
                    <table class="table whitespace-nowrap">
                        <tbody>
                            <tr class="text-gray-500 bg-gray-100 dark:bg-dark-850 dark:text-dark-500">
                                <th class="w-14">
                                    <div class="flex items-center">
                                        <label for="checkboxAll" class="hidden input-check-label"></label>
                                        <input id="checkboxAll" class="input-check input-check-primary" type="checkbox" x-model="selectAll" x-on:click="toggleAll" />
                                    </div>
                                </th>
                                <th x-on:click="sort('projectID')" class="!font-medium cursor-pointer">ID <span x-show="sortBy === 'projectID'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th x-on:click="sort('projectName')" class="!font-medium cursor-pointer">Project and Client Name <span x-show="sortBy === 'projectName'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th x-on:click="sort('assignee')" class="!font-medium cursor-pointer">Assigned To</th>
                                <th x-on:click="sort('dueDate')" class="!font-medium cursor-pointer">Due Date <span x-show="sortBy === 'dueDate'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th x-on:click="sort('totalAmount')" class="!font-medium cursor-pointer">Total Amount ($) <span x-show="sortBy === 'totalAmount'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th x-on:click="sort('progress')" class="!font-medium cursor-pointer">% Complete <span x-show="sortBy === 'progress'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th x-on:click="sort('status')" class="!font-medium cursor-pointer">Status <span x-show="sortBy === 'status'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th class="!font-medium">Action</th>
                            </tr>
                            <template x-if="displayedProjects.length > 0">

                                <template x-for="(project, index) in displayedProjects" :key="index">
                                    <tr>
                                        <td>
                                            <div class="flex items-center">
                                                <label :for="`project${index}`" class="hidden input-check-label"></label>
                                                <input :id="`project${index}`" class="input-check input-check-primary" type="checkbox" @click="toggleItem(project)" :checked="selectedItems.includes(project)" />
                                            </div>
                                        </td>

                                        <td x-text="project.projectID"></td>
                                        <td>
                                            <h6 class="mb-1"><a href="apps-projects-overview.html" data-modal-target="contactOverviewModal" class="text-current link link-primary grow" x-text="project.projectName"></a></h6>
                                            <p x-text="project.clientName" class="text-sm text-gray-500 dark:text-dark-500"></p>
                                        </td>
                                        <td>
                                            <div class="flex ml-3 -space-x-3 grow">
                                                <template x-for="assignee in project.assignees">
                                                    <a href="#!" class="transition duration-300 ease-linear hover:z-10" title="avatar link">
                                                        <img class="border-2 border-white rounded-full dark:border-dark-900 size-8" :src="assignee.image" alt="">
                                                    </a>
                                                </template>
                                            </div>
                                        </td>
                                        <td x-text="project.dueDate"></td>
                                        <td x-text="`$${project.totalAmount}`"></td>
                                        <td>
                                            <div class="flex items-center gap-2">
                                                <p x-text="`${project.progress}%`"></p>
                                                <div class="progress-bar progress-1">
                                                    <div class="text-white progress-bar-wrap bg-primary-500" :style="`width: ${project.progress}%`"></div>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span x-text="project.status" :class="{
                                                'badge badge-purple': project.status === 'Active',
                                                'badge badge-orange': project.status === 'On Hold',
                                                'badge badge-yellow': project.status === 'Pending',
                                                'badge badge-green': project.status === 'Completed'
                                            }"></span>
                                        </td>
                                        <td>
                                            <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown">
                                                <button x-ref="button" x-on:click="toggle()" :aria-expanded="open.toString()" type="button" title="dropdown-button" class="flex items-center text-gray-500 dark:text-dark-500">
                                                    <i class="ri-more-2-fill"></i>
                                                </button>
                                                <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden" dropdown-position="right">
                                                    <ul>
                                                        <li>
                                                            <a href="apps-projects-overview.html" data-modal-target="contactOverviewModal" class="dropdown-item">
                                                                <i class="align-middle ltr:mr-2 rtl:ml-2 ri-eye-line"></i> <span>Overview</span>
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a href="#!" @click="editProject(project.projectID)" data-modal-target="addProjectModal" class="dropdown-item">
                                                                <i class="align-middle ltr:mr-2 rtl:ml-2 ri-pencil-line"></i> <span>Edit</span>
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a href="#!" @click=" deleteItem = project.projectID" data-modal-target="deleteModal" class="dropdown-item">
                                                                <i class="align-middle ltr:mr-2 rtl:ml-2 ri-delete-bin-line"></i> <span>Delete</span>
                                                            </a>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                </template>
                            </template>
                            <tr>
                                <template x-if="displayedProjects.length == 0">
                                    <td colspan="10" class="!p-8">
                                        <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" class="mx-auto size-12" viewBox="0 0 48 48">
                                            <linearGradient id="SVGID_1__h35ynqzIJzH4_gr1" x1="34.598" x2="15.982" y1="15.982" y2="34.598" gradientUnits="userSpaceOnUse">
                                                <stop offset="0" stop-color="#60e8fe"></stop>
                                                <stop offset=".033" stop-color="#6ae9fe"></stop>
                                                <stop offset=".197" stop-color="#97f0fe"></stop>
                                                <stop offset=".362" stop-color="#bdf5ff"></stop>
                                                <stop offset=".525" stop-color="#dafaff"></stop>
                                                <stop offset=".687" stop-color="#eefdff"></stop>
                                                <stop offset=".846" stop-color="#fbfeff"></stop>
                                                <stop offset="1" stop-color="#fff"></stop>
                                            </linearGradient>
                                            <path fill="url(#SVGID_1__h35ynqzIJzH4_gr1)" d="M40.036,33.826L31.68,25.6c0.847-1.739,1.335-3.684,1.335-5.748c0-7.27-5.894-13.164-13.164-13.164	S6.688,12.582,6.688,19.852c0,7.27,5.894,13.164,13.164,13.164c2.056,0,3.995-0.485,5.728-1.326l3.914,4.015l4.331,4.331	c1.715,1.715,4.496,1.715,6.211,0C41.751,38.321,41.751,35.541,40.036,33.826z"></path>
                                            <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M31.95,25.739l8.086,8.086c1.715,1.715,1.715,4.496,0,6.211l0,0c-1.715,1.715-4.496,1.715-6.211,0	l-4.331-4.331"></path>
                                            <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M7.525,24.511c-1.771-4.694-0.767-10.196,3.011-13.975c3.847-3.847,9.48-4.817,14.228-2.912"></path>
                                            <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M30.856,12.603c3.376,5.114,2.814,12.063-1.688,16.565c-4.858,4.858-12.565,5.129-17.741,0.814"></path>
                                        </svg>
                                        <p class="mt-2 text-center text-gray-500 dark:text-dark-500">No matching records found</p>
                                    </td>
                                </template>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="grid grid-cols-12 gap-5 mt-5 items-center" x-show="displayedProjects.length !== 0">
                    <div class="col-span-12 md:col-span-6 justify-center md:justify-start flex flex-wrap">
                        <p class="text-gray-500 dark:text-dark-500 ltr:pr-1 rtl:pl-1">Showing <b x-text="showingStart"></b> - <b x-text="showingEnd"></b> of <b x-text="filteredProjects.length"></b> Results</p>
                        <span x-show="selectedItems.length > 0"><b x-text="selectedItems.length != 0 ? selectedItems.length : ''"> </b> Row Selected</span>
                    </div>
                    <div class="col-span-12 md:col-span-6">
                        <div class="flex justify-center md:justify-end pagination pagination-primary">
                            <button @click="prevPage()" :disabled="currentPage === 1" class="pagination-pre">
                                <i data-lucide="chevron-left" class="mr-1 ltr:inline-block rtl:hidden size-4"></i>
                                <i data-lucide="chevron-right" class="ml-1 ltr:hidden rtl:inline-block size-4"></i>
                                Prev
                            </button>
                            <template x-for="page in totalPages" :key="page">
                                <button @click="gotoPage(page)" :class="{ 'active': currentPage === page }" class="pagination-item">
                                    <span x-text="page"></span>
                                </button>
                            </template>
                            <button @click="nextPage()" :disabled="currentPage === totalPages" class="pagination-next">
                                Next
                                <i data-lucide="chevron-right" class="ml-1 ltr:inline-block rtl:hidden size-4"></i>
                                <i data-lucide="chevron-left" class="mr-1 ltr:hidden rtl:inline-block size-4"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div id="addProjectModal" class="!hidden modal show" :class="{'show d-block': showAddProjectForm || showEditProjectForm}" x-show="showAddProjectForm || showEditProjectForm">
        <div class="modal-wrap modal-center">
            <div class="modal-header">
                <h6 class="modal-title" x-text="showAddProjectForm ? 'Add project' : 'Edit project'">Add Project</h6>
                <button data-modal-close="addProjectModal" class="link link-red"><i data-lucide="x" class="size-5"></i></button>
            </div>
            <div class="modal-content">
                <div class="grid grid-cols-12 gap-4">
                    <div class="col-span-12">
                        <label for="projectTitleInput" class="form-label">Project Title</label>
                        <input type="text" id="projectTitleInput" class="form-input" placeholder="Project title" x-model="projectForm.projectName" @input="validateField('projectName', projectForm.projectName, 'Project name is required.')">
                        <span x-show="errors.projectName" class="text-red-500" x-text="errors.projectName"></span>
                    </div>
                    <div class="col-span-12">
                        <label for="clientName" class="form-label">Client Name</label>
                        <input type="text" id="clientName" class="form-input" placeholder="Enter name" x-model="projectForm.clientName" @input="validateField('clientName', projectForm.clientName, 'Client name is required.')">
                        <span x-show="errors.clientName" class="text-red-500" x-text="errors.clientName"></span>
                    </div>
                    <div class="col-span-6">
                        <label for="dueDateInput" class="form-label">Due Date</label>
                        <input type="text" id="dueDateInput" class="form-input" placeholder="Select due date" data-provider="flatpickr" data-date-format="d M, Y" x-model="projectForm.dueDate" @input="validateField('dueDate', projectForm.dueDate, 'Due date is required.')">
                        <span x-show="errors.dueDate" class="text-red-500" x-text="errors.dueDate"></span>
                    </div>
                    <div class="col-span-6">
                        <label for="totalAmountInput" class="form-label">Total Amount ($)</label>
                        <input type="number" id="totalAmountInput" class="form-input" placeholder="$00.00" x-model="projectForm.totalAmount" @input="validateField('totalAmount', projectForm.totalAmount, 'Total amount is required.')">
                        <span x-show="errors.totalAmount" class="text-red-500" x-text="errors.totalAmount"></span>
                    </div>
                    <div class="col-span-12">
                        <label for="progressInput" class="form-label">% Complete</label>
                        <input type="text" id="progressInput" class="form-input" placeholder="0" x-model="projectForm.progress" @input="validateField('progress', projectForm.progress, 'Progress is required.')">
                        <span x-show="errors.progress" class="text-red-500" x-text="errors.progress"></span>
                        <div class="mt-3 progress-bar progress-1">
                            <div class="text-white progress-bar-wrap bg-gradient-to-r from-primary-500 to-pink-500 via-purple-500" x-bind:style="`width: ${projectForm.progress}%`"></div>
                        </div>
                    </div>
                    <div class="col-span-12">
                        <label for="assignedSelect" class="form-label">Assignee To</label>
                        <div id="assignedSelect" placeholder="Select Assignee To" x-model="projectForm.assignee" @change="validateField('assignee',  document.querySelector('#assignedSelect') , 'Assignee is required.')"></div>
                        <span x-show="errors.assignee" class="text-red-500" x-text="errors.assignee"></span>
                    </div>
                    <div class="col-span-12">
                        <label for="statusSelect2" class="form-label">Status</label>
                        <div id="statusSelect2" placeholder="Select Status" x-model="projectForm.status" @change="validateField('status', document.querySelector('#statusSelect2') , 'Status is required.')"></div>
                        <span x-show="errors.status" class="text-red-500" x-text="errors.status"></span>
                    </div>
                </div>
                <div class="flex items-center justify-end gap-2 mt-5">
                    <button type="button" class="btn btn-active-red" data-modal-close="addProjectModal" @click="resetForm()">
                        <i data-lucide="x" class="inline-block size-4"></i>
                        <span class="align-baseline">Close</span>
                    </button>
                    <button type="button" class="btn btn-primary" x-text="showAddProjectForm ? 'Add project' : 'Update project'" @click="submitForm()"></button>
                </div>
            </div>
        </div>
    </div>

    <!--delete modal-->
    <div id="deleteModal" class="!hidden modal show">
        <div class="modal-wrap modal-xs modal-center">
            <div class="text-center modal-content p-7">
                <div class="flex items-center justify-center mx-auto mb-4 text-red-500 rounded-full bg-red-500/10 size-14 backdrop-blur-xl">
                    <i data-lucide="trash-2" class="size-6"></i>
                </div>
                <h5 class="mb-4">Are you sure you want to delete this project ?</h5>
                <div class="flex items-center justify-center gap-2">
                    <button class="btn btn-red" @click="deleteProject()" data-modal-close="deleteModal">Delete</button>
                    <button data-modal-close="deleteModal" class="btn link link-primary">Cancel</button>
                </div>
            </div>
        </div>
    </div>
</div>
</div>
{{> partials/footer }}
</div><!--end-->


{{> partials/vendor-scripts }}

<script type="module" src="assets/js/apps/projects/list-view.init.js"></script>

<script type="module" src="assets/js/main.js"></script>

</body>
</html>