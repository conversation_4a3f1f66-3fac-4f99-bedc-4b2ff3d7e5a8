{{> partials/main }}

<head>

    {{> partials/title-meta title="Music" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}

<div class="absolute inset-0 bg-gradient-to-br from-pink-500/15 via-yellow-500/15 to-green-500/15"></div>

<div class="relative" x-data="audioPlayer()">
    {{> partials/page-heading title="Music" sub-title="Dashboards" }}

    <div class="grid grid-cols-12 gap-x-space">
        <div class="col-span-12 border-0 ltr:bg-gradient-to-r rtl:bg-gradient-to-l from-sky-400 via-purple-300 card to-pink-400">
            <div class="grid items-center grid-cols-12 px-10">
                <div class="col-span-12 md:col-span-8 lg:col-span-7 xl:col-span-9">
                    <div class="lg:max-w-[400px] xl:max-w-[500px] py-10">
                        <h1 class="mb-3 text-white">Dream Top <span class="text-pink-400">10</span> Tracks</h1>
                        <p class="mb-6 text-primary-50">On March 24, 2025, Dream released his second song, entitled "Mask", which garnered over 24.7 million views on YouTube.</p>
                        <button type="button" class="text-white rounded-lg rounded-tl-3xl rounded-br-3xl py-2.5 hover:-translate-y-1.5 -tracking-tighter btn bg-white/20 backdrop-blur-xl"><i data-lucide="play" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> Play All</button>
                    </div>
                </div>
                <div class="col-span-12 md:col-span-4 lg:col-span-5 xl:col-span-3">
                    <div class="mt-6 lg:-mt-8">
                        <img src="assets/images/dashboards/music/main.png" loading="lazy" alt="">
                    </div>
                </div>
            </div>
        </div><!--end col-->
        <div class="col-span-12">
            <div class="mb-space">
                <div class="flex items-center mb-5">
                    <h6 class="text-15 grow">Weekly Top Tracks</h6>
                    <a href="#!" class="btn btn-primary"><i class="ri-add-circle-line ltr:mr-1 rtl:ml-1"></i> Add Track</a>
                </div>

                <div class="grid grid-cols-3 lg:grid-cols-6 2xl:grid-cols-9 gap-space">
                    <div class="relative">
                        <img src="assets/images/dashboards/music/img-01.jpg" loading="lazy" alt="" class="rounded-xl">
                        <div class="mt-3 text-center">
                            <h6 class="mb-1"><a href="#!" class="before:absolute before:inset-0">Urban Music</a></h6>
                            <p class="text-gray-500 dark:text-dark-500">EDM</p>
                        </div>
                    </div>
                    <div class="relative">
                        <img src="assets/images/dashboards/music/img-02.jpg" loading="lazy" alt="" class="rounded-xl">
                        <div class="mt-3 text-center">
                            <h6 class="mb-1"><a href="#!" class="before:absolute before:inset-0">Feel The Music</a></h6>
                            <p class="text-gray-500 dark:text-dark-500">Love</p>
                        </div>
                    </div>
                    <div class="relative">
                        <img src="assets/images/dashboards/music/img-03.jpg" loading="lazy" alt="" class="rounded-xl">
                        <div class="mt-3 text-center">
                            <h6 class="mb-1"><a href="#!" class="before:absolute before:inset-0">Make IT Real</a></h6>
                            <p class="text-gray-500 dark:text-dark-500">Remix</p>
                        </div>
                    </div>
                    <div class="relative">
                        <img src="assets/images/dashboards/music/img-04.jpg" loading="lazy" alt="" class="rounded-xl">
                        <div class="mt-3 text-center">
                            <h6 class="mb-1"><a href="#!" class="before:absolute before:inset-0">Music Fast</a></h6>
                            <p class="text-gray-500 dark:text-dark-500">DJ Remix</p>
                        </div>
                    </div>
                    <div class="relative">
                        <img src="assets/images/dashboards/music/img-05.jpg" loading="lazy" alt="" class="rounded-xl">
                        <div class="mt-3 text-center">
                            <h6 class="mb-1"><a href="#!" class="before:absolute before:inset-0">Harmony Wave</a></h6>
                            <p class="text-gray-500 dark:text-dark-500">Events</p>
                        </div>
                    </div>
                    <div class="relative">
                        <img src="assets/images/dashboards/music/img-06.jpg" loading="lazy" alt="" class="rounded-xl">
                        <div class="mt-3 text-center">
                            <h6 class="mb-1"><a href="#!" class="before:absolute before:inset-0">Old is A Gold</a></h6>
                            <p class="text-gray-500 dark:text-dark-500">Jazz</p>
                        </div>
                    </div>
                    <div class="relative">
                        <img src="assets/images/dashboards/music/img-07.jpg" loading="lazy" alt="" class="rounded-xl">
                        <div class="mt-3 text-center">
                            <h6 class="mb-1"><a href="#!" class="before:absolute before:inset-0">Electro Music</a></h6>
                            <p class="text-gray-500 dark:text-dark-500">Live Stream</p>
                        </div>
                    </div>
                    <div class="relative">
                        <img src="assets/images/dashboards/music/img-06.jpg" loading="lazy" alt="" class="rounded-xl">
                        <div class="mt-3 text-center">
                            <h6 class="mb-1"><a href="#!" class="before:absolute before:inset-0">Jazz Concept</a></h6>
                            <p class="text-gray-500 dark:text-dark-500">Concept</p>
                        </div>
                    </div>
                    <div class="relative">
                        <img src="assets/images/dashboards/music/img-07.jpg" loading="lazy" alt="" class="rounded-xl">
                        <div class="mt-3 text-center">
                            <h6 class="mb-1"><a href="#!" class="before:absolute before:inset-0">Electro Music</a></h6>
                            <p class="text-gray-500 dark:text-dark-500">Live Stream</p>
                        </div>
                    </div>
                </div>
            </div>
        </div><!--end col-->
        <div class="col-span-12 row-span-2 md:col-span-6 2xl:col-span-4">
            <div class="mb-space">
                <h6 class="mb-4 text-15">Featured Songs</h6>
                <div class="space-y-3">
                    <template x-for="song in featuredSongs" :key="song.musicID">
                        <div class="flex items-center gap-3">
                            <div class="relative group/items">
                                <img :src="song.image" alt="Banner" loading="lazy" title="Song Banner" class="rounded-md size-12">
                                <a href="#!" title="Song Play" @click="setCurrentSong(song)" class="absolute inset-0 flex items-center justify-center text-white transition duration-300 ease-linear rounded-md opacity-0 group-hover/items:opacity-100 bg-gray-950/30">
                                    <i data-lucide="play" class="size-5"></i>
                                </a>
                            </div>
                            <div class="grow">
                                <h6 class="mb-1.5"><a href="#!" x-text="song.title">Love Me Like You Do</a></h6>
                                <div class="flex ">
                                    <a href="#!" class="flex px-2 link hover:underline link-purple" x-text="song.artist">Ellie Goulding</a>
                                    <i class="ml-1 ri-time-line"></i>
                                    <p class="px-1 text-gray-500 dark:text-dark-500" x-text="song.duration"> 4:12 min</p>
                                </div>
                            </div>
                            <div class="shrink-0">
                                <a href="#!" title="Song Play" @click="togglePlaySongs(song)" class="text-gray-500 dark:text-dark-500 hover:bg-white dark:hover:bg-dark-900 btn hover:shadow-lg hover:shadow-gray-200 dark:hover:shadow-dark-850 btn-icon">
                                    <i :class="isPlaying && currentSong.musicID === song.musicID ? 'ri-pause-large-line' : 'ri-play-large-line'" style="font-size: 21px;"></i>
                                </a>
                            </div>
                        </div>
                    </template>
                </div>
            </div>
        </div>
        <div class="col-span-12 row-span-2 md:col-span-6 2xl:col-span-4">
            <div class="mb-space">
                <h6 class="mb-4 text-15">Popular Singers</h6>
                <div class="space-y-3">
                    <template x-for="singer in popularSingers" :key="singer.singerID">
                        <div class="flex items-center gap-3">
                            <div class="relative group/items shrink-0">
                                <img :src="singer.image" loading="lazy" alt="singer" title="singer Images" class="rounded-md size-11">
                                <a href="#!" title="Play btn" class="absolute inset-0 flex items-center justify-center text-white transition duration-300 ease-linear rounded-md opacity-0 group-hover/items:opacity-100 bg-gray-950/30">
                                    <i data-lucide="play" class="size-5"></i>
                                </a>
                            </div>
                            <div class="grow">
                                <h6 class="mb-1"><a href="#!" x-text="singer.name">Ariana Grande</a></h6>
                                <p class="text-gray-500 dark:text-dark-500 line-clamp-1" x-text="singer.description">A well-known pop singer with many hit albums, singles, and awards. She has a large fan base and connects with her followers on social media.</p>
                            </div>
                        </div>
                    </template>
                </div>
            </div>
        </div>
        <div class="col-span-12 xl:col-span-6 2xl:col-span-4 mb-space">
            <h6 class="mb-4 text-15">Monthly Top Artists</h6>
            <div data-simplebar class="pb-3">
                <div class="flex gap-3 *:shrink-0">
                    <a href="#!" title="Artists Images" class="relative inline-block" x-data="{ tooltip: 'Shakira' }">
                        <img src="assets/images/avatar/user-9.png" loading="lazy" alt="" class="border-4 rounded-full shadow-lg border-white/10 shadow-gray-200 dark:shadow-dark-850 size-14" x-tooltip="tooltip">
                    </a>
                    <a href="#!" title="Artists Images" class="relative inline-block" x-data="{ tooltip: 'Katy Perry' }">
                        <img src="assets/images/avatar/user-10.png" loading="lazy" alt="" class="border-4 rounded-full shadow-lg border-white/10 shadow-gray-200 dark:shadow-dark-850 size-14" x-tooltip="tooltip">
                    </a>
                    <a href="#!" title="Artists Images" class="relative inline-block" x-data="{ tooltip: 'Harry Styles' }">
                        <img src="assets/images/avatar/user-11.png" loading="lazy" alt="" class="border-4 rounded-full shadow-lg border-white/10 shadow-gray-200 dark:shadow-dark-850 size-14" x-tooltip="tooltip">
                    </a>
                    <a href="#!" title="Artists Images" class="relative inline-block" x-data="{ tooltip: 'Rihanna' }">
                        <img src="assets/images/avatar/user-13.png" loading="lazy" alt="" class="border-4 rounded-full shadow-lg border-white/10 shadow-gray-200 dark:shadow-dark-850 size-14" x-tooltip="tooltip">
                    </a>
                    <a href="#!" title="Artists Images" class="relative inline-block" x-data="{ tooltip: 'Michael Jackson' }">
                        <img src="assets/images/avatar/user-14.png" loading="lazy" alt="" class="border-4 rounded-full shadow-lg border-white/10 shadow-gray-200 dark:shadow-dark-850 size-14" x-tooltip="tooltip">
                    </a>
                    <a href="#!" title="Artists Images" class="relative inline-block" x-data="{ tooltip: 'Alicia Keys' }">
                        <img src="assets/images/avatar/user-15.png" loading="lazy" alt="" class="border-4 rounded-full shadow-lg border-white/10 shadow-gray-200 dark:shadow-dark-850 size-14" x-tooltip="tooltip">
                    </a>
                    <a href="#!" title="Artists Images" class="relative inline-block" x-data="{ tooltip: 'Celine Dion' }">
                        <img src="assets/images/avatar/user-16.png" loading="lazy" alt="" class="border-4 rounded-full shadow-lg border-white/10 shadow-gray-200 dark:shadow-dark-850 size-14" x-tooltip="tooltip">
                    </a>
                    <a href="#!" title="Artists Images" class="relative inline-block" x-data="{ tooltip: 'Britney Spears' }">
                        <img src="assets/images/avatar/user-17.png" loading="lazy" alt="" class="border-4 rounded-full shadow-lg border-white/10 shadow-gray-200 dark:shadow-dark-850 size-14" x-tooltip="tooltip">
                    </a>
                    <a href="#!" title="Artists Images" class="relative inline-block" x-data="{ tooltip: 'Bob Dylan' }">
                        <img src="assets/images/avatar/user-3.png" loading="lazy" alt="" class="border-4 rounded-full shadow-lg border-white/10 shadow-gray-200 dark:shadow-dark-850 size-14" x-tooltip="tooltip">
                    </a>
                </div>
            </div>
        </div>
        <div class="relative col-span-12 xl:col-span-6 2xl:col-span-4 card ltr:bg-gradient-to-tr rtl:bg-gradient-to-tl from-sky-500/15 via-purple-500/15 to-primary-500/15">
            <img src="assets/images/dashboards/music/upgrade.png" loading="lazy" alt="" class="absolute top-0 opacity-75 ltr:right-5 rtl:left-5">
            <div class="relative card-body">
                <div class="max-w-52">
                    <h5 class="mb-2 capitalize">Check the power Of Domiex</h5>
                    <p class="mb-3 text-gray-500 dark:text-dark-500">Immerse yourself in a world where music comes alive.</p>
                </div>
                <button type="button" class="btn btn-primary">Upgrade Now</button>
            </div>
        </div>
    </div><!--end grid-->
    <div class="fixed ltr:right-0 rtl:left-0 ltr:left-sidebar rtl:right-sidebar group-data-[sidebar=small]:ltr:left-sidebar-small group-data-[sidebar=small]:rtl:right-sidebar-small group-data-[sidebar=medium]:ltr:left-sidebar-medium group-data-[sidebar=medium]:rtl:right-sidebar-medium bottom-0 bg-white/30 dark:bg-dark-900/50 backdrop-blur-lg border-t z-[1020] p-3 border-gray-200 dark:border-dark-800">
        <div class="flex items-center gap-3">
            <img :src="currentSong.image" loading="lazy" alt="Music" class="rounded-md size-10 shrink-0">
            <div class="w-64 shrink-0">
                <h6 class="mb-1" x-text="currentSong.title"></h6>
                <p class="text-gray-500 dark:text-dark-300" x-text="currentSong.artist"></p>
            </div>
            <div class="flex items-center gap-2 mx-auto grow">
                <audio x-ref="audio" src="assets/images/dashboards/music/music.mp3" @timeupdate="updateTime" @loadedmetadata="updateDuration" preload="metadata" controls class="hidden"></audio>
                <a href="#!" title="Skip Icon" @click="skip(-10)" class="flex items-center justify-center text-xl size-10 link hover:text-gray-800 dark:hover:text-dark-50">
                    <i class="ri-skip-back-line"></i>
                </a>
                <a href="#!" title="Play Icon" @click="togglePlay" class="flex items-center justify-center text-xl size-10 link hover:text-gray-800 dark:hover:text-dark-50">
                    <i :class="isPlaying ? 'ri-pause-line' : 'ri-play-line'"></i>
                </a>
                <a href="#!" title="Skip Forward Icon" @click="skip(10)" class="flex items-center justify-center text-xl size-10 link hover:text-gray-800 dark:hover:text-dark-50">
                    <i class="ri-skip-forward-line"></i>
                </a>
                <div class="grow">
                    <div class="relative flex items-center gap-3">
                        <div class="w-full h-2 overflow-hidden bg-gray-200 rounded-full cursor-pointer dark:bg-dark-800" @click="seek($event)">
                            <div class="h-full bg-primary-500" :style="{ width: (currentTime / duration) * 100 + '%' }"></div>
                        </div>
                        <div class="flex justify-end text-sm">
                            <span x-text="formatTime(currentTime)"></span>
                            <span class="mx-1">/</span>
                            <span x-text="formatTime(duration)"></span>
                        </div>
                    </div>
                </div>
                <div class="flex items-center gap-2">
                    <a href="#!" title="Muted Icon" @click="toggleMute" class="flex items-center justify-center text-xl size-10 link hover:text-gray-800 dark:hover:text-dark-50">
                        <i :class="isMuted ? 'ri-volume-mute-line text-red-500' : 'ri-volume-up-line'"></i>
                    </a>
                    <input type="range" aria-label="volume Range Input" class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-dark-700" min="0" max="100" step="1" x-model="volume" @input="updateVolume" class="w-24">
                </div>
            </div>
            <div class="shrink-0">
                <a href="#!" title="shuffle Icon"><i data-lucide="shuffle" class="size-5"></i></a>
            </div>
        </div>
    </div>
</div>

</div>
{{> partials/footer }}
</div>

{{> partials/vendor-scripts }}

<script type="module" src="assets/js/dashboards/music.init.js"></script>

<script type="module" src="assets/js/main.js"></script>

</body>
</html>