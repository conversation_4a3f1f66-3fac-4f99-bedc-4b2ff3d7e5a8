{{> partials/main }}

<head>

    {{> partials/title-meta title="Mask Input" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}

{{> partials/page-heading title="Mask Input" sub-title="UI Advanced" }}

<div class="grid grid-cols-12 gap-x-space">
    <div class="col-span-12 lg:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Date Mask</h6>
        </div>
        <div class="card-body">
            <input x-mask="99/99/9999" placeholder="MM/DD/YYYY" class="form-input">
        </div>
    </div><!--end col-->
    <div class="col-span-12 lg:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Dynamic Masks</h6>
        </div>
        <div class="card-body">
            <input class="form-input" x-mask:dynamic="$input.startsWith('34') || $input.startsWith('37') ? '9999 999999 99999' : '9999 9999 9999 9999'" placeholder="0000 0000 0000 0000">
        </div>
    </div><!--end col-->
    <div class="col-span-12 lg:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Pin Code Masks</h6>
        </div>
        <div class="card-body">
            <input class="form-input" x-mask:dynamic="$input.startsWith('34') || $input.startsWith('37') ? '9999 999999 99999' : '9999'" placeholder="0000">
        </div>
    </div><!--end col-->
    <div class="col-span-12 lg:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Phone Number Masks</h6>
        </div>
        <div class="card-body">
            <input class="form-input" x-mask:dynamic="$input.startsWith('34') || $input.startsWith('37') ? '9999 999999 99999' : '999 99999 999'" placeholder="000 0000 000">
        </div>
    </div><!--end col-->
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Money Inputs</h6>
        </div>
        <div class="card-body">
            <div class="grid grid-cols-1 xl:grid-cols-2 gap-space">
                <div>
                    <label for="moneyInput1" class="form-label">Here is a fully functioning money input mask:</label>
                    <input id="moneyInput1" class="form-input" x-mask:dynamic="$money($input)" value="99999" placeholder="0">
                </div>
                <div>
                    <label for="moneyInput2" class="form-label">If you wish to swap the periods for commas and vice versa (as is required in certain currencies), you can do so using the second optional parameter:</label>
                    <input class="form-input" id="moneyInput2" x-mask:dynamic="$money($input, ',')" value="12000.69" placeholder="0.0000.00">
                </div>
                <div>
                    <label for="moneyInput3" class="form-label">You may also choose to override the thousands separator by supplying a third optional argument:</label>
                    <input class="form-input" id="moneyInput3" x-mask:dynamic="$money($input, '.', ' ')" value="99999.69" placeholder="0.00">
                </div>
                <div>
                    <label for="moneyInput4" class="form-label">You can also override the default precision of 2 digits by using any desired number of digits as the fourth optional argument:</label>
                    <input class="form-input" id="moneyInput4" x-mask:dynamic="$money($input, '.', ',', 4)" value="12000.6911" placeholder="00,000.0000">
                </div>
            </div>
        </div>
    </div><!--end col-->
</div><!--end grid-->


</div>
{{> partials/footer }}
</div>

{{> partials/vendor-scripts }}

<script type="module" src="assets/js/main.js"></script>

<script type="module" src="assets/js/ui/advanced-mask.init.js"></script>

</body>
</html>