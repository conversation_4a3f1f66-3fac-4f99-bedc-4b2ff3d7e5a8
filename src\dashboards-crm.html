{{> partials/main }}

<head>

    {{> partials/title-meta title="CRM" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}
{{> partials/page-heading title="CRM" sub-title="Dashboards" }}

<div class="grid grid-cols-12 gap-x-space">
    <div class="col-span-12">
        <div class="mb-space" dir="ltr">
            <div>
                <h6 class="mb-1">Sales Analytics</h6>
                <p class="text-gray-500 dark:text-dark-500">Unlocking Insights and Driving Growth Through Data-Driven Sales Strategies.</p>
            </div>
            <div x-data="salesAnalyticsApp" class="mt-5 lg:-mt-8" dir="ltr">
                <div class="!min-h-full" data-chart-colors="[bg-primary-500, bg-pink-300, bg-sky-300, bg-slate-600]" x-ref="salesAnalyticsChart"></div>
            </div>
        </div>
    </div><!--end Sales Analytics-->
    <div class="col-span-12 card bg-[url('../images/dashboards/dashboard-patterm.png')] dark:bg-none">
        <div class="grid grid-cols-12 gap-0">
            <div class="col-span-12 border-b border-gray-200 dark:border-dark-800 md:col-span-6 xl:col-span-3 md:ltr:border-r md:rtl:border-l">
                <div class="card-body">
                    <div class="flex items-center gap-3 mb-8">
                        <p class="text-gray-500 dark:text-dark-500 grow"><i data-lucide="users-round" class="inline-block size-4"></i> Customers Visits</p>
                        <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown shrink-0">
                            <button x-ref="button" x-on:click="toggle()" title="dropdown-button" :aria-expanded="open.toString()" type="button" class="flex items-center text-gray-500 dark:text-dark-500">
                                <i data-lucide="ellipsis" class="size-5"></i>
                            </button>
                            <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden" dropdown-position="right">
                                <ul>
                                    <li>
                                        <a href="#!" class="dropdown-item">
                                            <span>Weekly</span>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="#!" class="dropdown-item">
                                            <span>Monthly</span>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="#!" class="dropdown-item">
                                            <span>Yearly</span>
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center gap-3">
                        <h5 class="grow"><span x-data="animatedCounter(345121, 500, 0)" x-init="updateCounter" x-text="Math.round(current)"></span></h5>
                        <p class="text-gray-500 dark:text-dark-500 shrink-0"><span class="badge badge-green"><i data-lucide="trending-up" class="inline-block size-4"></i> 17.9%</span></p>
                    </div>
                </div>
            </div><!--end col-->
            <div class="col-span-12 border-b border-gray-200 dark:border-dark-800 md:col-span-6 xl:col-span-3 xl:ltr:border-r xl:rtl:border-l">
                <div class="card-body">
                    <div class="flex items-center gap-3 mb-8">
                        <p class="text-gray-500 dark:text-dark-500 grow"><i data-lucide="feather" class="inline-block size-4"></i> Impressions</p>
                        <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown shrink-0">
                            <button x-ref="button" x-on:click="toggle()" title="dropdown-button" :aria-expanded="open.toString()" type="button" class="flex items-center text-gray-500 dark:text-dark-500">
                                <i data-lucide="ellipsis" class="size-5"></i>
                            </button>
                            <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden" dropdown-position="right">
                                <ul>
                                    <li>
                                        <a href="#!" class="dropdown-item">
                                            <span>Weekly</span>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="#!" class="dropdown-item">
                                            <span>Monthly</span>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="#!" class="dropdown-item">
                                            <span>Yearly</span>
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center gap-3">
                        <h5 class="grow"><span x-data="animatedCounter(516871, 500, 0)" x-init="updateCounter" x-text="Math.round(current)"></span></h5>
                        <p class="text-gray-500 dark:text-dark-500 shrink-0"><span class="badge badge-green"><i data-lucide="trending-up" class="inline-block size-4"></i> 23.7%</span></p>
                    </div>
                </div>
            </div><!--end col-->
            <div class="col-span-12 border-b border-gray-200 dark:border-dark-800 md:col-span-6 xl:col-span-3 md:ltr:border-r md:rtl:border-l">
                <div class="card-body">
                    <div class="flex items-center gap-3 mb-8">
                        <p class="text-gray-500 dark:text-dark-500 grow"><i data-lucide="shuffle" class="inline-block size-4"></i> Total Orders</p>
                        <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown shrink-0">
                            <button x-ref="button" x-on:click="toggle()" title="dropdown-button" :aria-expanded="open.toString()" type="button" class="flex items-center text-gray-500 dark:text-dark-500">
                                <i data-lucide="ellipsis" class="size-5"></i>
                            </button>
                            <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden" dropdown-position="right">
                                <ul>
                                    <li>
                                        <a href="#!" class="dropdown-item">
                                            <span>Weekly</span>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="#!" class="dropdown-item">
                                            <span>Monthly</span>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="#!" class="dropdown-item">
                                            <span>Yearly</span>
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center gap-3">
                        <h5 class="grow"><span x-data="animatedCounter(14596, 500, 0)" x-init="updateCounter" x-text="Math.round(current)"></span></h5>
                        <p class="text-gray-500 dark:text-dark-500 shrink-0"><span class="badge badge-red"><i data-lucide="trending-down" class="inline-block size-4"></i> 1.6%</span></p>
                    </div>
                </div>
            </div><!--end col-->
            <div class="col-span-12 border-b border-gray-200 dark:border-dark-800 md:col-span-6 xl:col-span-3">
                <div class="card-body">
                    <div class="flex items-center gap-3 mb-8">
                        <p class="text-gray-500 dark:text-dark-500 grow"><i data-lucide="sparkles" class="inline-block size-4"></i> Wishlists</p>
                        <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown shrink-0">
                            <button x-ref="button" x-on:click="toggle()" title="dropdown-button" :aria-expanded="open.toString()" type="button" class="flex items-center text-gray-500 dark:text-dark-500">
                                <i data-lucide="ellipsis" class="size-5"></i>
                            </button>
                            <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden" dropdown-position="right">
                                <ul>
                                    <li>
                                        <a href="#!" class="dropdown-item">
                                            <span>Weekly</span>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="#!" class="dropdown-item">
                                            <span>Monthly</span>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="#!" class="dropdown-item">
                                            <span>Yearly</span>
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center gap-3">
                        <h5 class="grow"><span x-data="animatedCounter(102450, 500, 0)" x-init="updateCounter" x-text="Math.round(current)"></span></h5>
                        <p class="text-gray-500 dark:text-dark-500 shrink-0"><span class="badge badge-green"><i data-lucide="trending-up" class="inline-block size-4"></i> 3.2%</span></p>
                    </div>
                </div>
            </div><!--end col-->
            <div class="col-span-12 border-b border-gray-200 dark:border-dark-800 md:col-span-6 xl:col-span-3 xl:border-b-0 md:ltr:border-r md:rtl:border-l">
                <div class="card-body">
                    <div class="flex items-center gap-3 mb-8">
                        <p class="text-gray-500 dark:text-dark-500 grow"><i data-lucide="hand-coins" class="inline-block size-4"></i> Total Earning</p>
                        <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown shrink-0">
                            <button x-ref="button" x-on:click="toggle()" title="dropdown-button" :aria-expanded="open.toString()" type="button" class="flex items-center text-gray-500 dark:text-dark-500">
                                <i data-lucide="ellipsis" class="size-5"></i>
                            </button>
                            <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden" dropdown-position="right">
                                <ul>
                                    <li>
                                        <a href="#!" class="dropdown-item">
                                            <span>Weekly</span>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="#!" class="dropdown-item">
                                            <span>Monthly</span>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="#!" class="dropdown-item">
                                            <span>Yearly</span>
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center gap-3">
                        <h5 class="grow">$<span x-data="animatedCounter(316, 500, 0)" x-init="updateCounter" x-text="Math.round(current)"></span>M</h5>
                        <p class="text-gray-500 dark:text-dark-500 shrink-0"><span class="badge badge-green"><i data-lucide="trending-up" class="inline-block size-4"></i> 16.1%</span></p>
                    </div>
                </div>
            </div><!--end col-->
            <div class="col-span-12 border-b border-gray-200 dark:border-dark-800 md:col-span-6 xl:border-b-0 xl:col-span-3 xl:ltr:border-r xl:rtl:border-l">
                <div class="card-body">
                    <div class="flex items-center gap-3 mb-8">
                        <p class="text-gray-500 dark:text-dark-500 grow"><i data-lucide="coins" class="inline-block size-4"></i> Total Profit</p>
                        <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown shrink-0">
                            <button x-ref="button" x-on:click="toggle()" title="dropdown-button" :aria-expanded="open.toString()" type="button" class="flex items-center text-gray-500 dark:text-dark-500">
                                <i data-lucide="ellipsis" class="size-5"></i>
                            </button>
                            <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden" dropdown-position="right">
                                <ul>
                                    <li>
                                        <a href="#!" class="dropdown-item">
                                            <span>Weekly</span>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="#!" class="dropdown-item">
                                            <span>Monthly</span>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="#!" class="dropdown-item">
                                            <span>Yearly</span>
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center gap-3">
                        <h5 class="grow">$<span x-data="animatedCounter(287, 500, 0)" x-init="updateCounter" x-text="Math.round(current)"></span>M</h5>
                        <p class="text-gray-500 dark:text-dark-500 shrink-0"><span class="badge badge-green"><i data-lucide="trending-up" class="inline-block size-4"></i> 9.7%</span></p>
                    </div>
                </div>
            </div><!--end col-->
            <div class="col-span-12 border-b border-gray-200 dark:border-dark-800 md:col-span-6 xl:col-span-3 md:border-b-0 md:ltr:border-r md:rtl:border-l">
                <div class="card-body">
                    <div class="flex items-center gap-3 mb-8">
                        <p class="text-gray-500 dark:text-dark-500 grow"><i data-lucide="coins" class="inline-block size-4"></i> Repeat Orders</p>
                        <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown shrink-0">
                            <button x-ref="button" x-on:click="toggle()" title="dropdown-button" :aria-expanded="open.toString()" type="button" class="flex items-center text-gray-500 dark:text-dark-500">
                                <i data-lucide="ellipsis" class="size-5"></i>
                            </button>
                            <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden" dropdown-position="right">
                                <ul>
                                    <li>
                                        <a href="#!" class="dropdown-item">
                                            <span>Weekly</span>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="#!" class="dropdown-item">
                                            <span>Monthly</span>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="#!" class="dropdown-item">
                                            <span>Yearly</span>
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center gap-3">
                        <h5 class="grow"><span x-data="animatedCounter(53629, 500, 0)" x-init="updateCounter" x-text="Math.round(current)"></span></h5>
                        <p class="text-gray-500 dark:text-dark-500 shrink-0"><span class="badge badge-green"><i data-lucide="trending-up" class="inline-block size-4"></i> 9.7%</span></p>
                    </div>
                </div>
            </div><!--end col-->
            <div class="col-span-12 md:col-span-6 xl:col-span-3">
                <div class="card-body">
                    <div class="flex items-center gap-3 mb-8">
                        <p class="text-gray-500 dark:text-dark-500 grow"><i data-lucide="square-mouse-pointer" class="inline-block size-4"></i> Conversion Rate</p>
                        <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown shrink-0">
                            <button x-ref="button" x-on:click="toggle()" title="dropdown-button" :aria-expanded="open.toString()" type="button" class="flex items-center text-gray-500 dark:text-dark-500">
                                <i data-lucide="ellipsis" class="size-5"></i>
                            </button>
                            <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden" dropdown-position="right">
                                <ul>
                                    <li>
                                        <a href="#!" class="dropdown-item">
                                            <span>Weekly</span>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="#!" class="dropdown-item">
                                            <span>Monthly</span>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="#!" class="dropdown-item">
                                            <span>Yearly</span>
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center gap-3">
                        <h5 class="grow"><span x-data="animatedCounter(70, 500, 0)" x-init="updateCounter" x-text="Math.round(current)"></span>%</h5>
                        <p class="text-gray-500 dark:text-dark-500 shrink-0"><span class="badge badge-red"><i data-lucide="trending-down" class="inline-block size-4"></i> 0.8%</span></p>
                    </div>
                </div>
            </div><!--end col-->
        </div><!--end grid-->
    </div><!--end col-->
    <div class="col-span-12 2xl:col-span-5 card">
        <div class="flex items-center gap-3 card-header">
            <h6 class="card-title grow">Deal Revenue Forecast</h6>
            <a href="#!" class="link link-primary shrink-0">View All <i class="align-baseline ri-arrow-right-line"></i></a>
        </div>
        <div class="card-body">
            <div class="grid grid-cols-12 gap-x-space">
                <div class="col-span-12 md:col-span-5">
                    <div x-data="basicRadialbarApp" dir="ltr">
                        <div class="!min-h-full" data-chart-colors="[bg-slate-600, bg-slate-100]" x-ref="basicRadialbarChart"></div>
                    </div>
                </div>
                <div class="col-span-12 md:col-span-7">
                    <h6 class="mb-2">Team Goal</h6>
                    <div class="space-y-4">
                        <div>
                            <div class="flex items-center gap-3 mb-2">
                                <h6 class="text-xs grow">Marketing</h6>
                                <h6 class="text-xs text-gray-500 dark:text-dark-500">$15,498/$80,000</h6>
                            </div>
                            <div class="progress-bar progress-1">
                                <div class="w-[26%] text-white progress-bar-wrap bg-sky-400"></div>
                            </div>
                        </div>
                        <div>
                            <div class="flex items-center gap-3 mb-2">
                                <h6 class="text-xs grow">Sales Revenue</h6>
                                <h6 class="text-xs text-gray-500 dark:text-dark-500">$44,000/$1,00,000</h6>
                            </div>
                            <div class="progress-bar progress-1">
                                <div class="w-[44%] text-white progress-bar-wrap bg-sky-400"></div>
                            </div>
                        </div>
                        <div>
                            <div class="flex items-center gap-3 mb-2">
                                <h6 class="text-xs grow">Ads Revenue</h6>
                                <h6 class="text-xs text-gray-500 dark:text-dark-500">$82,578/$1,50,000</h6>
                            </div>
                            <div class="progress-bar progress-1">
                                <div class="w-[67%] text-white progress-bar-wrap bg-sky-400"></div>
                            </div>
                        </div>
                        <div>
                            <div class="flex items-center gap-3 mb-2">
                                <h6 class="text-xs grow">Digital Marketing</h6>
                                <h6 class="text-xs text-gray-500 dark:text-dark-500">$1,57,000/$2,00,000</h6>
                            </div>
                            <div class="progress-bar progress-1">
                                <div class="w-[79%] text-white progress-bar-wrap bg-sky-400"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 xl:col-span-6 2xl:col-span-4 card">
        <div class="flex items-center gap-3 card-header">
            <h6 class="card-title grow">Deals Open</h6>
            <a href="#!" class="link link-primary shrink-0">View More <i data-lucide="chevron-right" class="inline-block ltr:ml-0.5 rtl:mr-0.5 size-4"></i></a>
        </div>
        <div class="card-body">
            <div data-simplebar class="xl:h-52 -mx-space px-space">
                <div class="gap-3 flex flex-col">
                    <div class="mb-0 card">
                        <div class="card-body">
                            <div class="flex flex-col gap-3 md:flex-row">
                                <div class="grow">
                                    <p class="mb-1 text-gray-500 dark:text-dark-500">Closing Date: 20 Jul, 2024</p>
                                    <h6><a href="#!">Financial Work History</a></h6>
                                    <div class="flex items-center gap-2 mt-3">
                                        <img src="assets/images/avatar/user-18.png" loading="lazy" alt="User Images" class="rounded-full size-6">
                                        <p>Donna Berlin</p>
                                    </div>
                                </div>
                                <div class="md:ltr:text-right md:rtl:text-left shrink-0">
                                    <h6 class="mb-1">$87,000</h6>
                                    <span class="badge badge-sub-primary">Contract sent</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="mb-0 card">
                        <div class="card-body">
                            <div class="flex flex-col gap-3 md:flex-row">
                                <div class="grow">
                                    <p class="mb-1 text-gray-500 dark:text-dark-500">Closing Date: 18 Jul, 2024</p>
                                    <h6><a href="#!">Domiex Admin Role</a></h6>
                                    <div class="flex items-center gap-2 mt-3">
                                        <img src="assets/images/avatar/user-11.png" loading="lazy" alt="" class="rounded-full size-6">
                                        <p>Willian Brim</p>
                                    </div>
                                </div>
                                <div class="md:ltr:text-right md:rtl:text-left shrink-0">
                                    <h6 class="mb-1">$49,599</h6>
                                    <span class="badge badge-sub-primary">Contract sent</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="mb-0 card">
                        <div class="card-body">
                            <div class="flex flex-col gap-3 md:flex-row">
                                <div class="grow">
                                    <p class="mb-1 text-gray-500 dark:text-dark-500">Closing Date: 10 Jul, 2024</p>
                                    <h6><a href="#!">API & Employee Statistic</a></h6>
                                    <div class="flex items-center gap-2 mt-3">
                                        <img src="assets/images/avatar/user-15.png" loading="lazy" alt="" class="rounded-full size-6">
                                        <p>Marla Ramos</p>
                                    </div>
                                </div>
                                <div class="md:ltr:text-right md:rtl:text-left shrink-0">
                                    <h6 class="mb-1">$34,999</h6>
                                    <span class="badge badge-sub-primary">Contract sent</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 xl:col-span-6 2xl:col-span-3 card">
        <div class="card-body">
            <p class="mb-3 font-medium text-primary-500">Upgrade to Premium</p>
            <h3 class="mb-2 capitalize">Make the best with the premium</h3>

            <div class="flex -space-x-2 grow rtl:space-x-reverse">
                <a href="#!" class="transition duration-300 ease-linear hover:z-10">
                    <img class="border-2 border-white rounded-full dark:border-dark-900 size-7" alt="User Images" loading="lazy" src="assets/images/avatar/user-17.png">
                </a>
                <a href="#!" class="transition duration-300 ease-linear hover:z-10">
                    <img class="border-2 border-white rounded-full dark:border-dark-900 size-7" alt="User Images" loading="lazy" src="assets/images/avatar/user-18.png">
                </a>
                <a href="#!" class="transition duration-300 ease-linear hover:z-10">
                    <img class="border-2 border-white rounded-full dark:border-dark-900 size-7" alt="User Images" loading="lazy" src="assets/images/avatar/user-14.png">
                </a>
            </div>

            <div class="flex mt-16 mb-3">
                <p class="text-gray-500 dark:text-dark-500 grow text-13">Pay Monthly</p>
                <h6 class="shrink-0">$19.99</h6>
            </div>
            <a href="pages-pricing.html" class="w-full border-gray-200 dark:border-dark-800 btn btn-outline-gray">Upgrade Now</a>
        </div>
    </div><!--end col-->
    <div class="col-span-12 card" x-data="leadsTable()">
        <div class="grid grid-cols-12 lg:items-center card-header gap-space">
            <div class="col-span-12 lg:col-span-3">
                <h6 class="card-title">Leads (154)</h6>
            </div>
            <div class="col-span-12 lg:col-start-7 lg:col-span-6 2xl:col-span-4 2xl:col-start-9">
                <div class="flex items-center gap-space">
                    <div class="relative group/form grow">
                        <input type="text" class="pl-9 form-input group-[&.right]/form:pr-9 group-[&.right]/form:pl-4" placeholder="Search for ..." x-model="searchTerm" @input="filteredLeads()">
                        <button title="search-button" class="absolute inset-y-0 flex items-center text-gray-500 dark:text-dark-500 left-3 group-[&.right]/form:right-3 group-[&.right]/form:left-auto focus:outline-hidden">
                            <i data-lucide="search" class="size-4"></i>
                        </button>
                    </div>
                    <button type="button" class="btn btn-primary shrink-0"><i data-lucide="circle-plus" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> Add Lead</button>
                </div>
            </div><!--end col-->
        </div>
        <div class="pt-0 card-body">
            <div class="overflow-x-auto table-box">
                <table class="table whitespace-nowrap">
                    <tbody>
                        <tr class="text-gray-500 bg-gray-100 dark:bg-dark-850 dark:text-dark-500">
                            <th x-on:click="sort('leadName')" class="cursor-pointer">Lead Name <span x-show="sortBy === 'leadName'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                            <th x-on:click="sort('rating')" class="cursor-pointer">Rating <span x-show="sortBy === 'rating'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                            <th x-on:click="sort('date')" class="cursor-pointer">Date <span x-show="sortBy === 'date'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                            <th x-on:click="sort('contact')" class="cursor-pointer">Contact <span x-show="sortBy === 'contact'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                            <th x-on:click="sort('leadSource')" class="cursor-pointer">Lead Source <span x-show="sortBy === 'leadSource'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                            <th x-on:click="sort('leadStatus')" class="cursor-pointer">Lead Status <span x-show="sortBy === 'leadStatus'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                            <th x-on:click="sort('totalBalance')" class="cursor-pointer">Total Balance <span x-show="sortBy === 'totalBalance'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                        </tr>
                        <template x-if="displayedLeads.length > 0">
                            <template x-for="(lead, index) in displayedLeads" :key="index">
                                <tr>
                                    <td x-text="lead.leadName"></td>
                                    <td><i class="text-yellow-500 align-baseline ri-star-fill"></i> (<span x-text="lead.rating"></span>)</td>
                                    <td x-text="lead.date"></td>
                                    <td x-text="lead.contact"></td>
                                    <td><span class="badge badge-gray" x-text="lead.leadSource"></span></td>
                                    <td>
                                        <span x-text="lead.leadStatus" :class="{
                                                'badge badge-sky': lead.leadStatus === 'New',
                                                'badge badge-green': lead.leadStatus === 'Contacted',
                                                'badge badge-yellow': lead.leadStatus === 'Interested',
                                                'badge badge-red': lead.leadStatus === 'Closed',
                                                'badge badge-purple': lead.leadStatus === 'Negotiation'
                                            }"></span>
                                    </td>
                                    <td x-text="lead.totalBalance"></td>
                                </tr>
                            </template>
                        </template>
                        <tr>
                            <template x-if="displayedLeads.length == 0">
                                <td colspan="10" class="!p-8e">
                                    <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" class="mx-auto size-12" viewBox="0 0 48 48">
                                        <linearGradient id="SVGID_1__h35ynqzIJzH4_gr1" x1="34.598" x2="15.982" y1="15.982" y2="34.598" gradientUnits="userSpaceOnUse">
                                            <stop offset="0" stop-color="#60e8fe"></stop>
                                            <stop offset=".033" stop-color="#6ae9fe"></stop>
                                            <stop offset=".197" stop-color="#97f0fe"></stop>
                                            <stop offset=".362" stop-color="#bdf5ff"></stop>
                                            <stop offset=".525" stop-color="#dafaff"></stop>
                                            <stop offset=".687" stop-color="#eefdff"></stop>
                                            <stop offset=".846" stop-color="#fbfeff"></stop>
                                            <stop offset="1" stop-color="#fff"></stop>
                                        </linearGradient>
                                        <path fill="url(#SVGID_1__h35ynqzIJzH4_gr1)" d="M40.036,33.826L31.68,25.6c0.847-1.739,1.335-3.684,1.335-5.748c0-7.27-5.894-13.164-13.164-13.164	S6.688,12.582,6.688,19.852c0,7.27,5.894,13.164,13.164,13.164c2.056,0,3.995-0.485,5.728-1.326l3.914,4.015l4.331,4.331	c1.715,1.715,4.496,1.715,6.211,0C41.751,38.321,41.751,35.541,40.036,33.826z"></path>
                                        <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M31.95,25.739l8.086,8.086c1.715,1.715,1.715,4.496,0,6.211l0,0c-1.715,1.715-4.496,1.715-6.211,0	l-4.331-4.331"></path>
                                        <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M7.525,24.511c-1.771-4.694-0.767-10.196,3.011-13.975c3.847-3.847,9.48-4.817,14.228-2.912"></path>
                                        <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M30.856,12.603c3.376,5.114,2.814,12.063-1.688,16.565c-4.858,4.858-12.565,5.129-17.741,0.814"></path>
                                    </svg>
                                    <p class="mt-2 text-center text-gray-500 dark:text-dark-500">No matching records found</p>
                                </td>
                            </template>
                        </tr>
                    </tbody>
                </table>
            </div>
                <div class="grid items-center grid-cols-12 gap-space mt-space" x-show="displayedLeads.length > 0">
                    <div class="col-span-12 text-center lg:col-span-6 lg:ltr:text-left lg:rtl:text-right">
                        <p class="text-gray-500 dark:text-dark-500">Showing <b x-text="showingStart"></b> - <b x-text="showingEnd"></b> of <b x-text="filterLeads.length"></b> Results</p>
                    </div>
                    <div class="col-span-12 lg:col-span-6">
                        <div class="flex justify-center gap-2 lg:justify-end pagination pagination-primary">
                            <button @click="prevPage()" :disabled="currentPage === 1" class="pagination-pre">
                                <i data-lucide="chevron-left" class="mr-1 ltr:inline-block rtl:hidden size-4"></i>
                                <i data-lucide="chevron-right" class="ml-1 rtl:inline-block ltr:hidden size-4"></i>
                                Prev
                            </button>
                            <template x-for="page in totalPages" :key="page">
                                <button @click="gotoPage(page)" :class="{ 'active': currentPage === page }" class="pagination-item">
                                    <span x-text="page"></span>
                                </button>
                            </template>
                            <button @click="nextPage()" :disabled="currentPage === totalPages" class="pagination-next">
                                Next
                                <i data-lucide="chevron-right" class="ml-1 ltr:inline-block rtl:hidden size-4"></i>
                                <i data-lucide="chevron-left" class="mr-1 rtl:inline-block ltr:hidden size-4"></i>
                            </button>
                        </div>
                    </div>
                </div>
        </div>
    </div><!--end col-->
</div><!--end grid-->

</div>
{{> partials/footer }}
</div>

{{> partials/vendor-scripts }}

<script type="module" src="assets/js/dashboards/crm.init.js"></script>

<script type="module" src="assets/js/main.js"></script>

</body>
</html>