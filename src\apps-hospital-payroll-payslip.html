{{> partials/main }}

<head>

    {{> partials/title-meta title="Payslip" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}
        {{> partials/page-heading title="Payslip" sub-title="Payroll" }}

        <div class="grid grid-cols-12 mb-space">
            <div class="relative col-span-12 xl:col-span-8 xl:col-start-3 card print:col-span-12 print:border-0 print:shadow-none">
                <img src="assets/images/favicon.ico" alt="" class="absolute flex items-center justify-center -translate-x-1/2 -translate-y-1/2 top-1/2 left-1/2 size-64 opacity-15">
                <div class="card-body">
                    <div class="grid grid-cols-12 pb-4 mb-4 border-b border-gray-200 dark:border-dark-800">
                        <div class="col-span-12 md:col-span-5">
                            <div class="mb-5">
                                <img src="assets/images/main-logo.png" alt="" class="inline-block h-8 dark:hidden">
                                <img src="assets/images/logo-white.png" alt="" class="hidden h-8 dark:inline-block">
                            </div>
                            <h6 class="mb-1">Domiex - Multispeciality Hospital</h6>
                            <p class="mb-1 text-gray-500 dark:text-dark-500">1960 Squaw Valley Rd, Olympic Valley, California, United States - 96146</p>
                            <p class="text-gray-500 dark:text-dark-500"><a href="mailto:<EMAIL>"><EMAIL></a></p>
                        </div>
                    </div>

                    <h6 class="mb-3">Employee Information:</h6>
                    <div class="grid grid-cols-12 gap-2">
                        <div class="col-span-12 md:col-span-6">
                            <span class="text-gray-500 dark:text-dark-500">Employee ID:</span> PE-A14E0112
                        </div>
                        <div class="col-span-12 md:col-span-6">
                            <span class="text-gray-500 dark:text-dark-500">Employee Name:</span> Sophia Mia
                        </div>
                        <div class="col-span-12 md:col-span-6">
                            <span class="text-gray-500 dark:text-dark-500">Designation:</span> Accounts Specialist
                        </div>
                        <div class="col-span-12 md:col-span-6">
                            <span class="text-gray-500 dark:text-dark-500">Pay Period:</span> February
                        </div>
                        <div class="col-span-12 md:col-span-6">
                            <span class="text-gray-500 dark:text-dark-500">Department:</span> Finance
                        </div>
                        <div class="col-span-12 md:col-span-6">
                            <span class="text-gray-500 dark:text-dark-500">Month:</span> Nov - Dec, 2024
                        </div>
                        <div class="col-span-12 md:col-span-6">
                            <span class="text-gray-500 dark:text-dark-500">Worked Days:</span> 24 Days
                        </div>
                    </div>

                    <div class="grid grid-cols-12 gap-space">
                        <div class="col-span-12 md:col-span-6">
                            <div class="mt-5 overflow-x-auto">
                                <table class="table bordered">
                                    <tbody>
                                        <tr class="text-gray-500 bg-gray-100 dark:bg-dark-850 dark:text-dark-500">
                                            <th>Earnings</th>
                                            <th>Amount</th>
                                        </tr>
                                        <tr>
                                            <td>Basic Salary</td>
                                            <td>$17,500</td>
                                        </tr>
                                        <tr>
                                            <td>Incentive Pay</td>
                                            <td>$900</td>
                                        </tr>
                                        <tr>
                                            <td>Other Allowance</td>
                                            <td>$450</td>
                                        </tr>
                                        <tr>
                                            <td>Total Earnings</td>
                                            <td>$18,850</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="col-span-12 md:col-span-6">
                            <div class="mt-5 overflow-x-auto">
                                <table class="table bordered">
                                    <tbody>
                                        <tr class="text-gray-500 bg-gray-100 dark:bg-dark-850 dark:text-dark-500">
                                            <th>Deduction</th>
                                            <th>Amount</th>
                                        </tr>
                                        <tr>
                                            <td>Tax</td>
                                            <td>$700</td>
                                        </tr>
                                        <tr>
                                            <td>Health Insurance</td>
                                            <td>$1,100</td>
                                        </tr>
                                        <tr>
                                            <td>Absences (3)</td>
                                            <td>$1,245.22</td>
                                        </tr>
                                        <tr>
                                            <td>Total Deduction</td>
                                            <td>$3,045.22</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            
                            <div class="p-3 mt-3 text-center border border-gray-200 rounded-md dark:border-dark-800">
                                <h6>Net Pay: $15,804.78</h6>
                            </div>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-12 gap-2 mt-10">
                        <div class="col-span-12">
                            <p class="mb-2 text-gray-500 dark:text-dark-500">For any Queries, Please mail us at <span class="font-medium"><EMAIL></span></p>
                            <p class="mb-1 text-gray-500 dark:text-dark-500">Regards</p>
                            <p>SRBThemes</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-span-12 text-right xl:col-span-8 xl:col-start-3 print:hidden">
                <button type="button" @click="window.print()" class="btn btn-primary">Print Now</button>
            </div>
        </div>

    </div>
    {{> partials/footer }}
</div>


{{> partials/vendor-scripts }}

<script type="module" src="assets/js/main.js"></script>

</body>
</html>