{{> partials/main }}

<head>

    {{> partials/title-meta title="Account Billing & Plan" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}

<div class="relative mb-6">
    <div class="relative overflow-hidden rounded-md h-44 bg-primary-500/10">
        <div class="border-[60px] border-t-primary-500 border-l-primary-500 absolute opacity-10 -top-2 left-0 rotate-45 size-96"></div>
        <div class="border-[60px] border-green-500 absolute opacity-10 top-20 left-8 rotate-45 size-80"></div>
        <div class="border-[60px] border-pink-500 absolute opacity-10 top-36 left-28 rotate-45 size-40"></div>
    </div>
    <div class="text-center">
        <div class="relative inline-block mx-auto">
            <div class="relative p-1 rounded-full bg-gradient-to-tr from-primary-300 via-red-300 to-green-300 -mt-14">
                <img src="assets/images/avatar/user-17.png" alt="" class="mx-auto border-4 border-white rounded-full dark:border-dark-900 size-28">
            </div>
            <div class="absolute border-2 border-white dark:border-dark-900 rounded-full size-4 bg-green-500 bottom-2.5 ltr:right-2.5 rtl:left-2.5"></div>
        </div>
        <h5 class="mt-2 mb-1">Sophia Mia <i data-lucide="badge-check" class="inline-block text-primary-500 fill-primary-500/20 size-5"></i></h5>
        <ul class="flex flex-wrap items-center justify-center gap-2 text-gray-500 dark:text-dark-500 text-14">
            <li><i data-lucide="building-2" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-middle">UI / UX Designer</span></li>
            <li><i data-lucide="map-pin" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-middle">Argentina</span></li>
            <li><i data-lucide="calendar-days" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-middle">24 April, 2024</span></li>
        </ul>
    </div>
</div>

<ul class="pb-2 overflow-x-auto tabs-pills lg:pb-0">
    <li>
        <a href="pages-account-settings.html" class="nav-item text-gray-500 dark:text-dark-500 [&.active]:bg-primary-500 [&.active]:text-primary-50">
            <i data-lucide="user-round" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-middle whitespace-nowrap">Account</span>
        </a>
    </li>
    <li>
        <a href="pages-account-security.html" class="nav-item text-gray-500 dark:text-dark-500 [&.active]:bg-primary-500 [&.active]:text-primary-50">
            <i data-lucide="shield-check" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-middle whitespace-nowrap">Security</span>
        </a>
    </li>
    <li>
        <a href="pages-account-billing-plan.html" class="nav-item text-gray-500 dark:text-dark-500 [&.active]:bg-primary-500 [&.active]:text-primary-50 active">
            <i data-lucide="gem" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-middle whitespace-nowrap">Billing & Plans</span>
        </a>
    </li>
    <li>
        <a href="pages-account-notification.html" class="nav-item text-gray-500 dark:text-dark-500 [&.active]:bg-primary-500 [&.active]:text-primary-50">
            <i data-lucide="bell" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-middle whitespace-nowrap">Notification</span>
        </a>
    </li>
    <li>
        <a href="pages-account-statements.html" class="nav-item text-gray-500 dark:text-dark-500 [&.active]:bg-primary-500 [&.active]:text-primary-50">
            <i data-lucide="list-tree" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-middle whitespace-nowrap">Statements</span>
        </a>
    </li>
    <li>
        <a href="pages-account-logs.html" class="nav-item text-gray-500 dark:text-dark-500 [&.active]:bg-primary-500 [&.active]:text-primary-50">
            <i data-lucide="log-out" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-middle whitespace-nowrap">Logs</span>
        </a>
    </li>
</ul>

<div class="mt-4 card">
    <div class="card-header">
        <h6 class="mb-1 card-title">Billing Settings</h6>
        <p class="text-gray-500 dark:text-dark-500">Take control of your billing and plan details here.</p>
    </div>
    <div class="card-body">
        <div class="grid grid-cols-1 gap-5 xl:grid-cols-2">
            <div>
                <h6 class="mb-3">Current Plan:</h6>
                <div class="mb-0 card">
                    <div class="card-body">
                        <div class="flex items-center">
                            <div class="grow">
                                <h6 class="mb-2">Basic Plan <span class="align-middle ltr:ml-1 rtl:mr-1 whitespace-nowrap badge badge-red">Monthly</span></h6>
                                <p class="text-gray-500 dark:text-dark-500">Our most sought-after plan tailored for small teams.</p>
                            </div>
                            <h3>$20 <small class="text-sm font-normal text-gray-500 dark:text-dark-500">Per month</small></h3>
                        </div>

                        <div class="mt-4">
                            <div class="flex items-center gap-3 mb-2">
                                <h6 class="text-xs grow">16 of 30 Users</h6>
                                <h6 class="text-xs font-semibold text-sky-500">55.47%</h6>
                            </div>
                            <div class="progress-bar progress-1">
                                <div class="w-1/2 text-white progress-bar-wrap bg-primary-500"></div>
                            </div>
                        </div>
                        <div class="mt-5 text-right">
                            <a href="pages-pricing.html" class="btn btn-primary">
                                <span class="align-middle whitespace-nowrap">Upgrade Plan</span>
                                <i data-lucide="arrow-up-right" class="ltr:inline-block rtl:hidden size-4"></i>
                                <i data-lucide="arrow-up-left" class="ltr:hidden rtl:inline-block size-4"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div><!--end col-->
            <div>
                <h6 class="mb-3">Billing Information:</h6>
                <div class="mb-0 card">
                    <div class="card-body">
                        <div class="flex items-center">
                            <div class="grow">
                                <h6 class="mb-2">Sophia Mia</h6>
                                <p class="mb-1 text-gray-500 dark:text-dark-500">3011 E Semoran Blvd, Apopka, Florida,</p>
                                <p class="mb-1 text-gray-500 dark:text-dark-500">United States - 32703.</p>
                                <p class="text-gray-500 dark:text-dark-500">+(*************</p>
                            </div>
                        </div>
                        <div class="mt-5 ltr:text-right rtl:text-left">
                            <button data-modal-target="billingEditModal" type="button" class="btn btn-sub-gray"><i data-lucide="pencil" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-middle whitespace-nowrap">Edit Billing</span></button>
                        </div>
                    </div>
                </div>
            </div><!--end col-->
        </div><!--end grid-->
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h6 class="card-title">Payment Methods</h6>
    </div>
    <div class="card-body">
        <div x-data="{ defaultCard: 1 }" class="grid grid-cols-1 gap-5 sm:grid-cols-2 xl:grid-cols-4">
            <div class="mb-0 card">
                <div class="card-body bg-gradient-to-b from-primary-500/10 via-red-500/5 backdrop-lg">
                    <img src="assets/images/payment/visa.png" alt="" class="h-10">
                </div>
                <div class="pt-0 card-body">
                    <div class="flex items-center">
                        <div class="grow">
                            <h6 class="mb-1">xxxx xxxx xxx 1547</h6>
                            <p class="text-gray-500 dark:text-dark-500">Expiry on 01/2030</p>
                        </div>
                    </div>
                    <div class="flex items-center justify-between gap-3 mt-5">
                        <a href="#!" class="link link-green" :class="{ 'text-green-500': defaultCard === 1 }" @click.prevent="defaultCard === 1 ? defaultCard = null : defaultCard = 1">
                            <span x-text="defaultCard === 1 ? 'Default Set' : 'Set as Default'"></span>
                        </a>
                        <a href="#!" data-modal-target="addCardPaymentModal" class="link link-primary"><i data-lucide="pencil" class="inline-block size-4"></i> Edit</a>
                    </div>
                </div>
            </div><!--end col-->
            <div class="mb-0 card">
                <div class="card-body bg-gradient-to-b from-primary-500/10 via-red-500/5 backdrop-lg">
                    <img src="assets/images/payment/american.png" alt="" class="h-10">
                </div>
                <div class="pt-0 card-body">
                    <div class="flex items-center">
                        <div class="grow">
                            <h6 class="mb-1">xxxx xxxx xxx 8749</h6>
                            <p class="text-gray-500 dark:text-dark-500">Expiry on 24/2030</p>
                        </div>
                    </div>
                    <div class="flex items-center justify-between gap-3 mt-5">
                        <a href="#!" class="link link-green" :class="{ 'text-green-500': defaultCard === 2 }" @click.prevent="defaultCard === 2 ? defaultCard = null : defaultCard = 2">
                            <span x-text="defaultCard === 2 ? 'Default Set' : 'Set as Default'"></span>
                        </a>
                        <a href="#!" data-modal-target="addCardPaymentModal" class="link link-primary"><i data-lucide="pencil" class="inline-block size-4"></i> Edit</a>
                    </div>
                </div>
            </div><!--end col-->
            <div class="mb-0 card">
                <div class="card-body bg-gradient-to-b from-primary-500/10 via-red-500/5 backdrop-lg">
                    <img src="assets/images/payment/mastercard.png" alt="" class="h-10">
                </div>
                <div class="pt-0 card-body">
                    <div class="flex items-center">
                        <div class="grow">
                            <h6 class="mb-1">xxxx xxxx xxx 3641</h6>
                            <p class="text-gray-500 dark:text-dark-500">Expiry on 13/2028</p>
                        </div>
                    </div>
                    <div class="flex items-center justify-between gap-3 mt-5">
                        <a href="#!" class="link link-green" :class="{ 'text-green-500': defaultCard === 3 }" @click.prevent="defaultCard === 3 ? defaultCard = null : defaultCard = 3">
                            <span x-text="defaultCard === 3 ? 'Default Set' : 'Set as Default'"></span>
                        </a>
                        <a href="#!" data-modal-target="addCardPaymentModal" class="link link-primary"><i data-lucide="pencil" class="inline-block size-4"></i> Edit</a>
                    </div>
                </div>
            </div><!--end col-->
            <a href="#!" title="add" data-modal-target="addCardPaymentModal" class="flex items-center justify-center mb-0 border-dashed card">
                <div class="card-body">
                    <div class="flex items-center justify-center">
                        <i data-lucide="circle-plus" class="text-green-500 stroke-1 size-10 fill-green-500/10"></i>
                    </div>
                </div>
            </a><!--end col-->
        </div><!--end grid-->

    </div>
</div><!--end card-->

<div class="card">
    <div class="card-header">
        <div class="grid items-center grid-cols-12 gap-3">
            <div class="col-span-12 md:col-span-5 xl:col-span-3">
                <h6 class="card-title">Billing History</h6>
            </div>
            <div class="col-span-12 md:col-span-7 xl:col-span-3 xl:col-start-10">
                <div class="relative group/form">
                    <input type="email" class="ltr:pl-9 rtl:pr-9 form-input ltr:group-[&.right]/form:pr-9 rtl:group-[&.right]/form:pl-9 ltr:group-[&.right]/form:pl-4 rtl:group-[&.right]/form:pr-4" placeholder="Search for ...">
                    <div class="absolute inset-y-0 flex items-center text-gray-500 dark:text-dark-500 ltr:left-3 rtl:right-3 ltr:group-[&.right]/form:right-3 rtl:group-[&.right]/form:left-3 ltr:group-[&.right]/form:left-auto rtl:group-[&.right]/form:right-auto focus:outline-hidden">
                        <i data-lucide="search" class="size-4"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="card-body">
        <div x-data="billingHistoryTable()" x-init="init()">
            <div class="overflow-x-auto">
                <table class="table">
                    <tbody>
                        <tr class="bg-gray-100 dark:bg-dark-850">
                            <th x-on:click="sort('statementsID')" class="!font-medium text-gray-500 dark:text-dark-500 cursor-pointer whitespace-nowrap">Billing ID <span x-show="sortBy === 'statementsID'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span>
                            </th>
                            <th x-on:click="sort('paymentDate')" class="!font-medium text-gray-500 dark:text-dark-500 cursor-pointer whitespace-nowrap">Payment Date <span x-show="sortBy === 'paymentDate'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                            <th x-on:click="sort('plan')" class="!font-medium text-gray-500 dark:text-dark-500 cursor-pointer whitespace-nowrap">Plan <span x-show="sortBy === 'plan'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                            <th x-on:click="sort('paymentType')" class="!font-medium text-gray-500 dark:text-dark-500 cursor-pointer whitespace-nowrap">Payment Type <span x-show="sortBy === 'paymentType'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                            <th x-on:click="sort('amount')" class="!font-medium text-gray-500 dark:text-dark-500 cursor-pointer whitespace-nowrap">Amount <span x-show="sortBy === 'amount'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                            <th x-on:click="sort('status')" class="!font-medium text-gray-500 dark:text-dark-500 cursor-pointer whitespace-nowrap">status <span x-show="sortBy === 'status'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                            <th class="!font-medium text-gray-500 dark:text-dark-500 ltr:!text-right rtl:!text-left">Invoice</th>
                        </tr>
                        <template x-for="(product, index) in displayedProducts" :key="index">
                            <tr>
                                <td><a href="#!" class="link link-primary" x-text="product.statementsID"></a></td>
                                <td x-text="product.paymentDate"></td>
                                <td x-text="product.plan"></td>
                                <td x-text="product.paymentType"></td>
                                <td x-text="product.amount"></td>
                                <td>
                                    <span x-text="product.status" :class="{
                                                'badge badge-red': product.status === 'Unpaid',
                                                'badge badge-yellow': product.status === 'Pending',
                                                'badge badge-green': product.status === 'Paid'
                                            }"></span>
                                </td>
                                <td class="ltr:text-right rtl:text-left">
                                    <a href="#!" title="Download" class="link link-primary"><i class="align-bottom ri-download-2-line"></i></a>
                                </td>
                            </tr>
                        </template>
                    </tbody>
                </table>
            </div>
            <div class="grid grid-cols-12 gap-5 mt-5 items-center">
                <div class="col-span-12 md:col-span-6 justify-center md:justify-start flex flex-wrap">
                    <p class="text-gray-500 dark:text-dark-500">Showing <b x-text="showingStart"></b> - <b x-text="showingEnd"></b> of <b x-text="products.length"></b> Results</p>
                </div>
                <div class="col-span-12 md:col-span-6">
                    <div class="flex justify-center md:justify-end pagination pagination-primary">
                        <button @click="prevPage()" :disabled="currentPage === 1" class="pagination-pre">
                            <i data-lucide="chevron-left" class="mr-1 ltr:inline-block rtl:hidden size-5"></i>
                            <i data-lucide="chevron-right" class="ml-1 ltr:hidden rtl:inline-block size-5"></i>
                            Prev
                        </button>
                        <template x-for="page in totalPages" :key="page">
                            <button @click="gotoPage(page)" :class="{ 'active': currentPage === page }" class="pagination-item">
                                <span x-text="page"></span>
                            </button>
                        </template>
                        <button @click="nextPage()" :disabled="currentPage === totalPages" class="pagination-next">
                            Next
                            <i data-lucide="chevron-right" class="ml-1 rtl:hidden size-5 ltr:inline-block"></i>
                            <i data-lucide="chevron-left" class="mr-1 rtl:inline-block ltr:hidden size-5"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div><!--end card-->

</div>
{{> partials/footer }}
</div>

<div id="billingEditModal" class="!hidden modal show">
    <div class="modal-wrap modal-center modal-lg">
        <div class="modal-header">
            <h6>Billing Information</h6>
            <button data-modal-close="billingEditModal" class="link link-red"><i data-lucide="x" class="size-5"></i></button>
        </div>
        <div class="modal-content">
            <form action="#!">
                <div class="grid grid-cols-12 gap-5">
                    <div class="col-span-12">
                        <label for="namePersonalInput" class="form-label">Personal / Company Name</label>
                        <input type="text" id="namePersonalInput" class="form-input" placeholder="Enter your name" value="Sophia Mia" required>
                    </div>
                    <div class="col-span-12">
                        <label for="addressInput" class="form-label">Personal / Company Address</label>
                        <input type="text" id="addressInput" class="form-input" placeholder="Your address" value="3011 E Semoran Blvd" required>
                    </div>
                    <div class="col-span-6">
                        <label for="sampleSelect" class="form-label">Country</label>
                        <div id="sampleSelect"></div>
                    </div>
                    <div class="col-span-6">
                        <label for="stateInput" class="form-label">State</label>
                        <input type="text" id="stateInput" class="form-input" placeholder="Your state" value="Florida" required>
                    </div><!--end col-->
                    <div class="col-span-6">
                        <label for="cityInput" class="form-label">City</label>
                        <input type="text" id="cityInput" class="form-input" placeholder="Your city" value="Apopka" required>
                    </div><!--end col-->
                    <div class="col-span-6">
                        <label for="zipCodeInput" class="form-label">Zip Code</label>
                        <input type="number" id="zipCodeInput" class="form-input" placeholder="Zip code" value="32703" required>
                    </div><!--end col-->
                    <div class="col-span-6">
                        <label for="phoneNumberInput" class="form-label">Phone Number</label>
                        <input type="tel" id="phoneNumberInput" class="form-input" placeholder="Enter your phone number" value="+(*************" required>
                    </div><!--end col-->
                    <div class="col-span-6">
                        <label for="emailInput" class="form-label">Email Address</label>
                        <input type="email" id="emailInput" class="form-input" placeholder="Enter your email" value="<EMAIL>" required>
                    </div><!--end col-->
                    <div class="col-span-12">
                        <div class="flex items-center justify-end gap-2">
                            <button data-modal-close="billingEditModal" class="btn btn-active-red">Cancel</button>
                            <button type="submit" class="btn btn-primary">Update</button>
                        </div>
                    </div>
                </div><!--end grid-->
            </form>
        </div>
    </div>
</div><!--end-->

<div id="addCardPaymentModal" class="!hidden modal show">
    <div class="modal-wrap modal-center">
        <div class="modal-header">
            <h6>Add Card</h6>
            <button data-modal-close="addCardPaymentModal" class="link link-red"><i data-lucide="x" class="size-5"></i></button>
        </div>
        <div class="modal-content">
            <form action="#!">
                <div class="grid grid-cols-12 gap-5">
                    <div class="col-span-12">
                        <label for="cardNumberInput" class="form-label">Card Number</label>
                        <input type="text" id="cardNumberInput" class="form-input" x-mask:dynamic="$input.startsWith('34') || $input.startsWith('37')
                            ? '9999 999999 99999' : '9999 9999 9999 9999' " placeholder="0000 0000 0000 0000" required>
                    </div>
                    <div class="col-span-12 lg:col-span-3">
                        <label for="cvvInput" class="form-label">CVV</label>
                        <input type="text" id="cvvInput" class="form-input" x-mask="999" placeholder="000" required>
                    </div>
                    <div class="col-span-12 lg:col-span-3">
                        <label for="expiryDateInput" class="form-label">Expiry Date</label>
                        <input type="text" id="expiryDateInput" class="form-input" x-mask="99/9999" placeholder="MM/YYYY" required>
                    </div>
                    <div class="col-span-12 lg:col-span-6">
                        <label for="nameOnTheCardInput" class="form-label">Name on th Card</label>
                        <input type="text" id="nameOnTheCardInput" class="form-input" placeholder="Enter name" required>
                    </div><!--end col-->
                    <div class="col-span-12">
                        <div class="items-start input-check-group">
                            <input id="checkboxBasic1" class="mt-0.5 input-check input-check-primary shrink-0" type="checkbox" />
                            <label for="checkboxBasic1" class="input-check-label grow">
                                <span class="block mb-1.5 font-medium">Set as Default</span>
                                <span class="block text-gray-500 dark:text-dark-500">Scheduled payment will be automatically deducted from this card.</span>
                            </label>
                        </div>
                    </div><!--end col-->
                    <div class="col-span-12">
                        <div class="flex items-center justify-end gap-2">
                            <button data-modal-close="addCardPaymentModal" class="btn btn-active-red">Cancel</button>
                            <button type="submit" class="btn btn-primary">Add Card</button>
                        </div>
                    </div>
                </div><!--end grid-->
            </form>
        </div>
    </div>
</div><!--end-->

{{> partials/vendor-scripts }}

<script type="module" src="assets/js/pages/account-billing-plan.init.js"></script>

<script type="module" src="assets/js/main.js"></script>

</body>
</html>