{{> partials/main }}

<head>

    {{> partials/title-meta title="Add Leave" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}
{{> partials/page-heading title="Add Leave" sub-title="Staff" }}

<div class="grid grid-cols-12 gap-space" x-data="leaveForm()" x-init="init()">
    <div class="col-span-12 lg:col-span-8 2xl:col-span-9 card">
        <div class="card-header">
            <h6 class="card-title">Create Leave Request</h6>
        </div>
        <div class="card-body">
            <div class="grid grid-cols-12 gap-space">
                <div class="col-span-12">
                    <label for="leaveTypeSelect" class="form-label">Leave Type <span class="text-red-500">*</span></label>
                    <div id="leaveTypeSelect" placeholder="Select Leave Type" x-model="leaveForm.leaveType" @change="validateField('leaveType', document.querySelector('#leaveTypeSelect') , 'Leave type is required.')"></div>
                    <span x-show="errors.leaveType" class="text-red-500" x-text="errors.leaveType"></span>
                </div>
                <div class="col-span-12 md:col-span-6">
                    <label for="contactNumber" class="form-label">Contact Number <span class="text-red-500">*</span></label>
                    <input type="number" id="contactNumber" class="form-input" placeholder="Enter your contact number" x-model="leaveForm.contactNumber" @input="validateField('contactNumber', leaveForm.contactNumber, 'Contact number is required.')">
                    <span x-show="errors.contactNumber" class="text-red-500" x-text="errors.contactNumber"></span>
                </div>
                <div class="col-span-12 md:col-span-6">
                    <label for="emergencyNumber" class="form-label">Emergency Number <span class="text-red-500">*</span></label>
                    <input type="number" id="emergencyNumber" class="form-input" placeholder="Enter your emergency number" x-model="leaveForm.emergencyNumber" @input="validateField('emergencyNumber', leaveForm.emergencyNumber, 'Emergency number is required.')">
                    <span x-show="errors.emergencyNumber" class="text-red-500" x-text="errors.emergencyNumber"></span>
                </div>
                <div class="col-span-12 md:col-span-4">
                    <label for="startDateInput" class="form-label">Start Date <span class="text-red-500">*</span></label>
                    <input type="text" id="startDateInput" class="form-input" placeholder="Select date"  data-provider="flatpickr" data-date-format="Y-m-d"  x-model="leaveForm.startDate" @input="validateField('startDate', leaveForm.startDate, 'Start date is required.') , calculateTotalDays() ">
                    <span x-show="errors.startDate" class="text-red-500" x-text="errors.startDate"></span>
                </div>
                <div class="col-span-12 md:col-span-4">
                    <label for="endDateInput" class="form-label">End Date <span class="text-red-500">*</span></label>
                    <input type="text" id="endDateInput" class="form-input" placeholder="Select date"  data-provider="flatpickr" data-date-format="Y-m-d" x-model="leaveForm.endDate" @input="validateField('endDate', leaveForm.endDate, 'End date is required.'), calculateTotalDays()">
                    <span x-show="errors.endDate" class="text-red-500" x-text="errors.endDate"></span>
                </div>
                <div class="col-span-12 md:col-span-4">
                    <label for="totalDays" class="form-label">Total Days <span class="text-red-500">*</span></label>
                    <input type="number" id="totalDays" class="form-input" placeholder="0" x-model="leaveForm.totalDays" readonly>
                    <span x-show="errors.totalDays" class="text-red-500" x-text="errors.totalDays"></span>
                </div>
                <div class="col-span-12">
                    <label for="reasonsInput" class="form-label">Reason <span class="text-red-500">*</span></label>
                    <textarea id="reasonsInput" rows="3" class="h-auto form-input" placeholder="Enter reasons" x-model="leaveForm.reason" @input="validateField('reason', leaveForm.reason, 'Reason is required.')"></textarea>
                    <span x-show="errors.reason" class="text-red-500" x-text="errors.reason"></span>
                </div>
                <div class="col-span-12">
                    <div class="flex items-center justify-end gap-2">
                        <button type="reset" @click="resetForm" class="btn btn-sub-gray">Reset</button>
                        <button class="btn btn-primary" @click="submitForm($event)">Apply Leave Request</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-span-12 lg:col-span-4 2xl:col-span-3">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title">Leave Summary</h6>
            </div>
            <div class="card-body">
                <table class="table flush table-sm">
                    <tr>
                        <td class="text-gray-500 dark:text-dark-500">Casual Leave</td>
                        <td class="font-medium" x-text="leaves.casual"></td>
                    </tr>
                    <tr>
                        <td class="text-gray-500 dark:text-dark-500">Sick Leave</td>
                        <td class="font-medium" x-text="leaves.sick">1</td>
                    </tr>
                    <tr>
                        <td class="text-gray-500 dark:text-dark-500">Maternity Leave</td>
                        <td class="font-medium" x-text="leaves.maternity"></td>
                    </tr>
                    <tr>
                        <td class="text-gray-500 dark:text-dark-500">Emergency Leave</td>
                        <td class="font-medium" x-text="leaves.emergency"></td>
                    </tr>
                    <tr>
                        <td class="text-gray-500 dark:text-dark-500">Vacation Leave</td>
                        <td class="font-medium" x-text="leaves.vacation"></td>
                    </tr>
                    <tr>
                        <td class="text-gray-500 dark:text-dark-500">Total Leave</td>
                        <td class="font-medium" x-text="totalLeave()"></td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
</div>
</div>
{{> partials/footer }}
</div>

{{> partials/vendor-scripts }}

<script type="module" src="assets/js/apps/hospital/staff/create-leave.init.js"></script>

<script type="module" src="assets/js/main.js"></script>

</body>
</html>