{{> partials/landing }}

<head>

    {{> partials/title-meta title="Ecommerce" }}

    <link rel="stylesheet" href="/assets/libs/slick-image-compare/style.css">

    <link href="/assets/libs/flatpickr/flatpickr.css" rel="stylesheet">
    <!-- plugins CSS -->
    <link rel="stylesheet" href="assets/css/plugins.css">
    <!-- Icons CSS -->
    <link rel="stylesheet" href="assets/css/icons.css">
    <!-- Tailwind CSS -->
    <link rel="stylesheet" href="assets/css/tailwind.css">

    <script type="module" src="assets/js/admin.bundle.js"></script>

</head>

{{> partials/body }}

<!----------Start Menu------------>
<div x-data="{ isSticky: false , isMenuOpen: false , activeTab: 0 }" x-init="window.addEventListener('scroll', () => { isSticky = window.scrollY > 0 })">
    <header class="container mx-auto px-4 lg:max-w-[1300px] [&.scroll-sticky]:max-w-full landing-navbar top-0 h-20 bg-white rounded-b-lg [&.scroll-sticky]:rounded-none shadow-lg shadow-gray-200/50 dark:bg-dark-950 dark:shadow-dark-850" :class="{ 'scroll-sticky': isSticky }">
        <div class="flex items-center w-full gap-5">
            <a href="index.html" title="Logo">
                <img src="assets/images/main-logo.png" alt="" class="inline-block h-7 dark:hidden">
                <img src="assets/images/logo-white.png" alt="" class="hidden h-7 dark:inline-block">
            </a>
            <div class="mx-auto navbar-collapase" :class="{ 'hidden xl:flex': !isMenuOpen }">
                <div x-data="tabsHandler()" @scroll.window="updateTabOnScroll" class="flex flex-col xl:flex-row xl:items-center *:py-3 xl:py-0 xl:*:px-3 *:inline-block *:text-16 *:tracking-wide *:font-medium">

                    <a href="#products" title="products" @click="setActive(1)" :class="{ 'active': activeTab === 1 }" class="leading-normal [&.active]:text-primary-500 hover:text-primary-500 transition duration-300 ease-linear">
                        Products
                    </a>
                    <a href="#new-arrivals" title="new-arrivals" @click="setActive(2)" :class="{ 'active': activeTab === 2 }" class="leading-normal [&.active]:text-primary-500 hover:text-primary-500 transition duration-300 ease-linear">
                        New Arrivals
                    </a>
                    <a href="#service" title="service" @click="setActive(3)" :class="{ 'active': activeTab === 3 }" class="leading-normal [&.active]:text-primary-500 hover:text-primary-500 transition duration-300 ease-linear">
                        Service
                    </a>
                    <a href="#cta" title="cta" @click="setActive(4)" :class="{ 'active': activeTab === 4 }" class="leading-normal [&.active]:text-primary-500 hover:text-primary-500 transition duration-300 ease-linear">
                        CTA
                    </a>
                </div>
            </div>
            <div class="flex items-center gap-2 ltr:ml-auto rtl:mr-auto xl:ltr:ml-0 xl:rtl:mr-0">
                <button title="menu toggle" @click="isMenuOpen = !isMenuOpen" type="button" class="rounded-full xl:ltr:ml-0 xl:rtl:mr-0 ltr:ml-auto rtl:mr-auto navbar-toggle btn btn-sub-sky btn-icon xl:!hidden">
                    <i :class="isMenuOpen ? 'ri-close-line' : 'ri-menu-2-line'" class="text-lg"></i>
                </button>
                <button type="button" title="search" class="rounded-full btn btn-icon btn-active-gray"><i data-lucide="search" class="size-4"></i></button>
                <a href="apps-ecommerce-shop-cart.html" title="shopping-cart" class="rounded-full btn btn-icon btn-active-gray"><i data-lucide="shopping-cart" class="size-4"></i></a>
                <div x-data="{
                    open: false,
                    toggle() {
                        if (this.open) {
                            return this.close();
                        }
                
                        this.$refs.button.focus();
                
                        this.open = true;
                    },
                    close(focusAfter) {
                        if (!this.open) return;
                
                        this.open = false;
                
                        focusAfter && focusAfter.focus();
                    }
                }" x-on:keydown.escape.prevent.stop="close($refs.button)" x-on:focusin.window="!$refs.panel.contains($event.target) && close()" x-id="['dropdown-button']" class="dropdown">
                    <button x-ref="button" x-on:click="toggle()" :aria-expanded="open" :aria-controls="$id('dropdown-button')" type="button" class="rounded-full btn btn-icon btn-active-gray">
                        <img src="assets/images/avatar/user-17.png" alt="Profile" class="rounded-full">
                    </button>

                    <div x-ref="panel" x-show="open" x-transition.origin.top.right x-on:click.outside="close($refs.button)" :id="$id('dropdown-button')" style="display: none;" class="dropdown-menu dropdown-right !w-72">
                        <div class="p-4">
                            <div class="flex items-center gap-3 mb-4">
                                <img src="assets/images/avatar/user-17.png" alt="" class="rounded-md size-11">
                                <div>
                                    <h6 class="mb-0.5">Shopia Mia</h6>
                                    <p class="flex items-center gap-2 text-gray-500 dark:text-dark-500"><span class="inline-block align-baseline bg-green-500 rounded-full size-2"></span> Active</p>
                                </div>
                            </div>
                            <a href="pages-user.html" class="flex items-center gap-2 w-full first-of-type:rounded-t-md last-of-type:rounded-b-md px-4 py-2.5 text-sm hover:bg-gray-50 dark:hover:bg-dark-850">
                                <i class="align-baseline ltr:mr-1 rtl:ml-1 ri-user-line"></i> Profile
                            </a>
                            <a href="apps-ecommerce-shop-cart.html" class="flex items-center gap-2 w-full first-of-type:rounded-t-md last-of-type:rounded-b-md px-4 py-2.5 text-sm hover:bg-gray-50 dark:hover:bg-dark-850">
                                <i class="align-baseline ltr:mr-1 rtl:ml-1 ri-shopping-cart-2-line"></i> Shopping Cart
                            </a>
                            <a href="apps-ecommerce-wishlist.html" class="flex items-center gap-2 w-full first-of-type:rounded-t-md last-of-type:rounded-b-md px-4 py-2.5 text-sm hover:bg-gray-50 dark:hover:bg-dark-850">
                                <i class="align-baseline ltr:mr-1 rtl:ml-1 ri-bookmark-line"></i> Wish List
                            </a>
                            <a href="pages-help-center.html" class="flex items-center gap-2 w-full first-of-type:rounded-t-md last-of-type:rounded-b-md px-4 py-2.5 text-sm hover:bg-gray-50 dark:hover:bg-dark-850">
                                <i class="align-baseline ltr:mr-1 rtl:ml-1 ri-customer-service-2-line"></i> Help Center
                            </a>
                            <a href="pages-account-settings.html" class="flex items-center gap-2 w-full first-of-type:rounded-t-md last-of-type:rounded-b-md px-4 py-2.5 text-sm hover:bg-gray-50 dark:hover:bg-dark-850">
                                <i class="align-baseline ltr:mr-1 rtl:ml-1 ri-settings-3-line"></i> Account Settings
                            </a>
                        </div>
                        <div class="flex items-center gap-2 p-4 border-purple-500/20 border-y bg-purple-500/10">
                            <img src="assets/images/ecommerce/landing/gift.png" alt="" class="size-8 shrink-0">
                            <div class="grow">
                                <h6 class="mb-0.5">Refer a friend</h6>
                                <p class="text-gray-500 dark:text-dark-500">7 invitation remaining</p>
                            </div>
                        </div>
                        <div class="p-4">
                            <a href="auth-signin-basic.html" class="flex items-center gap-2 w-full first-of-type:rounded-t-md last-of-type:rounded-b-md px-4 py-2.5 text-sm hover:bg-gray-50 dark:hover:bg-dark-850">
                                <i class="align-baseline ltr:mr-1 rtl:ml-1 ri-logout-circle-r-line"></i> Sign Out
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>
</div><!----------End Menu------------>

<!----------Start Home------------>
<section class="relative pb-48 min-h-screen overflow-hidden pt-80 bg-[url('../images/ecommerce/landing/home.jpg')] bg-cover bg-center">
    <div class="container mx-auto px-4 lg:max-w-[1350px] lg:px-20">
        <h1 class="absolute inset-x-0 md:text-[80px] xl:text-[140px] 2xl:text-[11rem] bottom-10 text-white/60 text-center font-bold">DOMIEX FASHION</h1>
    </div>
</section><!----------End Home------------>

<!----------Start Section------------>
<section class="relative -mt-10 pb-14 md:pb-24">
    <div class="container mx-auto px-4 lg:max-w-[1350px]" x-data="products()" x-init="init()">
        <div class="grid grid-cols-12">
            <div class="col-span-12 lg:col-span-8 lg:col-start-3">
                <div class="p-5 bg-white rounded-lg dark:bg-dark-950">
                    <form action="#!" class="relative block group/form">
                        <input type="text" class="ltr:pl-9 rtl:pr-9 form-input dark:bg-transparent" placeholder="Search for product, brand etc...">
                        <button title="search" type="submit" class="absolute inset-y-0 flex items-center text-gray-500 dark:text-dark-500 ltr:left-3 rtl:right-3 focus:outline-hidden">
                            <i data-lucide="search" class="size-4"></i>
                        </button>
                    </form>
                    <div class="flex flex-wrap items-center justify-center gap-2 mt-2 whitespace-nowrap">
                        <h6>Popular Search:</h6>
                        <a href="#!" title="link" class="link link-primary">Fashion,</a>
                        <a href="#!" title="link" class="link link-primary">Girl Top,</a>
                        <a href="#!" title="link" class="link link-primary">Boys Fashion,</a>
                        <a href="#!" title="link" class="link link-primary">Watch</a>
                    </div>
                </div>
            </div>
        </div><!--end grid-->
        <div x-data="{ active: 'columns-4' }">
            <div class="flex items-center gap-5">
                <ul class="flex items-center gap-6 overflow-x-auto grow">
                    <li>
                        <a href="#!" title="tabs link" @click="activeTab = 'Men'; filterProducts()" :class="{ 'active': activeTab === 'Men' }" class="relative block py-2 text-center link link-primary [&.active]:text-primary-500 ">
                            Men
                        </a>
                    </li>
                    <li>
                        <a href="#!" title="tabs link" @click="activeTab = 'Women';filterProducts() " :class="{ ' active': activeTab === 'Women' }" class="relative block py-2 text-center link link-primary [&.active]:text-primary-500">
                            Women
                        </a>
                    </li>
                    <li>
                        <a href="#!" title="tabs link" @click="activeTab = 'Children'; filterProducts()" :class="{ ' active': activeTab === 'Children' }" class="relative block py-2 text-center link link-primary [&.active]:text-primary-500">
                            Children
                        </a>
                    </li>
                    <li>
                        <a href="#!" title="tabs link" @click="activeTab = 'brand'; filterProducts()" :class="{ ' active': activeTab === 'brand' }" class="relative block py-2 text-center link link-primary [&.active]:text-primary-500">
                            Brand
                        </a>
                    </li>
                </ul>
                <div class="items-center hidden gap-3 lg:flex shrink-0">
                    <a href="#!" title="Columns link" class="link link-primary" :class="{ 'text-primary-500 active': active === 'columns-4' }" @click.prevent="active = 'columns-4'">
                        <i data-lucide="columns-4" class="size-5"></i>
                    </a>
                    <a href="#!" title="Columns link" class="link link-primary" :class="{ 'text-primary-500': active === 'columns-3' }" @click.prevent="active = 'columns-3'">
                        <i data-lucide="columns-3" class="size-5"></i>
                    </a>
                </div>
            </div>
            <div class="grid gap-8 mt-5" :class="active === 'columns-4' ? 'grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4' : 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3'">
                <template x-for="product in filteredProduct" :key="index">
                    <div class="relative">
                        <div class="relative overflow-hidden bg-gray-100 rounded-md dark:bg-dark-900/40 group/item">
                            <img :src="product.image" alt="">
                            <div class="absolute flex transition-all duration-300 ease-linear opacity-0 top-2 group-hover/item:top-5 ltr:right-5 rtl:left-5 group-hover/item:opacity-100">
                                <button title="Rating Star Icon" type="button" class="text-lg rounded-full bg-white/90 link link-red btn btn-icon"><i class="ri-star-fill"></i></button>
                            </div>
                        </div>
                        <div class="mt-3">
                            <h6 class="mb-1 truncate"><a href="apps-ecommerce-product-overview.html" class="text-current link hover:text-primary-500 dark:text-current dark:hover:text-primary-500" x-text="product.name">Black Coloured Knitted Lycra Dress</a></h6>
                            <p class="text-gray-500 dark:text-dark-500" x-text="product.price">$49.99</p>
                        </div>
                    </div>
                </template>
            </div><!--end grid-->
            <div class="flex items-center justify-center mt-6">
                <a href="apps-ecommerce-products-grid.html" title="Loading btn" class="flex items-center gap-2 btn btn-primary">
                    Loading ...
                    <svg class="size-4 animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-0" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                </a>
            </div>
        </div>
    </div>
</section><!----------end Section------------>

<!----------Start Section------------>
<div class="relative flex items-center h-40 max-w-full overflow-x-hidden">
    <div class="absolute animate-marquee whitespace-nowrap will-change-transform">
        <h1 class="font-bold uppercase text-gray-500/10 dark:text-dark-500/20 text-8xl">&nbsp;Mens Fashion Winter Deal || Girls Fashion || Brand Clothes Fashion || Up to 50% Discount in Domiex Fashion</h1>
    </div>
</div><!----------end Section------------>

<!----------Start Section------------>
<section class="relative py-8 md:py-24" id="new-arrivals">
    <div class="container mx-auto px-4 lg:max-w-[1350px]">
        <div class="grid items-center grid-cols-12 gap-5 mb-8">
            <div class="col-span-12 2xl:col-span-5">
                <h1 class="relative leading-normal capitalize ltr:pl-5 rtl:pr-5 before:rounded-full drop-shadow-lg before:absolute before:w-1 before:bg-primary-500 before:h-1/2 ltr:before:left-0 rtl:before:right-0">New Arrivals this Spring Season</h1>
            </div>
            <div class="col-span-12 2xl:col-span-5 2xl:col-start-8">
                <p class="mb-3 text-gray-500 dark:text-dark-500">Spring is the time when nature blossoms, so look for pieces of clothing that feature flowers, leaves, and pale colors. Additionally, opt for light-weight fabrics like cotton or linen, since the weather is warming up.</p>
                <a href="apps-ecommerce-products-list.html" title="All Collection" class="font-medium border-gray-200 dark:border-dark-800 btn btn-outline-gray">
                    Show All Collection
                    <i class="ml-1 align-baseline ri-arrow-right-line ltr:inline-block rtl:hidden"></i>
                    <i class="mr-1 align-baseline ri-arrow-left-line ltr:hidden rtl:inline-block"></i>
                </a>
            </div>
        </div>
        <div class="grid gap-8 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
            <div class="relative">
                <div class="relative overflow-hidden bg-gray-100 group/item dark:bg-dark-900/40">
                    <img src="assets/images/ecommerce/landing/products/img-09.png" alt="">
                    <div class="absolute flex transition-all duration-300 ease-linear opacity-0 top-2 group-hover/item:top-5 ltr:right-5 rtl:left-5 group-hover/item:opacity-100">
                        <button title="Rating Star Icon" type="button" class="text-lg rounded-full bg-white/90 link link-red btn btn-icon"><i class="ri-star-fill"></i></button>
                    </div>
                    <div class="absolute px-4 py-1 text-red-100 bg-red-500 ltr:left-0 rtl:right-0 before:border-4 before:absolute ltr:before:border-l-transparent rtl:before:border-r-transparent before:border-b-transparent before:size-2 before:-bottom-2 before:border-red-500 ltr:before:right-0 rtl:before:left-0 top-2">
                        50% OFF
                    </div>
                </div>
                <div class="mt-4">
                    <h6 class="mb-1 truncate"><a href="apps-ecommerce-product-overview.html" class="text-current link hover:text-primary-500 dark:text-current dark:hover:text-primary-500">Fashion Hub Women Peach Net Dress</a></h6>
                    <p class="text-gray-500 dark:text-dark-500">$74.99 <span class="line-through">$149.99</span></p>
                </div>
            </div><!--end col-->
            <div class="relative">
                <div class="relative overflow-hidden bg-gray-100 group/item dark:bg-dark-900/40">
                    <img src="assets/images/ecommerce/landing/products/img-10.png" alt="">
                    <div class="absolute flex transition-all duration-300 ease-linear opacity-0 top-2 group-hover/item:top-5 ltr:right-5 rtl:left-5 group-hover/item:opacity-100">
                        <button title="Rating Star Icon" type="button" class="text-lg rounded-full bg-white/90 link link-red btn btn-icon"><i class="ri-star-fill"></i></button>
                    </div>
                </div>
                <div class="mt-4">
                    <h6 class="mb-1 truncate"><a href="apps-ecommerce-product-overview.html" class="text-current link hover:text-primary-500 dark:text-current dark:hover:text-primary-500">Fashion portrait of young elegant woman</a></h6>
                    <p class="text-gray-500 dark:text-dark-500">$187.00</p>
                </div>
            </div><!--end col-->
            <div class="relative">
                <div class="relative overflow-hidden bg-gray-100 group/item dark:bg-dark-900/40">
                    <img src="assets/images/ecommerce/landing/products/img-11.png" alt="">
                    <div class="absolute flex transition-all duration-300 ease-linear opacity-0 top-2 group-hover/item:top-5 ltr:right-5 rtl:left-5 group-hover/item:opacity-100">
                        <button type="button" title="Rating Star Icon" class="text-lg rounded-full bg-white/90 link link-red btn btn-icon"><i class="ri-star-fill"></i></button>
                    </div>
                    <div class="absolute px-4 py-1 text-red-100 bg-red-500 ltr:left-0 rtl:right-0 before:border-4 before:absolute ltr:before:border-l-transparent rtl:before:border-r-transparent before:border-b-transparent before:size-2 before:-bottom-2 before:border-red-500 ltr:before:right-0 rtl:before:left-0 top-2">
                        25% OFF
                    </div>
                </div>
                <div class="mt-4">
                    <h6 class="mb-1 truncate"><a href="apps-ecommerce-product-overview.html" class="text-current link hover:text-primary-500 dark:text-current dark:hover:text-primary-500">Demonstrating winter Clothes</a></h6>
                    <p class="text-gray-500 dark:text-dark-500">$59.99 <span class="line-through">$79.99</span></p>
                </div>
            </div><!--end col-->
            <div class="relative">
                <div class="relative overflow-hidden bg-gray-100 group/item dark:bg-dark-900/40">
                    <img src="assets/images/ecommerce/landing/products/img-12.png" alt="">
                    <div class="absolute flex transition-all duration-300 ease-linear opacity-0 top-2 group-hover/item:top-5 ltr:right-5 rtl:left-5 group-hover/item:opacity-100">
                        <button type="button" title="Rating Star Icon" class="text-lg rounded-full bg-white/90 link link-red btn btn-icon"><i class="ri-star-fill"></i></button>
                    </div>
                </div>
                <div class="mt-4">
                    <h6 class="mb-1 truncate"><a href="apps-ecommerce-product-overview.html" class="text-current link hover:text-primary-500 dark:text-current dark:hover:text-primary-500">Y2K Trending Korean Fashion Aesthetic Shirt</a></h6>
                    <p class="text-gray-500 dark:text-dark-500">$79.99</p>
                </div>
            </div><!--end col-->
        </div><!--end grid-->
    </div>
</section><!----------End Section------------>

<!----------Start Section------------>
<a href="#!" title="Banner" class="relative block overflow-hidden py-60 bg-center bg-[url('../images/ecommerce/landing/cta-01.jpg')]">
    <div class="container mx-auto px-4 lg:max-w-[1350px] relative">
        <h2 class="absolute hidden font-bold uppercase md:flex left-4 lg:text-8xl text-white/70">Summer</h2>
        <h2 class="absolute font-bold uppercase left-1/2 lg:right-4 lg:text-8xl text-white/70">Fashion</h2>
    </div>
</a><!----------End Section------------>

<!----------Start Section------------>
<section class="relative py-14 md:py-24">
    <div class="container mx-auto px-4 lg:max-w-[1350px]">
        <div class="grid items-center grid-cols-12 mb-10">
            <div class="col-span-12 text-center lg:col-span-6 lg:col-start-4">
                <h1 class="mb-2 leading-normal capitalize">The Coastal Edition</h1>
                <p class="text-gray-500 dark:text-dark-500">Clothing is practical and preppy, with plenty of coverage—it's perfect for moms and grandmothers who aren't interested in baring it all in a bikini at the beach.</p>
            </div>
        </div>
        <div class="grid grid-cols-12">
            <div class="col-span-6">
                <div class="relative">
                    <img src="assets/images/ecommerce/landing/img-01.jpg" alt="">
                    <div x-data="{
                        open: false,
                        toggle() {
                            if (this.open) {
                                return this.close();
                            }
                    
                            this.$refs.button.focus();
                    
                            this.open = true;
                        },
                        close(focusAfter) {
                            if (!this.open) return;
                    
                            this.open = false;
                    
                            focusAfter && focusAfter.focus();
                        }
                    }" x-on:keydown.escape.prevent.stop="close($refs.button)" x-on:focusin.window="!$refs.panel.contains($event.target) && close()" x-id="['dropdown-button']" class="absolute top-[20%] left-[38%]">
                        <button x-ref="button" x-on:click="toggle()" title="dropdown-button" :aria-expanded="open" :aria-controls="$id('dropdown-button')" type="button" class="flex">
                            <div class="absolute top-0 right-0 w-3 h-3 -mt-1 -mr-1 bg-white rounded-full animate-ping"></div>
                            <div class="absolute top-0 right-0 w-3 h-3 -mt-1 -mr-1 bg-white rounded-full"></div>
                        </button>

                        <div x-ref="panel" x-show="open" x-transition.origin.top.left x-on:click.outside="close($refs.button)" :id="$id('dropdown-button')" style="display: none;" class="absolute left-0 right-0 z-10 w-48 p-3 mt-1 bg-white rounded-md shadow-md dark:bg-dark-900">
                            <img src="assets/images/ecommerce/landing/img-03.jpg" alt="Images 03" class="object-cover w-full">
                            <h6 class="mt-3 mb-1 font-medium"><a href="#!">Faded Effect Top</a></h6>
                            <p class="text-gray-500 dark:text-dark-500">$34.65</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-span-6">
                <div class="relative">
                    <img src="assets/images/ecommerce/landing/img-02.jpg" alt="">
                    <div x-data="{
                        open: false,
                        toggle() {
                            if (this.open) {
                                return this.close();
                            }
                    
                            this.$refs.button.focus();
                    
                            this.open = true;
                        },
                        close(focusAfter) {
                            if (!this.open) return;
                    
                            this.open = false;
                    
                            focusAfter && focusAfter.focus();
                        }
                    }" x-on:keydown.escape.prevent.stop="close($refs.button)" x-on:focusin.window="!$refs.panel.contains($event.target) && close()" x-id="['dropdown-button']" class="absolute bottom-[25%] left-[33%]">
                        <button x-ref="button" title="dropdown-button" x-on:click="toggle()" :aria-expanded="open" :aria-controls="$id('dropdown-button')" type="button" class="flex">
                            <div class="absolute top-0 right-0 w-3 h-3 -mt-1 -mr-1 bg-white rounded-full animate-ping"></div>
                            <div class="absolute top-0 right-0 w-3 h-3 -mt-1 -mr-1 bg-white rounded-full"></div>
                        </button>

                        <div x-ref="panel" x-show="open" x-transition.origin.top.left x-on:click.outside="close($refs.button)" :id="$id('dropdown-button')" style="display: none;" class="absolute left-0 right-0 z-10 w-48 p-3 mt-1 bg-white rounded-md shadow-md dark:bg-dark-900">
                            <img src="assets/images/ecommerce/landing/img-04.jpg" alt="Images 03" class="object-cover w-full">
                            <h6 class="mt-3 mb-1 font-medium"><a href="#!">Short sleeve white</a></h6>
                            <p class="text-gray-500 dark:text-dark-500">$49.99</p>
                        </div>
                    </div>
                    <div x-data="{
                        open: false,
                        toggle() {
                            if (this.open) {
                                return this.close();
                            }
                    
                            this.$refs.button.focus();
                    
                            this.open = true;
                        },
                        close(focusAfter) {
                            if (!this.open) return;
                    
                            this.open = false;
                    
                            focusAfter && focusAfter.focus();
                        }
                    }" x-on:keydown.escape.prevent.stop="close($refs.button)" x-on:focusin.window="!$refs.panel.contains($event.target) && close()" x-id="['dropdown-button']" class="absolute bottom-[38%] left-[45%]">
                        <button x-ref="button" title="dropdown-button" x-on:click="toggle()" :aria-expanded="open" :aria-controls="$id('dropdown-button')" type="button" class="flex">
                            <div class="absolute top-0 right-0 w-3 h-3 -mt-1 -mr-1 bg-white rounded-full animate-ping"></div>
                            <div class="absolute top-0 right-0 w-3 h-3 -mt-1 -mr-1 bg-white rounded-full"></div>
                        </button>

                        <div x-ref="panel" x-show="open" x-transition.origin.top.left x-on:click.outside="close($refs.button)" :id="$id('dropdown-button')" style="display: none;" class="absolute left-0 right-0 z-10 w-48 p-3 mt-1 bg-white rounded-md shadow-md dark:bg-dark-900">
                            <img src="assets/images/ecommerce/landing/img-05.jpg" alt="Images 03" class="object-cover w-full">
                            <h6 class="mt-3 mb-1 font-medium"><a href="#!">Luxury handbag</a></h6>
                            <p class="text-gray-500 dark:text-dark-500">$79.99</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section><!----------End Section------------>

<!----------Start Section------------>
<section class="relative pt-10 pb-10 md:pb-24" id="service">
    <div class="container mx-auto px-4 lg:max-w-[1350px]">
        <div class="grid items-center grid-cols-12 gap-5 lg:gap-8">
            <div class="col-span-12 sm:col-span-6 lg:col-span-3 lg:row-span-2">
                <div class="flex flex-col gap-3 lg:items-center lg:flex-row md:p-5">
                    <div class="flex items-center justify-center size-12 shrink-0">
                        <i data-lucide="truck" class="text-gray-500 stroke-1 dark:text-dark-500 size-8"></i>
                    </div>
                    <div class="overflow-hidden grow">
                        <h6 class="mb-1">Free Shipping</h6>
                        <p class="text-gray-500 truncate dark:text-dark-500">Enjoy free shipping on orders over $149.</p>
                    </div>
                </div>
            </div>
            <div class="col-span-12 sm:col-span-6 lg:col-span-3">
                <div class="flex flex-col gap-3 lg:items-center lg:flex-row md:p-5">
                    <div class="flex items-center justify-center size-12 shrink-0">
                        <i data-lucide="handshake" class="text-gray-500 stroke-1 dark:text-dark-500 size-8"></i>
                    </div>
                    <div class="overflow-hidden grow">
                        <h6 class="mb-1">Money Guarantee</h6>
                        <p class="text-gray-500 truncate dark:text-dark-500">Exchange within 30 days</p>
                    </div>
                </div>
            </div>
            <div class="col-span-12 sm:col-span-6 lg:col-span-3">
                <div class="flex flex-col gap-3 lg:items-center lg:flex-row md:p-5">
                    <div class="flex items-center justify-center size-12 shrink-0">
                        <i data-lucide="headset" class="text-gray-500 stroke-1 dark:text-dark-500 size-8"></i>
                    </div>
                    <div class="overflow-hidden grow">
                        <h6 class="mb-1">Online Help Center</h6>
                        <p class="text-gray-500 truncate dark:text-dark-500">24 hours a day, 7 days a week</p>
                    </div>
                </div>
            </div>
            <div class="col-span-12 sm:col-span-6 lg:col-span-3 lg:row-span-2">
                <div class="flex flex-col gap-3 lg:items-center lg:flex-row md:p-5">
                    <div class="flex items-center justify-center size-12 shrink-0">
                        <i data-lucide="credit-card" class="text-gray-500 stroke-1 dark:text-dark-500 size-8"></i>
                    </div>
                    <div class="overflow-hidden grow">
                        <h6 class="mb-1">Flexible Payment Options</h6>
                        <p class="text-gray-500 truncate dark:text-dark-500">Pay Using Multiple Credit Cards</p>
                    </div>
                </div>
            </div>
            <div class="col-span-12 text-center lg:col-span-6">
                <h1 class="relative leading-normal capitalize lg:ltr:pl-5 lg:rtl:pr-5 lg:before:rounded-full drop-shadow-lg lg:before:absolute lg:before:w-1 lg:before:bg-primary-500 lg:before:h-1/2 lg:ltr:before:left-0 lg:rtl:before:right-0">Benefits You Get When Using Our Service</h1>
            </div>
        </div>
    </div><!--end container-->
</section><!----------End Section------------>

<!----------Start Section------------>
<section class="relative pt-8 pb-14 md:pb-24" id="cta">
    <div class="container mx-auto px-4 lg:max-w-[1350px]">
        <div class="grid items-center grid-cols-12 gap-5">
            <div class="col-span-12 md:col-span-6">
                <div id="beforeAfterImages" class="relative">
                    <img src="assets/images/ecommerce/landing/cta-02.jpg" alt="before image" />
                    <img src="assets/images/ecommerce/landing/cta-03.jpg" alt="after image" />
                    <div class="absolute top-5 left-5">
                        <span class="text-gray-500 bg-white badge text-13 border-transparent">After</span>
                    </div>
                    <div class="absolute bottom-5 right-5">
                        <span class="text-gray-500 bg-white badge text-13 border-transparent">Before</span>
                    </div>
                </div>
            </div>
            <div class="col-span-12 text-center md:col-span-6 2xl:col-span-4 2xl:col-start-8">
                <h1 class="relative mb-3 leading-normal capitalize ltr:pl-5 rtl:pr-5 before:rounded-full drop-shadow-lg before:absolute before:w-1 before:bg-primary-500 before:h-1/2 ltr:before:left-0 rtl:before:right-0">Layer Up with Expertly Designed Pieces</h1>
                <p class="mb-5 text-gray-500">Discover our collection of meticulously crafted pieces that are perfect for layering. Each item is designed to complement your style and keep you comfortable, no matter the season.</p>
                <a href="apps-ecommerce-products-grid.html" title="shop now" class="font-medium border-gray-200 dark:border-dark-800 btn btn-outline-gray">
                    Shop Now
                    <i class="ml-1 align-baseline ri-arrow-right-line ltr:inline-block rtl:hidden"></i>
                    <i class="mr-1 align-baseline ri-arrow-left-line ltr:hidden rtl:inline-block"></i>
                </a>
            </div>
        </div>
    </div>
</section><!----------End Section------------>

<!----------Start Section------------>
<section class="relative pt-8">
    <div class="container mx-auto px-4 lg:max-w-[1350px]">
        <div class="mb-8 text-center">
            <h1 class="relative leading-normal capitalize drop-shadow-lg">Follow us Instagram @domiex</h1>
        </div>
    </div>
    <div class="grid items-center grid-cols-2 lg:grid-cols-3 2xl:grid-cols-5">
        <a href="#!" title="Instagram Post" class="relative block group/item">
            <img src="assets/images/ecommerce/landing/instagram/img-01.jpg" alt="">
            <div class="absolute inset-0 transition-all duration-300 opacity-0 bg-gray-950/15 group-hover/item:opacity-100"></div>
            <i data-lucide="instagram" class="absolute block text-white transition-all duration-300 -translate-x-1/2 -translate-y-1/2 opacity-0 size-8 top-[45%] group-hover/item:top-1/2 group-hover/item:opacity-100 left-1/2"></i>
        </a>
        <a href="#!" title="Instagram Post" class="relative block group/item">
            <img src="assets/images/ecommerce/landing/instagram/img-02.jpg" alt="">
            <div class="absolute inset-0 transition-all duration-300 opacity-0 bg-gray-950/15 group-hover/item:opacity-100"></div>
            <i data-lucide="instagram" class="absolute block text-white transition-all duration-300 -translate-x-1/2 -translate-y-1/2 opacity-0 size-8 top-[45%] group-hover/item:top-1/2 group-hover/item:opacity-100 left-1/2"></i>
        </a>
        <a href="#!" title="Instagram Post" class="relative block group/item">
            <img src="assets/images/ecommerce/landing/instagram/img-03.jpg" alt="">
            <div class="absolute inset-0 transition-all duration-300 opacity-0 bg-gray-950/15 group-hover/item:opacity-100"></div>
            <i data-lucide="instagram" class="absolute block text-white transition-all duration-300 -translate-x-1/2 -translate-y-1/2 opacity-0 size-8 top-[45%] group-hover/item:top-1/2 group-hover/item:opacity-100 left-1/2"></i>
        </a>
        <a href="#!" title="Instagram Post" class="relative block group/item">
            <img src="assets/images/ecommerce/landing/instagram/img-04.jpg" alt="">
            <div class="absolute inset-0 transition-all duration-300 opacity-0 bg-gray-950/15 group-hover/item:opacity-100"></div>
            <i data-lucide="instagram" class="absolute block text-white transition-all duration-300 -translate-x-1/2 -translate-y-1/2 opacity-0 size-8 top-[45%] group-hover/item:top-1/2 group-hover/item:opacity-100 left-1/2"></i>
        </a>
        <a href="#!" title="Instagram Post" class="relative block group/item">
            <img src="assets/images/ecommerce/landing/instagram/img-05.jpg" alt="">
            <div class="absolute inset-0 transition-all duration-300 opacity-0 bg-gray-950/15 group-hover/item:opacity-100"></div>
            <i data-lucide="instagram" class="absolute block text-white transition-all duration-300 -translate-x-1/2 -translate-y-1/2 opacity-0 size-8 top-[45%] group-hover/item:top-1/2 group-hover/item:opacity-100 left-1/2"></i>
        </a>
    </div>
</section><!----------End Section------------>

<!----------Start Footer------------>
<footer class="relative">
    <div class="container mx-auto px-4 lg:max-w-[1350px]">
        <div class="grid grid-cols-12 gap-4 py-16 md:gap-x-8">
            <div class="col-span-12 lg:col-span-5 lg:pr-10">
                <h5 class="drop-shadow-lg">Stay Connected</h5>
                <div class="relative mt-5">
                    <input type="text" class="border-0 border-b-2 rounded-none dark:bg-transparent ltr:pr-8 rtl:pl-8 form-input" placeholder="Enter your email">
                    <button type="submit" title="submit" class="absolute ltr:right-0 rtl:left-0 link link-primary top-2">
                        <i data-lucide="move-right" class="ltr:inline-block rtl:hidden"></i>
                        <i data-lucide="move-left" class="ltr:hidden rtl:inline-block"></i>
                    </button>
                </div>
                <p class="mt-5 text-gray-500 dark:text-dark-500">Enjoy 15% off your first purchase as a thank you for staying in touch.</p>
            </div>
            <div class="col-span-12 md:col-span-4 lg:col-span-2">
                <h5 class="mb-5 drop-shadow-lg">Quick Links</h5>
                <ul class="space-y-5">
                    <li><a href="#!" class="link link-primary">My Account</a></li>
                    <li><a href="#!" class="link link-primary">Cart</a></li>
                    <li><a href="#!" class="link link-primary">Wishlist</a></li>
                    <li><a href="#!" class="link link-primary">Product Overview</a></li>
                    <li><a href="#!" class="link link-primary">Checkout</a></li>
                </ul>
            </div>
            <div class="col-span-12 md:col-span-4 lg:col-span-2">
                <h5 class="mb-5 drop-shadow-lg">Services</h5>
                <ul class="space-y-5">
                    <li><a href="#!" class="link link-primary">Privacy Policy</a></li>
                    <li><a href="#!" class="link link-primary">Refund Policy</a></li>
                    <li><a href="#!" class="link link-primary">Shipping & Return</a></li>
                    <li><a href="#!" class="link link-primary">Term & Condition</a></li>
                    <li><a href="#!" class="link link-primary">Help Center</a></li>
                </ul>
            </div>
            <div class="col-span-12 md:col-span-4 lg:col-span-3">
                <h5 class="mb-5 drop-shadow-lg">Our Store</h5>
                <p class="mb-4 text-gray-500 dark:text-dark-500">Find the nearest location to you. <a href="#!" class="text-gray-900 underline dark:text-dark-50">Visit our Stores</a></p>
                <p class="mb-1"><a href="tel:241012345678">+241 01234 5678</a></p>
                <p><a href="mailto:<EMAIL>"><EMAIL></a></p>
            </div>
        </div><!--end grid-->
        <div class="grid flex-wrap justify-between grid-cols-1 gap-5 py-6 text-center border-t border-gray-200 border-dashed md:grid-cols-3 dark:border-dark-800">
            <div class="flex justify-center gap-6 text-lg md:justify-start">
                <a href="#!" title="twitter" class="link link-sky"><i class="ri-twitter-x-line"></i></a>
                <a href="#!" title="instagram" class="link link-pink"><i class="ri-instagram-line"></i></a>
                <a href="#!" title="amazon" class="link link-green"><i class="ri-amazon-line"></i></a>
                <a href="#!" title="chrome" class="link link-red"><i class="ri-chrome-line"></i></a>
            </div>
            <div class="text-center">
                <p x-data="{ year: new Date().getFullYear() }" class="text-gray-500 dark:text-dark-500">
                    &copy; <span x-text="year"></span> Domiex. Crafted by <a href="#!" title="SRBThemes" class="font-semibold">SRBThemes</a>
                </p>
            </div><!--end col-->
            <div class="flex justify-center gap-5 text-lg md:justify-end">
                <a href="#!" title="Payment"><img src="assets/images/payment/american.png" alt="" class="h-6"></a>
                <a href="#!" title="Payment"><img src="assets/images/payment/mastercard.png" alt="" class="h-6"></a>
                <a href="#!" title="Payment"><img src="assets/images/payment/visa.png" alt="" class="h-6"></a>
            </div>
        </div><!--end grid-->
    </div>
</footer><!----------End Section------------>

<button x-on:click="let mode = document.querySelector('[data-mode]').getAttribute('data-mode');
let newMode = mode === 'light' ? 'dark' : 'light';
document.querySelector('[data-mode]').setAttribute('data-mode', newMode);" class="fixed flex items-center justify-center text-white ltr:right-0 rtl:left-0 bg-primary-500 ltr:rounded-l-md rtl:rounded-r-md size-12 top-1/2">
    <i data-lucide="moon" class="inline-block size-5 dark:hidden"></i>
    <i data-lucide="sun" class="hidden size-5 dark:inline-block"></i>
</button>

{{> partials/vendor-scripts }}

<script src="assets/libs/slick-image-compare/slick-image-compare.umd.js"></script>

<script>
    document.addEventListener("alpine:init", () => {
        Alpine.data("tabsHandler", () => ({
            activeTab: 1,
            sections: ["products", "new-arrivals", "service", "cta"],

            setActive(tab) {
                this.activeTab = tab;
                document.getElementById(this.sections[tab - 1]).scrollIntoView({ behavior: "smooth" });
            },

            updateTabOnScroll() {
                let scrollPosition = window.scrollY;
                this.sections.forEach((id, index) => {
                    let section = document.getElementById(id);
                    if (section) {
                        let offset = section.offsetTop - 120; // Adjust based on header size
                        let height = section.offsetHeight;
                        if (scrollPosition >= offset && scrollPosition < offset + height) {
                            this.activeTab = index + 1;
                        }
                    }
                });
            }
        }));
    });
</script>

<script type="module" src="assets/js/landing/ecommerce.init.js"></script>

<script type="module" src="assets/js/main.js"></script>

</body>

</html>