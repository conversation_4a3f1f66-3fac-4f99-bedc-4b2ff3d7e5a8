{{> partials/main }}

<head>

    {{> partials/title-meta title="Account Logs" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}

<div class="relative mb-6">
    <div class="relative overflow-hidden rounded-md h-44 bg-primary-500/10">
        <div class="border-[60px] border-t-primary-500 border-l-primary-500 absolute opacity-10 -top-2 left-0 rotate-45 size-96"></div>
        <div class="border-[60px] border-green-500 absolute opacity-10 top-20 left-8 rotate-45 size-80"></div>
        <div class="border-[60px] border-pink-500 absolute opacity-10 top-36 left-28 rotate-45 size-40"></div>
    </div>
    <div class="text-center">
        <div class="relative inline-block mx-auto">
            <div class="relative p-1 rounded-full bg-gradient-to-tr from-primary-300 via-red-300 to-green-300 -mt-14">
                <img src="assets/images/avatar/user-17.png" alt="" class="mx-auto border-4 border-white rounded-full dark:border-dark-900 size-28">
            </div>
            <div class="absolute border-2 border-white dark:border-dark-900 rounded-full size-4 bg-green-500 bottom-2.5 ltr:right-2.5 rtl:left-2.5"></div>
        </div>
        <h5 class="mt-2 mb-1">Sophia Mia <i data-lucide="badge-check" class="inline-block text-primary-500 fill-primary-500/20 size-5"></i></h5>
        <ul class="flex flex-wrap items-center justify-center gap-2 text-gray-500 dark:text-dark-500 text-14">
            <li><i data-lucide="building-2" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-middle">UI / UX Designer</span></li>
            <li><i data-lucide="map-pin" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-middle">Argentina</span></li>
            <li><i data-lucide="calendar-days" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-middle">24 April, 2024</span></li>
        </ul>
    </div>
</div>

<ul class="pb-2 overflow-x-auto tabs-pills lg:pb-0">
    <li>
        <a href="pages-account-settings.html" class="nav-item text-gray-500 dark:text-dark-500 [&.active]:bg-primary-500 [&.active]:text-primary-50">
            <i data-lucide="user-round" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-middle whitespace-nowrap">Account</span>
        </a>
    </li>
    <li>
        <a href="pages-account-security.html" class="nav-item text-gray-500 dark:text-dark-500 [&.active]:bg-primary-500 [&.active]:text-primary-50">
            <i data-lucide="shield-check" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-middle whitespace-nowrap">Security</span>
        </a>
    </li>
    <li>
        <a href="pages-account-billing-plan.html" class="nav-item text-gray-500 dark:text-dark-500 [&.active]:bg-primary-500 [&.active]:text-primary-50">
            <i data-lucide="gem" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-middle whitespace-nowrap">Billing & Plans</span>
        </a>
    </li>
    <li>
        <a href="pages-account-notification.html" class="nav-item text-gray-500 dark:text-dark-500 [&.active]:bg-primary-500 [&.active]:text-primary-50">
            <i data-lucide="bell" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-middle whitespace-nowrap">Notification</span>
        </a>
    </li>
    <li>
        <a href="pages-account-statements.html" class="nav-item text-gray-500 dark:text-dark-500 [&.active]:bg-primary-500 [&.active]:text-primary-50">
            <i data-lucide="list-tree" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-middle whitespace-nowrap">Statements</span>
        </a>
    </li>
    <li>
        <a href="pages-account-logs.html" class="nav-item text-gray-500 dark:text-dark-500 [&.active]:bg-primary-500 [&.active]:text-primary-50 active">
            <i data-lucide="log-out" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-middle whitespace-nowrap">Logs</span>
        </a>
    </li>
</ul>

<div class="mt-5 card">
    <div class="flex items-center gap-3 card-header">
        <h6 class="card-title grow">Device and active sessions</h6>
        <button type="button" data-modal-target="logoutAllModal" class="flex px-3 py-1.5 text-xs font-medium btn-sub-gray btn">All Logouts <i data-lucide="move-right" class="inline-block ml-2 size-4"></i></button>
    </div>
    <div class="card-body">
        <div class="grid grid-cols-1 gap-5 xl:grid-cols-2 2xl:grid-cols-3">
            <div class="mb-0 card">
                <div class="flex items-center gap-3 card-body">
                    <div class="flex items-center justify-center size-12 shrink-0">
                        <i data-lucide="monitor-smartphone" class="text-green-500 fill-green-500/10"></i>
                    </div>
                    <div class="grow">
                        <h6 class="mb-1">Apple Mac 12.5.1 <span class="text-xs ltr:ml-1 rtl:mr-1 badge badge-green">Online</span></h6>
                        <p class="text-sm text-gray-500 dark:text-dark-500">London, United Kingdom. - 81.64.22.98</p>
                    </div>
                    <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown shrink-0">
                        <button x-ref="button" x-on:click="toggle()" :aria-expanded="open.toString()" type="button" type="button" class="flex link link-primary">
                            <i data-lucide="chevron-right"></i>
                        </button>

                        <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden" dropdown-position="right">
                            <a href="#!" class="dropdown-item">
                                Overview
                            </a>
                            <a data-modal-target="logoutModal" href="#!" class="dropdown-item">
                                Log Out
                            </a>
                        </div>
                    </div>
                </div>
            </div><!--end card-->
            <div class="mb-0 card">
                <div class="flex items-center gap-3 card-body">
                    <div class="flex items-center justify-center size-12 shrink-0">
                        <i data-lucide="smartphone" class="text-gray-500 fill-gray-100 dark:text-dark-500 dark:fill-dark-850"></i>
                    </div>
                    <div class="grow">
                        <h6 class="mb-1">Apple iPhone iOS 14.8.5 <span class="text-xs ltr:ml-1 rtl:mr-1 badge badge-gray">Offline</span></h6>
                        <p class="text-sm text-gray-500 dark:text-dark-500">Berlin, Germany. - ***************</p>
                    </div>
                    <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown shrink-0">
                        <button x-ref="button" x-on:click="toggle()" :aria-expanded="open.toString()" type="button" type="button" class="flex link link-primary">
                            <i data-lucide="chevron-right"></i>
                        </button>

                        <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden" dropdown-position="right">
                            <a href="#!" class="dropdown-item">
                                Overview
                            </a>
                            <a data-modal-target="logoutModal" href="#!" class="dropdown-item">
                                Log Out
                            </a>
                        </div>
                    </div>
                </div>
            </div><!--end card-->
            <div class="mb-0 card">
                <div class="flex items-center gap-3 card-body">
                    <div class="flex items-center justify-center size-12 shrink-0">
                        <i data-lucide="smartphone" class="text-gray-500 fill-gray-100 dark:text-dark-500 dark:fill-dark-850"></i>
                    </div>
                    <div class="grow">
                        <h6 class="mb-1">Samsung Phone s24 <span class="text-xs ltr:ml-1 rtl:mr-1 badge badge-gray">Offline</span></h6>
                        <p class="text-sm text-gray-500 dark:text-dark-500">Paris, France. - ***************</p>
                    </div>
                    <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown shrink-0">
                        <button x-ref="button" x-on:click="toggle()" :aria-expanded="open.toString()" type="button" type="button" class="flex link link-primary">
                            <i data-lucide="chevron-right"></i>
                        </button>

                        <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden" dropdown-position="right">
                            <a href="#!" class="dropdown-item">
                                Overview
                            </a>
                            <a data-modal-target="logoutModal" href="#!" class="dropdown-item">
                                Log Out
                            </a>
                        </div>
                    </div>
                </div>
            </div><!--end card-->
            <div class="mb-0 card">
                <div class="flex items-center gap-3 card-body">
                    <div class="flex items-center justify-center size-12 shrink-0">
                        <i data-lucide="smartphone" class="text-gray-500 fill-gray-100 dark:text-dark-500 dark:fill-dark-850"></i>
                    </div>
                    <div class="grow">
                        <h6 class="mb-1">Apple iPhone iOS 14.8.5 <span class="text-xs ltr:ml-1 rtl:mr-1 badge badge-gray">Offline</span></h6>
                        <p class="text-sm text-gray-500 dark:text-dark-500">Berlin, Germany. - ***************</p>
                    </div>
                    <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown shrink-0">
                        <button x-ref="button" x-on:click="toggle()" :aria-expanded="open.toString()" type="button" type="button" class="flex link link-primary">
                            <i data-lucide="chevron-right"></i>
                        </button>

                        <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden" dropdown-position="right">
                            <a href="#!" class="dropdown-item">
                                Overview
                            </a>
                            <a data-modal-target="logoutModal" href="#!" class="dropdown-item">
                                Log Out
                            </a>
                        </div>
                    </div>
                </div>
            </div><!--end card-->
            <div class="mb-0 card">
                <div class="flex items-center gap-3 card-body">
                    <div class="flex items-center justify-center size-12 shrink-0">
                        <i data-lucide="smartphone" class="text-gray-500 fill-gray-100 dark:text-dark-500 dark:fill-dark-850"></i>
                    </div>
                    <div class="grow">
                        <h6 class="mb-1">Samsung Phone s24 <span class="text-xs ltr:ml-1 rtl:mr-1 badge badge-gray">Offline</span></h6>
                        <p class="text-sm text-gray-500 dark:text-dark-500">Paris, France. - ***************</p>
                    </div>
                    <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown shrink-0">
                        <button x-ref="button" x-on:click="toggle()" :aria-expanded="open.toString()" type="button" type="button" class="flex link link-primary">
                            <i data-lucide="chevron-right"></i>
                        </button>

                        <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden" dropdown-position="right">
                            <a href="#!" class="dropdown-item">
                                Overview
                            </a>
                            <a data-modal-target="logoutModal" href="#!" class="dropdown-item">
                                Log Out
                            </a>
                        </div>
                    </div>
                </div>
            </div><!--end card-->
        </div><!--end grid-->
    </div>
</div>

</div>
{{> partials/footer }}
</div>

<div id="logoutModal" class="!hidden modal show">
    <div class="modal-wrap modal-xs modal-center">
        <div class="text-center modal-content p-7">
            <div class="flex items-center justify-center mx-auto mb-4 bg-red-500 rounded-full size-12 text-red-50 ring-4 ring-red-500/10">
                <i data-lucide="log-out" class="size-6"></i>
            </div>
            <h5 class="mb-2">Logout This Device</h5>
            <p class="mb-5 text-gray-500 dark:text-dark-500">Are you sure you want to log out from this device?</p>
            <div class="flex items-center justify-end gap-2">
                <button class="btn btn-red">Logout Device</button>
                <button data-modal-close="logoutModal" class="btn link link-primary">Cancel</button>
            </div>
        </div>
    </div>
</div><!--end-->

<div id="logoutAllModal" class="!hidden modal show">
    <div class="modal-wrap modal-xs modal-center">
        <div class="text-center modal-content p-7">
            <div class="flex items-center justify-center mx-auto mb-4 bg-red-500 rounded-full size-12 text-red-50 ring-4 ring-red-500/10">
                <i data-lucide="log-out" class="size-6"></i>
            </div>
            <h5 class="mb-2">All Devices Logout</h5>
            <p class="mb-5 text-gray-500 dark:text-dark-500">Are you sure you want to log out from all device?</p>
            <div class="flex items-center justify-end gap-2">
                <button class="btn btn-red">Logout Device</button>
                <button data-modal-close="logoutAllModal" class="btn link link-primary">Cancel</button>
            </div>
        </div>
    </div>
</div><!--end-->

{{> partials/vendor-scripts }}

<script type="module" src="assets/js/main.js"></script>

</body>
</html>