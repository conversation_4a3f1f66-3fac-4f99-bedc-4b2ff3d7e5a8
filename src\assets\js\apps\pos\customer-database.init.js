import employeeListData from "../../../json/apps/pos/customer";

function employeeTable() {
    return {
        employees: [],
        filterEmployee: [],
        sortBy: 'name',
        searchTerm: '',
        sortDirection: 'asc',
        sortClasses: {
            'asc': '↑',
            'desc': '↓'
        },
        selectAll: false,
        selectedItems: [],
        currentPage: 1,
        itemsPerPage: 10,

        // Column visibility controls
        showLastOrder: true,
        showAddress: false,
        showCity: false,
        showCountry: false,
        showNotes: false,

        toggleAll() {
            this.selectedItems = this.selectAll ? [...this.filterEmployee] : [];
        },

        get totalPages() {
            return Math.ceil(this.filterEmployee.length / this.itemsPerPage);
        },

        get displayedEmployee() {
            const start = (this.currentPage - 1) * this.itemsPerPage;
            const end = start + this.itemsPerPage;
            return this.filterEmployee.slice(start, end);
        },

        get showingStart() {
            return Math.min((this.currentPage - 1) * this.itemsPerPage + 1, this.filterEmployee.length);
        },

        get showingEnd() {
            return Math.min(this.currentPage * this.itemsPerPage, this.filterEmployee.length);
        },

        init() {
            let startID = 31852;
            employeeListData.forEach((user, index) => {
                user.customerID = "SRBCU" + (startID + index).toString();
            });
            this.employees = employeeListData;
            this.filteredEmployee();
            this.sort('name');
        },

        filteredEmployee() {
            this.filterEmployee = this.employees;
            const searchTerm = this.searchTerm.trim().toLowerCase();
            if (searchTerm) {
                this.filterEmployee = this.filterEmployee.filter((employee) => {
                    return Object.values(employee).some(value =>
                        value && value.toString().toLowerCase().includes(searchTerm)
                    );
                });
            }
        },

        sort(column) {
            if (column === this.sortBy) {
                this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
            } else {
                this.sortDirection = 'asc';
                this.sortBy = column;
            }

            this.filterEmployee.sort((a, b) => {
                const valueA = a[column];
                const valueB = b[column];
                let comparison = 0;
                if (valueA > valueB) {
                    comparison = 1;
                } else if (valueA < valueB) {
                    comparison = -1;
                }
                return this.sortDirection === 'desc' ? comparison * -1 : comparison;
            });
        },

        prevPage() {
            if (this.currentPage > 1) {
                this.currentPage--;
            }
        },

        nextPage() {
            if (this.currentPage < this.totalPages) {
                this.currentPage++;
            }
        },

        gotoPage(page) {
            this.currentPage = page;
        },

        toggleAllColumns(show = true) {
            this.showAddress = show;
            this.showCity = show;
            this.showCountry = show;
            this.showRegistrationDate = show;
            this.showNotes = show;
        },

        get visibleColumnsCount() {
            let count = 9; // Base columns (always visible)
            if (this.showAddress) count++;
            if (this.showCity) count++;
            if (this.showCountry) count++;
            if (this.showRegistrationDate) count++;
            if (this.showNotes) count++;
            return count;
        },

        exportTable() {
            // Prepare table data for export
            let csvContent = "data:text/csv;charset=utf-8,";

            // Get visible columns based on current settings
            const visibleColumns = [
                'customerID',
                'name',
                'email',
                'phone',
                'totalOrders',
                ...(this.showLastOrder ? ['lastOrder'] : []),
                'outstanding',
                'status',
                ...(this.showAddress ? ['address'] : []),
                ...(this.showCity ? ['city'] : []),
                ...(this.showCountry ? ['country'] : []),
                ...(this.showNotes ? ['notes'] : [])
            ];

            // Add table headers
            csvContent += visibleColumns.join(",") + "\n";

            // Add table rows
            this.filterEmployee.forEach((employee) => {
                const values = visibleColumns.map(column => {
                    const value = employee[column];
                    // Handle values that might contain commas
                    return typeof value === 'string' && value.includes(',') ? `"${value}"` : value;
                });
                csvContent += values.join(",") + "\n";
            });

            // Encode CSV content and create a download link
            const encodedUri = encodeURI(csvContent);
            const link = document.createElement("a");
            link.setAttribute("href", encodedUri);
            link.setAttribute("download", "customer_database.csv");
            document.body.appendChild(link);

            // Trigger the download
            link.click();

            // Clean up
            document.body.removeChild(link);
        },

        exportExcel() {
            // Get visible columns based on current settings
            const visibleColumns = [
                'customerID',
                'name',
                'email',
                'phone',
                'totalOrders',
                ...(this.showLastOrder ? ['lastOrder'] : []),
                'outstanding',
                'status',
                ...(this.showAddress ? ['address'] : []),
                ...(this.showCity ? ['city'] : []),
                ...(this.showCountry ? ['country'] : []),
                ...(this.showNotes ? ['notes'] : [])
            ];

            // Create worksheet data
            const wsData = [
                visibleColumns, // Headers
                ...this.filterEmployee.map(employee =>
                    visibleColumns.map(column => employee[column])
                )
            ];

            // Create workbook and worksheet
            const wb = XLSX.utils.book_new();
            const ws = XLSX.utils.aoa_to_sheet(wsData);

            // Add worksheet to workbook
            XLSX.utils.book_append_sheet(wb, ws, "Customer Database");

            // Generate Excel file and trigger download
            XLSX.writeFile(wb, "customer_database.xlsx");
        }
    };
}

// Initialize Alpine.js
document.addEventListener('alpine:init', () => {
    Alpine.data('employeeTable', employeeTable);
});

// Initialize Lucide icons
document.addEventListener('DOMContentLoaded', function () {
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
});

