{{> partials/main }}

<head>

    {{> partials/title-meta title="Overview" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}
{{> partials/page-heading title="Overview" sub-title="Students" }}

<div class="grid grid-cols-12 gap-x-space">
    <div class="col-span-12 xl:col-span-8 2xl:col-span-9 xl:row-span-3">
        <div class="card">
            <div class="card-body">
                <div class="relative gap-4 mb-5 md:flex">
                    <img src="assets/images/avatar/user-14.png" alt="" class="rounded-md size-36 shrink-0">
                    <div class="mt-5 grow md:mt-0">
                        <h6 class="mb-2"><PERSON></h6>
                        <div class="flex flex-wrap gap-3 mb-2 whitespace-nowrap item-center">
                            <p class="text-gray-500 dark:text-dark-500"><i data-lucide="box" class="inline-block size-4 fill-gray-100 dark:fill-dark-850"></i> <span class="align-bottom">Class: 12 (A)</span></p>
                            <p class="text-gray-500 dark:text-dark-500"><i data-lucide="map-pin" class="inline-block size-4 fill-gray-100 dark:fill-dark-850"></i> <span class="align-bottom">California</span></p>
                            <p class="text-gray-500 dark:text-dark-500"><i data-lucide="calendar-check" class="inline-block size-4 fill-gray-100 dark:fill-dark-850"></i> <span class="align-bottom">05 Mar, 2007</span></p>
                        </div>
                        <p class="mb-2 text-gray-500 dark:text-dark-500"><i data-lucide="phone" class="inline-block size-4 fill-gray-100 dark:fill-dark-850"></i> <span class="align-bottom">****** 25 1525</span></p>
                        <p class="mb-3 text-gray-500 dark:text-dark-500"><i data-lucide="mail" class="inline-block size-4 fill-gray-100 dark:fill-dark-850"></i> <span class="align-bottom"><EMAIL></span></p>
                        <div class="flex flex-wrap gap-2 item-center">
                            <span class="badge badge-yellow">Learner (5)</span>
                            <span class="badge badge-green">Teacher (6)</span>
                            <span class="badge badge-purple">Skills (12)</span>
                        </div>
                    </div>
                    <div class="absolute top-0 shrink-0 ltr:right-0 rtl:left-0">
                        <a href="apps-school-students-admission.html" class="btn btn-sub-gray btn-icon"><i data-lucide="pencil" class="size-4"></i></a>
                    </div>
                </div>
                <div class="my-5">
                    <div class="grid grid-cols-12 gap-space">
                        <div class="col-span-12 sm:col-span-6 md:col-span-3">
                            <p class="mb-1 text-gray-500 dark:text-dark-500">Gender</p>
                            <h6>Male</h6>
                        </div>
                        <div class="col-span-12 sm:col-span-6 md:col-span-3">
                            <p class="mb-1 text-gray-500 dark:text-dark-500">Religion</p>
                            <h6>Islam</h6>
                        </div>
                        <div class="col-span-12 sm:col-span-6 md:col-span-3">
                            <p class="mb-1 text-gray-500 dark:text-dark-500">Father Occupation</p>
                            <h6>Web Developer</h6>
                        </div>
                        <div class="col-span-12 sm:col-span-6 md:col-span-3">
                            <p class="mb-1 text-gray-500 dark:text-dark-500">Admission Date</p>
                            <h6>15 Jun 2024</h6>
                        </div>
                        <div class="col-span-12 sm:col-span-6 md:col-span-3">
                            <p class="mb-1 text-gray-500 dark:text-dark-500">Roll No</p>
                            <h6>8</h6>
                        </div>
                        <div class="col-span-12 sm:col-span-6 md:col-span-3">
                            <p class="mb-1 text-gray-500 dark:text-dark-500">Father Name</p>
                            <h6>Mitchell Martin</h6>
                        </div>
                        <div class="col-span-12 sm:col-span-6 md:col-span-3">
                            <p class="mb-1 text-gray-500 dark:text-dark-500">Mother Name</p>
                            <h6>Theresa Martin</h6>
                        </div>
                        <div class="col-span-12 sm:col-span-6 md:col-span-3">
                            <p class="mb-1 text-gray-500 dark:text-dark-500">Parents Number</p>
                            <h6>+1 147 20 1478</h6>
                        </div>
                        <div class="col-span-12 md:col-span-6">
                            <div class="whitespace-normal">
                                <p class="mb-1 text-gray-500 dark:text-dark-500">Address</p>
                                <h6>1816 Angus Ave, Simi Valley, California</h6>
                            </div>
                        </div>
                    </div>
                </div>
                <p class="mb-4 text-gray-500 dark:text-dark-500">To achieve academic excellence and personal growth in Class 12 (A) by mastering the curriculum, developing critical thinking skills, and preparing for higher education and future career opportunities.</p>
                <h6 class="mb-3">Curriculum Mastery</h6>
                <ul class="space-y-2 list-inside list-circle">
                    <li class="text-gray-500 dark:text-dark-500">Thoroughly understand and excel in all subjects including Mathematics, Science (Physics, Chemistry, Biology), English, and Electives (such as Computer Science, Economics, History, etc.).</li>
                    <li class="text-gray-500 dark:text-dark-500">Regularly complete assignments, projects, and laboratory work to apply theoretical knowledge.</li>
                    <li class="text-gray-500 dark:text-dark-500">Engage in activities that enhance critical thinking, such as debates, discussions, and problem-solving exercises.</li>
                    <li class="text-gray-500 dark:text-dark-500">Apply logic and reasoning to solve complex problems in subjects like Mathematics and Science.</li>
                </ul>
            </div>
        </div>
    </div>
    <div class="col-span-12 lg:col-span-6 xl:col-span-4 2xl:col-span-3">
        <div class="card">
            <div class="flex items-center gap-4 card-body">
                <div class="grow">
                    <div class="flex items-center gap-3 mb-2.5">
                        <h6 class="grow">Complete Your Profile</h6>
                        <h6 class="text-xs font-semibold text-red-500">67.98%</h6>
                    </div>
                    <div class="bg-green-100 progress-bar progress-1">
                        <div class="w-[67.98%] text-white bg-green-500 progress-bar-wrap"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-span-12 lg:col-span-6 xl:col-span-4 2xl:col-span-3">
        <div class="card">
            <div class="card-body">
                <h6 class="mb-6">My Achievements and Milestones</h6>
                <div class="swiper mySwiper group/swiper">
                    <div class="swiper-wrapper">
                        <div class="swiper-slide">
                            <div class="py-3 text-center">
                                <div class="flex items-center justify-center p-2 mx-auto rounded-full bg-gradient-to-t from-yellow-500/10 ring-offset-2 dark:ring-offset-dark-900 size-28 ring-2 ring-yellow-500/10">
                                    <img src="assets/images/school/trophy.png" alt="" class="size-20">
                                </div>
                                <h6 class="mt-6">Academic Excellence Awards</h6>
                            </div>
                        </div>
                        <div class="swiper-slide">
                            <div class="py-3 text-center">
                                <div class="flex items-center justify-center p-2 mx-auto rounded-full bg-gradient-to-t from-green-500/10 ring-offset-2 dark:ring-offset-dark-900 size-28 ring-2 ring-green-500/10">
                                    <img src="assets/images/school/medal.png" alt="" class="size-20">
                                </div>
                                <h6 class="mt-6">Special Recognition Awards</h6>
                            </div>
                        </div>
                        <div class="swiper-slide">
                            <div class="py-3 text-center">
                                <div class="flex items-center justify-center p-2 mx-auto rounded-full bg-gradient-to-t from-yellow-500/10 ring-offset-2 dark:ring-offset-dark-900 size-28 ring-2 ring-yellow-500/10">
                                    <img src="assets/images/school/winner.png" alt="" class="size-20">
                                </div>
                                <h6 class="mt-6">Arts and Sports Awards</h6>
                            </div>
                        </div>
                    </div>
                    <div class="swiper-button-next after:font-remix after:text-2xl after:text-gray-800 dark:after:text-dark-100 opacity-0 group-hover/swiper:opacity-100 transition ease-linear duration-300 after:content-['\ea6e']"></div>
                    <div class="swiper-button-prev after:font-remix after:text-2xl after:text-gray-800 dark:after:text-dark-100 opacity-0 group-hover/swiper:opacity-100 transition ease-linear duration-300 after:content-['\ea64']"></div>
                </div>
            </div>
        </div>
    </div>
    <div x-data="upcomingExam" class="col-span-12 lg:col-span-6 xl:col-span-4 2xl:col-span-3 card">
        <div class="card-body">
            <div class="flex items-center justify-center mb-5 text-gray-500 bg-gray-100 rounded-md dark:text-dark-500 dark:bg-dark-850 size-14">
                <i data-lucide="graduation-cap"></i>
            </div>
            <h6 class="mb-1">Upcoming Test</h6>
            <div x-show="show" x-transition:enter="transition-opacity ease-out duration-500" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100" x-transition:leave="transition-opacity ease-in duration-500" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0">
                <p class="text-gray-500 dark:text-dark-500">
                    Your <span x-text="currentExam.subject" class="font-semibold"></span> Test will be on
                    <span x-text="currentExam.date" class="font-semibold"></span>
                </p>
            </div>
            <div class="flex items-center gap-2 mt-4">
                <button type="button" class="w-full btn btn-sub-gray">Learn More</button>
                <button type="button" class="w-full btn btn-primary" x-on:click="nextExam()">Next Exam</button>
            </div>
        </div>
    </div>
    <div class="col-span-12 md:col-span-6 xl:col-span-4 2xl:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Test Marks (Subject)</h6>
        </div>
        <div class="card-body">
            <div x-data="testMarksSubjectApp" dir="ltr">
                <div class="!min-h-full -ml-4" data-chart-colors="[bg-primary-300, bg-purple-300, bg-sky-300, bg-green-300, bg-red-200, bg-orange-200]" x-ref="testMarksSubjectChart"></div>
            </div>
        </div>
    </div>
    <div class="col-span-12 md:col-span-6 xl:col-span-4 2xl:col-span-3 card">
        <div class="card-header">
            <h6 class="card-title">Pending Quiz</h6>
        </div>
        <div class="card-body">
            <div class="space-y-4">
                <div class="flex items-center gap-3">
                    <div x-data="circleProgress(32)" x-init="animateProgress" class="relative size-12 shrink-0" dir="ltr">
                        <svg class="size-full" width="36" height="36" viewBox="0 0 36 36" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="18" cy="18" r="16" fill="none" class="stroke-current text-sky-500/15" stroke-width="3"></circle>
                            <g class="origin-center transform -rotate-90">
                                <circle cx="18" cy="18" r="16" fill="none" class="stroke-current text-sky-500" stroke-width="3" stroke-dasharray="100" :stroke-dashoffset="progress" style="transition: stroke-dashoffset 1s ease-out;"></circle>
                            </g>
                        </svg>
                        <!-- Percentage Text -->
                        <div class="absolute transform -translate-x-1/2 -translate-y-1/2 top-1/2 start-1/2">
                            <span class="text-xs font-bold text-center text-gray-800 dark:text-white" x-text="`${percent}%`"></span>
                        </div>
                    </div>
                    <div class="overflow-hidden grow">
                        <h6 class="mb-1 truncate"><a href="#!">Trivia Time: Fun Facts and Figures</a></h6>
                        <div class="flex items-center gap-3">
                            <p class="text-gray-500 grow dark:text-dark-500">Expert: <i class="text-yellow-500 ri-star-s-fill"></i> <i class="text-yellow-500 ri-star-s-fill"></i> <i class="text-yellow-500 ri-star-s-fill"></i></p>
                            <button type="button" class="btn btn-green px-2 py-0.5 text-11"><i class="ri-play-line"></i> Start</button>
                        </div>
                    </div>
                </div>
                <div class="flex items-center gap-3">
                    <div x-data="circleProgress(52)" x-init="animateProgress" class="relative size-12 shrink-0" dir="ltr">
                        <svg class="size-full" width="36" height="36" viewBox="0 0 36 36" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="18" cy="18" r="16" fill="none" class="stroke-current text-sky-500/15" stroke-width="3"></circle>
                            <g class="origin-center transform -rotate-90">
                                <circle cx="18" cy="18" r="16" fill="none" class="stroke-current text-sky-500" stroke-width="3" stroke-dasharray="100" :stroke-dashoffset="progress" style="transition: stroke-dashoffset 1s ease-out;"></circle>
                            </g>
                        </svg>
                        <!-- Percentage Text -->
                        <div class="absolute transform -translate-x-1/2 -translate-y-1/2 top-1/2 start-1/2">
                            <span class="text-xs font-bold text-center text-gray-800 dark:text-white" x-text="`${percent}%`"></span>
                        </div>
                    </div>
                    <div class="overflow-hidden grow">
                        <h6 class="mb-1 truncate"><a href="#!">Chemistry Conundrums: Elemental Quiz</a></h6>
                        <div class="flex items-center gap-3">
                            <p class="text-gray-500 grow dark:text-dark-500">Expert: <i class="text-yellow-500 ri-star-s-fill"></i> <i class="ri-star-s-fill"></i> <i class="ri-star-s-fill"></i></p>
                            <button type="button" class="btn btn-green px-2 py-0.5 text-11"><i class="ri-play-line"></i> Start</button>
                        </div>
                    </div>
                </div>
                <div class="flex items-center gap-3">
                    <div x-data="circleProgress(10)" x-init="animateProgress" class="relative size-12 shrink-0" dir="ltr">
                        <svg class="size-full" width="36" height="36" viewBox="0 0 36 36" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="18" cy="18" r="16" fill="none" class="stroke-current text-sky-500/15" stroke-width="3"></circle>
                            <g class="origin-center transform -rotate-90">
                                <circle cx="18" cy="18" r="16" fill="none" class="stroke-current text-sky-500" stroke-width="3" stroke-dasharray="100" :stroke-dashoffset="progress" style="transition: stroke-dashoffset 1s ease-out;"></circle>
                            </g>
                        </svg>
                        <!-- Percentage Text -->
                        <div class="absolute transform -translate-x-1/2 -translate-y-1/2 top-1/2 start-1/2">
                            <span class="text-xs font-bold text-center text-gray-800 dark:text-white" x-text="`${percent}%`"></span>
                        </div>
                    </div>
                    <div class="overflow-hidden grow">
                        <h6 class="mb-1 truncate"><a href="#!">A Mathematics Challenge</a></h6>
                        <div class="flex items-center gap-3">
                            <p class="text-gray-500 grow dark:text-dark-500">Expert: <i class="text-yellow-500 ri-star-s-fill"></i> <i class="text-yellow-500 ri-star-s-fill"></i> <i class="ri-star-s-fill"></i></p>
                            <button type="button" class="btn btn-green px-2 py-0.5 text-11"><i class="ri-play-line"></i> Start</button>
                        </div>
                    </div>
                </div>
                <div class="flex items-center gap-3">
                    <div x-data="circleProgress(95)" x-init="animateProgress" class="relative size-12 shrink-0" dir="ltr">
                        <svg class="size-full" width="36" height="36" viewBox="0 0 36 36" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="18" cy="18" r="16" fill="none" class="stroke-current text-sky-500/15" stroke-width="3"></circle>
                            <g class="origin-center transform -rotate-90">
                                <circle cx="18" cy="18" r="16" fill="none" class="stroke-current text-sky-500" stroke-width="3" stroke-dasharray="100" :stroke-dashoffset="progress" style="transition: stroke-dashoffset 1s ease-out;"></circle>
                            </g>
                        </svg>
                        <!-- Percentage Text -->
                        <div class="absolute transform -translate-x-1/2 -translate-y-1/2 top-1/2 start-1/2">
                            <span class="text-xs font-bold text-center text-gray-800 dark:text-white" x-text="`${percent}%`"></span>
                        </div>
                    </div>
                    <div class="overflow-hidden grow">
                        <h6 class="mb-1 truncate"><a href="#!">The Digital World Quiz</a></h6>
                        <div class="flex items-center gap-3">
                            <p class="text-gray-500 grow dark:text-dark-500">Expert: <i class="text-yellow-500 ri-star-s-fill"></i> <i class="text-yellow-500 ri-star-s-fill"></i> <i class="text-yellow-500 ri-star-s-fill"></i></p>
                            <button type="button" class="btn btn-green px-2 py-0.5 text-11"><i class="ri-play-line"></i> Start</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-span-12 md:col-span-6 xl:col-span-4 2xl:col-span-3 card">
        <div class="card-header">
            <h6 class="card-title">Upcoming Lecture</h6>
        </div>
        <div class="card-body">
            <div class="space-y-3">
                <div class="flex gap-3 item-center">
                    <div class="flex items-center justify-center rounded-md shrink-0 text-sky-500 bg-sky-500/10 size-10">
                        <i data-lucide="flask-conical" class="size-5"></i>
                    </div>
                    <div class="grow">
                        <h6>Chemistry</h6>
                        <p class="text-gray-500 dark:text-dark-500">09:00AM - 10:15AM</p>
                    </div>
                    <div class="shrink-0">
                        <a href="#!" class="btn btn-red btn-xs"><i class="ri-eye-line"></i> Live</a>
                    </div>
                </div>
                <div class="flex gap-3 item-center">
                    <div class="flex items-center justify-center text-purple-500 rounded-md bg-purple-500/10 shrink-0 size-10">
                        <i data-lucide="scale" class="size-5"></i>
                    </div>
                    <div class="grow">
                        <h6>Physics</h6>
                        <p class="text-gray-500 dark:text-dark-500">10:15AM - 11:30AM</p>
                    </div>
                    <div class="shrink-0">
                        <a href="#!" class="btn btn-sub-gray btn-xs"><i class="ri-eye-line"></i> Live</a>
                    </div>
                </div>
                <div class="flex gap-3 item-center">
                    <div class="flex items-center justify-center text-orange-500 rounded-md bg-orange-500/10 shrink-0 size-10">
                        <i data-lucide="atom" class="size-5"></i>
                    </div>
                    <div class="grow">
                        <h6>English</h6>
                        <p class="text-gray-500 dark:text-dark-500">11:30AM - 12:45PM</p>
                    </div>
                    <div class="shrink-0">
                        <a href="#!" class="btn btn-sub-gray btn-xs"><i class="ri-eye-line"></i> Live</a>
                    </div>
                </div>
                <div class="flex gap-3 item-center">
                    <div class="flex items-center justify-center text-green-500 rounded-md bg-green-500/10 shrink-0 size-10">
                        <i data-lucide="heart-pulse" class="size-5"></i>
                    </div>
                    <div class="grow">
                        <h6>Biology</h6>
                        <p class="text-gray-500 dark:text-dark-500">02:00PM - 03:15PM</p>
                    </div>
                    <div class="shrink-0">
                        <a href="#!" class="btn btn-sub-gray btn-xs"><i class="ri-eye-line"></i> Live</a>
                    </div>
                </div>
                <div class="flex gap-3 item-center">
                    <div class="flex items-center justify-center rounded-md shrink-0 text-primary-500 bg-primary-500/10 size-10">
                        <i data-lucide="japanese-yen" class="size-5"></i>
                    </div>
                    <div class="grow">
                        <h6>Mathematics</h6>
                        <p class="text-gray-500 dark:text-dark-500">03:15PM - 05:00PM</p>
                    </div>
                    <div class="shrink-0">
                        <a href="#!" class="btn btn-sub-gray btn-xs"><i class="ri-eye-line"></i> Live</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

</div>
{{> partials/footer }}
</div>


{{> partials/vendor-scripts }}

<!--apexcharts URL-->
<script src="assets/libs/dayjs/dayjs.min.js"></script>
<script src="assets/libs/dayjs/plugin/quarterOfYear.js"></script>

<script src="assets/libs/swiper/swiper-bundle.min.js"></script>

<script type="module" src="assets/js/apps/school/students/overview.init.js"></script>

<script type="module" src="assets/js/main.js"></script>

</body>
</html>