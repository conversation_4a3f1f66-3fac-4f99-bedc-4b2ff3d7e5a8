{{> partials/main }}

<head>

    {{> partials/title-meta title="Default" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}
{{> partials/page-heading title="Default" sub-title="Calendar" }}

<div x-data="calendarApp()" x-init="init()" @resize.window="handleResize">
    <div class="flex flex-wrap items-center gap-2 mb-space" id='external-events'>
        <div id='external-events' class="flex items-center gap-2 grow">
            <div class='block fc-event fc-h-event fc-daygrid-event fc-daygrid-block-event btn !btn-purple' draggable="true">
                <div class='fc-event-main'>Events</div>
            </div>
            <div class='block fc-event fc-h-event fc-daygrid-event fc-daygrid-block-event btn !btn-primary' draggable="true">
                <div class='fc-event-main'>Personal</div>
            </div>
            <div class='block fc-event fc-h-event fc-daygrid-event fc-daygrid-block-event btn !btn-green' draggable="true">
                <div class='fc-event-main'>Meeting</div>
            </div>
            <div class='block fc-event fc-h-event fc-daygrid-event fc-daygrid-block-event btn !btn-sky' draggable="true">
                <div class='fc-event-main'>Festival Function</div>
            </div>
        </div>

        <button type="button" class="btn btn-primary" id="newEvent" @click="openModal('add')" data-modal-target="addEventModal"><i data-lucide="circle-plus" class="inline-block size-4 ltr:mr-1 rtl:ml-1"></i> Add Event</button>
        <button class="hidden" id="editEvent" data-modal-target="addEventModal" @click="openModal('edit')"></button>
    </div>


    <div class="card">
        <div class="card-body">
            <div id="calendar">
            </div>
        </div>
    </div>


    <div id="addEventModal" class="!hidden modal show">

        <div class="modal-wrap modal-center modal-lg">
            <div class="modal-header">
                <h6 x-text="modalTitle"></h6>
                <button data-modal-close="addEventModal" class="link link-red"><i data-lucide="x" class="size-5"></i></button>
            </div>
            <div class="modal-content">
                <form id="eventForm" @submit.prevent="handleSubmit()">
                    <div class="grid grid-cols-12 gap-space">
                        <div class="col-span-12">
                            <label for="eventNameInput" class="form-label">Event Name</label>
                            <input type="text" id="eventNameInput" class="form-input" placeholder="Enter event name" x-model="form.eventName" @input="validateField('eventName' , form.eventName)">
                            <span x-show="errors.eventName" class="text-red-500" x-text="errors.eventName"></span>
                        </div>
                        <div class="col-span-4">
                            <label for="eventDateInput" class="form-label">Event Date</label>
                            <div class="relative group/form right">
                                <input data-provider="flatpickr" data-date-format="d M, Y" type="text" id="eventDateInput" class="ltr:pl-9 rtl:pr-9 form-input" placeholder="Select date" x-model="form.eventDate" @input="validateField('eventDate' , form.eventDate)" required>
                                <button class="absolute inset-y-0 flex items-center text-gray-500 dark:text-dark-500 ltr:left-3 rtl:right-3 focus:outline-hidden">
                                    <i data-lucide="calendar-check" class="size-4"></i>
                                </button>
                            </div>
                            <span x-show="errors.eventDate" class="text-red-500" x-text="errors.eventDate"></span>
                        </div>
                        <div class="col-span-4">
                            <label for="endEventDateInput" class="form-label">End Date</label>
                            <div class="relative group/form right">
                                <input data-provider="flatpickr" data-date-format="d M, Y" type="text" id="endEventDateInput" class="ltr:pl-9 rtl:pr-9 form-input" placeholder="Select date" x-model="form.endEventDate" @input="validateField('endEventDate' , form.endEventDate)" required>
                                <button class="absolute inset-y-0 flex items-center text-gray-500 dark:text-dark-500 ltr:left-3 rtl:right-3 focus:outline-hidden">
                                    <i data-lucide="calendar-check" class="size-4"></i>
                                </button>
                            </div>
                            <span x-show="errors.endEventDate" class="text-red-500" x-text="errors.endEventDate"></span>
                        </div>
                        <div class="col-span-4">
                            <label for="eventTimeInput" class="form-label">Event Time</label>
                            <div class="relative group/form right">
                                <input data-provider="timepickr" data-time-basic="true" type="text" id="eventTimeInput" class="ltr:pl-9 rtl:pr-9 form-input" placeholder="Select time" x-model="form.eventTime" @input="validateField('eventTime' , form.eventTime)" required>
                                <button class="absolute inset-y-0 flex items-center text-gray-500 dark:text-dark-500 ltr:left-3 rtl:right-3 focus:outline-hidden">
                                    <i data-lucide="clock" class="size-4"></i>
                                </button>
                            </div>
                            <span x-show="errors.eventTime" class="text-red-500" x-text="errors.eventTime"></span>
                        </div>
                        <div class="col-span-12">
                            <label for="locationInput" class="form-label">Color</label>
                            <div id="ColorSelect" x-model="form.class"></div>
                            <span x-show="errors.class" class="text-red-500" x-text="errors.class"></span>
                        </div>
                        <div class="col-span-12">
                            <label for="locationInput" class="form-label">Location</label>
                            <input type="text" id="locationInput" class="form-input" placeholder="Enter location" x-model="form.location" @input="validateField('location' , form.location)">
                            <span x-show="errors.location" class="text-red-500" x-text="errors.location"></span>
                        </div>
                        <div class="col-span-12">
                            <label for="guestInput" class="form-label">Add Guests</label>
                            <div class="relative">
                                <input type="email" id="guestInput" class="ltr:pr-14 rtl:pl-14 form-input" placeholder="<EMAIL>" x-model="form.guestEmail" @input="validateField('guestEmail' , form.guestEmail)">
                                <button type="button" @keydown.enter="addGuest()" @click="addGuest" id="addGuestButton" class="absolute btn ltr:right-1.5 rtl:left-1.5 top-1.5 btn-sub-primary btn-sm">Add</button>
                                <span x-show="errors.guestEmail" class="text-red-500" x-text="errors.guestEmail"></span>
                            </div>
                            <div class="flex gap-2 mt-2" id="guestList">
                                <template x-if="form.guests.length >= 0">
                                    <template x-for="(guest, index) in form.guests" :key="index">
                                        <div class="relative rounded-full size-9">
                                            <img :src="guest" alt="Guest Avatar" class="w-8 h-8 rounded-full">
                                            <a href="#!" @click="removeGuest(index)" class="absolute flex items-center justify-center text-white bg-gray-500 border-2 border-white rounded-full dark:bg-dark-500 dark:border-dark-900 size-4 -top-1 -right-1">
                                                <i class="text-xs ri-close-line"></i>
                                            </a>
                                        </div>
                                    </template>
                                </template>
                            </div>
                        </div>
                        <div class="col-span-12">
                            <div class="flex items-center justify-end gap-2">
                                <button type="button" id="closebutton" @click="closeModal()" data-modal-close="addEventModal" class="btn btn-active-red">Cancel</button>
                                <button type="submit" class="btn btn-primary" x-text="modalButton"></button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

</div>
{{> partials/footer }}
</div>

{{> partials/vendor-scripts }}

<script type="module" src="assets/js/apps/calendar/default.init.js"></script>

<script type="module" src="assets/js/main.js"></script>

</body>
</html>