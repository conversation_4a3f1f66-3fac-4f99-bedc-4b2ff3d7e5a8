{{> partials/main }}

<head>

    {{> partials/title-meta title="Sign Up" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

<div class="bg-gradient-to-t from-primary-500/80 to-primary-500 min-h-screen font-poppins flex items-center justify-center p-5 relative overflow-hidden dark:text-dark-800">
    <div class="absolute inset-0 overflow-hidden z-0 *:absolute">
        <div class="top-10 ltr:left-10 rtl:right-10 size-40 bg-white/10 rounded-full blur-2xl"></div>
        <div class="bottom-10 ltr:right-10 rtl:left-10 size-60 bg-primary-500/20 rounded-full blur-3xl"></div>
        <div class="top-1/3 ltr:left-1/4 rtl:right-1/4 size-20 bg-white/20 rounded-full blur-xl"></div>
        <div class="top-2/3 ltr:right-1/4 rtl:left-1/4 size-16 bg-primary-500/20 rounded-full blur-xl"></div>
        <div class="top-20 ltr:right-20 rtl:left-20 size-32 border-2 border-white/20 rounded-lg rotate-45 animate-ping"></div>
        <div class="bottom-40 ltr:left-20 rtl:right-20 size-24 border-2 border-white/20 rounded-full animate-ping"></div>
    </div>

    <div class="relative z-10 w-full max-w-md">
        <div class="relative">
            <div class="card bg-white/80 backdrop-blur-lg rounded-xl shadow-slate-900/10 border-white/50">
                <div class="card-body lg:p-8">
                    <div class="text-center mb-8 animate-slide-up">
                        <div class="inline-block relative">
                            <div class="absolute -top-2 -left-2 size-12 bg-primary-500/30 rounded-full blur-md"></div>
                            <a href="index.html" class="relative">
                                <img src="assets/images/main-logo.png" alt="" class="mx-auto d-block h-9">
                            </a>
                        </div>
                        <p class="text-gray-500 mt-3">Employee Login Portal</p>
                    </div>

                    <form id="loginForm" class="fade-down">
                        <div class="space-y-4">
                            <div class="relative">
                                <label for="employeeId" class="form-label hidden">
                                    Employee ID
                                </label>
                                <input type="text" id="employeeId" required class="form-input bg-white/30 border-slate-300 text-gray-800 rounded-xl h-14 ltr:pl-12 rtl:pr-12" placeholder="Employee ID">
                                <i data-lucide="user-round" class="size-6 ltr:left-4 rtl:right-4 absolute top-1/2 -translate-y-1/2 text-slate-500"></i>
                            </div>

                            <div class="relative">
                                <label for="password" class="form-label hidden">
                                    Password
                                </label>
                                <input type="password" id="password" required class="form-input bg-white/30 border-slate-300 text-gray-800 rounded-xl h-14 ltr:pl-12 rtl:pr-12" placeholder="Password">
                                <i data-lucide="lock" class="size-6 ltr:left-4 rtl:right-4 absolute top-1/2 -translate-y-1/2 text-slate-500"></i>
                            </div>
                        </div>

                        <div class="flex items-center justify-between my-6">
                            <div class="input-check-group">
                                <input id="rememberMe" class="input-check input-check-primary" type="checkbox">
                                <label for="rememberMe" class="input-check-label text-gray-800">Remember me</label>
                            </div>
                            <a href="#" data-modal-target="defaultModal" class="text-sm font-medium link text-gray-800 hover:text-primary-500 underline">
                                Forgot password?
                            </a>
                        </div>

                        <button type="submit" class="btn btn-primary w-full btn-lg hover:-translate-y-1">
                            <i data-lucide="log-in" class="inline-block ltr:mr-2 rtl:ml-2 size-4"></i>
                            Sign In
                        </button>
                    </form>
                    <div class="mt-8 text-center text-sm text-gray-500 animate-slide-up">
                        <span>Need help? </span>
                        <a href="#" class="link text-gray-800 hover:text-primary-500 font-medium">
                            Contact support
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <!-- Session Info-->
        <div id="sessionInfo" class="mt-4 p-3 bg-white/80 backdrop-blur-sm rounded-xl text-sm">
            <div class="flex justify-between items-center">
                <div class="grow">
                    <p class="mb-1">Session expires after 8 hours of inactivity</p>
                    <p>Last login: May 12, 2025 09:45 AM</p>
                </div>
                <div class="shrink-0">
                    <button id="extendSession" class="btn btn-sm btn-primary">Extend</button>
                </div>
            </div>
        </div>
        <!-- Footer -->
        <div x-data="{ year: new Date().getFullYear() }" class="mt-8 text-center text-white/70 text-sm animate-slide-up">
            &copy; <span x-text="year"></span> Domiex. Crafted by <a href="#!" class="font-semibold">SRBThemes</a>
        </div>
    </div>

    <div id="defaultModal" class="!hidden modal show">
        <div class="modal-wrap modal-center">
            <div class="modal-content">
                <h4 class="mb-3">Reset Password</h4>
                <p class="text-gray-500 dark:text-dark-500 mb-5">Enter your employee ID and we'll send you a password reset link.</p>

                <form id="resetPasswordForm">
                    <div>
                        <label for="resetEmployeeId" class="form-label hidden">Employee ID</label>
                        <div class="relative group/form">
                            <input type="text" id="resetEmployeeId" class="ltr:pl-9 rtl:pr-9 form-input ltr:group-[&.right]/form:pr-9 rtl:group-[&.right]/form:pl-9 ltr:group-[&.right]/form:pl-4 rtl:group-[&.right]/form:pr-4" placeholder="Employee ID" required>
                            <button class="absolute inset-y-0 flex items-center text-gray-500 dark:text-dark-500 ltr:left-3 rtl:right-3 ltr:group-[&.right]/form:right-3 rtl:group-[&.right]/form:left-3 ltr:group-[&.right]/form:left-auto rtl:group-[&.right]/form:right-auto focus:outline-hidden">
                                <i data-lucide="user-round" class="size-4"></i>
                            </button>
                        </div>
                    </div>
                    <div class="flex justify-end space-x-3 mt-6">
                        <button type="button" id="cancelResetPassword" class="btn btn-active-red">
                            Cancel
                        </button>
                        <button type="submit" class="btn btn-primary">
                            Send Reset Link
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

{{> partials/vendor-scripts }}

<script type="module" src="assets/js/ui/advanced-animation.init.js"></script>

<script type="module" src="assets/js/main.js"></script>

</body>

</html>