var menu  = [
        {
            "separator": true,
            "title": "Dashboards",
            "lang": "pe-dashboards",
            "children": []
        },
        {
            "title": "Dashboards",
            "lang": "pe-dashboards",
            "icon": "gauge",
            "link": "#",
            "separator": false,
            "children": [
                {
                    "title": "eCommerce",
                    "lang": "pe-ecommerce",
                    "link": "index.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Analytics",
                    "lang": "pe-analytics",
                    "link": "dashboards-analytics.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Email",
                    "lang": "pe-email",
                    "link": "dashboard-email.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "CRM",
                    "lang": "pe-crm",
                    "link": "dashboards-crm.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Hospital",
                    "lang": "pe-hospital",
                    "link": "dashboards-hospital.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "File Manager",
                    "lang": "pe-file-manager",
                    "link": "dashboards-file-manager.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Project",
                    "lang": "pe-project",
                    "link": "dashboards-projects.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "School",
                    "lang": "pe-school",
                    "link": "dashboards-school.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Music",
                    "lang": "pe-music",
                    "link": "dashboard-music.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Cashier Interface (POS)",
                    "lang": "pe-cashier-interface-pos",
                    "link": "dashboard-pos-user-interface.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "POS (Admin)",
                    "lang": "pe-pos-admin",
                    "link": "dashboard-pos-admin.html",
                    "dropdownPosition": null,
                    "children": []
                }
            ]
        },
        {
            "title": "Landing Page",
            "lang": "pe-landing-page",
            "icon": "box",
            "link": "#",
            "separator": false,
            "children": [
                {
                    "title": "eCommerce",
                    "lang": "pe-ecommerce",
                    "link": "landing-ecommerce.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Email",
                    "lang": "pe-email",
                    "link": "landing-email.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Invoice",
                    "lang": "pe-invoice",
                    "link": "landing-invoice.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "School",
                    "lang": "pe-school",
                    "link": "landing-school.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Doctors",
                    "lang": "pe-doctors",
                    "link": "landing-doctors.html",
                    "dropdownPosition": null,
                    "children": []
                }
            ]
        },
        {
            "separator": true,
            "title": "Apps",
            "lang": "pe-apps",
            "children": []
        },
        {
            "title": "Chat",
            "lang": "pe-chat",
            "icon": "messages-square",
            "link": "#",
            "separator": false,
            "children": [
                {
                    "title": "Default",
                    "lang": "pe-default",
                    "link": "apps-chat-default.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Group",
                    "lang": "pe-group",
                    "link": "apps-chat-group.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Video Call",
                    "lang": "pe-video-call",
                    "link": "apps-chat-group-video.html",
                    "dropdownPosition": null,
                    "children": []
                }
            ]
        },
        {
            "title": "Calendar",
            "lang": "pe-calendar",
            "icon": "calendar",
            "link": "#",
            "separator": false,
            "children": [
                {
                    "title": "Default",
                    "lang": "pe-default",
                    "link": "apps-calendar-default.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Week Number",
                    "lang": "pe-week-number",
                    "link": "apps-calendar-weeknumber.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Localize",
                    "lang": "pe-localize",
                    "link": "apps-calendar-localize.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Day View",
                    "lang": "pe-day-view",
                    "link": "apps-calendar-dayview.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "List View",
                    "lang": "pe-list-view",
                    "link": "apps-calendar-listview.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Time Grid View",
                    "lang": "pe-time-grid-view",
                    "link": "apps-calendar-timegrid.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Multi-Month Stack",
                    "lang": "pe-multi-month-stack",
                    "link": "apps-calendar-multi-month-stack.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Multi-Month Grid",
                    "lang": "pe-multi-month-grid",
                    "link": "apps-calendar-multi-month-grid.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Timeline",
                    "lang": "pe-timeline",
                    "link": "apps-calendar-timeline.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Date Nav Link",
                    "lang": "pe-date-nav-link",
                    "link": "apps-calendar-date-nav-link.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Clicking & Selecting",
                    "lang": "pe-clicking-selecting",
                    "link": "apps-calendar-date-clicking.html",
                    "dropdownPosition": null,
                    "children": []
                }
            ]
        },
        {
            "title": "Email",
            "lang": "pe-email",
            "icon": "mail",
            "link": "#",
            "separator": false,
            "children": [
                {
                    "title": "Mailbox",
                    "lang": "pe-mailbox",
                    "link": "apps-mailbox.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Templates",
                    "link": "#",
                    "lang": "pe-templates",
                    "children": [
                        {
                            "title": "Welcome",
                            "lang": "pe-welcome",
                            "link": "apps-email-templates-welcome.html",
                            "dropdownPosition": null,
                            "children": []
                        },
                        {
                            "title": "Newsletter",
                            "lang": "pe-newsletter",
                            "link": "apps-email-templates-newsletter.html",
                            "dropdownPosition": null,
                            "children": []
                        }
                    ]
                }
            ]
        },
        {
            "title": "Ecommerce",
            "lang": "pe-ecommerce",
            "icon": "shopping-bag",
            "link": "#",
            "separator": false,
            "children": [
                {
                    "title": "Products",
                    "link": "#",
                    "lang": "pe-products",
                    "children": [
                        {
                            "title": "List View",
                            "lang": "pe-list-view",
                            "link": "apps-ecommerce-products-list.html",
                            "dropdownPosition": null,
                            "children": []
                        },
                        {
                            "title": "Grid View",
                            "lang": "pe-grid-view",
                            "link": "apps-ecommerce-products-grid.html",
                            "dropdownPosition": null,
                            "children": []
                        },
                        {
                            "title": "Add New",
                            "lang": "pe-add-new",
                            "link": "apps-ecommerce-create-products.html",
                            "dropdownPosition": null,
                            "children": []
                        },
                        {
                            "title": "Overview",
                            "lang": "pe-overview",
                            "link": "apps-ecommerce-product-overview.html",
                            "dropdownPosition": null,
                            "children": []
                        },
                        {
                            "title": "Category List",
                            "lang": "pe-category-list",
                            "link": "apps-ecommerce-category.html",
                            "dropdownPosition": null,
                            "children": []
                        }
                    ]
                },
                {
                    "title": "Orders",
                    "link": "#",
                    "lang": "pe-orders",
                    "children": [
                        {
                            "title": "List View",
                            "lang": "pe-list-view",
                            "link": "apps-ecommerce-orders-list.html",
                            "dropdownPosition": null,
                            "children": []
                        },
                        {
                            "title": "Overview",
                            "lang": "pe-overview",
                            "link": "apps-ecommerce-orders-overview.html",
                            "dropdownPosition": null,
                            "children": []
                        },
                        {
                            "title": "Track",
                            "lang": "pe-track",
                            "link": "apps-ecommerce-orders-track.html",
                            "dropdownPosition": null,
                            "children": []
                        }
                    ]
                },
                {
                    "title": "Customers",
                    "link": "#",
                    "lang": "pe-customers",
                    "children": [
                        {
                            "title": "List View",
                            "lang": "pe-list-view",
                            "link": "apps-ecommerce-customer-list.html",
                            "dropdownPosition": null,
                            "children": []
                        },
                        {
                            "title": "Overview",
                            "lang": "pe-overview",
                            "link": "apps-ecommerce-customer-user.html",
                            "dropdownPosition": null,
                            "children": []
                        }
                    ]
                },
                {
                    "title": "Shop Cart",
                    "lang": "pe-shop-cart",
                    "link": "apps-ecommerce-shop-cart.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Checkout",
                    "lang": "pe-checkout",
                    "link": "apps-ecommerce-checkout.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Wishlist",
                    "lang": "pe-wishlist",
                    "link": "apps-ecommerce-wishlist.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Payment",
                    "lang": "pe-payment",
                    "link": "apps-ecommerce-payment.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Manage Reviews",
                    "lang": "pe-manage-reviews",
                    "link": "apps-ecommerce-manage-reviews.html",
                    "dropdownPosition": null,
                    "children": []
                }
            ]
        },
        {
            "title": "POS",
            "lang": "pe-pos",
            "icon": "store",
            "link": "#",
            "separator": false,
            "children": [
                {
                    "title": "Login Page",
                    "lang": "pe-login-page",
                    "link": "apps-pos-user-login.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Shopping Cart",
                    "lang": "pe-shopping-cart",
                    "link": "apps-pos-user-shopping-cart.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Inventory",
                    "lang": "pe-inventory",
                    "link": "apps-pos-inventory.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Employee",
                    "lang": "pe-employee",
                    "link": "apps-pos-employee.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Customer Database",
                    "lang": "pe-customer-database",
                    "link": "apps-pos-customer-database.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Sales Reports",
                    "lang": "pe-sales-reports",
                    "link": "apps-pos-sales-reports.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Promotions & Marketing",
                    "lang": "pe-promotions-marketing",
                    "link": "apps-pos-promotions-marketing.html",
                    "dropdownPosition": null,
                    "children": []
                },
            ]
        },
        {
            "title": "File Manager",
            "lang": "pe-file-manager",
            "icon": "folders",
            "link": "apps-file-manager.html",
            "separator": false,
            "dropdownPosition": null,
            "children": []
        },
        {
            "title": "Projects",
            "lang": "pe-projects",
            "icon": "monitor",
            "link": "#",
            "separator": false,
            "children": [
                {
                    "title": "List View",
                    "lang": "pe-list-view",
                    "link": "apps-projects-list.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Grid View",
                    "lang": "pe-grid-view",
                    "link": "apps-projects-grid.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Overview",
                    "lang": "pe-overview",
                    "link": "apps-projects-overview.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "RoadMap",
                    "lang": "pe-roadmap",
                    "link": "apps-projects-roadmap.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Task",
                    "lang": "pe-task",
                    "link": "apps-projects-task.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Files",
                    "lang": "pe-files",
                    "link": "apps-projects-files.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Users",
                    "lang": "pe-users",
                    "link": "apps-projects-users.html",
                    "dropdownPosition": null,
                    "children": []
                }
            ]
        },
        {
            "title": "CRM",
            "lang": "pe-crm",
            "icon": "shapes",
            "link": "#",
            "separator": false,
            "children": [
                {
                    "title": "Lead",
                    "lang": "pe-lead",
                    "link": "apps-crm-lead.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Contact",
                    "lang": "pe-contact",
                    "link": "apps-crm-contact.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Deal",
                    "lang": "pe-deal",
                    "link": "apps-crm-deal.html",
                    "dropdownPosition": null,
                    "children": []
                }
            ]
        },
        {
            "title": "Events",
            "lang": "pe-events",
            "icon": "trophy",
            "link": "#",
            "separator": false,
            "children": [
                {
                    "title": "Events List",
                    "lang": "pe-events-list",
                    "link": "apps-events-list.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Events Grid",
                    "lang": "pe-events-grid",
                    "link": "apps-events-grid.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Overview",
                    "lang": "pe-overview",
                    "link": "apps-event-overview.html",
                    "dropdownPosition": null,
                    "children": []
                }
            ]
        },
        {
            "title": "Hospital Management",
            "lang": "pe-hospital-management",
            "icon": "hospital",
            "link": "#",
            "separator": false,
            "children": [
                {
                    "title": "Patients",
                    "link": "#",
                    "lang": "pe-patients",
                    "children": [
                        {
                            "title": "List View",
                            "lang": "pe-list-view",
                            "link": "apps-hospital-patients-lists.html",
                            "dropdownPosition": null,
                            "children": []
                        },
                        {
                            "title": "Add Patient",
                            "lang": "pe-add-patients",
                            "link": "apps-hospital-patients-create.html",
                            "dropdownPosition": null,
                            "children": []
                        },
                        {
                            "title": "Patient Profile",
                            "lang": "pe-patients-profile",
                            "link": "apps-hospital-patients-overview.html",
                            "dropdownPosition": null,
                            "children": []
                        }
                    ]
                },
                {
                    "title": "Staff",
                    "link": "#",
                    "lang": "pe-staff",
                    "children": [
                        {
                            "title": "List View",
                            "lang": "pe-list-view",
                            "link": "apps-hospital-staff-lists.html",
                            "dropdownPosition": null,
                            "children": []
                        },
                        {
                            "title": "Leaves",
                            "lang": "pe-leaves",
                            "link": "apps-hospital-staff-leaves.html",
                            "dropdownPosition": null,
                            "children": []
                        },
                        {
                            "title": "Add Leave",
                            "lang": "pe-add-leave",
                            "link": "apps-hospital-staff-leave-add.html",
                            "dropdownPosition": null,
                            "children": []
                        },
                        {
                            "title": "Holidays",
                            "lang": "pe-holidays",
                            "link": "apps-hospital-staff-holidays.html",
                            "dropdownPosition": null,
                            "children": []
                        },
                        {
                            "title": "Attendance",
                            "lang": "pe-attendance",
                            "link": "apps-hospital-staff-attendance.html",
                            "dropdownPosition": null,
                            "children": []
                        }
                    ]
                },
                {
                    "title": "Appointments",
                    "link": "#",
                    "lang": "pe-appointments",
                    "children": [
                        {
                            "title": "List View",
                            "lang": "pe-list-view",
                            "link": "apps-hospital-appointments-lists.html",
                            "dropdownPosition": null,
                            "children": []
                        },
                        {
                            "title": "Book Appointment",
                            "lang": "pe-book-appointment",
                            "link": "apps-hospital-appointments-book.html",
                            "dropdownPosition": null,
                            "children": []
                        }
                    ]
                },
                {
                    "title": "Doctor Schedule",
                    "lang": "pe-doctor-schedule",
                    "link": "apps-hospital-doctor-schedule.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Departments",
                    "lang": "pe-departments",
                    "link": "apps-hospital-departments.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Payroll",
                    "link": "#",
                    "lang": "pe-payroll",
                    "children": [
                        {
                            "title": "Employee Salary",
                            "lang": "pe-employee-salary",
                            "link": "apps-hospital-payroll-employee-salary.html",
                            "dropdownPosition": null,
                            "children": []
                        },
                        {
                            "title": "Payslip",
                            "lang": "pe-payslip",
                            "link": "apps-hospital-payroll-payslip.html",
                            "dropdownPosition": null,
                            "children": []
                        }
                    ]
                }
            ]
        },
        {
            "title": "School",
            "lang": "pe-school",
            "icon": "school",
            "link": "#",
            "separator": false,
            "children": [
                {
                    "title": "Students",
                    "link": "#",
                    "lang": "pe-students",
                    "children": [
                        {
                            "title": "All Students",
                            "lang": "pe-all-students",
                            "link": "apps-school-students-list.html",
                            "dropdownPosition": null,
                            "children": []
                        },
                        {
                            "title": "Overview",
                            "lang": "pe-overview",
                            "link": "apps-school-students-overview.html",
                            "dropdownPosition": null,
                            "children": []
                        },
                        {
                            "title": "Admission Form",
                            "lang": "pe-admission-form",
                            "link": "apps-school-students-admission.html",
                            "dropdownPosition": null,
                            "children": []
                        }
                    ]
                },
                {
                    "title": "Teachers",
                    "link": "#",
                    "lang": "pe-teachers",
                    "children": [
                        {
                            "title": "All Teachers",
                            "lang": "pe-all-teachers",
                            "link": "apps-school-teachers-list.html",
                            "dropdownPosition": null,
                            "children": []
                        },
                        {
                            "title": "Overview",
                            "lang": "pe-overview",
                            "link": "apps-school-teachers-overview.html",
                            "dropdownPosition": null,
                            "children": []
                        },
                        {
                            "title": "Payroll",
                            "lang": "pe-payroll",
                            "link": "apps-school-teachers-payroll.html",
                            "dropdownPosition": null,
                            "children": []
                        }
                    ]
                },
                {
                    "title": "Parents",
                    "lang": "pe-parents",
                    "link": "apps-school-parents.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Library Book",
                    "lang": "pe-library-book",
                    "link": "apps-school-library-book.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Attendances",
                    "lang": "pe-attendances",
                    "link": "apps-school-attendances-students.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Exam",
                    "link": "#",
                    "lang": "pe-exam",
                    "children": [
                        {
                            "title": "Schedule",
                            "lang": "pe-schedule",
                            "link": "apps-school-exam-schedule.html",
                            "dropdownPosition": null,
                            "children": []
                        },
                        {
                            "title": "Exam Question",
                            "lang": "pe-exam-question",
                            "link": "apps-school-exam-question.html",
                            "dropdownPosition": null,
                            "children": []
                        }
                    ]
                }
            ]
        },
        {
            "title": "Invoice",
            "lang": "pe-invoice",
            "icon": "file-text",
            "link": "#",
            "separator": false,
            "children": [
                {
                    "title": "List View",
                    "lang": "pe-list-view",
                    "link": "apps-invoice-list.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Grid View",
                    "lang": "pe-grid-view",
                    "link": "apps-invoice-grid.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Add New",
                    "lang": "pe-add-new",
                    "link": "apps-invoice-create.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Overview 1",
                    "lang": "pe-overview-1",
                    "link": "apps-invoice-overview-1.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Overview 2",
                    "lang": "pe-overview-2",
                    "link": "apps-invoice-overview-2.html",
                    "dropdownPosition": null,
                    "children": []
                }
            ]
        },
        {
            "separator": true,
            "title": "Pages",
            "lang": "pe-pages",
            "children": []
        },
        {
            "title": "Authentication",
            "lang": "pe-authentication",
            "icon": "users-round",
            "link": "#",
            "separator": false,
            "children": [
                {
                    "title": "Sign In",
                    "link": "#",
                    "lang": "pe-sign-in",
                    "children": [
                        {
                            "title": "Basic",
                            "lang": "pe-basic",
                            "link": "auth-signin-basic.html",
                            "dropdownPosition": null,
                            "children": []
                        },
                        {
                            "title": "Modern",
                            "lang": "pe-modern",
                            "link": "auth-signin-modern.html",
                            "dropdownPosition": null,
                            "children": []
                        },
                        {
                            "title": "Creative",
                            "lang": "pe-creative",
                            "link": "auth-signin-creative.html",
                            "dropdownPosition": null,
                            "children": []
                        }
                    ]
                },
                {
                    "title": "Sign Up",
                    "link": "#",
                    "lang": "pe-sign-up",
                    "children": [
                        {
                            "title": "Basic",
                            "lang": "pe-basic",
                            "link": "auth-signup-basic.html",
                            "dropdownPosition": null,
                            "children": []
                        },
                        {
                            "title": "Modern",
                            "lang": "pe-modern",
                            "link": "auth-signup-modern.html",
                            "dropdownPosition": null,
                            "children": []
                        },
                        {
                            "title": "Creative",
                            "lang": "pe-creative",
                            "link": "auth-signup-creative.html",
                            "dropdownPosition": null,
                            "children": []
                        }
                    ]
                },
                {
                    "title": "Forgot Password",
                    "link": "#",
                    "lang": "pe-forgot-password",
                    "children": [
                        {
                            "title": "Basic",
                            "lang": "pe-basic",
                            "link": "auth-forgot-password-basic.html",
                            "dropdownPosition": null,
                            "children": []
                        },
                        {
                            "title": "Modern",
                            "lang": "pe-modern",
                            "link": "auth-forgot-password-modern.html",
                            "dropdownPosition": null,
                            "children": []
                        },
                        {
                            "title": "Creative",
                            "lang": "pe-creative",
                            "link": "auth-forgot-password-creative.html",
                            "dropdownPosition": null,
                            "children": []
                        }
                    ]
                },
                {
                    "title": "Two Step Verification",
                    "link": "#",
                    "lang": "pe-two-step-verification",
                    "children": [
                        {
                            "title": "Basic",
                            "lang": "pe-basic",
                            "link": "auth-two-step-verification-basic.html",
                            "dropdownPosition": null,
                            "children": []
                        },
                        {
                            "title": "Modern",
                            "lang": "pe-modern",
                            "link": "auth-two-step-verification-modern.html",
                            "dropdownPosition": null,
                            "children": []
                        },
                        {
                            "title": "Creative",
                            "lang": "pe-creative",
                            "link": "auth-two-step-verification-creative.html",
                            "dropdownPosition": null,
                            "children": []
                        }
                    ]
                },
                {
                    "title": "Reset Password",
                    "link": "#",
                    "lang": "pe-reset-password",
                    "children": [
                        {
                            "title": "Basic",
                            "lang": "pe-basic",
                            "link": "auth-reset-password-basic.html",
                            "dropdownPosition": null,
                            "children": []
                        },
                        {
                            "title": "Modern",
                            "lang": "pe-modern",
                            "link": "auth-reset-password-modern.html",
                            "dropdownPosition": null,
                            "children": []
                        },
                        {
                            "title": "Creative",
                            "lang": "pe-creative",
                            "link": "auth-reset-password-creative.html",
                            "dropdownPosition": null,
                            "children": []
                        }
                    ]
                },
                {
                    "title": "Successful Password",
                    "link": "#",
                    "lang": "pe-successful-password",
                    "children": [
                        {
                            "title": "Basic",
                            "lang": "pe-basic",
                            "link": "auth-successful-password-basic.html",
                            "dropdownPosition": null,
                            "children": []
                        },
                        {
                            "title": "Modern",
                            "lang": "pe-modern",
                            "link": "auth-successful-password-modern.html",
                            "dropdownPosition": null,
                            "children": []
                        },
                        {
                            "title": "Creative",
                            "lang": "pe-creative",
                            "link": "auth-successful-password-creative.html",
                            "dropdownPosition": null,
                            "children": []
                        }
                    ]
                },
                {
                    "title": "Account Deactivation",
                    "link": "#",
                    "lang": "pe-account-deactivation",
                    "children": [
                        {
                            "title": "Basic",
                            "lang": "pe-basic",
                            "link": "auth-account-deactivation-basic.html",
                            "dropdownPosition": null,
                            "children": []
                        },
                        {
                            "title": "Modern",
                            "lang": "pe-modern",
                            "link": "auth-account-deactivation-modern.html",
                            "dropdownPosition": null,
                            "children": []
                        },
                        {
                            "title": "Creative",
                            "lang": "pe-creative",
                            "link": "auth-account-deactivation-creative.html",
                            "dropdownPosition": null,
                            "children": []
                        }
                    ]
                }
            ]
        },
        {
            "title": "Pages",
            "lang": "pe-pages",
            "icon": "box",
            "link": "#",
            "separator": false,
            "children": [
                {
                    "title": "Blank",
                    "lang": "pe-blank",
                    "link": "pages-starter.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Account",
                    "link": "#",
                    "lang": "pe-account",
                    "children": [
                        {
                            "title": "Account",
                            "lang": "pe-account",
                            "link": "pages-account-settings.html",
                            "dropdownPosition": null,
                            "children": []
                        },
                        {
                            "title": "Security",
                            "lang": "pe-security",
                            "link": "pages-account-security.html",
                            "dropdownPosition": null,
                            "children": []
                        },
                        {
                            "title": "Billing & Plans",
                            "lang": "pe-billing-plans",
                            "link": "pages-account-billing-plan.html",
                            "dropdownPosition": null,
                            "children": []
                        },
                        {
                            "title": "Notification",
                            "lang": "pe-notification",
                            "link": "pages-account-notification.html",
                            "dropdownPosition": null,
                            "children": []
                        },
                        {
                            "title": "Statements",
                            "lang": "pe-statements",
                            "link": "pages-account-statements.html",
                            "dropdownPosition": null,
                            "children": []
                        },
                        {
                            "title": "Logs",
                            "lang": "pe-logs",
                            "link": "pages-account-logs.html",
                            "dropdownPosition": null,
                            "children": []
                        }
                    ]
                },
                {
                    "title": "User Profile",
                    "link": "#",
                    "lang": "pe-user-profile",
                    "children": [
                        {
                            "title": "Overview",
                            "lang": "pe-overview",
                            "link": "pages-user.html",
                            "dropdownPosition": null,
                            "children": []
                        },
                        {
                            "title": "Activity",
                            "lang": "pe-activity",
                            "link": "pages-user-activity.html",
                            "dropdownPosition": null,
                            "children": []
                        },
                        {
                            "title": "Followers",
                            "lang": "pe-followers",
                            "link": "pages-user-followers.html",
                            "dropdownPosition": null,
                            "children": []
                        },
                        {
                            "title": "Documents",
                            "lang": "pe-documents",
                            "link": "pages-user-documents.html",
                            "dropdownPosition": null,
                            "children": []
                        },
                        {
                            "title": "Notes",
                            "lang": "pe-notes",
                            "link": "pages-user-notes.html",
                            "dropdownPosition": null,
                            "children": []
                        },
                        {
                            "title": "Projects",
                            "lang": "pe-projects",
                            "link": "pages-user-projects.html",
                            "dropdownPosition": null,
                            "children": []
                        }
                    ]
                },
                {
                    "title": "Pricing",
                    "link": "#",
                    "lang": "pe-pricing",
                    "children": [
                        {
                            "title": "User",
                            "lang": "pe-user",
                            "link": "pages-pricing.html",
                            "dropdownPosition": null,
                            "children": []
                        },
                        {
                            "title": "Admin",
                            "lang": "pe-admin",
                            "link": "pages-pricing-admin.html",
                            "dropdownPosition": null,
                            "children": []
                        }
                    ]
                },
                {
                    "title": "Contact Us",
                    "lang": "pe-contact-us",
                    "link": "pages-contact-us.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "FAQ's",
                    "lang": "pe-faqs",
                    "link": "pages-faq.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Licenses",
                    "lang": "pe-licenses",
                    "link": "pages-licenses.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Coming Soon",
                    "lang": "pe-coming-soon",
                    "link": "pages-coming-soon.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Maintenance",
                    "lang": "pe-maintenance",
                    "link": "pages-maintenance.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Privacy Policy",
                    "lang": "pe-privacy-policy",
                    "link": "pages-privacy-policy.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Help Center",
                    "lang": "pe-help-center",
                    "link": "pages-help-center.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Error Pages",
                    "link": "#",
                    "lang": "pe-error-pages",
                    "children": [
                        {
                            "title": "404",
                            "lang": "pe-404",
                            "link": "pages-404.html",
                            "dropdownPosition": null,
                            "children": []
                        },
                        {
                            "title": "500",
                            "lang": "pe-500",
                            "link": "pages-500.html",
                            "dropdownPosition": null,
                            "children": []
                        }
                    ]
                }
            ]
        },
        {
            "title": "Widgets",
            "lang": "pe-widgets",
            "icon": "align-start-vertical",
            "link": "#",
            "separator": false,
            "children": [
                {
                    "title": "Cards",
                    "lang": "pe-cards",
                    "link": "widgets-cards.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Banners",
                    "lang": "pe-banners",
                    "link": "widgets-banners.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Charts",
                    "lang": "pe-charts",
                    "link": "widgets-charts.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Data",
                    "lang": "pe-data",
                    "link": "widgets-data.html",
                    "dropdownPosition": null,
                    "children": []
                }
            ]
        },
        {
            "separator": true,
            "title": "Components",
            "lang": "pe-components",
            "children": []
        },
        {
            "title": "UI Elements",
            "lang": "pe-ui-elements",
            "icon": "key-round",
            "link": "#",
            "separator": false,
            "megaMenu": true,
            "children": [
                {
                    "title": "Alerts",
                    "lang": "pe-alerts",
                    "link": "ui-alerts.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Badges",
                    "lang": "pe-badges",
                    "link": "ui-badge.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Breadcrumb",
                    "lang": "pe-breadcrumb",
                    "link": "ui-breadcrumb.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Buttons Group",
                    "lang": "pe-buttons-group",
                    "link": "ui-buttons-group.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Buttons",
                    "lang": "pe-buttons",
                    "link": "ui-buttons.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Buttons Navigation",
                    "lang": "pe-buttons-navigation",
                    "link": "ui-button-navigation.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Dropdown",
                    "lang": "pe-dropdown",
                    "link": "ui-dropdown.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Loader",
                    "lang": "pe-loader",
                    "link": "ui-loader.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Accordion",
                    "lang": "pe-accordion",
                    "link": "ui-accordion.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Modal",
                    "lang": "pe-modal",
                    "link": "ui-modal.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Links",
                    "lang": "pe-links",
                    "link": "ui-links.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Tabs",
                    "lang": "pe-tabs",
                    "link": "ui-tabs.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Drawer",
                    "lang": "pe-drawer",
                    "link": "ui-drawer.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Pagination",
                    "lang": "pe-pagination",
                    "link": "ui-pagination.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Progress Bar",
                    "lang": "pe-progress-bar",
                    "link": "ui-progress-bar.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Tooltips",
                    "lang": "pe-tooltips",
                    "link": "ui-tooltips.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Cards",
                    "lang": "pe-cards",
                    "link": "ui-cards.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Timeline",
                    "lang": "pe-timeline",
                    "link": "ui-timeline.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Notification",
                    "lang": "pe-notification",
                    "link": "ui-notification.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "List Group",
                    "lang": "pe-list-group",
                    "link": "ui-list-group.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Cookie Consent",
                    "lang": "pe-cookie-consent",
                    "link": "ui-cookie.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Gallery",
                    "lang": "pe-gallery",
                    "link": "ui-gallery.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Video",
                    "lang": "pe-video",
                    "link": "ui-video.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Colors",
                    "lang": "pe-colors",
                    "link": "ui-colors.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Typography",
                    "lang": "pe-typography",
                    "link": "ui-typography.html",
                    "dropdownPosition": null,
                    "children": []
                }
            ]
        },
        {
            "title": "Advanced UI",
            "lang": "pe-advanced-ui",
            "icon": "gem",
            "link": "#",
            "separator": false,
            "children": [
                {
                    "title": "Animation",
                    "lang": "pe-animation",
                    "link": "ui-advanced-animation.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Simplebar",
                    "lang": "pe-simplebar",
                    "link": "ui-advanced-simplebar.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Swiper",
                    "lang": "pe-swiper",
                    "link": "ui-advanced-swiper.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "3D Effect",
                    "lang": "pe-3d-effect",
                    "link": "ui-advanced-3d-effect.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Word Counter",
                    "lang": "pe-word-counter",
                    "link": "ui-advanced-word-counter.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Chat Bot",
                    "lang": "pe-chat-bot",
                    "link": "ui-advanced-bot.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Images Annotation",
                    "lang": "pe-images-annotation",
                    "link": "ui-advanced-image-annotation.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Tree Map",
                    "lang": "pe-tree-map",
                    "link": "ui-advanced-tree.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Highlight Code",
                    "lang": "pe-highlight-code",
                    "link": "ui-advanced-highlight.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Mask Input",
                    "lang": "pe-mask-input",
                    "link": "ui-advanced-mask.html",
                    "dropdownPosition": null,
                    "children": []
                }
            ]
        },
        {
            "title": "Icons",
            "lang": "pe-icons",
            "icon": "pencil-ruler",
            "link": "#",
            "separator": false,
            "children": [
                {
                    "title": "Lucide",
                    "lang": "pe-lucide",
                    "link": "icons-lucide.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Remix Icons",
                    "lang": "pe-remix-icons",
                    "link": "icons-remix.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Heroicons",
                    "lang": "pe-heroicons",
                    "link": "icons-heroicons.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Boxicon",
                    "lang": "pe-boxicon",
                    "link": "icons-boxicon.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Line Awesome",
                    "lang": "pe-line-awesome",
                    "link": "icons-line-awesome.html",
                    "dropdownPosition": null,
                    "children": []
                }
            ]
        },
        {
            "separator": true,
            "title": "Forms & Tables",
            "lang": "pe-forms-tables",
            "children": []
        },
        {
            "title": "Forms",
            "lang": "pe-forms",
            "icon": "book-open",
            "link": "#",
            "separator": false,
            "children": [
                {
                    "title": "Basic Input",
                    "lang": "pe-basic-input",
                    "link": "form-basic-input.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Input Group",
                    "lang": "pe-input-group",
                    "link": "form-input-group.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "File Upload",
                    "lang": "pe-file-upload",
                    "link": "form-file-input.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Select",
                    "lang": "pe-select",
                    "link": "form-select.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Pickers",
                    "lang": "pe-pickers",
                    "link": "form-pickers.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Sliders",
                    "lang": "pe-sliders",
                    "link": "form-range.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Switches",
                    "lang": "pe-switches",
                    "link": "form-switches.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Checkbox & Radio",
                    "lang": "pe-checkbox-radio",
                    "link": "form-checkbox-radio.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Input Spin",
                    "lang": "pe-input-spin",
                    "link": "form-input-spin.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "reCAPTCHA",
                    "lang": "pe-recaptcha",
                    "link": "form-recaptcha.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Autosize",
                    "lang": "pe-autosize",
                    "link": "form-autosize.html",
                    "dropdownPosition": null,
                    "children": []
                }
            ]
        },
        {
            "title": "Editors",
            "lang": "pe-editors",
            "icon": "remove-formatting",
            "link": "form-editors.html",
            "separator": false,
            "dropdownPosition": null,
            "children": []
        },
        {
            "title": "Clipboard",
            "lang": "pe-clipboard",
            "icon": "clipboard",
            "link": "form-clipboard.html",
            "separator": false,
            "dropdownPosition": null,
            "children": []
        },
        {
            "title": "Form Wizard",
            "lang": "pe-form-wizard",
            "icon": "text-quote",
            "link": "form-wizard-basic.html",
            "separator": false,
            "dropdownPosition": null,
            "children": []
        },
        {
            "title": "Tables",
            "lang": "pe-tables",
            "icon": "table-2",
            "separator": false,
            "link": "#",
            "children": [
                {
                    "title": "Base",
                    "lang": "pe-base",
                    "link": "table-base.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Data Tables",
                    "link": "#",
                    "lang": "pe-data-tables",
                    "children": [
                        {
                            "title": "Basic",
                            "lang": "pe-basic",
                            "link": "table-datatables-basic.html",
                            "dropdownPosition": null,
                            "children": []
                        },
                        {
                            "title": "Bordered",
                            "lang": "pe-bordered",
                            "link": "table-datatables-bordered.html",
                            "dropdownPosition": null,
                            "children": []
                        },
                        {
                            "title": "Stripe",
                            "lang": "pe-stripe",
                            "link": "table-datatables-stripe.html",
                            "dropdownPosition": null,
                            "children": []
                        },
                        {
                            "title": "Hover Effect",
                            "lang": "pe-hover-effect",
                            "link": "table-datatables-hover.html",
                            "dropdownPosition": null,
                            "children": []
                        },
                        {
                            "title": "Row Grouping",
                            "lang": "pe-row-grouping",
                            "link": "table-datatables-row-grouping.html",
                            "dropdownPosition": null,
                            "children": []
                        },
                        {
                            "title": "Feature enable / disable",
                            "lang": "pe-feature-enable-disable",
                            "link": "table-datatables-enable-disable.html",
                            "dropdownPosition": null,
                            "children": []
                        }
                    ]
                }
            ]
        },
        {
            "separator": true,
            "title": "Charts & Maps",
            "lang": "pe-charts-maps",
            "children": []
        },
        {
            "title": "Apexcharts",
            "lang": "pe-apexcharts",
            "icon": "bar-chart-3",
            "separator": false,
            "link": "#",
            "children": [
                {
                    "title": "Area",
                    "lang": "pe-area",
                    "link": "apexchart-area.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Bar",
                    "lang": "pe-bar",
                    "link": "apexchart-bar.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Box Whisher",
                    "lang": "pe-box-whisher",
                    "link": "apexchart-box-whisker.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Bubble",
                    "lang": "pe-bubble",
                    "link": "apexchart-bubble.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Candlestick",
                    "lang": "pe-candlestick",
                    "link": "apexchart-candlestick.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Column",
                    "lang": "pe-column",
                    "link": "apexchart-column.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Funnel",
                    "lang": "pe-funnel",
                    "link": "apexchart-funnel.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Heatmap",
                    "lang": "pe-heatmap",
                    "link": "apexchart-heatmap.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Line",
                    "lang": "pe-line",
                    "link": "apexchart-line.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Mixed",
                    "lang": "pe-mixed",
                    "link": "apexchart-mixed.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Pie",
                    "lang": "pe-pie",
                    "link": "apexchart-pie.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Polar Area",
                    "lang": "pe-polar-area",
                    "link": "apexchart-polar-area.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Radar",
                    "lang": "pe-radar",
                    "link": "apexchart-radar.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Radialbar",
                    "lang": "pe-radialbar",
                    "link": "apexchart-radialbar.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Range Area",
                    "lang": "pe-range-area",
                    "link": "apexchart-range-area.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Scatter",
                    "lang": "pe-scatter",
                    "link": "apexchart-scatter.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Slope",
                    "lang": "pe-slope",
                    "link": "apexchart-slope.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Timeline",
                    "lang": "pe-timeline",
                    "link": "apexchart-timeline.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Treemap",
                    "lang": "pe-treemap",
                    "link": "apexchart-treemap.html",
                    "dropdownPosition": null,
                    "children": []
                }
            ]
        },
        {
            "title": "Apextree",
            "lang": "pe-apextree",
            "icon": "trending-up-down",
            "separator": false,
            "link": "#",
            "children": [
                {
                    "title": "Top to Bottom",
                    "lang": "pe-top-to-bottom",
                    "link": "apextree-top-bottom.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Bottom to Top",
                    "lang": "pe-bottom-to-top",
                    "link": "apextree-bottom-top.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Left to Right",
                    "lang": "pe-left-to-right",
                    "link": "apextree-left-right.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Right to Left",
                    "lang": "pe-right-to-left",
                    "link": "apextree-right-left.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Collapse Expand",
                    "lang": "pe-collapse-expand",
                    "link": "apextree-collapse-expand.html",
                    "dropdownPosition": null,
                    "children": []
                }
            ]
        },
        {
            "title": "ApexSankey",
            "lang": "pe-apexsankey",
            "icon": "dna",
            "separator": false,
            "link": "charts-apexsankey.html",
            "dropdownPosition": null,
            "children": []
        },
        {
            "title": "Echarts",
            "lang": "pe-echarts",
            "icon": "scatter-chart",
            "separator": false,
            "link": "#",
            "children": [
                {
                    "title": "Bar",
                    "lang": "pe-bar",
                    "link": "echart-bar.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Line",
                    "lang": "pe-line",
                    "link": "echart-line.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Pie",
                    "lang": "pe-pie",
                    "link": "echart-pie.html",
                    "dropdownPosition": null,
                    "children": []
                }
            ]
        },
        {
            "title": "Maps",
            "lang": "pe-maps",
            "icon": "map",
            "separator": false,
            "link": "#",
            "children": [
                {
                    "title": "Google Maps",
                    "lang": "pe-google-maps",
                    "link": "maps-google.html",
                    "dropdownPosition": null,
                    "children": []
                },
                {
                    "title": "Vector",
                    "lang": "pe-vector",
                    "link": "maps-vector.html",
                    "dropdownPosition": null,
                    "children": []
                }
            ]
        },
        {
            "separator": true,
            "title": "Docs & ChangeLog",
            "lang": "pe-docs-changeLog",
            "children": []
        },
        {
            "title": "Support",
            "lang": "pe-support",
            "icon": "life-buoy",
            "separator": false,
            "link": "https://1.envato.market/domiex-admin-dashboard-support",
            "dropdownPosition": null,
            "children": []
        },
        {
            "title": "Documentation",
            "lang": "pe-documentation",
            "icon": "file-text",
            "separator": false,
            "link": "https://srbthemes.kcubeinfotech.com/domiex/docs/html/index.html",
            "dropdownPosition": null,
            "children": []
        },
        {
            "title": "ChangeLog",
            "lang": "pe-changelog",
            "icon": "feather",
            "separator": false,
            "link": "https://srbthemes.kcubeinfotech.com/domiex/live/changelog.html",
            "dropdownPosition": null,
            "children": []
        }
    ];

export default menu;