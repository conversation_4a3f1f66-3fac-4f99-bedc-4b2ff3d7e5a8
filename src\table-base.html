{{> partials/main }}

<head>

    {{> partials/title-meta title="Base Table" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}

{{> partials/page-heading title="Base Table" sub-title="Tables" }}

<div class="grid grid-cols-12 gap-x-space">
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Basic Styling</h6>
        </div>
        <div class="card-body">
            <div x-data="{ data: [
                { id: 'SRB-871021506-01', name: '<PERSON><PERSON><PERSON>', date: '28 Jan, 2025', status: 'Pending', amount: '$8,563' },
                { id: 'SRB-871021506-02', name: '<PERSON>', date: '02 Feb, 2025', status: 'Completed', amount: '$17,201' },
                { id: 'SRB-871021506-03', name: '<PERSON>', date: '15 Feb, 2025', status: 'Pending', amount: '$3,245' },
                { id: 'SRB-871021506-04', name: '<PERSON>', date: '20 Feb, 2025', status: 'Completed', amount: '$12,890' },
                { id: 'SRB-871021506-05', name: 'Sarah Johnson', date: '25 Feb, 2025', status: 'Completed', amount: '$5,670' }
            ] }">
                <div class="overflow-x-auto">
                    <table class="table flush">
                        <tbody>
                            <tr>
                                <th>Transition ID</th>
                                <th>Customer Name</th>
                                <th>Date</th>
                                <th>Amount</th>
                                <th>Status</th>
                                <th>Action</th>
                            </tr>
                            <template x-for="(item, index) in data" :key="index">
                                <tr class="*:px-3 *:py-2.5">
                                    <td x-text="item.id"></td>
                                    <td x-text="item.name"></td>
                                    <td x-text="item.date"></td>
                                    <td x-text="item.amount"></td>
                                    <td>
                                        <span x-bind:class="{
                                            'badge badge-green inline-flex items-center gap-1': item.status === 'Completed',
                                            'badge badge-yellow inline-flex items-center gap-1': item.status === 'Pending'
                                        }">
                                            <template x-if="item.status === 'Pending'">
                                                <i data-lucide="triangle-alert" class="size-3.5"></i>
                                            </template>
                                            <template x-if="item.status === 'Completed'">
                                                <i data-lucide="check-circle" class="size-3.5"></i>
                                            </template>
                                            <span x-text="item.status"></span>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="flex items-center gap-2">
                                            <a href="#!" class="btn btn-icon !size-8 btn-sub-primary"><i data-lucide="eye" class="size-3.5"></i></a>
                                            <a href="#!" class="btn btn-icon !size-8 btn-sub-red"><i data-lucide="trash-2" class="size-3.5"></i></a>
                                        </div>
                                    </td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div><!--end-->
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Border Styling</h6>
        </div>
        <div class="card-body">
            <div x-data="{ data: [
                        { name: 'Joana Morar', age: 37, date: '21 Jan, 2024', address: 'United States', salary: '$15,236' },
                        { name: 'Fabian Gorczany', age: 29, date: '28 Jan, 2024', address: 'Romania', salary: '$8,563' },
                        { name: 'Dale Willms', age: 32, date: '02 Feb, 2024', address: 'Canada', salary: '$7,986' },
                        { name: 'Lea Steuber', age: 34, date: '11 Feb, 2024', address: 'Germany', salary: '$36,322' },
                        { name: 'Freda Renner', age: 26, date: '20 Feb, 2024', address: 'Mexico', salary: '$11,741' }
                    ] }">
                <div class="overflow-x-auto">
                    <table class="table">
                        <tbody>
                            <tr>
                                <th>Name</th>
                                <th>Age</th>
                                <th>Date</th>
                                <th>Address</th>
                                <th>Salary</th>
                                <th>Action</th>
                            </tr>
                            <template x-for="(item, index) in data" :key="index">
                                <tr>
                                    <td x-text="item.name"></td>
                                    <td x-text="item.age"></td>
                                    <td x-text="item.date"></td>
                                    <td x-text="item.address"></td>
                                    <td x-text="item.salary"></td>
                                    <td><a href="#!" class="btn btn-sm btn-sub-red">Delete</a></td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div><!--end-->
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Bordered Table</h6>
        </div>
        <div class="card-body">
            <div x-data="{ data: [
                        { name: 'Jeremy McMullen', age: 37, date: '21 Jan, 2024', address: 'United States', salary: '$15,236' },
                        { name: 'Charles Fischer', age: 29, date: '28 Jan, 2024', address: 'Romania', salary: '$8,563' },
                        { name: 'Louise Harms', age: 32, date: '02 Feb, 2024', address: 'Canada', salary: '$7,986' },
                        { name: 'Henry Boyle', age: 34, date: '11 Feb, 2024', address: 'Germany', salary: '$36,322' },
                        { name: 'John Brown', age: 26, date: '20 Feb, 2024', address: 'Mexico', salary: '$11,741' }
                    ] }">
                <div class="overflow-x-auto">
                    <table class="table bordered">
                        <tbody>
                            <tr>
                                <th>Name</th>
                                <th>Age</th>
                                <th>Date</th>
                                <th>Address</th>
                                <th>Salary</th>
                                <th>Action</th>
                            </tr>
                            <template x-for="(item, index) in data" :key="index">
                                <tr>
                                    <td x-text="item.name"></td>
                                    <td x-text="item.age"></td>
                                    <td x-text="item.date"></td>
                                    <td x-text="item.address"></td>
                                    <td x-text="item.salary"></td>
                                    <td><a href="#!" class="text-red-500">Delete</a></td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div><!--end-->
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Separate Table</h6>
        </div>
        <div class="card-body">
            <p class="mb-2 text-gray-500">Use border-separate to force each cell to display its own separate borders.</p>
            <div x-data="{ data: [
                        { name: 'Tatyana Weissnat', age: 37, date: '21 Jan, 2024', address: 'United States', salary: '$15,236' },
                        { name: 'Milford Nitzsche', age: 29, date: '28 Jan, 2024', address: 'Romania', salary: '$8,563' },
                        { name: 'Carmela Marks', age: 32, date: '02 Feb, 2024', address: 'Canada', salary: '$7,986' },
                        { name: 'Julianne Ruecker', age: 34, date: '11 Feb, 2024', address: 'Germany', salary: '$36,322' },
                        { name: 'Rosario Kertzmann', age: 26, date: '20 Feb, 2024', address: 'Mexico', salary: '$11,741' }
                    ] }">
                <div class="overflow-x-auto">
                    <table class="table !border-separate bordered">
                        <tbody>
                            <tr>
                                <th>Name</th>
                                <th>Age</th>
                                <th>Date</th>
                                <th>Address</th>
                                <th>Salary</th>
                                <th>Action</th>
                            </tr>
                            <template x-for="(item, index) in data" :key="index">
                                <tr>
                                    <td x-text="item.name"></td>
                                    <td x-text="item.age"></td>
                                    <td x-text="item.date"></td>
                                    <td x-text="item.address"></td>
                                    <td x-text="item.salary"></td>
                                    <td><a href="#!" class="text-red-500">Delete</a></td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div><!--end-->
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Border Spacing Table</h6>
        </div>
        <div class="card-body">
            <div x-data="{ data: [
                        { name: 'Jeremy McMullen', age: 37, date: '21 Jan, 2024', address: 'United States', salary: '$15,236' },
                        { name: 'Charles Fischer', age: 29, date: '28 Jan, 2024', address: 'Romania', salary: '$8,563' },
                        { name: 'Louise Harms', age: 32, date: '02 Feb, 2024', address: 'Canada', salary: '$7,986' },
                        { name: 'Henry Boyle', age: 34, date: '11 Feb, 2024', address: 'Germany', salary: '$36,322' },
                        { name: 'John Brown', age: 26, date: '20 Feb, 2024', address: 'Mexico', salary: '$11,741' }
                    ] }">
                <div class="overflow-x-auto">
                    <table class="table !border-separate !border-spacing-2 whitespace-nowrap bordered">
                        <tbody>
                            <tr>
                                <th>Name</th>
                                <th>Age</th>
                                <th>Date</th>
                                <th>Address</th>
                                <th>Salary</th>
                                <th>Action</th>
                            </tr>
                            <template x-for="(item, index) in data" :key="index">
                                <tr>
                                    <td x-text="item.name"></td>
                                    <td x-text="item.age"></td>
                                    <td x-text="item.date"></td>
                                    <td x-text="item.address"></td>
                                    <td x-text="item.salary"></td>
                                    <td><a href="#!" class="text-red-500">Delete</a></td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div><!--end-->
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Caption Side</h6>
        </div>
        <div class="card-body">
            <table class="table table-auto bordered">
                <caption class="pb-3 text-xs text-gray-500 dark:text-gray-400 caption-top">
                    Table 3.1: Professional wrestlers and their signature moves.
                </caption>
                <thead>
                    <tr>
                        <th>Wrestler</th>
                        <th>Signature Move(s)</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>"Stone Cold" Steve Austin</td>
                        <td>Stone Cold Stunner, Lou Thesz Press</td>
                    </tr>
                    <tr>
                        <td>Bret "The Hitman" Hart</td>
                        <td>The Sharpshooter</td>
                    </tr>
                    <tr>
                        <td>Razor Ramon</td>
                        <td>Razor's Edge, Fallaway Slam</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div><!--end-->
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Heading Light</h6>
        </div>
        <div class="card-body">
            <div x-data="{ data: [
                        { name: 'Jeremy McMullen', age: 37, date: '21 Jan, 2024', address: 'United States', salary: '$15,236' },
                        { name: 'Charles Fischer', age: 29, date: '28 Jan, 2024', address: 'Romania', salary: '$8,563' },
                        { name: 'Louise Harms', age: 32, date: '02 Feb, 2024', address: 'Canada', salary: '$7,986' },
                        { name: 'Henry Boyle', age: 34, date: '11 Feb, 2024', address: 'Germany', salary: '$36,322' },
                        { name: 'John Brown', age: 26, date: '20 Feb, 2024', address: 'Mexico', salary: '$11,741' }
                    ] }">
                <div class="overflow-x-auto">
                    <table class="table flush">
                        <tbody>
                            <tr class="bg-gray-100 dark:bg-dark-850">
                                <th>Name</th>
                                <th>Age</th>
                                <th>Date</th>
                                <th>Address</th>
                                <th>Salary</th>
                                <th>Action</th>
                            </tr>
                            <template x-for="(item, index) in data" :key="index">
                                <tr>
                                    <td x-text="item.name"></td>
                                    <td x-text="item.age"></td>
                                    <td x-text="item.date"></td>
                                    <td x-text="item.address"></td>
                                    <td x-text="item.salary"></td>
                                    <td><a href="#!" class="text-red-500">Delete</a></td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div><!--end-->
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Hovered Table</h6>
        </div>
        <div class="card-body">
            <div x-data="{ data: [
                        { name: 'Jeremy McMullen', age: 37, date: '21 Jan, 2024', address: 'United States', salary: '$15,236' },
                        { name: 'Charles Fischer', age: 29, date: '28 Jan, 2024', address: 'Romania', salary: '$8,563' },
                        { name: 'Louise Harms', age: 32, date: '02 Feb, 2024', address: 'Canada', salary: '$7,986' },
                        { name: 'Henry Boyle', age: 34, date: '11 Feb, 2024', address: 'Germany', salary: '$36,322' },
                        { name: 'John Brown', age: 26, date: '20 Feb, 2024', address: 'Mexico', salary: '$11,741' }
                    ] }">
                <div class="overflow-x-auto">
                    <table class="table hovered">
                        <tbody>
                            <tr class="bg-gray-100 dark:bg-dark-850">
                                <th>Name</th>
                                <th>Age</th>
                                <th>Date</th>
                                <th>Address</th>
                                <th>Salary</th>
                                <th>Action</th>
                            </tr>
                            <template x-for="(item, index) in data" :key="index">
                                <tr>
                                    <td x-text="item.name"></td>
                                    <td x-text="item.age"></td>
                                    <td x-text="item.date"></td>
                                    <td x-text="item.address"></td>
                                    <td x-text="item.salary"></td>
                                    <td><a href="#!" class="text-red-500">Delete</a></td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div><!--end-->
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Striped Even Table</h6>
        </div>
        <div class="card-body">
            <div x-data="{ data: [
                        { name: 'Jeremy McMullen', age: 37, date: '21 Jan, 2024', address: 'United States', salary: '$15,236' },
                        { name: 'Charles Fischer', age: 29, date: '28 Jan, 2024', address: 'Romania', salary: '$8,563' },
                        { name: 'Louise Harms', age: 32, date: '02 Feb, 2024', address: 'Canada', salary: '$7,986' },
                        { name: 'Henry Boyle', age: 34, date: '11 Feb, 2024', address: 'Germany', salary: '$36,322' },
                        { name: 'John Brown', age: 26, date: '20 Feb, 2024', address: 'Mexico', salary: '$11,741' }
                    ] }">
                <div class="overflow-x-auto">
                    <table class="table even-striped">
                        <tbody>
                            <tr>
                                <th>Name</th>
                                <th>Age</th>
                                <th>Date</th>
                                <th>Address</th>
                                <th>Salary</th>
                                <th>Action</th>
                            </tr>
                            <template x-for="(item, index) in data" :key="index">
                                <tr>
                                    <td x-text="item.name"></td>
                                    <td x-text="item.age"></td>
                                    <td x-text="item.date"></td>
                                    <td x-text="item.address"></td>
                                    <td x-text="item.salary"></td>
                                    <td><a href="#!" class="text-red-500">Delete</a></td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div><!--end-->
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Striped Odd Table</h6>
        </div>
        <div class="card-body">
            <div x-data="{ data: [
                        { name: 'Jeremy McMullen', age: 37, date: '21 Jan, 2024', address: 'United States', salary: '$15,236' },
                        { name: 'Charles Fischer', age: 29, date: '28 Jan, 2024', address: 'Romania', salary: '$8,563' },
                        { name: 'Louise Harms', age: 32, date: '02 Feb, 2024', address: 'Canada', salary: '$7,986' },
                        { name: 'Henry Boyle', age: 34, date: '11 Feb, 2024', address: 'Germany', salary: '$36,322' },
                        { name: 'John Brown', age: 26, date: '20 Feb, 2024', address: 'Mexico', salary: '$11,741' }
                    ] }">
                <div class="overflow-x-auto">
                    <table class="table odd-striped">
                        <tbody>
                            <tr class="!bg-white dark:!bg-dark-900">
                                <th>Name</th>
                                <th>Age</th>
                                <th>Date</th>
                                <th>Address</th>
                                <th>Salary</th>
                                <th>Action</th>
                            </tr>
                            <template x-for="(item, index) in data" :key="index">
                                <tr>
                                    <td x-text="item.name"></td>
                                    <td x-text="item.age"></td>
                                    <td x-text="item.date"></td>
                                    <td x-text="item.address"></td>
                                    <td x-text="item.salary"></td>
                                    <td><a href="#!" class="text-red-500">Delete</a></td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div><!--end-->
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Colored Border Table</h6>
        </div>
        <div class="card-body">
            <div x-data="{ data: [
                        { name: 'Jeremy McMullen', age: 37, date: '21 Jan, 2024', address: 'United States', salary: '$15,236' },
                        { name: 'Charles Fischer', age: 29, date: '28 Jan, 2024', address: 'Romania', salary: '$8,563' },
                        { name: 'Louise Harms', age: 32, date: '02 Feb, 2024', address: 'Canada', salary: '$7,986' },
                        { name: 'Henry Boyle', age: 34, date: '11 Feb, 2024', address: 'Germany', salary: '$36,322' },
                        { name: 'John Brown', age: 26, date: '20 Feb, 2024', address: 'Mexico', salary: '$11,741' }
                    ] }">
                <div class="overflow-x-auto">
                    <table class="table bordered">
                        <tbody>
                            <tr class="*:!border-primary-200 dark:*:!border-primary-900">
                                <th>Name</th>
                                <th>Age</th>
                                <th>Date</th>
                                <th>Address</th>
                                <th>Salary</th>
                                <th>Action</th>
                            </tr>
                            <template x-for="(item, index) in data" :key="index">
                                <tr class="*:border-primary-200 dark:*:!border-primary-900">
                                    <td x-text="item.name"></td>
                                    <td x-text="item.age"></td>
                                    <td x-text="item.date"></td>
                                    <td x-text="item.address"></td>
                                    <td x-text="item.salary"></td>
                                    <td><a href="#!" class="text-red-500">Delete</a></td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                </div>
            </div>
            <div x-data="{ data: [
                        { name: 'Jeremy McMullen', age: 37, date: '21 Jan, 2024', address: 'United States', salary: '$15,236' },
                        { name: 'Charles Fischer', age: 29, date: '28 Jan, 2024', address: 'Romania', salary: '$8,563' },
                        { name: 'Louise Harms', age: 32, date: '02 Feb, 2024', address: 'Canada', salary: '$7,986' },
                        { name: 'Henry Boyle', age: 34, date: '11 Feb, 2024', address: 'Germany', salary: '$36,322' },
                        { name: 'John Brown', age: 26, date: '20 Feb, 2024', address: 'Mexico', salary: '$11,741' }
                    ] }">
                <div class="mt-5 overflow-x-auto">
                    <table class="table border-green">
                        <tbody>
                            <tr class="*:!border-green-200 *:dark:!border-green-900">
                                <th>Name</th>
                                <th>Age</th>
                                <th>Date</th>
                                <th>Address</th>
                                <th>Salary</th>
                                <th>Action</th>
                            </tr>
                            <template x-for="(item, index) in data" :key="index">
                                <tr class="*:!border-green-200 *:dark:!border-green-900">
                                    <td x-text="item.name"></td>
                                    <td x-text="item.age"></td>
                                    <td x-text="item.date"></td>
                                    <td x-text="item.address"></td>
                                    <td x-text="item.salary"></td>
                                    <td><a href="#!" class="text-red-500">Delete</a></td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div><!--end-->
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Striped Colored Table</h6>
        </div>
        <div class="card-body">
            <div x-data="{ data: [
                        { name: 'Jeremy McMullen', age: 37, date: '21 Jan, 2024', address: 'United States', salary: '$15,236' },
                        { name: 'Charles Fischer', age: 29, date: '28 Jan, 2024', address: 'Romania', salary: '$8,563' },
                        { name: 'Louise Harms', age: 32, date: '02 Feb, 2024', address: 'Canada', salary: '$7,986' },
                        { name: 'Henry Boyle', age: 34, date: '11 Feb, 2024', address: 'Germany', salary: '$36,322' },
                        { name: 'John Brown', age: 26, date: '20 Feb, 2024', address: 'Mexico', salary: '$11,741' }
                    ] }">
                <div class="overflow-x-auto">
                    <table class="table odd-striped">
                        <tbody>
                            <tr class="!bg-white dark:!bg-dark-900">
                                <th>Name</th>
                                <th>Age</th>
                                <th>Date</th>
                                <th>Address</th>
                                <th>Salary</th>
                                <th>Action</th>
                            </tr>
                            <template x-for="(item, index) in data" :key="index">
                                <tr class="odd:!bg-purple-50 dark:odd:!bg-purple-500/10">
                                    <td x-text="item.name"></td>
                                    <td x-text="item.age"></td>
                                    <td x-text="item.date"></td>
                                    <td x-text="item.address"></td>
                                    <td x-text="item.salary"></td>
                                    <td><a href="#!" class="text-red-500">Delete</a></td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div><!--end-->
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Loading Table</h6>
        </div>
        <div class="card-body">
            <div x-data="{ loading: true, rows: [] }" x-init="setTimeout(() => {
                        loading = false;
                        rows = [
                            { name: 'Jeremy McMullen', age: 37, date: '21 Jan, 2024', address: 'United States', salary: '$15,236' },
                            { name: 'Charles Fischer', age: 29, date: '28 Jan, 2024', address: 'Romania', salary: '$8,563' },
                            { name: 'Louise Harms', age: 32, date: '02 Feb, 2024', address: 'Canada', salary: '$7,986' },
                            { name: 'Henry Boyle', age: 34, date: '11 Feb, 2024', address: 'Germany', salary: '$36,322' },
                            { name: 'John Brown', age: 26, date: '20 Feb, 2024', address: 'Mexico', salary: '$11,741' }
                        ];
                    }, 2000)">
                <div x-show="loading" class="flex items-center justify-center w-full h-64">
                    <div class="relative">
                        <div class="border-t-4 border-b-4 border-gray-200 rounded-full dark:border-dark-800 size-8"></div>
                        <div class="absolute top-0 left-0 border-t-4 border-b-4 rounded-full border-primary-500 size-8 animate-spin"></div>
                    </div>
                </div>

                <div x-show="!loading" class="overflow-x-auto">
                    <table class="table even-striped">
                        <tbody>
                            <tr>
                                <th>Name</th>
                                <th>Age</th>
                                <th>Date</th>
                                <th>Address</th>
                                <th>Salary</th>
                                <th>Action</th>
                            </tr>
                            <template x-for="row in rows" :key="row.name">
                                <tr>
                                    <td x-text="row.name"></td>
                                    <td x-text="row.age"></td>
                                    <td x-text="row.date"></td>
                                    <td x-text="row.address"></td>
                                    <td x-text="row.salary"></td>
                                    <td><a href="#!" class="text-red-500">Delete</a></td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div><!--end-->
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Sorting Tables</h6>
        </div>
        <div class="card-body">
            <div x-data="app()">
                <div class="overflow-x-auto">
                    <table class="table">
                        <tbody>
                            <tr class="*:cursor-pointer">
                                <th @click="sortBy('name')">
                                    <span class="align-middle">Name</span>
                                    <i x-bind:data-lucide="sortIcon('name')" class="inline-block text-gray-500 dark:text-dark-500 size-4"></i>
                                </th>
                                <th @click="sortBy('age')">
                                    <span class="align-middle">Age</span>
                                    <i x-bind:data-lucide="sortIcon('age')" class="inline-block text-gray-500 dark:text-dark-500 size-4"></i>
                                </th>
                                <th @click="sortBy('date')">
                                    <span class="align-middle">Date</span>
                                    <i x-bind:data-lucide="sortIcon('date')" class="inline-block text-gray-500 dark:text-dark-500 size-4"></i>
                                </th>
                                <th @click="sortBy('address')">
                                    <span class="align-middle">Address</span>
                                    <i x-bind:data-lucide="sortIcon('address')" class="inline-block text-gray-500 dark:text-dark-500 size-4"></i>
                                </th>
                                <th @click="sortBy('salary')">
                                    <span class="align-middle">Salary</span>
                                    <i x-bind:data-lucide="sortIcon('salary')" class="inline-block text-gray-500 dark:text-dark-500 size-4"></i>
                                </th>
                                <th>Action</th>
                            </tr>
                            <template x-for="(item, index) in sortedData" :key="index">
                                <tr>
                                    <td x-text="item.name" :contenteditable="item.editable === index" @input="updateValue($event.target.innerText, 'name', index)"></td>
                                    <td x-text="item.age" :contenteditable="item.editable === index" @input="updateValue($event.target.innerText, 'age', index)"></td>
                                    <td x-text="item.date" :contenteditable="item.editable === index" @input="updateValue($event.target.innerText, 'date', index)"></td>
                                    <td x-text="item.address" :contenteditable="item.editable === index" @input="updateValue($event.target.innerText, 'address', index)"></td>
                                    <td x-text="item.salary" :contenteditable="item.editable === index" @input="updateValue($event.target.innerText, 'salary', index)"></td>
                                    <td>
                                        <div class="flex items-center gap-2">
                                            <button @click="toggleEdit(index)" class="text-primary-500" x-text="item.editable === index ? 'Save' : 'Edit'"></button>
                                            <button @click="removeItem(index)" class="text-red-500">Remove</button>
                                        </div>
                                    </td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                </div>
                <div x-show="data.length === 0" class="p-5 text-center text-gray-500 dark:text-dark-500">
                    <p>No data available.</p>
                </div>
            </div>
        </div>
    </div><!--end-->
</div><!--end grid-->

</div>
{{> partials/footer }}
</div>


{{> partials/vendor-scripts }}

<script type="module" src="assets/js/table/table.init.js"></script>

<script type="module" src="assets/js/main.js"></script>

</body>

</html>