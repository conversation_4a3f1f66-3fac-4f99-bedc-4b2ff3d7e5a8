{{> partials/main }}

<head>

    {{> partials/title-meta title="Lead" }}

    {{> partials/head-css }}
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/dragula/3.7.3/dragula.min.css" />
</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}
{{> partials/page-heading title="Lead" sub-title="CRM" }}
<div x-data="dragAndDrop()" @resize.window="handleResize">
    <div class="card">
        <div class="card-header">
            <div class="grid grid-cols-12 gap-5">
                <div class="col-span-12 lg:col-span-6 xl:col-span-9">
                    <div class="relative group/form w-full xl:max-w-[300px]">
                        <input type="text" class="ltr:pl-9 rtl:pr-9 form-input ltr:group-[&.right]/form:pr-9 rtl:group-[&.right]/form:pl-9 ltr:group-[&.right]/form:pl-4 rtl:group-[&.right]/form:pr-4" placeholder="Search for leads..." x-model="searchTerm" @input="searchContact()">
                        <div class="absolute inset-y-0 flex items-center text-gray-500 dark:text-dark-500 ltr:left-3 rtl:right-3 ltr:group-[&.right]/form:right-3 rtl:group-[&.right]/form:left-3 ltr:group-[&.right]/form:left-auto rtl:group-[&.right]/form:right-auto focus:outline-hidden">
                            <i data-lucide="search" class="size-4"></i>
                        </div>
                    </div>
                </div>
                <div class="col-span-12 lg:col-span-6 xl:col-span-3">
                    <div class="justify-end gap-2 sm:flex">
                        <button type="button" class="mt-2 btn btn-primary shrink-0 sm:mt-0" data-modal-target="leadCreateModal" @click="handleModal('showAddContactForm')">
                            <i data-lucide="plus" class="inline-block size-4"></i>
                            <span class="align-baseline">Add Lead</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div data-simplebar>
                <div class="flex space-x-4">
                    <div class="w-[350px] shrink-0 bg-gray-100 p-5 rounded-md dark:bg-dark-850">
                        <h6 class="mb-4">New <span x-text="leads.length" class="badge badge-sky ltr:ml-1 rtl:mr-1"></span></h6>
                        <div class="max-h-[calc(100vh_-_25.1rem)] -mx-5 px-5" data-simplebar @dragover.prevent>
                            <div class="flex flex-col gap-2" id="leads-container" data-status="new">
                                <template x-for="lead in leads" :key="lead.id">
                                    <div class="p-3 bg-white border border-white rounded-sm dark:bg-dark-900 dark:border-dark-900" :class="{'bg-primary-500/15 border-primary-500/20': selectedTasks.includes(lead)}" draggable="true" @click="toggleSelection(lead)">
                                        <div class="flex items-center gap-3 mb-4">
                                            <div class="rounded-full size-12">
                                                <img :src="lead.image" alt="" class="rounded-full">
                                            </div>
                                            <div class="grow">
                                                <h6 x-text="lead.name" class="mb-1"></h6>
                                                <p class="text-sm text-gray-500 dark:text-dark-500"><i class="ri-time-line"></i> <span x-text="lead.date"></span> at
                                                    <span x-text="lead.time"></span>
                                                </p>
                                            </div>
                                        </div>
                                        <p class="mb-2"><i class="ltr:mr-1 rtl:ml-1 ri-mail-line"></i> <span class="text-gray-500 dark:text-dark-500" x-text="lead.email"></span></p>
                                        <p><i class="ltr:mr-1 rtl:ml-1 ri-phone-line"></i> <span class="text-gray-500 dark:text-dark-500" x-text="lead.phoneNumber"></span></p>
                                        <div class="flex items-center gap-3 mt-3">
                                            <a href="#!" data-modal-target="leadCreateModal" @click="editContact(lead.name)" class="link link-primary">Edit</a>
                                            <a href="#!" data-modal-target="deleteModal" @click="deleteItem = lead.name" class="link link-red">Delete</a>
                                        </div>
                                    </div>
                                </template>
                                <template x-if="leads.length == 0">
                                    <td colspan="10" class="!p-8">
                                        <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" class="mx-auto size-12" viewBox="0 0 48 48">
                                            <linearGradient id="SVGID_1__h35ynqzIJzH4_gr1" x1="34.598" x2="15.982" y1="15.982" y2="34.598" gradientUnits="userSpaceOnUse">
                                                <stop offset="0" stop-color="#60e8fe"></stop>
                                                <stop offset=".033" stop-color="#6ae9fe"></stop>
                                                <stop offset=".197" stop-color="#97f0fe"></stop>
                                                <stop offset=".362" stop-color="#bdf5ff"></stop>
                                                <stop offset=".525" stop-color="#dafaff"></stop>
                                                <stop offset=".687" stop-color="#eefdff"></stop>
                                                <stop offset=".846" stop-color="#fbfeff"></stop>
                                                <stop offset="1" stop-color="#fff"></stop>
                                            </linearGradient>
                                            <path fill="url(#SVGID_1__h35ynqzIJzH4_gr1)" d="M40.036,33.826L31.68,25.6c0.847-1.739,1.335-3.684,1.335-5.748c0-7.27-5.894-13.164-13.164-13.164	S6.688,12.582,6.688,19.852c0,7.27,5.894,13.164,13.164,13.164c2.056,0,3.995-0.485,5.728-1.326l3.914,4.015l4.331,4.331	c1.715,1.715,4.496,1.715,6.211,0C41.751,38.321,41.751,35.541,40.036,33.826z">
                                            </path>
                                            <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M31.95,25.739l8.086,8.086c1.715,1.715,1.715,4.496,0,6.211l0,0c-1.715,1.715-4.496,1.715-6.211,0	l-4.331-4.331">
                                            </path>
                                            <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M7.525,24.511c-1.771-4.694-0.767-10.196,3.011-13.975c3.847-3.847,9.48-4.817,14.228-2.912">
                                            </path>
                                            <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M30.856,12.603c3.376,5.114,2.814,12.063-1.688,16.565c-4.858,4.858-12.565,5.129-17.741,0.814">
                                            </path>
                                        </svg>
                                        <p class="mt-2 text-center text-gray-500 dark:text-dark-500">No matching records found</p>
                                    </td>
                                </template>
                            </div>
                        </div>
                    </div>
                    <div class="w-[350px] shrink-0 bg-gray-100 p-5 rounded-md dark:bg-dark-850">
                        <h6 class="mb-4">Hot <span x-text="hotLead.length" class="badge badge-red ltr:ml-1 rtl:mr-1"></span></h6>
                        <div class="max-h-[calc(100vh_-_25.1rem)] -mx-5 px-5" data-simplebar @dragover.prevent>
                            <div class="flex flex-col gap-2" id="hotLead-container" data-status="hot">
                                <template x-for="lead in hotLead" :key="lead.id">
                                    <div class="p-3 bg-white border border-white rounded-sm cursor-pointer dark:bg-dark-900 dark:border-dark-900" :class="{'bg-primary-500/15 border-primary-500/20': selectedTasks.includes(lead)}" draggable="true" @click="toggleSelection(lead)">
                                        <div class="flex items-center gap-3 mb-4">
                                            <div class="rounded-full size-12">
                                                <img :src="lead.image" alt="" class="rounded-full">
                                            </div>
                                            <div class="grow">
                                                <h6 x-text="lead.name" class="mb-1"></h6>
                                                <p class="text-sm text-gray-500 dark:text-dark-500"><i class="ri-time-line"></i> <span x-text="lead.date"></span> at
                                                    <span x-text="lead.time"></span>
                                                </p>
                                            </div>
                                        </div>
                                        <p class="mb-2"><i class="ltr:mr-1 rtl:ml-1 ri-mail-line"></i> <span class="text-gray-500" x-text="lead.email"></span></p>
                                        <p><i class="ltr:mr-1 rtl:ml-1 ri-phone-line"></i> <span class="text-gray-500" x-text="lead.phoneNumber"></span></p>
                                        <div class="flex items-center gap-3 mt-3">
                                            <a href="#!" data-modal-target="leadCreateModal" @click="editContact(lead.name)" class="link link-primary">Edit</a>
                                            <a href="#!" data-modal-target="deleteModal" @click="deleteItem = lead.name" class="link link-red">Delete</a>
                                        </div>
                                    </div>
                                </template>
                                <template x-if="hotLead.length == 0">
                                    <td colspan="10" class="!p-8">
                                        <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" class="mx-auto size-12" viewBox="0 0 48 48">
                                            <linearGradient id="SVGID_1__h35ynqzIJzH4_gr1" x1="34.598" x2="15.982" y1="15.982" y2="34.598" gradientUnits="userSpaceOnUse">
                                                <stop offset="0" stop-color="#60e8fe"></stop>
                                                <stop offset=".033" stop-color="#6ae9fe"></stop>
                                                <stop offset=".197" stop-color="#97f0fe"></stop>
                                                <stop offset=".362" stop-color="#bdf5ff"></stop>
                                                <stop offset=".525" stop-color="#dafaff"></stop>
                                                <stop offset=".687" stop-color="#eefdff"></stop>
                                                <stop offset=".846" stop-color="#fbfeff"></stop>
                                                <stop offset="1" stop-color="#fff"></stop>
                                            </linearGradient>
                                            <path fill="url(#SVGID_1__h35ynqzIJzH4_gr1)" d="M40.036,33.826L31.68,25.6c0.847-1.739,1.335-3.684,1.335-5.748c0-7.27-5.894-13.164-13.164-13.164	S6.688,12.582,6.688,19.852c0,7.27,5.894,13.164,13.164,13.164c2.056,0,3.995-0.485,5.728-1.326l3.914,4.015l4.331,4.331	c1.715,1.715,4.496,1.715,6.211,0C41.751,38.321,41.751,35.541,40.036,33.826z">
                                            </path>
                                            <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M31.95,25.739l8.086,8.086c1.715,1.715,1.715,4.496,0,6.211l0,0c-1.715,1.715-4.496,1.715-6.211,0	l-4.331-4.331">
                                            </path>
                                            <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M7.525,24.511c-1.771-4.694-0.767-10.196,3.011-13.975c3.847-3.847,9.48-4.817,14.228-2.912">
                                            </path>
                                            <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M30.856,12.603c3.376,5.114,2.814,12.063-1.688,16.565c-4.858,4.858-12.565,5.129-17.741,0.814">
                                            </path>
                                        </svg>
                                        <p class="mt-2 text-center text-gray-500 dark:text-dark-500">No matching records
                                            found</p>
                                    </td>
                                </template>
                            </div>
                        </div>
                    </div>
                    <div class="w-[350px] shrink-0 bg-gray-100 p-5 rounded-md dark:bg-dark-850">
                        <h6 class="mb-4">Pending <span x-text="pendingLead.length" class="badge badge-green ltr:ml-1 rtl:mr-1"></span></h6>
                        <div class="max-h-[calc(100vh_-_25.1rem)] -mx-5 px-5" data-simplebar @dragover.prevent>
                            <div class="flex flex-col gap-2" id="pendingLead-container" data-status="pending">
                                <template x-for="lead in pendingLead" :key="lead.id">
                                    <div class="p-3 bg-white border border-white rounded-sm cursor-pointer dark:bg-dark-900 dark:border-dark-900" :class="{'bg-primary-500/15 border-primary-500/20': selectedTasks.includes(lead)}" draggable="true" @click="toggleSelection(lead)">
                                        <div class="flex items-center gap-3 mb-4">
                                            <div class="rounded-full size-12">
                                                <img :src="lead.image" alt="" class="rounded-full">
                                            </div>
                                            <div class="grow">
                                                <h6 x-text="lead.name" class="mb-1"></h6>
                                                <p class="text-sm text-gray-500"><i class="ri-time-line"></i> <span x-text="lead.date"></span> at <span x-text="lead.time"></span>
                                                </p>
                                            </div>
                                        </div>
                                        <p class="mb-2"><i class="ltr:mr-1 rtl:ml-1 ri-mail-line"></i> <span class="text-gray-500" x-text="lead.email"></span></p>
                                        <p><i class="ltr:mr-1 rtl:ml-1 ri-phone-line"></i> <span class="text-gray-500" x-text="lead.phoneNumber"></span></p>
                                        <div class="flex items-center gap-3 mt-3">
                                            <a href="#!" data-modal-target="leadCreateModal" @click="editContact(lead.name)" class="link link-primary">Edit</a>
                                            <a href="#!" data-modal-target="deleteModal" @click="deleteItem = lead.name" class="link link-red">Delete</a>
                                        </div>
                                    </div>
                                </template>
                                <template x-if="pendingLead.length == 0">
                                    <td colspan="10" class="!p-8">
                                        <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" class="mx-auto size-12" viewBox="0 0 48 48">
                                            <linearGradient id="SVGID_1__h35ynqzIJzH4_gr1" x1="34.598" x2="15.982" y1="15.982" y2="34.598" gradientUnits="userSpaceOnUse">
                                                <stop offset="0" stop-color="#60e8fe"></stop>
                                                <stop offset=".033" stop-color="#6ae9fe"></stop>
                                                <stop offset=".197" stop-color="#97f0fe"></stop>
                                                <stop offset=".362" stop-color="#bdf5ff"></stop>
                                                <stop offset=".525" stop-color="#dafaff"></stop>
                                                <stop offset=".687" stop-color="#eefdff"></stop>
                                                <stop offset=".846" stop-color="#fbfeff"></stop>
                                                <stop offset="1" stop-color="#fff"></stop>
                                            </linearGradient>
                                            <path fill="url(#SVGID_1__h35ynqzIJzH4_gr1)" d="M40.036,33.826L31.68,25.6c0.847-1.739,1.335-3.684,1.335-5.748c0-7.27-5.894-13.164-13.164-13.164	S6.688,12.582,6.688,19.852c0,7.27,5.894,13.164,13.164,13.164c2.056,0,3.995-0.485,5.728-1.326l3.914,4.015l4.331,4.331	c1.715,1.715,4.496,1.715,6.211,0C41.751,38.321,41.751,35.541,40.036,33.826z">
                                            </path>
                                            <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M31.95,25.739l8.086,8.086c1.715,1.715,1.715,4.496,0,6.211l0,0c-1.715,1.715-4.496,1.715-6.211,0	l-4.331-4.331">
                                            </path>
                                            <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M7.525,24.511c-1.771-4.694-0.767-10.196,3.011-13.975c3.847-3.847,9.48-4.817,14.228-2.912">
                                            </path>
                                            <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M30.856,12.603c3.376,5.114,2.814,12.063-1.688,16.565c-4.858,4.858-12.565,5.129-17.741,0.814">
                                            </path>
                                        </svg>
                                        <p class="mt-2 text-center text-gray-500 dark:text-dark-500">No matching records
                                            found</p>
                                    </td>
                                </template>
                            </div>
                        </div>
                    </div>
                    <div class="w-[350px] shrink-0 bg-gray-100 p-5 rounded-md dark:bg-dark-850">
                        <h6 class="mb-4">Lost <span x-text="lostLead.length" class="badge badge-purple ltr:ml-1 rtl:mr-1"></span></h6>
                        <div class="max-h-[calc(100vh_-_25.1rem)] -mx-5 px-5" data-simplebar @dragover.prevent>
                            <div class="flex flex-col gap-2" id="lostLead-container" data-status="lost">
                                <template x-for="lead in lostLead" :key="lead.id">
                                    <div class="p-3 bg-white rounded-sm opacity-75 cursor-pointer dark:bg-dark-900 dark:border-dark-900" draggable="true" @click="toggleSelection(lead)">
                                        <div class="flex items-center gap-3 mb-4">
                                            <div class="rounded-full size-12">
                                                <img :src="lead.image" alt="" class="rounded-full">
                                            </div>
                                            <div class="grow">
                                                <h6 x-text="lead.name" class="mb-1"></h6>
                                                <p class="text-sm text-gray-500 dark:text-dark-500"><i class="ri-time-line"></i> <span x-text="lead.date"></span> at
                                                    <span x-text="lead.time"></span>
                                                </p>
                                            </div>
                                        </div>
                                        <p class="mb-2"><i class="ltr:mr-1 rtl:ml-1 ri-mail-line"></i> <span class="text-gray-500 dark:text-dark-500" x-text="lead.email"></span></p>
                                        <p><i class="ltr:mr-1 rtl:ml-1 ri-phone-line"></i> <span class="text-gray-500 dark:text-dark-500" x-text="lead.phoneNumber"></span></p>
                                        <div class="flex items-center gap-3 mt-3">
                                            <a href="#!" data-modal-target="leadCreateModal" @click="editContact(lead.name)" class="link link-primary">Edit</a>
                                            <a href="#!" data-modal-target="deleteModal" @click="deleteItem = lead.name" class="link link-red">Delete</a>
                                        </div>
                                    </div>
                                </template>
                                <template x-if="lostLead.length == 0">
                                    <td colspan="10" class="!p-8">
                                        <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" class="mx-auto size-12" viewBox="0 0 48 48">
                                            <linearGradient id="SVGID_1__h35ynqzIJzH4_gr1" x1="34.598" x2="15.982" y1="15.982" y2="34.598" gradientUnits="userSpaceOnUse">
                                                <stop offset="0" stop-color="#60e8fe"></stop>
                                                <stop offset=".033" stop-color="#6ae9fe"></stop>
                                                <stop offset=".197" stop-color="#97f0fe"></stop>
                                                <stop offset=".362" stop-color="#bdf5ff"></stop>
                                                <stop offset=".525" stop-color="#dafaff"></stop>
                                                <stop offset=".687" stop-color="#eefdff"></stop>
                                                <stop offset=".846" stop-color="#fbfeff"></stop>
                                                <stop offset="1" stop-color="#fff"></stop>
                                            </linearGradient>
                                            <path fill="url(#SVGID_1__h35ynqzIJzH4_gr1)" d="M40.036,33.826L31.68,25.6c0.847-1.739,1.335-3.684,1.335-5.748c0-7.27-5.894-13.164-13.164-13.164	S6.688,12.582,6.688,19.852c0,7.27,5.894,13.164,13.164,13.164c2.056,0,3.995-0.485,5.728-1.326l3.914,4.015l4.331,4.331	c1.715,1.715,4.496,1.715,6.211,0C41.751,38.321,41.751,35.541,40.036,33.826z">
                                            </path>
                                            <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M31.95,25.739l8.086,8.086c1.715,1.715,1.715,4.496,0,6.211l0,0c-1.715,1.715-4.496,1.715-6.211,0	l-4.331-4.331">
                                            </path>
                                            <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M7.525,24.511c-1.771-4.694-0.767-10.196,3.011-13.975c3.847-3.847,9.48-4.817,14.228-2.912">
                                            </path>
                                            <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M30.856,12.603c3.376,5.114,2.814,12.063-1.688,16.565c-4.858,4.858-12.565,5.129-17.741,0.814">
                                            </path>
                                        </svg>
                                        <p class="mt-2 text-center text-gray-500 dark:text-dark-500">No matching records
                                            found</p>
                                    </td>
                                </template>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!--leads create modals-->
    <div id="leadCreateModal" class="!hidden modal show" :class="{'show d-block': showAddContactForm || showEditContactForm}" x-show="showAddContactForm || showEditContactForm  ">
        <div class="modal-wrap modal-center">
            <div class="p-2 modal-content">
                <div class="h-24 p-5 rounded-t-sm bg-gradient-to-r from-primary-500/20 via-pink-500/20 to-green-500/20">
                </div>
                <div class="p-4">
                    <div class="-mt-16">
                        <label for="logo">
                            <div class="inline-flex items-center justify-center overflow-hidden bg-gray-100 border-2 border-white border-solid rounded-full cursor-pointer dark:border-dark-900 dark:bg-dark-850 size-24">
                                <img x-show="contactForm.image" :src="contactForm.image" class="object-cover w-full h-full rounded-full">
                                <div x-show="!contactForm.image" class="flex flex-col items-center text-gray-500 dark:text-dark-500">
                                    <i data-lucide="upload"></i>
                                </div>
                            </div>
                        </label>
                        <div class="hidden mt-4">
                            <label class="block">
                                <span class="sr-only">Choose profile photo</span>
                                <input type="file" name="logo" id="logo" @change="fileChosen" class="block w-full text-sm file:rounded-md focus:outline-0 text-slate-500 file:mr-4 file:py-2 file:px-4 file:border-0 file:text-sm file:font-semibold file:bg-violet-50 file:text-violet-700 hover:file:bg-violet-100 " />
                            </label>
                        </div>
                    </div>
                    <span x-show="errors.image" class="text-red-500" x-text="errors.image"></span>
                    <div class="grid grid-cols-12 gap-4 mt-5">
                        <div class="col-span-12">
                            <label for="fullNameInput" class="form-label">Full Name</label>
                            <input type="text" id="fullNameInput" class="form-input" placeholder="Full name" x-model="contactForm.name" @input="validateField('name', contactForm.name, 'Full name is required.')">
                            <span x-show="errors.name" class="text-red-500" x-text="errors.name"></span>
                        </div>

                        <div class="col-span-12">
                            <label for="emailInput" class="form-label">Email</label>
                            <input type="email" id="emailInput" class="form-input" placeholder="<EMAIL>" x-model="contactForm.email" @input="validateField('email', contactForm.email, 'Email is required.', this.validateEmail)">
                            <span x-show="errors.email" class="text-red-500" x-text="errors.email"></span>
                        </div>
                        <div class="col-span-12">
                            <label for="phoneNumberInput" class="form-label">Phone Number</label>
                            <input type="tel" id="phoneNumberInput" class="form-input" placeholder="+(00) 0000 000" pattern="[0-9]{3}-[0-9]{2}-[0-9]{3}" x-model="contactForm.phoneNumber" @input="formatPhone()">
                            <span x-show="errors.phoneNumber" class="text-red-500" x-text="errors.phoneNumber"></span>
                        </div>

                        <div class="col-span-6">
                            <label for="statusSelect2" class="form-label">Status</label>
                            <div id="statusSelect2" x-model="contactForm.status" @change="validateField('status', $event.target.value)"></div>
                            <span x-show="errors.status" class="text-red-500" x-text="errors.status"></span>
                        </div>
                        <div class="flex items-center justify-end col-span-12 gap-2 mt-5">
                            <button type="button" class="btn btn-active-red" @click="resetForm()" data-modal-close="leadCreateModal" >
                                <i data-lucide="x" class="inline-block size-4"></i> <span class="align-baseline">Close</span>
                            </button>
                            <button type="button" class="btn btn-primary" x-text="showAddContactForm ? 'Add Lead' : 'Update Lead'" @click="submitForm($event)">
                                <i data-lucide="plus" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div><!--end-->

    <!--delete modal-->
    <div id="deleteModal" class="!hidden modal show">
        <div class="modal-wrap modal-xs modal-center">
            <div class="text-center modal-content p-7">
                <div class="flex items-center justify-center mx-auto mb-4 text-red-500 rounded-full bg-red-500/10 size-14 backdrop-blur-xl">
                    <i data-lucide="trash-2" class="size-6"></i>
                </div>
                <h5 class="mb-4">Are you sure you want to delete this Lead ?</h5>
                <div class="flex items-center justify-center gap-2">
                    <button class="btn btn-red" @click="deleteProduct()" data-modal-close="deleteModal">Delete</button>
                    <button data-modal-close="deleteModal" class="btn link link-primary">Cancel</button>
                </div>
            </div>
        </div>
    </div><!--end-->

</div>
</div>
{{> partials/footer }}
</div>


{{> partials/vendor-scripts }}

<script src="https://cdnjs.cloudflare.com/ajax/libs/dragula/3.7.3/dragula.min.js"></script>

<script type="module" src="assets/js/apps/crm/lead-list.init.js"></script>
<script type="module" src="assets/js/main.js"></script>

</body>
</html>