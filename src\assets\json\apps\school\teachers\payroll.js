import user1 from  "/assets/images/avatar/user-1.png"
import user2 from  "/assets/images/avatar/user-2.png"
import user4 from  "/assets/images/avatar/user-4.png"
import user5 from  "/assets/images/avatar/user-5.png"
import user6 from  "/assets/images/avatar/user-6.png"
import user9 from  "/assets/images/avatar/user-9.png"
import user10 from "/assets/images/avatar/user-10.png"
import user11 from "/assets/images/avatar/user-11.png"
import user12 from "/assets/images/avatar/user-12.png"
import user14 from "/assets/images/avatar/user-14.png"

const payrollData =[
    {
        "teacherName": "<PERSON>",
        "image": user1,
        "email": "<EMAIL>",
        "gross": "$70,000",
        "taxes": "$20,000",
        "netSalary": "$50,000",
        "performance": "Excellent",
        "status": "Active"
    },
    {
        "teacherName": "<PERSON>",
        "image": user2,
        "email": "<EMAIL>",
        "gross": "$75,000",
        "taxes": "$22,000",
        "netSalary": "$53,000",
        "performance": "Good",
        "status": "Active"
    },
    {
        "teacherName": "<PERSON> <PERSON>",
        "email": "<EMAIL>",
        "gross": "$80,000",
        "taxes": "$25,000",
        "netSalary": "$55,000",
        "performance": "Excellent",
        "status": "Active"
    },
    {
        "teacherName": "Emily Davis",
        "image": user4,
        "email": "<EMAIL>",
        "gross": "$65,000",
        "taxes": "$18,000",
        "netSalary": "$47,000",
        "performance": "Good",
        "status": "Inactive"
    },
    {
        "teacherName": "James Brown",
        "image": user5,
        "email": "<EMAIL>",
        "gross": "$68,000",
        "taxes": "$19,000",
        "netSalary": "$49,000",
        "performance": "Satisfactory",
        "status": "Active"
    },
    {
        "teacherName": "Patricia Wilson",
        "image": user6,
        "email": "<EMAIL>",
        "gross": "$73,000",
        "taxes": "$21,000",
        "netSalary": "$52,000",
        "performance": "Excellent",
        "status": "Active"
    },
    {
        "teacherName": "Robert Martinez",
        "email": "<EMAIL>",
        "gross": "$78,000",
        "taxes": "$23,000",
        "netSalary": "$55,000",
        "performance": "Good",
        "status": "Active"
    },
    {
        "teacherName": "Linda Anderson",
        "email": "<EMAIL>",
        "gross": "$72,000",
        "taxes": "$20,000",
        "netSalary": "$52,000",
        "performance": "Satisfactory",
        "status": "Inactive"
    },
    {
        "teacherName": "Thomas Lee",
        "image": user9,
        "email": "<EMAIL>",
        "gross": "$76,000",
        "taxes": "$22,000",
        "netSalary": "$54,000",
        "performance": "Excellent",
        "status": "Active"
    },
    {
        "teacherName": "Barbara Hernandez",
        "image": user10,
        "email": "<EMAIL>",
        "gross": "$70,000",
        "taxes": "$20,000",
        "netSalary": "$50,000",
        "performance": "Good",
        "status": "Active"
    },
    {
        "teacherName": "Christopher White",
        "image": user11,
        "email": "<EMAIL>",
        "gross": "$69,000",
        "taxes": "$19,000",
        "netSalary": "$50,000",
        "performance": "Satisfactory",
        "status": "Inactive"
    },
    {
        "teacherName": "Nancy Harris",
        "image": user12,
        "email": "<EMAIL>",
        "gross": "$74,000",
        "taxes": "$21,000",
        "netSalary": "$53,000",
        "performance": "Excellent",
        "status": "Active"
    },
    {
        "teacherName": "Kevin Clark",
        "email": "<EMAIL>",
        "gross": "$79,000",
        "taxes": "$24,000",
        "netSalary": "$55,000",
        "performance": "Good",
        "status": "Active"
    },
    {
        "teacherName": "Sarah Lewis",
        "image": user14,
        "email": "<EMAIL>",
        "gross": "$71,000",
        "taxes": "$20,000",
        "netSalary": "$51,000",
        "performance": "Satisfactory",
        "status": "Inactive"
    }
]
export default payrollData;