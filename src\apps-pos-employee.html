{{> partials/main }}

<head>
    {{> partials/title-meta title="Employee" }}
    {{> partials/head-css }}
</head>

{{> partials/body }}
{{> partials/topbar }}
{{> partials/sidebar }}
{{> partials/page-wrapper }}
{{> partials/page-heading title="Employee" sub-title="POS" }}
<div x-data="employeeTable">
    <div class="card">
        <div class="card-header flex items-center gap-3 border-dashed">
            <h6 class="card-title grow">POS Employee</h6>
            <div class="shrink-0">
                <button type="button" data-modal-target="addEmployeeModal" class="btn btn-primary">
                    <i data-lucide="plus" class="size-4 inline-block ltr:mr-1 rtl:ml-1"></i> Add Employee
                </button>
            </div>
        </div>
        <div class="card-header border-dashed">
            <div class="grid grid-cols-12 gap-5">
                <div class="col-span-4 relative group/form">
                    <input type="text" class="ltr:pl-9 rtl:pr-9 form-input ltr:group-[&.right]/form:pr-9 rtl:group-[&.right]/form:pl-9 ltr:group-[&.right]/form:pl-4 rtl:group-[&.right]/form:pr-4" placeholder="Search for employee, name, email..." @input="filteredEmployee" x-model="searchTerm">
                    <button aria-label="search-button" class="absolute inset-y-0 flex items-center text-gray-500 dark:text-dark-500 ltr:left-3 rtl:right-3 ltr:group-[&.right]/form:right-3 rtl:group-[&.right]/form:left-3 ltr:group-[&.right]/form:left-auto rtl:group-[&.right]/form:right-auto focus:outline-hidden">
                        <i data-lucide="search" class="size-4"></i>
                    </button>
                </div>
                <div class="col-span-4">
                    <div id="departmentsFilterSelect" placeholder="All Departments"></div>
                </div>
                <div class="col-span-4">
                    <div id="statusFilterSelect" placeholder="All Status"></div>
                </div>
            </div>
        </div>
        <div class="card-body pt-0">
            <div class="overflow-x-auto table-box">
                <table class="table whitespace-nowrap">
                    <tbody>
                        <tr class="text-gray-500 bg-gray-100 dark:bg-dark-850 dark:text-dark-500">
                            <th x-on:click="sort('employeeID')" class="cursor-pointer font-medium">Employee ID <span x-show="sortBy === 'employeeID'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                            <th x-on:click="sort('name')" class="cursor-pointer font-medium">Name <span x-show="sortBy === 'name'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                            <th x-on:click="sort('position')" class="cursor-pointer font-medium">Position <span x-show="sortBy === 'position'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                            <th x-on:click="sort('department')" class="cursor-pointer font-medium">Department <span x-show="sortBy === 'department'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                            <th x-on:click="sort('contact')" class="cursor-pointer font-medium">Contact <span x-show="sortBy === 'contact'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                            <th x-on:click="sort('joinDate')" class="cursor-pointer font-medium">Join Date <span x-show="sortBy === 'joinDate'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                            <th x-on:click="sort('status')" class="cursor-pointer font-medium">Status <span x-show="sortBy === 'status'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                            <th class="cursor-pointer font-medium">Actions</th>
                        </tr>
                        <template x-if="displayedEmployee.length > 0">
                            <template x-for="(employee, index) in displayedEmployee" :key="index">
                                <tr>
                                    <td><a href="#!" x-text="employee.employeeID" class="link link-primary fw-semibold"></a></td>
                                    <td>
                                        <div class="flex items-center gap-2">
                                            <template x-if="employee.avatar && (employee.avatar.includes('assets') || employee.avatar.startsWith('data:'))">
                                                <img :src="employee.avatar" :alt="employee.name" class="size-10 rounded-full object-cover">
                                            </template>
                                            <template x-if="!employee.avatar || (!employee.avatar.includes('assets') && !employee.avatar.startsWith('data:'))">
                                                <div class="size-10 flex items-center justify-center rounded-full bg-gray-100 dark:bg-dark-850 text-gray-500 dark:text-dark-500 shrink-0">
                                                    <span x-text="employee.avatar"></span>
                                                </div>
                                            </template>
                                            <div class="grow">
                                                <h6 class="mb-0 text-sm"><a href="#!" class="link link-primary text-current dark:text-current" x-text="employee.name"></a></h6>
                                                <small class="text-gray-500 dark:text-dark-500" x-text="employee.email"></small>
                                            </div>
                                        </div>
                                    </td>

                                    <!-- Updated Avatar Display Logic for Employee Details Modal -->
                                    <div class="size-20 bg-gray-100 dark:bg-dark-850 rounded-full flex items-center justify-center">
                                        <template x-if="currentEmployee.avatar && (currentEmployee.avatar.includes('assets') || currentEmployee.avatar.startsWith('data:'))">
                                            <img :src="currentEmployee.avatar" :alt="currentEmployee.name" class="size-20 rounded-full object-cover">
                                        </template>
                                        <template x-if="!currentEmployee.avatar || (!currentEmployee.avatar.includes('assets') && !currentEmployee.avatar.startsWith('data:'))">
                                            <span class="text-lg font-semibold text-gray-500 dark:text-dark-500" x-text="currentEmployee.avatar || ''"></span>
                                        </template>
                                    </div>
                                    <td>
                                        <span x-text="employee.position" class="badge badge-sky"></span>
                                    </td>
                                    <td>
                                        <span x-text="employee.department" class="badge badge-primary"></span>
                                    </td>
                                    <td>
                                        <div class="flex items-center gap-1">
                                            <i data-lucide="phone" class="size-4"></i>
                                            <span x-text="employee.contact"></span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="flex items-center gap-1">
                                            <i data-lucide="calendar" class="size-4"></i>
                                            <span x-text="employee.joinDate"></span>
                                        </div>
                                    </td>
                                    <td>
                                        <span x-text="employee.status" :class="{
                                        'badge badge-green': employee.status === 'Active',
                                        'badge badge-red': employee.status === 'Inactive'
                                    }"></span>
                                    </td>
                                    <td>
                                        <div class="flex gap-2 justify-center">
                                            <button type="button" class="btn btn-icon size-8 btn-sub-sky" data-modal-target="employeeDetailsModal" aria-label="View">
                                                <i data-lucide="eye" class="size-4"></i>
                                            </button>
                                            <button type="button" class="btn btn-icon size-8 btn-sub-primary" data-modal-target="addEmployeeModal" aria-label="Edit">
                                                <i data-lucide="edit" class="size-4"></i>
                                            </button>
                                            <button type="button" class="btn btn-icon size-8 btn-sub-red" data-modal-target="deleteModal" aria-label="Delete">
                                                <i data-lucide="trash-2" class="size-4"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </template>
                        </template>
                        <tr>
                            <template x-if="displayedEmployee.length == 0">
                                <td colspan="10" class="!p-8e">
                                    <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" class="mx-auto size-12" viewBox="0 0 48 48">
                                        <linearGradient id="SVGID_1__h35ynqzIJzH4_gr1" x1="34.598" x2="15.982" y1="15.982" y2="34.598" gradientUnits="userSpaceOnUse">
                                            <stop offset="0" stop-color="#60e8fe"></stop>
                                            <stop offset=".033" stop-color="#6ae9fe"></stop>
                                            <stop offset=".197" stop-color="#97f0fe"></stop>
                                            <stop offset=".362" stop-color="#bdf5ff"></stop>
                                            <stop offset=".525" stop-color="#dafaff"></stop>
                                            <stop offset=".687" stop-color="#eefdff"></stop>
                                            <stop offset=".846" stop-color="#fbfeff"></stop>
                                            <stop offset="1" stop-color="#fff"></stop>
                                        </linearGradient>
                                        <path fill="url(#SVGID_1__h35ynqzIJzH4_gr1)" d="M40.036,33.826L31.68,25.6c0.847-1.739,1.335-3.684,1.335-5.748c0-7.27-5.894-13.164-13.164-13.164	S6.688,12.582,6.688,19.852c0,7.27,5.894,13.164,13.164,13.164c2.056,0,3.995-0.485,5.728-1.326l3.914,4.015l4.331,4.331	c1.715,1.715,4.496,1.715,6.211,0C41.751,38.321,41.751,35.541,40.036,33.826z"></path>
                                        <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M31.95,25.739l8.086,8.086c1.715,1.715,1.715,4.496,0,6.211l0,0c-1.715,1.715-4.496,1.715-6.211,0	l-4.331-4.331"></path>
                                        <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M7.525,24.511c-1.771-4.694-0.767-10.196,3.011-13.975c3.847-3.847,9.48-4.817,14.228-2.912"></path>
                                        <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M30.856,12.603c3.376,5.114,2.814,12.063-1.688,16.565c-4.858,4.858-12.565,5.129-17.741,0.814"></path>
                                    </svg>
                                    <p class="mt-2 text-center text-gray-500 dark:text-dark-500">No matching records found</p>
                                </td>
                            </template>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="grid items-center grid-cols-12 gap-space mt-space" x-show="displayedEmployee.length > 0">
                <div class="col-span-12 text-center lg:col-span-6 lg:ltr:text-left lg:rtl:text-right">
                    <p class="text-gray-500 dark:text-dark-500">Showing <b x-text="showingStart"></b> - <b x-text="showingEnd"></b> of <b x-text="filterEmployee.length"></b> Results</p>
                </div>
                <div class="col-span-12 lg:col-span-6">
                    <div class="flex justify-center gap-2 lg:justify-end pagination pagination-primary">
                        <button @click="prevPage()" :disabled="currentPage === 1" class="pagination-pre">
                            <i data-lucide="chevron-left" class="mr-1 ltr:inline-block rtl:hidden size-4"></i>
                            <i data-lucide="chevron-right" class="ml-1 rtl:inline-block ltr:hidden size-4"></i>
                            Prev
                        </button>
                        <template x-for="page in totalPages" :key="page">
                            <button @click="gotoPage(page)" :class="{ 'active': currentPage === page }" class="pagination-item">
                                <span x-text="page"></span>
                            </button>
                        </template>
                        <button @click="nextPage()" :disabled="currentPage === totalPages" class="pagination-next">
                            Next
                            <i data-lucide="chevron-right" class="ml-1 ltr:inline-block rtl:hidden size-4"></i>
                            <i data-lucide="chevron-left" class="mr-1 rtl:inline-block ltr:hidden size-4"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add/Edit Employee Modal -->
    <div id="addEmployeeModal" class="!hidden modal show">
        <div class="modal-wrap modal-center modal-lg">
            <div class="p-2 modal-content">
                <div class="h-24 p-5 rounded-t-sm bg-gradient-to-r from-primary-500/20 via-pink-500/20 to-green-500/20">
                </div>
                <form action="#!">
                    <div class="p-4">
                        <div class="-mt-16">
                            <label for="logo">
                                <div class="inline-flex items-center justify-center overflow-hidden bg-gray-100 border-2 border-white border-solid rounded-full cursor-pointer dark:border-dark-900 dark:bg-dark-850 size-24">
                                    <div class="flex flex-col items-center text-gray-500 dark:text-dark-500">
                                        <i data-lucide="upload"></i>
                                    </div>
                                </div>
                            </label>
                            <div class="hidden mt-4">
                                <label class="block">
                                    <span class="sr-only">Choose profile photo</span>
                                    <input type="file" name="logo" id="logo" class="block w-full text-sm file:rounded-md focus:outline-0 text-slate-500 file:mr-4 file:py-2 file:px-4 file:border-0 file:text-sm file:font-semibold file:bg-violet-50 file:text-violet-700 hover:file:bg-violet-100">
                                </label>
                            </div>
                        </div>
                        <div class="grid grid-cols-12 gap-4 mt-4">
                            <div class="col-span-12">
                                <h6 class="text-base font-semibold">Personal Information</h6>
                            </div>
                            <div class="col-span-12 md:col-span-6">
                                <label for="firstName" class="form-label">First Name *</label>
                                <input type="text" id="firstName" class="form-input" placeholder="Enter first name" required>
                            </div>
                            <div class="col-span-12 md:col-span-6">
                                <label for="lastName" class="form-label">Last Name *</label>
                                <input type="text" id="lastName" class="form-input" placeholder="Enter last name" required>
                            </div>
                            <div class="col-span-12 md:col-span-6">
                                <label for="email" class="form-label">Email Address *</label>
                                <input type="email" id="email" class="form-input" placeholder="Enter email address" required>
                            </div>
                            <div class="col-span-12 md:col-span-6">
                                <label for="phone" class="form-label">Phone Number</label>
                                <input type="tel" id="phone" class="form-input" placeholder="Enter phone number">
                            </div>
                            <div class="col-span-12">
                                <h6 class="text-base font-semibold">Employment Information</h6>
                            </div>
                            <div class="col-span-12 md:col-span-6">
                                <label for="employeeId" class="form-label">Employee ID *</label>
                                <input type="text" id="employeeId" class="form-input" placeholder="Enter employee ID" required>
                            </div>
                            <div class="col-span-12 md:col-span-6">
                                <label for="department" class="form-label">Department *</label>
                                <select id="department" class="form-input form-select" required>
                                    <option value="">Select Department</option>
                                    <option value="Management">Management</option>
                                    <option value="Sales">Sales</option>
                                    <option value="IT">IT</option>
                                    <option value="HR">Human Resources</option>
                                    <option value="Finance">Finance</option>
                                </select>
                            </div>
                            <div class="col-span-12 md:col-span-6">
                                <label for="position" class="form-label">Position *</label>
                                <input type="text" id="position" class="form-input" placeholder="Enter position" required>
                            </div>
                            <div class="col-span-12 md:col-span-6">
                                <label for="joinDate" class="form-label">Join Date</label>
                                <input data-provider="flatpickr" data-date-format="d M, Y" type="text" placeholder="YYYY-MM-DD" id="joinDate" class="form-input">
                            </div>
                            <div class="col-span-12 md:col-span-6">
                                <label for="employmentType" class="form-label">Employment Type</label>
                                <select id="employmentType" class="form-input form-select">
                                    <option value="">Select Type</option>
                                    <option value="full-time">Full Time</option>
                                    <option value="part-time">Part Time</option>
                                    <option value="contract">Contract</option>
                                    <option value="intern">Intern</option>
                                </select>
                            </div>
                            <div class="col-span-12 md:col-span-6">
                                <label for="statusSelect" class="form-label">Status *</label>
                                <div id="statusSelect" placeholder="Select Status"></div>
                            </div>
                            <div class="col-span-12">
                                <label for="address" class="form-label">Address</label>
                                <textarea id="address" class="form-input h-auto" rows="3" placeholder="Enter address"></textarea>
                            </div>
                            <div class="col-span-12 flex justify-end gap-3 mt-4">
                                <button type="button" data-modal-close="addEmployeeModal" class="btn btn-sub-gray">Cancel</button>
                                <button type="submit" class="btn btn-primary">Add Employee</button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!--Delete Modal-->
    <div id="deleteModal" class="!hidden modal show">
        <div class="modal-wrap modal-xs modal-center">
            <div class="text-center modal-content p-7">
                <div class="flex items-center justify-center mx-auto mb-4 text-red-500 rounded-full bg-red-500/10 size-14 backdrop-blur-xl">
                    <i data-lucide="trash-2" class="size-6"></i>
                </div>
                <h5 class="mb-4">Are you sure you want to delete this Employee?</h5>
                <div class="flex items-center justify-center gap-2">
                    <button class="btn btn-red">Delete</button>
                    <button data-modal-close="deleteModal" class="btn link link-primary">Cancel</button>
                </div>
            </div>
        </div>
    </div>
</div>
</div>
{{> partials/footer }}
</div>

{{> partials/vendor-scripts }}

<script type="module" src="assets/js/apps/pos/employee.init.js"></script>
<script type="module" src="assets/js/main.js"></script>

</body>

</html>