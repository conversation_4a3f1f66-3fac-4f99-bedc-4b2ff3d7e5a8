{{> partials/main }}

<head>

    {{> partials/title-meta title="Sign Up" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

<div class="relative">
    <div class="grid grid-cols-12">
        <div class="relative col-span-12 py-8 overflow-hidden bg-gray-100 dark:bg-dark-850 lg:min-h-screen lg:col-span-6 md:p-9 xl:p-12">
            <div class="absolute bottom-0 w-32 -rotate-45 -top-64 -right-8 bg-gray-200/20 dark:bg-dark-800/20"></div>
            <div class="p-4">
                <a href="index.html">
                    <img src="assets/images/main-logo.png" alt="" class="h-8 dark:hidden">
                    <img src="assets/images/logo-white.png" alt="" class="hidden h-8 dark:inline-block">
                </a>
                <h1 class="max-w-lg mt-8 text-2xl font-normal leading-tight capitalize md:leading-tight md:text-4xl">The most straightforward way to manage your projects</h1>

                <img src="assets/images/others/auth-creative.png" alt="" class="mt-9 xl:mt-0 relative xl:absolute xl:scale-110 rounded-lg shadow-lg xl:top-[315px] ltr:xl:left-[115px] rtl:xl:right-[115px]">
            </div>
        </div>
        <div class="flex items-center col-span-12 lg:min-h-screen lg:col-span-6 py-9 md:py-12">
            <div class="grid w-full grid-cols-12">
                <div class="col-span-12 mx-4 mb-0 2xl:col-span-8 2xl:col-start-3 md:mx-12 card">
                    <div class="md:p-10 card-body">
                        <h4 class="mb-2 font-bold leading-relaxed text-center text-transparent drop-shadow-lg ltr:bg-gradient-to-r rtl:bg-gradient-to-l from-primary-500 vie-purple-500 to-pink-500 bg-clip-text">Create a New Account</h4>
                        <p class="mb-5 text-center text-gray-500 dark:text-dark-500">Already have an account? <a href="auth-signin-creative.html" class="">Sign In</a></p>
                        <form x-data="formValidation()" @submit.prevent="validateForm">
                            <div class="grid grid-cols-12 gap-4 mt-5">
                                <div class="col-span-12 md:col-span-6">
                                    <label for="firstNameInput" class="form-label">First Name</label>
                                    <input type="text" id="firstNameInput" class="w-full form-input" placeholder="Enter your first name"
                                           x-model="form.firstName" @input="validateField('firstName')">
                                    <p x-show="errors.firstName" class="text-sm text-red-500" x-text="errors.firstName"></p>
                                </div>
                                <div class="col-span-12 md:col-span-6">
                                    <label for="lastNameInput" class="form-label">Last Name</label>
                                    <input type="text" id="lastNameInput" class="w-full form-input" placeholder="Enter your last name"
                                           x-model="form.lastName" @input="validateField('lastName')">
                                    <p x-show="errors.lastName" class="text-sm text-red-500" x-text="errors.lastName"></p>
                                </div>
                                <div class="col-span-12 md:col-span-6">
                                    <label for="userNameInput" class="form-label">Username</label>
                                    <input type="text" id="userNameInput" class="w-full form-input" placeholder="Enter your username"
                                           x-model="form.userName" @input="validateField('userName')">
                                    <p x-show="errors.userName" class="text-sm text-red-500" x-text="errors.userName"></p>
                                </div>
                                <div class="col-span-12 md:col-span-6">
                                    <label for="emailInput" class="form-label">Email</label>
                                    <input type="email" id="emailInput" class="w-full form-input" placeholder="Enter your email"
                                           x-model="form.email" @input="validateField('email')">
                                    <p x-show="errors.email" class="text-sm text-red-500" x-text="errors.email"></p>
                                </div>
                                <div class="col-span-12">
                                    <div x-data="{ show: false }">
                                        <label for="passwordInput" class="form-label">Password</label>
                                        <div class="relative">
                                            <input type="password" id="passwordInput" x-bind:type="show ? 'text' : 'password'" class="ltr:pr-8 rtl:pl-8 form-input" placeholder="Enter your password"
                                                   x-model="form.password" @input="validateField('password')">
                                            <button type="button" @click="show = !show" class="absolute inset-y-0 flex items-center text-gray-500 dark:text-dark-500 ltr:right-3 rtl:left-3 focus:outline-hidden">
                                                <i data-lucide="eye" x-show="show" class="size-5"></i>
                                                <i data-lucide="eye-off" x-show="!show" class="size-5"></i>
                                            </button>
                                        </div>
                                        <p x-show="errors.password" class="text-sm text-red-500" x-text="errors.password"></p>
                                    </div>
                                </div>
                                <div class="col-span-12">
                                    <div x-data="{ show: false }">
                                        <label for="confirmPasswordInput" class="form-label">Confirm Password</label>
                                        <div class="relative">
                                            <input type="password" id="confirmPasswordInput" x-bind:type="show ? 'text' : 'password'" class="ltr:pr-8 rtl:pl-8 form-input" placeholder="Enter your confirm password"
                                                   x-model="form.confirmPassword" @input="validateField('confirmPassword')">
                                            <button type="button" @click="show = !show" class="absolute inset-y-0 flex items-center text-gray-500 dark:text-dark-500 ltr:right-3 rtl:left-3 focus:outline-hidden">
                                                <i data-lucide="eye" x-show="show" class="size-5"></i>
                                                <i data-lucide="eye-off" x-show="!show" class="size-5"></i>
                                            </button>
                                        </div>
                                        <p x-show="errors.confirmPassword" class="text-sm text-red-500" x-text="errors.confirmPassword"></p>
                                    </div>
                                </div>
                                <div class="col-span-12">
                                    <div class="items-start input-check-group grow">
                                        <input id="checkboxBasic1" class="input-check input-check-primary shrink-0" type="checkbox" x-model="form.terms" @change="validateField('terms')" />
                                        <label for="checkboxBasic1" class="leading-normal input-check-label">By creating an account, you agree to all of our terms condition & policies.</label>
                                    </div>
                                    <p x-show="errors.terms" class="text-sm text-red-500" x-text="errors.terms"></p>
                                </div>
                                <div class="col-span-12">
                                    <button type="submit" class="w-full btn btn-primary">Sign Up</button>
                                </div>
                            </div>
                        </form>
                        <div class="relative my-5 text-center text-gray-500 before:absolute dark:text-dark-500 before:border-gray-200 dark:before:border-dark-800 before:border-dashed before:w-full ltr:before:left-0 rtl:before:right-0 before:top-2.5 before:border-b">
                            <p class="relative inline-block px-2 bg-white dark:bg-dark-900">OR</p>
                        </div>
    
                        <div class="space-y-2">
                            <button type="button" class="w-full border-gray-200 dark:border-dark-800 btn hover:bg-gray-50 dark:hover:bg-dark-850 hover:text-primary-500"><img src="assets/images/others/google.png" alt="" class="inline-block h-4 ltr:mr-1 rtl:ml-1"> SignUp Vie Google</button>
                            <button type="button" class="w-full border-gray-200 dark:border-dark-800 btn hover:bg-gray-50 dark:hover:bg-dark-850 hover:text-primary-500"><i data-lucide="facebook" class="inline-block ltr:mr-1 rtl:ml-1 size-4 text-primary-500"></i> SignUp Vie Facebook</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{{> partials/vendor-scripts }}

<script type="module" src="assets/js/main.js"></script>

<script type="module" src="assets/js/auth/signup-validation.js"></script>

</body>
</html>