{{> partials/main }}

<head>

    {{> partials/title-meta title="Group Chat" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}
{{> partials/page-heading title="Group Chat" sub-title="Chats" }}

<div class="grid grid-cols-12 gap-x-space" x-data="groupChatComponent()" x-init="init()">
    <div class="col-span-12 2xl:col-span-3 card" id="chat-list">
        <div class="card-body">
            <button type="button" title="create group btn" data-modal-target="createGroupModal" @click="showGroupAddModal = true" class="w-full btn btn-primary">Create New Group</button>
            <div class="relative my-4 group/form">
                <input type="text" class="ltr:pl-9 rtl:pr-9 form-input ltr:group-[&.right]/form:pr-9 rtl:group-[&.right]/form:pl-9 ltr:group-[&.right]/form:pl-4 rtl:group-[&.right]/form:pr-4" placeholder="Search for ..." x-model="searchTerms" @input="filteredGroup">
                <button title="search btn" class="absolute inset-y-0 flex items-center text-gray-500 dark:text-dark-500 ltr:left-3 rtl:right-3 ltr:group-[&.right]/form:right-3 rtl:group-[&.right]/form:left-3 ltr:group-[&.right]/form:left-auto rtl:group-[&.right]/form:right-auto focus:outline-hidden">
                    <i data-lucide="search" class="size-4"></i>
                </button>
            </div>
            <div class="max-h-[calc(100vh_-_22.5rem)] -mx-space" data-simplebar >
                <ul class="flex flex-col gap-3">
                    <template x-if="filteredGroup.length > 0">
                        <template x-for="(group , index) in filteredGroup" :key="index">
                            <li>
                                <a href="#!" class="flex items-center gap-2 px-space py-2.5 hover:bg-gray-50 dark:hover:bg-dark-850 [&.active]:bg-primary-500/10 transition ease-linear duration-300 group/item unread" @click="setActiveChat(index)" :class="{'active': isActiveChat(index)} ">
                                    <div class="relative flex items-center justify-center p-2 font-semibold transition duration-200 ease-linear bg-green-500/10 [&.active]:bg-white dark:[&.active]:bg-dark-900 rounded-full size-11 shrink-0 group-[&.unread]/item:bg-white dark:group-[&.unread]/item:bg-dark-900">
                                        <img :src="group.image" alt="" class="rounded-full">
                                    </div>
                                    <div class="overflow-hidden grow">
                                        <h6 class="mb-0.5 truncate" x-text="group.name">Shopify Developers</h6>
                                        <p class="text-sm group-[&.unread]/item:font-medium truncate group-[&.unread]/item:text-gray-950 dark:group-[&.unread]/item:text-dark-50 text-gray-500 dark:text-dark-500 unread" x-text="group.message">Hello, How are you?</p>
                                    </div>
                                    <div class="ltr:text-right rtl:text-left shrink-0">
                                        <p class="mb-1 text-xs text-gray-500 dark:text-dark-500" x-text="group.time">11:48AM</p>
                                        <template x-if="group.badge">
                                            <span class="badge-sub-red badge-square size-5" x-text="group.badge">2</span>
                                        </template>
                                    </div>
                                </a>
                            </li>
                        </template>
                    </template>
                    <template x-if="filteredGroup.length === 0">
                        <p class="text-center">No groups found</p>
                    </template>
                </ul>
            </div>
        </div>
    </div>
    <div class="col-span-12 overflow-hidden 2xl:col-span-6 card"  id="chat-wrapper">
        <!-- messages -->
        <div class="max-h-[calc(100vh_-_19rem)] min-h-[calc(100vh_-_19rem)] relative" data-simplebar>
            <div class="sticky inset-x-0 top-0 z-50 flex items-center gap-3 border-b border-gray-200 card-body bg-white/30 dark:bg-dark-900/60 dark:border-dark-800 backdrop-blur-lg">
                <div class="xl:hidden shrink-0">
                    <button class="btn btn-sub-gray btn-icon" @Click="backToChatList()">
                        <i data-lucide="chevrons-left" class="size-5"></i>
                    </button>
                </div>
                <div class="relative flex items-center justify-center p-2 font-semibold transition duration-200 ease-linear bg-gray-100 rounded-full dark:bg-dark-850 size-11 shrink-0">
                    <img :src="avatar" alt="" class="rounded-full">
                </div>
                <div class="grow">
                    <h6><a href="#!" x-text="groupName">Shopify Developers</a></h6>
                    <p class="text-gray-500 dark:text-dark-500">Active</p>
                </div>
                <button title="phone call btn" class="btn btn-active-gray btn-icon shrink-0">
                    <i data-lucide="phone" class="size-5"></i>
                </button>
                <button title="video call btn" class="btn btn-active-gray btn-icon shrink-0">
                    <i data-lucide="video" class="size-5"></i>
                </button>
            </div>
            <div class="pb-0 card-body">
                <div class="flex flex-col justify-end min-h-[calc(100vh_-_24rem)] gap-5" id="groupchat-messages">
                    <template x-for="(message, index) in groupMessages" :key="index">
                        <div class="flex items-end max-w-xl gap-3 ltr:[&.right]:ml-auto rtl:[&.right]:mr-auto group/chat" :class="message.type == 'sent'? 'right' : ''">
                            <div class="relative flex items-center justify-center font-semibold transition duration-200 ease-linear bg-gray-100 dark:bg-dark-850 rounded-full size-8 shrink-0 group-[&.right]/chat:order-2">
                                <img :src="message.user.avatar" alt="" class="rounded-full">
                                <span class="absolute bottom-0 bg-green-500 border-2 border-white dark:border-dark-900 rounded-full ltr:right-0 rtl:left-0 size-2.5"></span>
                            </div>
                            <div class="grow *:mb-3">
                                <div class="flex items-end gap-2 last:mb-0">
                                    <div class="grow">
                                        <p class="ltr:group-[&.right]/chat:text-right rtl:group-[&.right]/chat:text-left text-gray-500 dark:text-dark-500 mb-1 text-xs" x-text="message.timestamp">Today, 09:59 AM</p>
                                        <div class="px-4 py-2.5 last:mb-0 bg-gray-100 rounded-xl dark:bg-dark-850 ltr:rounded-bl-none rtl:rounded-br-none group-[&.right]/chat:order-1 ltr:group-[&.right]/chat:rounded-bl-lg rtl:group-[&.right]/chat:rounded-br-lg ltr:group-[&.right]/chat:rounded-br-none rtl:group-[&.right]/chat:rounded-bl-none" x-text="message.message">
                                            Hey team, I hope everyone is doing well. Let's do a quick standup. What are everyone's updates for today?
                                        </div>
                                    </div>
                                    <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown shrink-0">
                                        <button x-ref="button" x-on:click="toggle()" title="dropdown btn" :aria-expanded="open.toString()" type="button" class="flex items-center text-gray-500">
                                            <i class="ri-more-2-fill"></i>
                                        </button>
                                        <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed dropdown-menu p-2" dropdown-position="right">
                                            <ul>
                                                <li>
                                                    <a href="#!" class="flex items-center px-4 py-1.5 text-gray-500 hover:text-primary-500 text-14 transition duration-300 ease-linear">
                                                        <i class="align-middle ltr:mr-2 rtl:ml-2 ri-reply-line"></i> <span>Reply</span>
                                                    </a>
                                                </li>
                                                <li>
                                                    <a href="#!" @click="deleteIndex = message.message; showDeleteModal = true" class="flex items-center px-4 py-1.5 text-gray-500 hover:text-red-500 text-14 transition duration-300 ease-linear" data-modal-target="deleteModal">
                                                        <i class="align-middle ltr:mr-2 rtl:ml-2 ri-delete-bin-line"></i> <span>Delete</span>
                                                    </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </template>
                </div>
            </div>
        </div>
        <!-- send messsge div -->
        <div class="card-body">
            <div class="flex items-center gap-2 p-2 border border-gray-200 rounded-md dark:border-dark-800">
                <button title="voice audio" class="btn btn-active-gray btn-icon shrink-0">
                    <i data-lucide="audio-lines" class="size-5"></i>
                </button>
                <input type="text" class="border-0 form-input grow" placeholder="Type something ..." x-model="message" @keydown.enter.prevent="sendMessage(message)">
                <button title="submit" type="submit" @click="sendMessage(message)" class="btn btn-active-primary btn-icon shrink-0">
                    <i data-lucide="send" class="size-5"></i>
                </button>
                <div class="shrink-0">
                    <label for="sendImages" class="btn btn-active-gray btn-icon">
                        <i data-lucide="image" class="size-5"></i>
                    </label>
                    <input title="Images upload" type="file" id="sendImages" class="hidden">
                </div>
                <button title="emoji" class="text-lg btn btn-active-gray btn-icon shrink-0">
                    😊
                </button>
                <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown">
                    <button x-ref="button" x-on:click="toggle()" title="dropdown btn" :aria-expanded="open.toString()" type="button" class="text-lg btn btn-active-gray btn-icon shrink-0">
                        <i data-lucide="ellipsis" class="size-5"></i>
                    </button>
                    <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden" dropdown-position="right">
                        <ul>
                            <li>
                                <a href="#!" class="dropdown-item">
                                    <i class="align-middle ltr:mr-2 rtl:ml-2 ri-chat-4-line"></i> <span>Clear Chat</span>
                                </a>
                            </li>
                            <li>
                                <a href="#!" data-modal-target="deleteModal" class="dropdown-item">
                                    <i class="align-middle ltr:mr-2 rtl:ml-2 ri-delete-bin-line"></i> <span>Delete</span>
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-span-12 2xl:col-span-3 card">
        <div class="card-body">
            <div class="text-center">
                <div class="relative flex items-center justify-center p-4 font-semibold transition duration-200 ease-linear bg-green-500/10 rounded-full size-24 mx-auto shrink-0 group-[&.unread]/item:bg-white dark:group-[&.unread]/item:bg-dark-900">
                    <img :src="avatar" alt="" class="rounded-full">
                </div>
                <h6 class="mt-3" x-text="groupName">Shopify Developers</h6>
                <p class="text-gray-500 dark:text-dark-500">Create by admin</p>
            </div>
            <div>
                <div class="flex items-center mt-5 mb-3">
                    <h6 class="grow">Member (<span x-text="memberCount"></span>)</h6>
                    <a href="#!" class="link link-primary"><i data-lucide="plus" class="inline-block size-4"></i> Add</a>
                </div>

                <div class="px-5 -mx-5 max-h-72" data-simplebar>
                    <div class="flex flex-col gap-4">
                        <template x-for="member in filteredMembers" :key="member.id">
                            <a href="#!" class="flex items-center gap-2">
                                <div class="flex items-center justify-center font-semibold transition duration-200 ease-linear bg-gray-100 rounded-full dark:bg-dark-850 shrink-0 size-6">
                                    <img :src="member.avatar" alt="" class="rounded-full">
                                </div>
                                <h6 class="grow" x-text="member.name"></h6>
                                <p class="text-gray-500 dark:text-dark-500" x-text="member.role"></p>
                            </a>
                        </template>
                    </div>
                </div>

                <div class="mt-5">
                    <h6 class="mb-2">Attachments</h6>
                    <div class="px-5 -mx-5 max-h-36" data-simplebar>
                        <div class="flex flex-col gap-3">
                            <a href="#!" class="flex items-center gap-2 p-2 transition duration-300 ease-linear border border-gray-200 border-dashed rounded-md dark:border-dark-800 hover:border-gray-300 dark:hover:border-dark-700 hover:shadow-lg hover:shadow-gray-200 dark:hover:shadow-dark-850">
                                <div class="flex items-center justify-center font-semibold text-gray-500 transition duration-200 ease-linear bg-gray-100 rounded-full shrink-0 size-10 dark:bg-dark-850">
                                    <i data-lucide="file-text" class="size-4"></i>
                                </div>
                                <div class="grow">
                                    <h6>shopify-docs.txt</h6>
                                    <p class="text-sm text-gray-500 dark:text-dark-500">154 kb</p>
                                </div>
                                <div class="shrink-0">
                                    <i data-lucide="download" class="text-gray-500 dark:text-dark-500 size-5 fill-gray-200 dark:fill-dark-850"></i>
                                </div>
                            </a>
                            <a href="#!" class="flex items-center gap-2 p-2 transition duration-300 ease-linear border border-gray-200 border-dashed rounded-md dark:border-dark-800 hover:border-gray-300 dark:hover:border-dark-700 hover:shadow-lg hover:shadow-gray-200 dark:hover:shadow-dark-850">
                                <div class="flex items-center justify-center font-semibold transition duration-200 ease-linear bg-gray-100 rounded-full dark:bg-dark-850 shrink-0 size-10">
                                    <i data-lucide="image" class="size-4"></i>
                                </div>
                                <div class="grow">
                                    <h6>main-logo.png</h6>
                                    <p class="text-sm text-gray-500 dark:text-dark-500">467 kb</p>
                                </div>
                                <div class="shrink-0">
                                    <i data-lucide="download" class="text-gray-500 size-5 dark:text-dark-500 fill-gray-200 dark:fill-dark-800"></i>
                                </div>
                            </a>
                            <a href="#!" class="flex items-center gap-2 p-2 transition duration-300 ease-linear border border-gray-200 border-dashed rounded-md dark:border-dark-800 hover:border-gray-300 dark:hover:border-dark-700 hover:shadow-lg hover:shadow-gray-200 dark:hover:shadow-dark-850">
                                <div class="flex items-center justify-center font-semibold text-gray-500 transition duration-200 ease-linear bg-gray-100 rounded-full dark:text-dark-500 dark:bg-dark-850 shrink-0 size-10">
                                    <i data-lucide="file-archive" class="size-4"></i>
                                </div>
                                <div class="grow">
                                    <h6>chat.zip</h6>
                                    <p class="text-sm text-gray-500 dark:text-dark-500">48 mb</p>
                                </div>
                                <div class="shrink-0">
                                    <i data-lucide="download" class="text-gray-500 dark:text-dark-500 size-5 fill-gray-200 dark:fill-dark-850"></i>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!--Add Group Modals-->
    <div id="createGroupModal" class="!hidden modal show" x-show="showGroupAddModal">
        <div class="modal-wrap modal-center">
            <div class="modal-header">
                <h6>New Group</h6>
                <button data-modal-close="createGroupModal" class="link link-red"><i data-lucide="x" class="size-5"></i></button>
            </div>
            <div class="modal-content">
                <form @submit.prevent="addGroup()">
                    <div class="mb-5">
                        <label for="basicInput1" class="form-label">Group Name</label>
                        <input type="text" id="basicInput1" class="form-input" required placeholder="Enter group title" x-model="groupForm.name" required>
                    </div>
                    <div class="mb-5">
                        <label for="addMemberSelect" class="form-label">Add Contacts</label>
                        <div id="addMemberSelect" placeholder="Select Contact" required></div>
                    </div>
                    <div class="flex items-center justify-end gap-2">
                        <button type="button" data-modal-close="createGroupModal" class="btn btn-sub-gray">Cancel</button>
                        <button type="submit" class="btn btn-primary">Create Group</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!--delete modals-->
    <div id="deleteModal" class="!hidden modal show" x-show="showDeleteModal">
        <div class="modal-wrap modal-xs modal-center">
            <div class="text-center modal-content p-7">
                <div class="flex items-center justify-center mx-auto mb-4 text-red-500 rounded-full bg-red-500/10 size-14 backdrop-blur-xl">
                    <i data-lucide="trash-2" class="size-6"></i>
                </div>
                <h5 class="mb-4">Are you sure you want to delete this msg ?</h5>
                <div class="flex items-center justify-center gap-2">
                    <button class="btn btn-red" @click="DeleteMessage()">Delete</button>
                    <button data-modal-close="deleteModal" class="btn link link-primary">Cancel</button>
                </div>
            </div>
        </div>
    </div><!--end-->
</div>
</div>
{{> partials/footer }}
</div>



{{> partials/vendor-scripts }}

<script type="module" src="assets/js/apps/chat/group.init.js"></script>

<script type="module" src="assets/js/main.js"></script>

</body>
</html>