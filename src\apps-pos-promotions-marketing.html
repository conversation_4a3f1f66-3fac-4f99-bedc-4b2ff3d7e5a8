{{> partials/main }}

<head>

    {{> partials/title-meta title="Store Configuration" }}

    <link rel="stylesheet" href="/assets/libs/virtual-select-plugin/virtual-select.min.css">

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}
{{> partials/page-heading title="Store Configuration" sub-title="POS" }}

<!-- Main Container -->
<div x-data="promotionTable">
    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-x-5">
        <div class="card before:absolute ltr:before:left-2 rtl:before:right-2 before:w-2 before:inset-y-2 before:bg-gradient-to-r before:from-primary-500 before:to-primary-600 before:rounded-md relative">
            <div class="card-body flex items-center ltr:pl-8 rtl:pr-8 gap-4">
                <div class="size-12 bg-primary-500/15 text-primary-500 rounded-lg flex items-center justify-center shrink-0">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M9.568 3H5.25A2.25 2.25 0 0 0 3 5.25v4.318c0 .597.237 1.17.659 1.591l9.581 9.581c.699.699 1.78.872 2.607.33a18.095 18.095 0 0 0 5.223-5.223c.542-.827.369-1.908-.33-2.607L11.16 3.66A2.25 2.25 0 0 0 9.568 3Z" />
                        <path stroke-linecap="round" stroke-linejoin="round" d="M6 6h.008v.008H6V6Z" />
                    </svg>
                </div>
                <div class="grow">
                    <p class="text-sm text-gray-500 dark:text-dark-500 mb-1">Active Promotions</p>
                    <h4>23</h4>
                </div>
            </div>
        </div>
        <div class="card before:absolute ltr:before:left-2 rtl:before:right-2 before:w-2 before:inset-y-2 before:bg-gradient-to-r before:from-green-500 before:to-green-600 before:rounded-md relative">
            <div class="card-body flex items-center ltr:pl-8 rtl:pr-8 gap-4">
                <div class="size-12 bg-green-500/15 text-green-500 rounded-lg flex items-center justify-center shrink-0">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                    </svg>
                </div>
                <div class="grow">
                    <p class="text-sm text-gray-500 dark:text-dark-500 mb-1">Revenue Impact</p>
                    <h4>$12.4K</h4>
                </div>
            </div>
        </div>
        <div class="card before:absolute ltr:before:left-2 rtl:before:right-2 before:w-2 before:inset-y-2 before:bg-gradient-to-r before:from-purple-500 before:to-purple-600 before:rounded-md relative">
            <div class="card-body flex items-center ltr:pl-8 rtl:pr-8 gap-4">
                <div class="size-12 bg-purple-500/15 text-purple-500 rounded-lg flex items-center justify-center shrink-0">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M18 18.72a9.094 9.094 0 0 0 3.741-.479 3 3 0 0 0-4.682-2.72m.94 3.198.001.031c0 .225-.012.447-.037.666A11.944 11.944 0 0 1 12 21c-2.17 0-4.207-.576-5.963-1.584A6.062 6.062 0 0 1 6 18.719m12 0a5.971 5.971 0 0 0-.941-3.197m0 0A5.995 5.995 0 0 0 12 12.75a5.995 5.995 0 0 0-5.058 2.772m0 0a3 3 0 0 0-4.681 2.72 8.986 8.986 0 0 0 3.74.477m.94-3.197a5.971 5.971 0 0 0-.94 3.197M15 6.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm6 3a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Zm-13.5 0a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Z" />
                    </svg>
                </div>
                <div class="grow">
                    <p class="text-sm text-gray-500 dark:text-dark-500 mb-1">Customers Reached</p>
                    <h4>1,847</h4>
                </div>
            </div>
        </div>
        <div class="card before:absolute ltr:before:left-2 rtl:before:right-2 before:w-2 before:inset-y-2 before:bg-gradient-to-r before:from-orange-500 before:to-orange-600 before:rounded-md relative">
            <div class="card-body flex items-center ltr:pl-8 rtl:pr-8 gap-4">
                <div class="size-12 bg-orange-500/15 text-orange-500 rounded-lg flex items-center justify-center shrink-0">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 18 9 11.25l4.306 4.306a11.95 11.95 0 0 1 5.814-5.518l2.74-1.22m0 0-5.94-2.281m5.94 2.28-2.28 5.941" />
                    </svg>
                </div>
                <div class="grow">
                    <p class="text-sm text-gray-500 dark:text-dark-500 mb-1">Conversion Rate</p>
                    <h4>8.5%</h4>
                </div>
            </div>
        </div>
    </div>

    <!-- Actions and Filters -->
    <div class="card mb-6">
        <div class="card-header flex flex-col sm:flex-row sm:items-center gap-4">
            <h6 class="card-title grow">Promotions Management</h6>
            <div class="relative group/form">
                <input type="text" class="ltr:pl-9 rtl:pr-9 form-input ltr:group-[&.right]/form:pr-9 rtl:group-[&.right]/form:pl-9 ltr:group-[&.right]/form:pl-4 rtl:group-[&.right]/form:pr-4" placeholder="Search for ..." @input="filteredEmployee" x-model="searchTerm">
                <button aria-label="search-button" class="absolute inset-y-0 flex items-center text-gray-500 dark:text-dark-500 ltr:left-3 rtl:right-3 ltr:group-[&.right]/form:right-3 rtl:group-[&.right]/form:left-3 ltr:group-[&.right]/form:left-auto rtl:group-[&.right]/form:right-auto focus:outline-hidden">
                    <i data-lucide="search" class="size-4"></i>
                </button>
            </div>
            <select class="form-input form-select w-auto">
                <option>All Status</option>
                <option>Active</option>
                <option>Scheduled</option>
                <option>Expired</option>
                <option>Draft</option>
            </select>
        </div>

        <div class="card-body pt-0">
            <div class="overflow-x-auto table-box">
                <table class="table hovered whitespace-nowrap">
                    <tbody>
                        <tr class="text-gray-500 bg-gray-100 dark:bg-dark-850 dark:text-dark-500">
                            <td>
                                <div class="input-check-group">
                                    <input id="checkboxBasic1" class="input-check input-check-primary" type="checkbox" x-model="selectAll" @change="toggleAll" />
                                    <label for="checkboxBasic1" class="input-check-label hidden">Default</label>
                                </div>
                            </td>
                            <th x-on:click="sort('promoCode')" class="cursor-pointer font-medium">
                                Promo code
                                <span x-show="sortBy === 'promoCode'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span>
                            </th>
                            <th x-on:click="sort('promotionName')" class="cursor-pointer font-medium">
                                Promotion Name
                                <span x-show="sortBy === 'promotionName'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span>
                            </th>
                            <th x-on:click="sort('type')" class="cursor-pointer font-medium">
                                Type
                                <span x-show="sortBy === 'type'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span>
                            </th>
                            <th x-on:click="sort('discount')" class="cursor-pointer font-medium">
                                Discount
                                <span x-show="sortBy === 'discount'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span>
                            </th>
                            <th x-on:click="sort('duration')" class="cursor-pointer font-medium">
                                Duration
                                <span x-show="sortBy === 'duration'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span>
                            </th>
                            <th x-on:click="sort('status')" class="cursor-pointer font-medium">
                                Status
                                <span x-show="sortBy === 'status'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span>
                            </th>
                            <th x-on:click="sort('usage')" class="cursor-pointer font-medium">
                                Usage
                                <span x-show="sortBy === 'usage'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span>
                            </th>
                            <th class="cursor-pointer font-medium">Actions</th>
                        </tr>

                        <template x-if="displayedPromotion.length > 0">
                            <template x-for="(promotion, index) in displayedPromotion" :key="index">
                                <tr>
                                    <td>
                                        <div class="input-check-group">
                                            <input :id="'checkbox-' + index" class="input-check input-check-primary" type="checkbox" :checked="isItemSelected(promotion)" @change="toggleItem(promotion)" />
                                            <label :for="'checkbox-' + index" class="input-check-label hidden">Default</label>
                                        </div>
                                    </td>
                                    <td>
                                        <p class="text-gray-500 dark:text-dark-500" x-text="promotion.promoCode"></p>
                                    </td>
                                    <td>
                                        <div class="flex items-center gap-3">
                                            <div class="size-10 bg-primary-500/15 rounded-lg flex items-center justify-center shrink-0">
                                                <span class="text-primary-500 font-medium text-sm" x-text="generateAvatarText(promotion)"></span>
                                            </div>
                                            <div class="grow">
                                                <h6 class="text-sm font-medium" x-text="promotion.promotionName.title"></h6>
                                                <div class="text-sm text-gray-500 dark:text-dark-500" x-text="promotion.promotionName.description"></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td x-text="promotion.type"></td>
                                    <td x-text="promotion.discount"></td>
                                    <td>
                                        <p class="text-gray-500 dark:text-dark-500" x-text="promotion.duration"></p>
                                    </td>
                                    <td>
                                        <span x-text="promotion.status" :class="{
                            'badge badge-green': promotion.status === 'Active',
                            'badge badge-sky': promotion.status === 'Scheduled',
                            'badge badge-orange': promotion.status === 'Inactive',
                        }"></span>
                                    </td>
                                    <td>
                                        <p class="text-gray-500 dark:text-dark-500" x-text="promotion.usage"></p>
                                    </td>
                                    <td>
                                        <div class="flex gap-2">
                                            <button class="link link-primary" @click="editPromotion(promotion)">Edit</button>
                                            <button class="link link-red" @click="deletePromotion(promotion)">Delete</button>
                                        </div>
                                    </td>
                                </tr>
                            </template>
                        </template>

                        <template x-if="displayedPromotion.length === 0">
                            <tr>
                                <td colspan="9" class="!p-8">
                                    <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" class="mx-auto size-12" viewBox="0 0 48 48">
                                        <linearGradient id="SVGID_1__h35ynqzIJzH4_gr1" x1="34.598" x2="15.982" y1="15.982" y2="34.598" gradientUnits="userSpaceOnUse">
                                            <stop offset="0" stop-color="#60e8fe"></stop>
                                            <stop offset=".033" stop-color="#6ae9fe"></stop>
                                            <stop offset=".197" stop-color="#97f0fe"></stop>
                                            <stop offset=".362" stop-color="#bdf5ff"></stop>
                                            <stop offset=".525" stop-color="#dafaff"></stop>
                                            <stop offset=".687" stop-color="#eefdff"></stop>
                                            <stop offset=".846" stop-color="#fbfeff"></stop>
                                            <stop offset="1" stop-color="#fff"></stop>
                                        </linearGradient>
                                        <path fill="url(#SVGID_1__h35ynqzIJzH4_gr1)" d="M40.036,33.826L31.68,25.6c0.847-1.739,1.335-3.684,1.335-5.748c0-7.27-5.894-13.164-13.164-13.164	S6.688,12.582,6.688,19.852c0,7.27,5.894,13.164,13.164,13.164c2.056,0,3.995-0.485,5.728-1.326l3.914,4.015l4.331,4.331	c1.715,1.715,4.496,1.715,6.211,0C41.751,38.321,41.751,35.541,40.036,33.826z"></path>
                                        <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M31.95,25.739l8.086,8.086c1.715,1.715,1.715,4.496,0,6.211l0,0c-1.715,1.715-4.496,1.715-6.211,0	l-4.331-4.331"></path>
                                        <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M7.525,24.511c-1.771-4.694-0.767-10.196,3.011-13.975c3.847-3.847,9.48-4.817,14.228-2.912"></path>
                                        <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M30.856,12.603c3.376,5.114,2.814,12.063-1.688,16.565c-4.858,4.858-12.565,5.129-17.741,0.814"></path>
                                    </svg>
                                    <p class="mt-2 text-center text-gray-500 dark:text-dark-500">No matching records found</p>
                                </td>
                            </tr>
                        </template>
                    </tbody>
                </table>
            </div>

            <div class="grid items-center grid-cols-12 gap-space mt-space" x-show="displayedPromotion.length > 0">
                <div class="col-span-12 text-center lg:col-span-6 lg:ltr:text-left lg:rtl:text-right">
                    <p class="text-gray-500 dark:text-dark-500">Showing <b x-text="showingStart"></b> - <b x-text="showingEnd"></b> of <b x-text="filterPromotion.length"></b> Results</p>
                </div>
                <div class="col-span-12 lg:col-span-6">
                    <div class="flex justify-center gap-2 lg:justify-end pagination pagination-primary">
                        <button @click="prevPage()" :disabled="currentPage === 1" class="pagination-pre">
                            <i data-lucide="chevron-left" class="mr-1 ltr:inline-block rtl:hidden size-4"></i>
                            <i data-lucide="chevron-right" class="ml-1 rtl:inline-block ltr:hidden size-4"></i>
                            Prev
                        </button>
                        <template x-for="page in totalPages" :key="page">
                            <button @click="gotoPage(page)" :class="{ 'active': currentPage === page }" class="pagination-item">
                                <span x-text="page"></span>
                            </button>
                        </template>
                        <button @click="nextPage()" :disabled="currentPage === totalPages" class="pagination-next">
                            Next
                            <i data-lucide="chevron-right" class="ml-1 ltr:inline-block rtl:hidden size-4"></i>
                            <i data-lucide="chevron-left" class="mr-1 rtl:inline-block ltr:hidden size-4"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-12 gap-x-5">
        <div class="card col-span-12 xl:col-span-8">
            <div class="card-header">
                <h6 class="card-title">
                    <span x-text="editingPromotion ? 'Edit Promotion' : 'Quick Create Promotion'"></span>
                </h6>
            </div>
            <div class="card-body">
                <form class="space-y-4" @submit.prevent="savePromotion">
                    <div>
                        <label class="form-label">Promotion Name</label>
                        <input type="text" class="form-input" x-model="newPromotion.promotionName.title" placeholder="Enter promotion name" required>
                    </div>
                    <div>
                        <label class="form-label">Description</label>
                        <input type="text" class="form-input" x-model="newPromotion.promotionName.description" placeholder="Enter promotion description" required>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                        <div>
                            <label class="form-label">Type</label>
                            <select class="form-input form-select" x-model="newPromotion.type" required>
                                <option value="Percentage">Percentage</option>
                                <option value="Fixed Amount">Fixed Amount</option>
                                <option value="BOGO">Buy One Get One</option>
                            </select>
                        </div>
                        <div>
                            <label class="form-label">Value</label>
                            <input type="text" class="form-input" x-model="newPromotion.discount" placeholder="10%, $5, etc." required>
                        </div>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                        <div>
                            <label class="form-label">Start Date</label>
                            <input type="date" class="form-input" x-model="newPromotion.startDate" required>
                        </div>
                        <div>
                            <label class="form-label">End Date</label>
                            <input type="date" class="form-input" x-model="newPromotion.endDate" required>
                        </div>
                    </div>
                    <div>
                        <label class="form-label">Status</label>
                        <select class="form-input form-select" x-model="newPromotion.status" required>
                            <option value="Active">Active</option>
                            <option value="Scheduled">Scheduled</option>
                            <option value="Inactive">Inactive</option>
                        </select>
                    </div>
                    <div class="flex gap-2">
                        <button type="submit" class="btn btn-primary grow">
                            <span x-text="editingPromotion ? 'Save Changes' : 'Create Promotion'"></span>
                        </button>
                        <button type="button" class="btn btn-sub-red shrink-0" x-show="editingPromotion" @click="cancelEdit()">
                            Cancel
                        </button>
                    </div>
                </form>
            </div>
        </div>
        <div class="card col-span-12 xl:col-span-4">
            <div class="card-header flex items-center gap-4">
                <h6 class="card-title grow">Recent Activity</h6>
                <a href="#!" class="link link-red shrink-0">View More <i data-lucide="move-right" class="size-4 inline-block ltr:ml-1 rtl:mr-1"></i></a>
            </div>
            <div class="card-body">
                <div class="max-h-[460px] -mx-space px-space" data-simplebar>
                    <div class="flex flex-col gap-4">
                        <template x-for="activity in recentActivities" :key="activity.id">
                            <div class="flex items-start space-x-3">
                                <div class="size-2 rounded-full mt-2" :class="{
                                'bg-green-500': activity.type === 'green',
                                'bg-blue-500': activity.type === 'blue',
                                'bg-orange-500': activity.type === 'orange',
                                'bg-yellow-500': activity.type === 'yellow',
                                'bg-red-500': activity.type === 'red'
                            }"></div>
                                <div class="flex-1">
                                    <p class="text-sm">
                                        <span x-text="activity.promotionName"></span>
                                        <span x-text="activity.action === 'created' ? ' created' : 
                                                 activity.action === 'updated' ? ' updated' : 
                                                 activity.action === 'deleted' ? ' deleted' : 
                                                 ' expired'"></span>
                                    </p>
                                    <p class="text-xs text-gray-500 dark:text-dark-500" x-text="getTimeAgo(activity.timestamp)"></p>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{{> partials/footer }}
</div>

{{> partials/vendor-scripts }}

<script type="module" src="assets/js/apps/pos/promotions-marketing.init.js"></script>

<script type="module" src="assets/js/main.js"></script>

</body>

</html>