{{> partials/main }}

<head>

    {{> partials/title-meta title="User Followers" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}

<div class="relative mb-6">
    <div class="relative overflow-hidden rounded-md h-44 bg-primary-500/10">
        <div class="border-[60px] border-t-primary-500 border-l-primary-500 absolute opacity-10 -top-2 left-0 rotate-45 size-96"></div>
        <div class="border-[60px] border-green-500 absolute opacity-10 top-20 left-8 rotate-45 size-80"></div>
        <div class="border-[60px] border-pink-500 absolute opacity-10 top-36 left-28 rotate-45 size-40"></div>
    </div>
    <div class="text-center">
        <div class="relative inline-block mx-auto">
            <div class="relative p-1 rounded-full bg-gradient-to-tr from-primary-300 via-red-300 to-green-300 -mt-14">
                <img src="assets/images/avatar/user-17.png" alt="" class="mx-auto border-4 border-white rounded-full dark:border-dark-900 size-28">
            </div>
            <div class="absolute border-2 border-white dark:border-dark-900 rounded-full size-4 bg-green-500 bottom-2.5 ltr:right-2.5 rtl:left-2.5"></div>
        </div>
        <h5 class="mt-2 mb-1">Sophia Mia <i data-lucide="badge-check" class="inline-block text-primary-500 fill-primary-500/20 size-5"></i></h5>
        <ul class="flex flex-wrap items-center justify-center gap-2 text-gray-500 dark:text-dark-500 text-14">
            <li><i data-lucide="building-2" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-middle">UI / UX Designer</span></li>
            <li><i data-lucide="map-pin" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-middle">Argentina</span></li>
            <li><i data-lucide="calendar-days" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-middle">24 April, 2024</span></li>
        </ul>
    </div>
</div>

<ul class="pb-2 overflow-x-auto tabs-pills lg:pb-0">
    <li>
        <a href="pages-user.html" class="nav-item text-gray-500 dark:text-dark-500 [&.active]:bg-primary-500 [&.active]:text-primary-50">
            <i data-lucide="eye" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-middle whitespace-nowrap">Overview</span>
        </a>
    </li>
    <li>
        <a href="pages-user-activity.html" class="nav-item text-gray-500 dark:text-dark-500 [&.active]:bg-primary-500 [&.active]:text-primary-50">
            <i data-lucide="sparkles" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-middle whitespace-nowrap">Activity</span>
        </a>
    </li>
    <li>
        <a href="pages-user-followers.html" class="nav-item text-gray-500 dark:text-dark-500 [&.active]:bg-primary-500 [&.active]:text-primary-50 active">
            <i data-lucide="user-round" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-middle whitespace-nowrap">Followers</span>
        </a>
    </li>
    <li>
        <a href="pages-user-documents.html" class="nav-item text-gray-500 dark:text-dark-500 [&.active]:bg-primary-500 [&.active]:text-primary-50">
            <i data-lucide="file-text" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-middle whitespace-nowrap">Documents</span>
        </a>
    </li>
    <li>
        <a href="pages-user-notes.html" class="nav-item text-gray-500 dark:text-dark-500 [&.active]:bg-primary-500 [&.active]:text-primary-50">
            <i data-lucide="list" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-middle whitespace-nowrap">Notes</span>
        </a>
    </li>
    <li>
        <a href="pages-user-projects.html" class="nav-item text-gray-500 dark:text-dark-500 [&.active]:bg-primary-500 [&.active]:text-primary-50">
            <i data-lucide="monitor" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-middle whitespace-nowrap">Projects</span>
        </a>
    </li>
</ul>

<div class="grid grid-cols-1 mt-5 lg:grid-cols-2 gap-x-5">
    <div class="card">
        <div class="flex flex-wrap items-center gap-3 card-body">
            <div class="shrink-0">
                <img src="assets/images/avatar/user-13.png" alt="" class="rounded-md size-20">
            </div>
            <div class="grow">
                <h6 class="mb-1">Christina Williams</h6>
                <div class="flex flex-wrap items-center gap-4 mb-3">
                    <p class="text-gray-500 dark:text-dark-500"><i data-lucide="mail" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <a href="mailto:<EMAIL>" class="align-middle whitespace-nowrap"><EMAIL></a></p>
                    <p class="text-gray-500 dark:text-dark-500"><i data-lucide="phone" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-middle whitespace-nowrap">+(546) 01234 567 89</span></p>
                </div>
                <a href="pages-user.html" class="text-primary-500">
                    View More
                    <i data-lucide="move-right" class="ml-1 ltr:inline-block rtl:hidden size-4"></i>
                    <i data-lucide="move-left" class="mr-1 rtl:inline-block ltr:hidden size-4"></i>
                </a>
            </div>
            <div x-data="{ loadingButton: false, isActive: false }">
                <button @click="loadingButton = true; setTimeout(() => { loadingButton = false; isActive = !isActive; }, 2000)" class="btn btn-sub-gray btn-icon-text">
                    <span class="flex items-center gap-2" x-show="!isActive"><i class="ri-user-add-line"></i> Follow</span>
                    <span class="flex items-center gap-2" x-show="isActive"><i class="ri-user-unfollow-line"></i> UnFollow</span>
                    <svg x-show="loadingButton" class="size-4 animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-0" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                </button>
            </div>
        </div>
    </div><!--end col-->
    <div class="card">
        <div class="flex flex-wrap items-center gap-3 card-body">
            <div class="shrink-0">
                <img src="assets/images/avatar/user-14.png" alt="" class="rounded-md size-20">
            </div>
            <div class="grow">
                <h6 class="mb-1">Thomas Blamer</h6>
                <div class="flex flex-wrap items-center gap-4 mb-3">
                    <p class="text-gray-500 dark:text-dark-500"><i data-lucide="mail" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <a href="mailto:<EMAIL>" class="align-middle whitespace-nowrap"><EMAIL></a></p>
                    <p class="text-gray-500 dark:text-dark-500"><i data-lucide="phone" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-middle whitespace-nowrap">************</span></p>
                </div>
                <a href="pages-user.html" class="text-primary-500">
                    View More
                    <i data-lucide="move-right" class="ml-1 ltr:inline-block rtl:hidden size-4"></i>
                    <i data-lucide="move-left" class="mr-1 rtl:inline-block ltr:hidden size-4"></i>
                </a>
            </div>
            <div x-data="{ loadingButton: false, isActive: true }">
                <button @click="loadingButton = true; setTimeout(() => { loadingButton = false; isActive = !isActive; }, 2000)" class="btn btn-sub-gray btn-icon-text">
                    <span class="flex items-center gap-2" x-show="!isActive"><i class="ri-user-add-line"></i> Follow</span>
                    <span class="flex items-center gap-2" x-show="isActive"><i class="ri-user-unfollow-line"></i> UnFollow</span>
                    <svg x-show="loadingButton" class="size-4 animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-0" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                </button>
            </div>
        </div>
    </div><!--end col-->
    <div class="card">
        <div class="flex flex-wrap items-center gap-3 card-body">
            <div class="shrink-0">
                <img src="assets/images/avatar/user-15.png" alt="" class="rounded-md size-20">
            </div>
            <div class="grow">
                <h6 class="mb-1">Patricia Graham</h6>
                <div class="flex flex-wrap items-center gap-4 mb-3">
                    <p class="text-gray-500 dark:text-dark-500"><i data-lucide="mail" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <a href="mailto:<EMAIL>" class="align-middle whitespace-nowrap"><EMAIL></a></p>
                    <p class="text-gray-500 dark:text-dark-500"><i data-lucide="phone" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-middle whitespace-nowrap">************</span></p>
                </div>
                <a href="pages-user.html" class="text-primary-500">
                    View More
                    <i data-lucide="move-right" class="ml-1 ltr:inline-block rtl:hidden size-4"></i>
                    <i data-lucide="move-left" class="mr-1 rtl:inline-block ltr:hidden size-4"></i>
                </a>
            </div>
            <div x-data="{ loadingButton: false, isActive: true }">
                <button @click="loadingButton = true; setTimeout(() => { loadingButton = false; isActive = !isActive; }, 2000)" class="btn btn-sub-gray btn-icon-text">
                    <span class="flex items-center gap-2" x-show="!isActive"><i class="ri-user-add-line"></i> Follow</span>
                    <span class="flex items-center gap-2" x-show="isActive"><i class="ri-user-unfollow-line"></i> UnFollow</span>
                    <svg x-show="loadingButton" class="size-4 animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-0" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                </button>
            </div>
        </div>
    </div><!--end col-->
    <div class="card">
        <div class="flex flex-wrap items-center gap-3 card-body">
            <div class="shrink-0">
                <img src="assets/images/avatar/user-19.png" alt="" class="rounded-md size-20">
            </div>
            <div class="grow">
                <h6 class="mb-1">Patricia Graham</h6>
                <div class="flex flex-wrap items-center gap-4 mb-3">
                    <p class="text-gray-500 dark:text-dark-500"><i data-lucide="mail" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <a href="mailto:<EMAIL>" class="align-middle whitespace-nowrap"><EMAIL></a></p>
                    <p class="text-gray-500 dark:text-dark-500"><i data-lucide="phone" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-middle whitespace-nowrap">************</span></p>
                </div>
                <a href="pages-user.html" class="text-primary-500">
                    View More
                    <i data-lucide="move-right" class="ml-1 ltr:inline-block rtl:hidden size-4"></i>
                    <i data-lucide="move-left" class="mr-1 rtl:inline-block ltr:hidden size-4"></i>
                </a>
            </div>
            <div x-data="{ loadingButton: false, isActive: false }">
                <button @click="loadingButton = true; setTimeout(() => { loadingButton = false; isActive = !isActive; }, 2000)" class="btn btn-sub-gray btn-icon-text">
                    <span class="flex items-center gap-2" x-show="!isActive"><i class="ri-user-add-line"></i> Follow</span>
                    <span class="flex items-center gap-2" x-show="isActive"><i class="ri-user-unfollow-line"></i> UnFollow</span>
                    <svg x-show="loadingButton" class="size-4 animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-0" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                </button>
            </div>
        </div>
    </div><!--end col-->
    <div class="card">
        <div class="flex flex-wrap items-center gap-3 card-body">
            <div class="shrink-0">
                <img src="assets/images/avatar/user-20.png" alt="" class="rounded-md size-20">
            </div>
            <div class="grow">
                <h6 class="mb-1">Joseph Obrien</h6>
                <div class="flex flex-wrap items-center gap-4 mb-3">
                    <p class="text-gray-500 dark:text-dark-500"><i data-lucide="mail" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <a href="mailto:<EMAIL>" class="align-middle whitespace-nowrap"><EMAIL></a></p>
                    <p class="text-gray-500 dark:text-dark-500"><i data-lucide="phone" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-middle whitespace-nowrap">************</span></p>
                </div>
                <a href="pages-user.html" class="text-primary-500">
                    View More
                    <i data-lucide="move-right" class="ml-1 ltr:inline-block rtl:hidden size-4"></i>
                    <i data-lucide="move-left" class="mr-1 rtl:inline-block ltr:hidden size-4"></i>
                </a>
            </div>
            <div x-data="{ loadingButton: false, isActive: false }">
                <button @click="loadingButton = true; setTimeout(() => { loadingButton = false; isActive = !isActive; }, 2000)" class="btn btn-sub-gray btn-icon-text">
                    <span class="flex items-center gap-2" x-show="!isActive"><i class="ri-user-add-line"></i> Follow</span>
                    <span class="flex items-center gap-2" x-show="isActive"><i class="ri-user-unfollow-line"></i> UnFollow</span>
                    <svg x-show="loadingButton" class="size-4 animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-0" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                </button>
            </div>
        </div>
    </div><!--end col-->
    <div class="card">
        <div class="flex flex-wrap items-center gap-3 card-body">
            <div class="shrink-0">
                <img src="assets/images/avatar/user-45.png" alt="" class="rounded-md size-20">
            </div>
            <div class="grow">
                <h6 class="mb-1">Edward Chapman</h6>
                <div class="flex flex-wrap items-center gap-4 mb-3">
                    <p class="text-gray-500 dark:text-dark-500"><i data-lucide="mail" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <a href="mailto:<EMAIL>" class="align-middle whitespace-nowrap"><EMAIL></a></p>
                    <p class="text-gray-500 dark:text-dark-500"><i data-lucide="phone" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-middle whitespace-nowrap">************</span></p>
                </div>
                <a href="pages-user.html" class="text-primary-500">
                    View More
                    <i data-lucide="move-right" class="ml-1 ltr:inline-block rtl:hidden size-4"></i>
                    <i data-lucide="move-left" class="mr-1 rtl:inline-block ltr:hidden size-4"></i>
                </a>
            </div>
            <div x-data="{ loadingButton: false, isActive: true }">
                <button @click="loadingButton = true; setTimeout(() => { loadingButton = false; isActive = !isActive; }, 2000)" class="btn btn-sub-gray btn-icon-text">
                    <span class="flex items-center gap-2" x-show="!isActive"><i class="ri-user-add-line"></i> Follow</span>
                    <span class="flex items-center gap-2" x-show="isActive"><i class="ri-user-unfollow-line"></i> UnFollow</span>
                    <svg x-show="loadingButton" class="size-4 animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-0" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                </button>
            </div>
        </div>
    </div><!--end col-->
    <div class="card">
        <div class="flex flex-wrap items-center gap-3 card-body">
            <div class="shrink-0">
                <img src="assets/images/avatar/user-38.png" alt="" class="rounded-md size-20">
            </div>
            <div class="grow">
                <h6 class="mb-1">Annie Akins</h6>
                <div class="flex flex-wrap items-center gap-4 mb-3">
                    <p class="text-gray-500 dark:text-dark-500"><i data-lucide="mail" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <a href="mailto:<EMAIL>" class="align-middle whitespace-nowrap"><EMAIL></a></p>
                    <p class="text-gray-500 dark:text-dark-500"><i data-lucide="phone" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-middle whitespace-nowrap">************</span></p>
                </div>
                <a href="pages-user.html" class="text-primary-500">
                    View More
                    <i data-lucide="move-right" class="ml-1 ltr:inline-block rtl:hidden size-4"></i>
                    <i data-lucide="move-left" class="mr-1 rtl:inline-block ltr:hidden size-4"></i>
                </a>
            </div>
            <div x-data="{ loadingButton: false, isActive: true }">
                <button @click="loadingButton = true; setTimeout(() => { loadingButton = false; isActive = !isActive; }, 2000)" class="btn btn-sub-gray btn-icon-text">
                    <span class="flex items-center gap-2" x-show="!isActive"><i class="ri-user-add-line"></i> Follow</span>
                    <span class="flex items-center gap-2" x-show="isActive"><i class="ri-user-unfollow-line"></i> UnFollow</span>
                    <svg x-show="loadingButton" class="size-4 animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-0" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                </button>
            </div>
        </div>
    </div><!--end col-->
    <div class="card">
        <div class="flex flex-wrap items-center gap-3 card-body">
            <div class="shrink-0">
                <img src="assets/images/avatar/user-35.png" alt="" class="rounded-md size-20">
            </div>
            <div class="grow">
                <h6 class="mb-1">Gena Kelly</h6>
                <div class="flex flex-wrap items-center gap-4 mb-3">
                    <p class="text-gray-500 dark:text-dark-500"><i data-lucide="mail" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <a href="mailto:<EMAIL>" class="align-middle whitespace-nowrap"><EMAIL></a></p>
                    <p class="text-gray-500 dark:text-dark-500"><i data-lucide="phone" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-middle whitespace-nowrap">************</span></p>
                </div>
                <a href="pages-user.html" class="text-primary-500">
                    View More
                    <i data-lucide="move-right" class="ml-1 ltr:inline-block rtl:hidden size-4"></i>
                    <i data-lucide="move-left" class="mr-1 rtl:inline-block ltr:hidden size-4"></i>
                </a>
            </div>
            <div x-data="{ loadingButton: false, isActive: true }">
                <button @click="loadingButton = true; setTimeout(() => { loadingButton = false; isActive = !isActive; }, 2000)" class="btn btn-sub-gray btn-icon-text">
                    <span class="flex items-center gap-2" x-show="!isActive"><i class="ri-user-add-line"></i> Follow</span>
                    <span class="flex items-center gap-2" x-show="isActive"><i class="ri-user-unfollow-line"></i> UnFollow</span>
                    <svg x-show="loadingButton" class="size-4 animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-0" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                </button>
            </div>
        </div>
    </div><!--end col-->
</div><!--end grid-->

<div class="grid grid-cols-12 gap-5 mb-5 items-center">
    <div class="col-span-12 lg:col-span-5 justify-center lg:justify-start flex flex-wrap">
        <p class="text-gray-500 dark:text-dark-500">Showing <b>8</b> of <b>76</b> Results</p>
    </div><!--end col-->
    <div class="col-span-12 lg:col-span-7">
        <div class="flex justify-center lg:justify-end pagination pagination-primary">
            <button type="button" class="pagination-pre" disabled>
                <i data-lucide="chevron-left" class="mr-1 ltr:inline-block rtl:hidden size-5"></i>
                <i data-lucide="chevron-right" class="ml-1 ltr:hidden rtl:inline-block size-5"></i>
                Prev
            </button>
            <button type="button" class="pagination-item active">1</button>
            <button type="button" class="pagination-item">2</button>
            <button type="button" class="pagination-item">3</button>
            <button type="button" class="pagination-item">...</button>
            <button type="button" class="pagination-item">10</button>
            <button type="button" class="pagination-next">
                Next
                <i data-lucide="chevron-right" class="ml-1 rtl:hidden size-5 ltr:inline-block"></i>
                <i data-lucide="chevron-left" class="mr-1 rtl:inline-block ltr:hidden size-5"></i>
            </button>
        </div>
    </div><!--end col-->
</div><!--end grid-->

</div>
{{> partials/footer }}
</div>

{{> partials/vendor-scripts }}

<script type="module" src="assets/js/main.js"></script>

</body>
</html>