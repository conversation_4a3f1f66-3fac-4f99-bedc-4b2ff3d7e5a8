import user1 from '/assets/images/avatar/user-11.png'
import user2 from '/assets/images/avatar/user-16.png'
import user3 from '/assets/images/avatar/user-18.png'
import user4 from '/assets/images/avatar/user-17.png'
import user5 from '/assets/images/avatar/user-20.png'
import user6 from '/assets/images/avatar/user-10.png'
import user7 from '/assets/images/avatar/user-8.png'
import user8 from '/assets/images/avatar/user-5.png'
import user9 from '/assets/images/avatar/user-1.png'
import user10 from '/assets/images/avatar/user-22.png'

const employeeListData = [
    {
        avatar: user1,
        name: '<PERSON>',
        email: '<EMAIL>',
        phone: '******-567-8900',
        totalOrders: '12',
        lastOrder: 'Jan 15, 2024',
        outstanding: '$1,200',
        status: 'Active',
        address: '123 Main St',
        city: 'New York',
        country: 'USA',
        notes: 'VIP customer, prefers email contact.'
    },
    {
        name: '<PERSON>',
        email: '<EMAIL>',
        phone: '******-567-8901',
        totalOrders: '8',
        lastOrder: 'Feb 10, 2024',
        outstanding: '$0',
        status: 'Pending',
        address: '456 Oak Ave',
        city: 'Toronto',
        country: 'Canada',
        notes: 'Requested invoice by mail.'
    },
    {
        avatar: user2,
        name: 'Alice Lee',
        email: '<EMAIL>',
        phone: '******-567-8902',
        totalOrders: '5',
        lastOrder: 'Mar 5, 2024',
        outstanding: '$350',
        status: 'Overdue',
        address: '789 Pine Rd',
        city: 'London',
        country: 'UK',
        notes: 'Late payments, follow up monthly.'
    },
    {
        avatar: user5,
        name: 'Michael Brown',
        email: '<EMAIL>',
        phone: '******-567-8903',
        totalOrders: '15',
        lastOrder: 'Apr 2, 2024',
        outstanding: '$500',
        status: 'Active',
        address: '321 Maple St',
        city: 'Sydney',
        country: 'Australia',
        notes: 'Frequent buyer, interested in loyalty program.'
    },
    {
        avatar: user6,
        name: 'Emily Clark',
        email: '<EMAIL>',
        phone: '******-567-8904',
        totalOrders: '9',
        lastOrder: 'May 12, 2024',
        outstanding: '$0',
        status: 'Active',
        address: '654 Cedar Blvd',
        city: 'Paris',
        country: 'France',
        notes: 'Prefers SMS notifications.'
    },
    {
        name: 'David Wilson',
        email: '<EMAIL>',
        phone: '******-567-8905',
        totalOrders: '7',
        lastOrder: 'Jun 1, 2024',
        outstanding: '$120',
        status: 'Inactive',
        address: '987 Spruce Ln',
        city: 'Berlin',
        country: 'Germany',
        registrationDate: '2021-05-05',
        notes: 'Inactive, last contacted in 2023.'
    },
    {
        name: 'Sophia Turner',
        email: '<EMAIL>',
        phone: '******-567-8906',
        totalOrders: '11',
        lastOrder: 'Jun 10, 2024',
        outstanding: '$80',
        status: 'Active',
        address: '222 Birch Dr',
        city: 'Mumbai',
        country: 'India',
        notes: 'Referred by Jane Doe.'
    },
    {
        name: 'Chris Evans',
        email: '<EMAIL>',
        phone: '******-567-8907',
        totalOrders: '6',
        lastOrder: 'Jun 15, 2024',
        outstanding: '$0',
        status: 'Pending',
        address: '333 Willow Way',
        city: 'Tokyo',
        country: 'Japan',
        notes: 'Requested product catalog.'
    },
    {
        name: 'Olivia Harris',
        email: '<EMAIL>',
        phone: '******-567-8908',
        totalOrders: '13',
        lastOrder: 'Jun 18, 2024',
        outstanding: '$200',
        status: 'Active',
        address: '444 Aspen Ct',
        city: 'Rio de Janeiro',
        country: 'Brazil',
        notes: 'Interested in seasonal promotions.'
    },
    {
        name: 'Ethan Walker',
        email: '<EMAIL>',
        phone: '******-567-8909',
        totalOrders: '10',
        lastOrder: 'Jun 20, 2024',
        outstanding: '$0',
        status: 'Inactive',
        address: '555 Redwood Pl',
        city: 'Cape Town',
        country: 'South Africa',
        notes: 'No recent activity.'
    },
    {
        avatar: user8,
        name: 'Liam Martinez',
        email: '<EMAIL>',
        phone: '******-567-8910',
        totalOrders: '14',
        lastOrder: 'Jul 2, 2024',
        outstanding: '$300',
        status: 'Active',
        address: '1010 Lakeview Dr',
        city: 'Barcelona',
        country: 'Spain',
        notes: 'Prefers weekend delivery.'
    },
    {
        name: 'Mia Kim',
        email: '<EMAIL>',
        phone: '******-567-8911',
        totalOrders: '4',
        lastOrder: 'Jul 5, 2024',
        outstanding: '$0',
        status: 'Pending',
        address: '2020 River Rd',
        city: 'Auckland',
        country: 'New Zealand',
        notes: 'Requested callback.'
    },
    {
        avatar: user4,
        name: 'Noah Kim',
        email: '<EMAIL>',
        phone: '******-567-8912',
        totalOrders: '17',
        lastOrder: 'Jul 8, 2024',
        outstanding: '$50',
        status: 'Overdue',
        address: '3030 Forest Ave',
        city: 'Seoul',
        country: 'South Korea',
        notes: 'Prefers online payment.'
    },
    {
        avatar: user10,
        name: 'Ava Nguyen',
        email: '<EMAIL>',
        phone: '******-567-8913',
        totalOrders: '3',
        lastOrder: 'Jul 10, 2024',
        outstanding: '$0',
        status: 'Active',
        address: '4040 Ocean Blvd',
        city: 'Hanoi',
        country: 'Vietnam',
        notes: 'Loyalty program member.'
    },
    {
        avatar: user3,
        name: 'William Scott',
        email: '<EMAIL>',
        phone: '******-567-8914',
        totalOrders: '6',
        lastOrder: 'Jul 12, 2024',
        outstanding: '$120',
        status: 'Inactive',
        address: '5050 Hill St',
        city: 'Dublin',
        country: 'Ireland',
        notes: 'No response to last offer.'
    },
    {
        name: 'Isabella Rivera',
        email: '<EMAIL>',
        phone: '******-567-8915',
        totalOrders: '8',
        lastOrder: 'Jul 15, 2024',
        outstanding: '$0',
        status: 'Active',
        address: '6060 Park Ave',
        city: 'Mexico City',
        country: 'Mexico',
        notes: 'Prefers text updates.'
    },
    {
        name: 'James Lee',
        email: '<EMAIL>',
        phone: '******-567-8916',
        totalOrders: '10',
        lastOrder: 'Jul 18, 2024',
        outstanding: '$200',
        status: 'Pending',
        address: '7070 Elm St',
        city: 'Singapore',
        country: 'Singapore',
        notes: 'Requested invoice split.'
    },
    {
        name: 'Charlotte King',
        email: '<EMAIL>',
        phone: '******-567-8917',
        totalOrders: '12',
        lastOrder: 'Jul 20, 2024',
        outstanding: '$0',
        status: 'Active',
        address: '8080 Willow Dr',
        city: 'Zurich',
        country: 'Switzerland',
        notes: 'Birthday in August.'
    },
    {
        name: 'Benjamin Young',
        email: '<EMAIL>',
        phone: '******-567-8918',
        totalOrders: '9',
        lastOrder: 'Jul 22, 2024',
        outstanding: '$90',
        status: 'Overdue',
        address: '9090 Cedar Ct',
        city: 'Cape Town',
        country: 'South Africa',
        notes: 'Prefers phone calls.'
    },
    {
        name: 'Amelia Clark',
        email: '<EMAIL>',
        phone: '******-567-8919',
        totalOrders: '11',
        lastOrder: 'Jul 25, 2024',
        outstanding: '$0',
        status: 'Active',
        address: '1111 Magnolia Ln',
        city: 'Brisbane',
        country: 'Australia',
        notes: 'Loyalty discount applied.'
    },
    {
        avatar: user9,
        name: 'Elijah Hall',
        email: '<EMAIL>',
        phone: '******-567-8920',
        totalOrders: '7',
        lastOrder: 'Jul 28, 2024',
        outstanding: '$60',
        status: 'Inactive',
        address: '1212 Oak Cir',
        city: 'Manchester',
        country: 'UK',
        notes: 'Inactive, follow up in Q3.'
    },
    {
        avatar: user7,
        name: 'Harper Scott',
        email: '<EMAIL>',
        phone: '******-567-8921',
        totalOrders: '13',
        lastOrder: 'Jul 30, 2024',
        outstanding: '$0',
        status: 'Active',
        address: '1313 Birch Blvd',
        city: 'Johannesburg',
        country: 'South Africa',
        notes: 'Prefers in-person meetings.'
    },
    {
        name: 'Lucas Wright',
        email: '<EMAIL>',
        phone: '******-567-8922',
        totalOrders: '5',
        lastOrder: 'Aug 2, 2024',
        outstanding: '$0',
        status: 'Pending',
        address: '1414 Spruce St',
        city: 'Vancouver',
        country: 'Canada',
        notes: 'Requested product demo.'
    },
    {
        name: 'Mason Baker',
        email: '<EMAIL>',
        phone: '******-567-8923',
        totalOrders: '6',
        lastOrder: 'Aug 5, 2024',
        outstanding: '$40',
        status: 'Overdue',
        address: '1515 Aspen Ave',
        city: 'Frankfurt',
        country: 'Germany',
        notes: 'Prefers email receipts.'
    },
    {
        name: 'Evelyn Perez',
        email: '<EMAIL>',
        phone: '******-567-8924',
        totalOrders: '8',
        lastOrder: 'Aug 8, 2024',
        outstanding: '$0',
        status: 'Active',
        address: '1616 Redwood Rd',
        city: 'Nice',
        country: 'France',
        notes: 'Interested in new arrivals.'
    },
    {
        name: 'Henry Torres',
        email: '<EMAIL>',
        phone: '******-567-8925',
        totalOrders: '9',
        lastOrder: 'Aug 10, 2024',
        outstanding: '$70',
        status: 'Inactive',
        address: '1717 Cypress Ct',
        city: 'Lisbon',
        country: 'Portugal',
        notes: 'No recent orders.'
    }
]

export default employeeListData