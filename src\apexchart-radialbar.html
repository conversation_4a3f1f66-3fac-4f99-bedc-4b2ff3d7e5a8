{{> partials/main }}

<head>

    {{> partials/title-meta title="Radialbar Charts" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}

{{> partials/page-heading title="Radialbar Charts" sub-title="Apexcharts" }}

<div class="grid grid-cols-12 gap-x-space">
    <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Basic</h6>
        </div>
        <div class="card-body">
            <div x-data="basicRadialbarApp" dir="ltr">
                <div class="!min-h-full" data-chart-colors="[bg-primary-500]" x-ref="basicRadialbarChart"></div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Multiple</h6>
        </div>
        <div class="card-body">
            <div x-data="multipleRadialbarApp" dir="ltr">
                <div class="!min-h-full" data-chart-colors="[bg-primary-500, bg-green-500, bg-yellow-500, bg-purple-500]" x-ref="multipleRadialbarChart"></div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Custom Angle Circle</h6>
        </div>
        <div class="card-body">
            <div x-data="customAngleRadialbarApp" dir="ltr">
                <div class="!min-h-full" data-chart-colors="[bg-primary-500, bg-green-500, bg-yellow-500, bg-purple-500]" x-ref="customAngleRadialbarChart"></div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Gradient</h6>
        </div>
        <div class="card-body">
            <div x-data="gradientRadialbarApp" dir="ltr">
                <div class="!min-h-full" data-chart-colors="[bg-primary-500, bg-green-500]" x-ref="gradientRadialbarChart"></div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Radialbars with Image</h6>
        </div>
        <div class="card-body">
            <div x-data="imageRadialbarApp" dir="ltr">
                <div class="!min-h-full" x-ref="imageRadialbarChart"></div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Stroked Gauge</h6>
        </div>
        <div class="card-body">
            <div x-data="strokedGaugeRadialbarApp" dir="ltr">
                <div class="!min-h-full" data-chart-colors="[bg-primary-500]" x-ref="strokedGaugeRadialbarChart"></div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Semi Circle Gauge</h6>
        </div>
        <div class="card-body">
            <div x-data="semiGaugeRadialbarApp" dir="ltr">
                <div class="!min-h-full" data-chart-colors="[bg-sky-500]" x-ref="semiGaugeRadialbarChart"></div>
            </div>
        </div>
    </div><!--end col-->
</div><!--end grid-->

</div>
{{> partials/footer }}
</div>

{{> partials/vendor-scripts }}

<script type="module" src="assets/js/charts/radialbar-chart.init.js"></script>

<script type="module" src="assets/js/main.js"></script>

</body>
</html>