{{> partials/main }}

<head>

    {{> partials/title-meta title="Category List" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}
{{> partials/page-heading title="Category List" sub-title="Ecommerce" }}

<div class="grid grid-cols-12 gap-5" x-data="productTable()">
    <div class="col-span-12 lg:col-span-7 xl:col-span-8">
        <div class="card">
            <div class="card-header">
                <div class="grid items-center grid-cols-12 gap-3">
                    <div class="col-span-12 md:col-span-3">
                        <h6 class="card-title">Category List</h6>
                    </div>

                    <div class="col-span-12 md:col-span-4 md:col-start-9">
                        <div class="flex gap-2">
                            <div class="relative group/form grow">
                                <input type="text" class="ltr:pl-9 rtl:pr-9 form-input ltr:group-[&.right]/form:pr-9 rtl:group-[&.right]/form:pl-9 ltr:group-[&.right]/form:pl-4 rtl:group-[&.right]/form:pr-4" placeholder="Search for ..." @input="filteredProduct" x-model="searchTerm">
                                <button class="absolute inset-y-0 flex items-center text-gray-500 dark:text-dark-500 ltr:left-3 rtl:right-3 ltr:group-[&.right]/form:right-3 rtl:group-[&.right]/form:left-3 ltr:group-[&.right]/form:left-auto rtl:group-[&.right]/form:right-auto focus:outline-hidden">
                                    <i data-lucide="search" class="size-4"></i>
                                </button>
                            </div>
                            <button class="btn btn-red btn-icon shrink-0" x-show="selectedItems.length > 0" @click="deleteSelectedItems()"><i data-lucide="trash" class="size-4"></i></button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="pt-0 card-body">
                <div class="overflow-x-auto table-box">
                    <table class="table hovered">
                        <tbody>
                            <tr class="text-gray-500 bg-gray-100 dark:bg-dark-850 dark:text-dark-500">
                                <th>
                                    <div class="!font-medium input-check-group">
                                        <label for="checkboxAll" class="hidden input-check-label"></label>
                                        <input id="checkboxAll" class="whitespace-nowrap input-check input-check-primary" type="checkbox" x-model="selectAll" x-on:click="toggleAll" />
                                    </div>
                                </th>
                                <th x-on:click="sort('categoryID')" class="whitespace-nowrap !font-medium cursor-pointer">Category ID <span x-show="sortBy === 'categoryID'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th x-on:click="sort('category')" class="whitespace-nowrap !font-medium cursor-pointer">Category Name <span x-show="sortBy === 'category'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th x-on:click="sort('products')" class="whitespace-nowrap !font-medium cursor-pointer">Products <span x-show="sortBy === 'products'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th x-on:click="sort('status')" class="whitespace-nowrap !font-medium cursor-pointer">status <span x-show="sortBy === 'status'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th class="whitespace-nowrap !font-medium">Action</th>
                            </tr>
                            <template x-if="displayedProducts.length > 0">
                                <template x-for="(product, index) in displayedProducts" :key="index">
                                    <tr>
                                        <td>
                                            <div class="input-check-group">
                                                <label :for="`category${index}`" class="hidden input-check-label"></label>
                                                <input :id="`category${index}`" class="input-check input-check-primary" type="checkbox" @click="toggleItem(product)" :checked="selectedItems.includes(product)" />
                                            </div>
                                        </td>
                                        <td><a href="#!" class="link link-primary" x-text="product.categoryID"></a></td>
                                        <td>
                                            <div class="flex items-center gap-2">
                                                <div class="flex items-center justify-center p-1 border border-gray-200 rounded-sm size-9 dark:border-dark-800 shrink-0">
                                                    <img :src="product.image" alt="" class="rounded-sm">
                                                </div>
                                                <h6><a href="#!" x-text="product.category"></a></h6>
                                            </div>
                                        </td>
                                        <td x-text="product.products"></td>
                                        <td>
                                            <span x-text="product.status" :class="{
                                                    'badge badge-green': product.status === 'Active',
                                                    'badge badge-gray': product.status === 'Inactive'
                                                }"></span>
                                        </td>
                                        <td>
                                            <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown">
                                                <button x-ref="button" x-on:click="toggle()" :aria-expanded="open.toString()" type="button" class="flex items-center text-gray-500 dark:text-dark-500">
                                                    <i class="ri-more-2-fill"></i>
                                                </button>
                                                <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden">
                                                    <ul>
                                                        <li>
                                                            <a href="#!" class="dropdown-item">
                                                                <i class="align-middle ltr:mr-2 rtl:ml-2 ri-eye-line"></i> <span>Overview</span>
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a href="#!" @click="editProduct(product.categoryID); close(); " class="dropdown-item">
                                                                <i class="align-middle ltr:mr-2 rtl:ml-2 ri-pencil-line"></i> <span>Edit</span>
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a href="#!" data-modal-target="deleteModal" @click="deleteItem = product.category; close()" class="dropdown-item">
                                                                <i class="align-middle ltr:mr-2 rtl:ml-2 ri-delete-bin-line"></i> <span>Delete</span>
                                                            </a>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                </template>
                            </template>
                            <tr>
                                <template x-if="displayedProducts.length == 0">
                                    <td colspan="10" class="!p-8">
                                        <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" class="mx-auto size-12" viewBox="0 0 48 48">
                                            <linearGradient id="SVGID_1__h35ynqzIJzH4_gr1" x1="34.598" x2="15.982" y1="15.982" y2="34.598" gradientUnits="userSpaceOnUse">
                                                <stop offset="0" stop-color="#60e8fe"></stop>
                                                <stop offset=".033" stop-color="#6ae9fe"></stop>
                                                <stop offset=".197" stop-color="#97f0fe"></stop>
                                                <stop offset=".362" stop-color="#bdf5ff"></stop>
                                                <stop offset=".525" stop-color="#dafaff"></stop>
                                                <stop offset=".687" stop-color="#eefdff"></stop>
                                                <stop offset=".846" stop-color="#fbfeff"></stop>
                                                <stop offset="1" stop-color="#fff"></stop>
                                            </linearGradient>
                                            <path fill="url(#SVGID_1__h35ynqzIJzH4_gr1)" d="M40.036,33.826L31.68,25.6c0.847-1.739,1.335-3.684,1.335-5.748c0-7.27-5.894-13.164-13.164-13.164	S6.688,12.582,6.688,19.852c0,7.27,5.894,13.164,13.164,13.164c2.056,0,3.995-0.485,5.728-1.326l3.914,4.015l4.331,4.331	c1.715,1.715,4.496,1.715,6.211,0C41.751,38.321,41.751,35.541,40.036,33.826z"></path>
                                            <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M31.95,25.739l8.086,8.086c1.715,1.715,1.715,4.496,0,6.211l0,0c-1.715,1.715-4.496,1.715-6.211,0	l-4.331-4.331"></path>
                                            <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M7.525,24.511c-1.771-4.694-0.767-10.196,3.011-13.975c3.847-3.847,9.48-4.817,14.228-2.912"></path>
                                            <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M30.856,12.603c3.376,5.114,2.814,12.063-1.688,16.565c-4.858,4.858-12.565,5.129-17.741,0.814"></path>
                                        </svg>
                                        <p class="mt-2 text-center text-gray-500 dark:text-dark-500">No matching records found</p>
                                    </td>
                                </template>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="grid items-center grid-cols-12 mt-3 gap-space" x-show="displayedProducts.length !== 0">
                    <div class="flex col-span-12 xl:col-span-5 justify-center ltr:xl:justify-start flex-wrap">
                        <p class="text-gray-500 dark:text-dark-500 ltr:pr-1 rtl:pl-1">Showing <b x-text="showingStart"></b> - <b x-text="showingEnd"></b> of <b x-text="filterProduct.length"></b> Results</p>
                        <span x-show="selectedItems.length > 0"><b x-text="selectedItems.length != 0 ? selectedItems.length : ''"> </b> Row Selected</span>
                    </div>
                    <div class="col-span-12 xl:col-span-7">
                        <div class="flex justify-center xl:justify-end pagination pagination-primary">
                            <button @click="prevPage()" :disabled="currentPage === 1" class="pagination-pre">
                                <i data-lucide="chevron-left" class="mr-1 ltr:inline-block rtl:hidden size-4"></i>
                                <i data-lucide="chevron-right" class="ml-1 ltr:hidden rtl:inline-block size-4"></i>
                                Prev
                            </button>
                            <template x-for="page in totalPages" :key="page">
                                <button @click="gotoPage(page)" :class="{ 'active': currentPage === page }" class="pagination-item">
                                    <span x-text="page"></span>
                                </button>
                            </template>
                            <button @click="nextPage()" :disabled="currentPage === totalPages" class="pagination-next">
                                Next
                                <i data-lucide="chevron-right" class="ml-1 ltr:inline-block rtl:hidden size-4"></i>
                                <i data-lucide="chevron-left" class="mr-1 ltr:hidden rtl:inline-block size-4"></i>
                            </button>
                        </div>
                    </div>
                </div>
                   
            </div>
        </div><!--end card-->
    </div><!--end col-->
    <div class="col-span-12 lg:col-span-5 xl:col-span-4">
        <div class="sticky top-24 card">
            <div class="card-header">
                <h6 class="card-title" x-text="showAddProductForm ? 'Add New Category' : 'Edit Category'">Add New Category</h6>
            </div>
            <div class="card-body">
                <div class="grid grid-cols-12 gap-5" x-show="showAddProductForm || showEditProductForm">
                    <div class="col-span-12">
                        <div>
                            <label for="logo" class="flex items-center justify-center p-2 mx-auto overflow-hidden bg-gray-100 border border-gray-200 rounded-sm cursor-pointer dark:bg-dark-850 dark:border-dark-800 size-32">
                                <img x-show="productForm.image" :src="productForm.image" class="object-cover w-full h-full">
                                <span x-show="!productForm.image" class="flex flex-col items-center text-gray-500 dark:text-dark-500">
                                    <i data-lucide="upload"></i>
                                    <span class="block mt-2">Upload Images</span>
                            </label>
                            <div class="hidden mt-4">
                                <label class="block">
                                    <span class="sr-only">Choose profile photo</span>
                                    <input type="file" name="logo" id="logo" @change="fileChosen" class="block w-full text-sm file:rounded-md focus:outline-0 text-slate-500 file:mr-4 file:py-2 file:px-4 file:border-0 file:text-sm file:font-semibold file:bg-violet-50 file:text-violet-700 hover:file:bg-violet-100 " />
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="col-span-12">
                        <label for="categoryNameInput" class="form-label">Category Name</label>
                        <input type="text" id="categoryNameInput" class="form-input" placeholder="Category name" x-model="productForm.category" @input="validateField('categoryName' , productForm.category , 'Category Name is required')">
                        <span x-show="errors.categoryName" class="text-red-500" x-text="errors.categoryName"></span>
                    </div>
                    <div class="col-span-12">
                        <label for="descriptionTextarea" class="form-label">Description</label>
                        <textarea name="descriptionTextarea" id="descriptionTextarea" rows="3" class="h-auto form-input" placeholder="Enter your description" x-model="productForm.description" @input="validateField('description' , productForm.description , 'Description is required')"></textarea>
                        <span x-show="errors.description" class="text-red-500" x-text="errors.description"></span>
                    </div>
                    <div class="col-span-12">
                        <label for="statusSelect" class="form-label" >Status</label>
                        <div id="statusSelect" x-model="productForm.status" @change="validateField('status', document.querySelector('#statusSelect') , 'Status is required.')"></div>
                        <span x-show="errors.status" class="text-red-500" x-text="errors.status"></span>    
                    </div>
                    <div class="flex items-center justify-end col-span-12 gap-2">
                        <button class="btn btn-sub-gray" @click="resetForm()"><i data-lucide="rotate-ccw" class="inline-block ltr:mr-1 rtl:ml-1 align-center size-4"></i> <span class="align-middle">Reset</span></button>
                        <button class="btn btn-primary" @click="submitForm()" x-text="showAddProductForm ? 'Add Category' : 'Edit Category'"><i data-lucide="plus" class="inline-block ltr:mr-1 rtl:ml-1 align-center size-4"></i> <span class="align-middle">Add Category</span></button>
                    </div>
                </div>
            </div>
        </div>
    </div><!--end col-->
    <div id="deleteModal" class="!hidden modal show">
        <div class="modal-wrap modal-xs modal-center">
            <div class="text-center modal-content p-7">
                <div class="flex items-center justify-center mx-auto mb-4 text-red-500 rounded-full bg-red-500/10 size-14 backdrop-blur-xl">
                    <i data-lucide="trash-2" class="size-6"></i>
                </div>
                <h5 class="mb-4">Are you sure you want to delete this Category ?</h5>
                <div class="flex items-center justify-center gap-2">
                    <button class="btn btn-red" data-modal-close="deleteModal" @click="deleteProduct()">Delete</button>
                    <button data-modal-close="deleteModal" class="btn link link-primary">Cancel</button>
                </div>
            </div>
        </div>
    </div><!--end-->
</div><!--end grid-->
</div>
{{> partials/footer }}
</div>

{{> partials/vendor-scripts }}

<script type="module" src="assets/js/apps/ecommerce/product-category.init.js"></script>

<script type="module" src="assets/js/main.js"></script>

</body>
</html>