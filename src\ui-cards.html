{{> partials/main }}

<head>

    {{> partials/title-meta title="Cards" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}

{{> partials/page-heading title="Cards" sub-title="UI" }}

<div class="grid grid-cols-12 gap-x-space">
    <div class="col-span-12 sm:col-span-6 xl:col-span-4">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title">Card Header</h6>
            </div>
            <div class="card-body">
                <p class="text-gray-500 dark:text-dark-500">Business Content means any sound recordings, musical works, album cover artwork, photographs, images, audiovisual works, third party metadata (including editorial content) and other copyrighted materials made available by Seller through the Business Products, but excluding the Business Product Software and Incorporated</p>
            </div>
            <div class="card-footer">
                <h6 class="card-title">Card Footer</h6>
            </div>
        </div>
    </div><!--col-->
    <div class="col-span-12 sm:col-span-6 xl:col-span-4">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title">Card Header</h6>
            </div>
            <div class="card-body">
                <p class="text-gray-500 dark:text-dark-500">Business Content means any sound recordings, musical works, album cover artwork, photographs, images, audiovisual works, third party metadata (including editorial content) and other copyrighted materials made available by Seller through the Business Products, but excluding the Business Product Software and Incorporated</p>
            </div>
        </div>
    </div><!--col-->
    <div class="col-span-12 sm:col-span-6 xl:col-span-4">
        <div class="card">
            <div class="card-body">
                <p class="text-gray-500 dark:text-dark-500">Business Content means any sound recordings, musical works, album cover artwork, photographs, images, audiovisual works, third party metadata (including editorial content) and other copyrighted materials made available by Seller through the Business Products, but excluding the Business Product Software and Incorporated</p>
            </div>
            <div class="card-footer">
                <h6 class="card-title">Card Footer</h6>
            </div>
        </div>
    </div><!--col-->
</div><!--end grid-->

<div class="grid grid-cols-12 gap-x-space">
    <div class="col-span-12 sm:col-span-6 xl:col-span-3">
        <div class="card">
            <div class="h-32 overflow-hidden rounded-t-md">
                <img class="object-cover object-center w-full" src='assets/images/gallery/img-01.jpg' alt='Gallery'>
            </div>
            <div class="relative mx-auto -mt-16 overflow-hidden border-4 border-white rounded-full dark:border-dark-900 size-32">
                <img class="object-cover object-center size-32" src='assets/images/avatar/user-21.png' alt='Woman looking front'>
            </div>
            <div class="mt-2 text-center">
                <h5 class="text-16">Linda J. Bell</h5>
                <p class="text-sm text-gray-500 dark:text-dark-500">Sr. Web Designer</p>
            </div>
            <ul class="flex items-center justify-around py-4 mt-2">
                <li class="flex flex-col items-center justify-around">
                    <i data-lucide="star" class="block mb-2 text-yellow-500 size-5"></i>
                    <h6>2k</h6>
                </li>
                <li class="flex flex-col items-center justify-between">
                    <i data-lucide="users" class="block mb-2 text-primary-500 size-5"></i>
                    <h6>10k</h6>
                </li>
                <li class="flex flex-col items-center justify-around">
                    <i data-lucide="briefcase-business" class="block mb-2 text-green-500 size-5"></i>
                    <div>15</div>
                </li>
            </ul>
            <div class="mx-8 mt-2 card-footer">
                <div x-data="{ loadingButton: false, isActive: false }">
                    <button @click="loadingButton = true; setTimeout(() => { loadingButton = false; isActive = !isActive; }, 2000)" class="flex items-center gap-2 mx-auto text-white bg-pink-500 border-pink-500 btn hover:bg-pink-600 hover:text-white hover:border-pink-600 focus:bg-pink-600 focus:text-white focus:border-pink-600">
                        <span class="flex items-center gap-2" x-show="!isActive"><i class="ri-user-add-line"></i> Follow</span>
                        <span class="flex items-center gap-2" x-show="isActive"><i class="ri-user-unfollow-line"></i> UnFollow</span>
                        <svg x-show="loadingButton" class="text-white size-4 animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-0" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </div><!--col-->
    <div class="col-span-12 sm:col-span-6 xl:col-span-3">
        <div class="card">
            <div class="h-32 overflow-hidden rounded-t-md">
                <img class="object-cover object-center w-full" src='assets/images/gallery/img-02.jpg' alt='Gallery'>
            </div>
            <div class="relative mx-auto -mt-16 overflow-hidden border-4 border-white rounded-full dark:border-dark-900 size-32">
                <img class="object-cover object-center size-32" src='assets/images/avatar/user-18.png' alt='Woman looking front'>
            </div>
            <div class="mt-2 text-center">
                <h5 class="text-16">Jennifer Brunner</h5>
                <p class="text-sm text-gray-500 dark:text-dark-500">Laravel Developer</p>
            </div>
            <ul class="flex items-center justify-around py-4 mt-2">
                <li class="flex flex-col items-center justify-around">
                    <i data-lucide="star" class="block mb-2 text-yellow-500 size-5"></i>
                    <h6>1.1k</h6>
                </li>
                <li class="flex flex-col items-center justify-between">
                    <i data-lucide="users" class="block mb-2 text-primary-500 size-5"></i>
                    <h6>2.5k</h6>
                </li>
                <li class="flex flex-col items-center justify-around">
                    <i data-lucide="briefcase-business" class="block mb-2 text-green-500 size-5"></i>
                    <div>2</div>
                </li>
            </ul>
            <div class="mx-8 mt-2 card-footer">
                <div x-data="{ loadingButton: false, isActive: true }">
                    <button @click="loadingButton = true; setTimeout(() => { loadingButton = false; isActive = !isActive; }, 2000)" class="flex items-center gap-2 mx-auto text-white bg-pink-500 border-pink-500 btn hover:bg-pink-600 hover:text-white hover:border-pink-600 focus:bg-pink-600 focus:text-white focus:border-pink-600">
                        <span class="flex items-center gap-2" x-show="!isActive"><i class="ri-user-add-line"></i> Follow</span>
                        <span class="flex items-center gap-2" x-show="isActive"><i class="ri-user-unfollow-line"></i> UnFollow</span>
                        <svg x-show="loadingButton" class="text-white size-4 animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-0" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </div><!--col-->
    <div class="col-span-12 sm:col-span-6 xl:col-span-3">
        <div class="card">
            <div class="h-32 overflow-hidden rounded-t-md">
                <img class="object-cover object-center w-full" src='assets/images/gallery/img-03.jpg' alt='Gallery'>
            </div>
            <div class="relative mx-auto -mt-16 overflow-hidden border-4 border-white rounded-full dark:border-dark-900 size-32">
                <img class="object-cover object-center size-32" src='assets/images/avatar/user-17.png' alt='Woman looking front'>
            </div>
            <div class="mt-2 text-center">
                <h5 class="text-16">Sandra Alexander</h5>
                <p class="text-sm text-gray-500 dark:text-dark-500">ASP.Net Developer</p>
            </div>
            <ul class="flex items-center justify-around py-4 mt-2">
                <li class="flex flex-col items-center justify-around">
                    <i data-lucide="star" class="block mb-2 text-yellow-500 size-5"></i>
                    <h6>3.6k</h6>
                </li>
                <li class="flex flex-col items-center justify-between">
                    <i data-lucide="users" class="block mb-2 text-primary-500 size-5"></i>
                    <h6>12k</h6>
                </li>
                <li class="flex flex-col items-center justify-around">
                    <i data-lucide="briefcase-business" class="block mb-2 text-green-500 size-5"></i>
                    <div>8</div>
                </li>
            </ul>
            <div class="mx-8 mt-2 card-footer">
                <div x-data="{ loadingButton: false, isActive: false }">
                    <button @click="loadingButton = true; setTimeout(() => { loadingButton = false; isActive = !isActive; }, 2000)" class="flex items-center gap-2 mx-auto text-white bg-pink-500 border-pink-500 btn hover:bg-pink-600 hover:text-white hover:border-pink-600 focus:bg-pink-600 focus:text-white focus:border-pink-600">
                        <span class="flex items-center gap-2" x-show="!isActive"><i class="ri-user-add-line"></i> Follow</span>
                        <span class="flex items-center gap-2" x-show="isActive"><i class="ri-user-unfollow-line"></i> UnFollow</span>
                        <svg x-show="loadingButton" class="text-white size-4 animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-0" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </div><!--col-->
    <div class="col-span-12 sm:col-span-6 xl:col-span-3">
        <div class="card">
            <div class="h-32 overflow-hidden rounded-t-md">
                <img class="object-cover object-center w-full" src='assets/images/gallery/img-04.jpg' alt='Gallery'>
            </div>
            <div class="relative mx-auto -mt-16 overflow-hidden border-4 border-white rounded-full dark:border-dark-900 size-32">
                <img class="object-cover object-center size-32" src='assets/images/avatar/user-14.png' alt='Woman looking front'>
            </div>
            <div class="mt-2 text-center">
                <h5 class="text-16">James Hazelwood</h5>
                <p class="text-sm text-gray-500 dark:text-dark-500">Wordpress Developer</p>
            </div>
            <ul class="flex items-center justify-around py-4 mt-2">
                <li class="flex flex-col items-center justify-around">
                    <i data-lucide="star" class="block mb-2 text-yellow-500 size-5"></i>
                    <h6>2.9k</h6>
                </li>
                <li class="flex flex-col items-center justify-between">
                    <i data-lucide="users" class="block mb-2 text-primary-500 size-5"></i>
                    <h6>11.8k</h6>
                </li>
                <li class="flex flex-col items-center justify-around">
                    <i data-lucide="briefcase-business" class="block mb-2 text-green-500 size-5"></i>
                    <div>5</div>
                </li>
            </ul>
            <div class="mx-8 mt-2 card-footer">
                <div x-data="{ loadingButton: false, isActive: false }">
                    <button @click="loadingButton = true; setTimeout(() => { loadingButton = false; isActive = !isActive; }, 2000)" class="flex items-center gap-2 mx-auto text-white bg-pink-500 border-pink-500 btn hover:bg-pink-600 hover:text-white hover:border-pink-600 focus:bg-pink-600 focus:text-white focus:border-pink-600">
                        <span class="flex items-center gap-2" x-show="!isActive"><i class="ri-user-add-line"></i> Follow</span>
                        <span class="flex items-center gap-2" x-show="isActive"><i class="ri-user-unfollow-line"></i> UnFollow</span>
                        <svg x-show="loadingButton" class="text-white size-4 animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-0" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </div><!--col-->
</div><!--end grid-->

<h5 class="mt-2 mb-5 underline">Card Hover Effect:</h5>

<div class="grid grid-cols-12 gap-x-space">
    <div class="col-span-12 sm:col-span-6 xl:col-span-3">
        <div class="relative overflow-hidden transition-all duration-500 card group/item hover:-translate-y-1 hover:shadow-2xl">
            <div class="card-body">
                <span class="absolute z-0 size-20 transition-all duration-500 rounded-full top-5 bg-primary-500 group-hover/item:scale-[10]"></span>
                <div class="relative z-10 max-w-md mx-auto">
                    <div class="grid text-white transition-all duration-500 rounded-full size-20 place-items-center bg-primary-500 group-hover/item:bg-primary-400/30">
                        <i data-lucide="message-circle-more" class="size-8"></i>
                    </div>
                    <div class="pt-5 space-y-6 text-base leading-7 text-gray-500 transition-all duration-500 dark:text-dark-500 group-hover/item:text-white/90">
                        <p>Perfect for learning how the framework works, prototyping a new idea, or creating a demo to share online.</p>
                    </div>
                    <div class="pt-5">
                        <a href="#" class="font-medium transition-all duration-500 text-primary-500 group-hover/item:text-white">Read the docs <i data-lucide="move-right" class="inline-block ml-1 size-4"></i></a>
                    </div>
                </div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 sm:col-span-6 xl:col-span-3">
        <div class="relative overflow-hidden transition-all duration-500 card group/item hover:-translate-y-1 hover:shadow-2xl">
            <div class="card-body">
                <span class="absolute z-0 size-20 transition-all duration-500 rounded-full top-5 bg-green-500 group-hover/item:scale-[10]"></span>
                <div class="relative z-10 max-w-md mx-auto">
                    <div class="grid text-white transition-all duration-500 bg-green-500 rounded-full size-20 place-items-center group-hover/item:bg-green-400/30">
                        <i data-lucide="message-circle-more" class="size-8"></i>
                    </div>
                    <div class="pt-5 space-y-6 text-base leading-7 text-gray-500 transition-all duration-500 dark:text-dark-500 group-hover/item:text-white/90">
                        <p>Perfect for learning how the framework works, prototyping a new idea, or creating a demo to share online.</p>
                    </div>
                    <div class="pt-5">
                        <a href="#" class="font-medium text-green-500 transition-all duration-500 group-hover/item:text-white">Read the docs <i data-lucide="move-right" class="inline-block ml-1 size-4"></i></a>
                    </div>
                </div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 sm:col-span-6 xl:col-span-3">
        <div class="relative overflow-hidden transition-all duration-500 card group/item hover:-translate-y-1 hover:shadow-2xl hover:!border-primary-500/30">
            <div class="card-body">
                <span class="absolute z-0 size-20 transition-all duration-500 rounded-full top-5 bg-primary-500/10 group-hover/item:scale-[10]"></span>
                <div class="relative z-10 max-w-md mx-auto">
                    <div class="grid transition-all duration-500 rounded-full size-20 text-primary-500 bg-primary-500/10 place-items-center">
                        <i data-lucide="message-circle-more" class="size-8"></i>
                    </div>
                    <div class="pt-5 space-y-6 text-base leading-7 text-gray-500 transition-all duration-500 dark:text-dark-500 group-hover/item:text-primary-500">
                        <p>Perfect for learning how the framework works, prototyping a new idea, or creating a demo to share online.</p>
                    </div>
                    <div class="pt-5">
                        <a href="#" class="font-medium transition-all duration-500 text-primary-500 group-hover/item:text-primary-500">Read the docs <i data-lucide="move-right" class="inline-block ml-1 size-4"></i></a>
                    </div>
                </div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 sm:col-span-6 xl:col-span-3">
        <div class="relative overflow-hidden transition-all duration-500 card group/item hover:-translate-y-1 hover:shadow-2xl hover:!border-purple-500/30">
            <div class="card-body">
                <span class="absolute z-0 size-20 transition-all duration-500 rounded-full top-5 bg-purple-500/10 group-hover/item:scale-[10]"></span>
                <div class="relative z-10 max-w-md mx-auto">
                    <div class="grid text-purple-500 transition-all duration-500 rounded-full bg-purple-500/10 size-20 place-items-center">
                        <i data-lucide="message-circle-more" class="size-8"></i>
                    </div>
                    <div class="pt-5 space-y-6 text-base leading-7 text-gray-500 transition-all duration-500 dark:text-dark-500 group-hover/item:text-purple-500">
                        <p>Perfect for learning how the framework works, prototyping a new idea, or creating a demo to share online.</p>
                    </div>
                    <div class="pt-5">
                        <a href="#" class="font-medium text-purple-500 transition-all duration-500 group-hover/item:text-purple-500">Read the docs <i data-lucide="move-right" class="inline-block ml-1 size-4"></i></a>
                    </div>
                </div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 sm:col-span-6 xl:col-span-3">
        <div class="relative overflow-hidden transition-all duration-500 card group/item hover:-translate-y-1 hover:shadow-2xl">
            <div class="card-body">
                <div class="relative z-10 max-w-md mx-auto">
                    <div class="grid transition-all duration-500 rounded-full text-primary-500 size-20 place-items-center bg-primary-500/15">
                        <i data-lucide="airplay" class="size-8"></i>
                    </div>
                    <div class="pt-5 space-y-6 text-base leading-7 text-gray-500 transition-all duration-500 dark:text-dark-500">
                        <p>Perfect for learning how the framework works, prototyping a new idea, or creating a demo to share online.</p>
                    </div>
                    <div class="pt-5">
                        <a href="#" class="font-medium transition-all duration-500 text-primary-500">Read the docs <i data-lucide="move-right" class="inline-block ml-1 size-4"></i></a>
                    </div>
                </div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 sm:col-span-6 xl:col-span-3">
        <div class="relative overflow-hidden transition-all duration-500 card group/item hover:-translate-y-1 hover:shadow-2xl">
            <div class="card-body">
                <div class="relative z-10 max-w-md mx-auto">
                    <div class="grid text-purple-500 transition-all duration-500 rounded-full size-20 place-items-center bg-purple-500/15">
                        <i data-lucide="airplay" class="size-8"></i>
                    </div>
                    <div class="pt-5 space-y-6 text-base leading-7 text-gray-500 transition-all duration-500 dark:text-dark-500">
                        <p>Perfect for learning how the framework works, prototyping a new idea, or creating a demo to share online.</p>
                    </div>
                    <div class="pt-5">
                        <a href="#" class="font-medium text-purple-500 transition-all duration-500">Read the docs <i data-lucide="move-right" class="inline-block ml-1 size-4"></i></a>
                    </div>
                </div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 sm:col-span-6 xl:col-span-3">
        <div class="relative overflow-hidden transition-all duration-500 card group/item hover:-translate-y-1 hover:shadow-2xl">
            <div class="card-body">
                <div class="relative z-10 max-w-md mx-auto">
                    <div class="grid text-orange-500 transition-all duration-500 rounded-full size-20 place-items-center bg-orange-500/15">
                        <i data-lucide="airplay" class="size-8"></i>
                    </div>
                    <div class="pt-5 space-y-6 text-base leading-7 text-gray-500 transition-all duration-500 dark:text-dark-500">
                        <p>Perfect for learning how the framework works, prototyping a new idea, or creating a demo to share online.</p>
                    </div>
                    <div class="pt-5">
                        <a href="#" class="font-medium text-orange-500 transition-all duration-500">Read the docs <i data-lucide="move-right" class="inline-block ml-1 size-4"></i></a>
                    </div>
                </div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 sm:col-span-6 xl:col-span-3">
        <div class="relative overflow-hidden transition-all duration-500 card group/item hover:-translate-y-1 hover:shadow-2xl">
            <div class="card-body">
                <div class="relative z-10 max-w-md mx-auto">
                    <div class="grid text-green-500 transition-all duration-500 rounded-full size-20 place-items-center bg-green-500/15">
                        <i data-lucide="airplay" class="size-8"></i>
                    </div>
                    <div class="pt-5 space-y-6 text-base leading-7 text-gray-500 transition-all duration-500 dark:text-dark-500">
                        <p>Perfect for learning how the framework works, prototyping a new idea, or creating a demo to share online.</p>
                    </div>
                    <div class="pt-5">
                        <a href="#" class="font-medium text-green-500 transition-all duration-500">Read the docs <i data-lucide="move-right" class="inline-block ml-1 size-4"></i></a>
                    </div>
                </div>
            </div>
        </div>
    </div><!--end col-->
</div><!--end grid-->

<h5 class="mt-2 mb-5 underline">Card Colored:</h5>

<div class="grid grid-cols-12 gap-x-space">
    <div class="col-span-12 sm:col-span-6 xl:col-span-3 card border-primary-300 shadow-primary-100 dark:border-primary-500/30 dark:shadow-primary-500/10">
        <div class="card-body">
            <h6 class="mb-2">Need a help in Claim?</h6>
            <p class="mb-3 text-gray-500 dark:text-dark-500">Go to this step by step guideline process on how to certify for your weekly benefits:</p>
            <a href="#" class="inline-flex items-center gap-2 font-medium text-primary-500 hover:underline">
                See our guideline
                <i data-lucide="square-arrow-out-up-right" class="size-4"></i>
            </a>
        </div>
    </div><!--end col-->
    <div class="col-span-12 border-purple-300 sm:col-span-6 xl:col-span-3 card shadow-purple-100 dark:border-purple-500/30 dark:shadow-purple-500/10">
        <div class="card-body">
            <h6 class="mb-2">Need a help in Claim?</h6>
            <p class="mb-3 text-gray-500 dark:text-dark-500">Go to this step by step guideline process on how to certify for your weekly benefits:</p>
            <a href="#" class="inline-flex items-center gap-2 font-medium text-purple-500 hover:underline">
                See our guideline
                <i data-lucide="square-arrow-out-up-right" class="size-4"></i>
            </a>
        </div>
    </div><!--end col-->
    <div class="col-span-12 border-green-300 sm:col-span-6 xl:col-span-3 card shadow-green-100 dark:border-green-500/30 dark:shadow-green-500/10">
        <div class="card-body">
            <h6 class="mb-2">Need a help in Claim?</h6>
            <p class="mb-3 text-gray-500 dark:text-dark-500">Go to this step by step guideline process on how to certify for your weekly benefits:</p>
            <a href="#" class="inline-flex items-center gap-2 font-medium text-green-500 hover:underline">
                See our guideline
                <i data-lucide="square-arrow-out-up-right" class="size-4"></i>
            </a>
        </div>
    </div><!--end col-->
    <div class="col-span-12 border-red-300 sm:col-span-6 xl:col-span-3 card shadow-red-100 dark:border-red-500/30 dark:shadow-red-500/10">
        <div class="card-body">
            <h6 class="mb-2">Need a help in Claim?</h6>
            <p class="mb-3 text-gray-500 dark:text-dark-500">Go to this step by step guideline process on how to certify for your weekly benefits:</p>
            <a href="#" class="inline-flex items-center gap-2 font-medium text-red-500 hover:underline">
                See our guideline
                <i data-lucide="square-arrow-out-up-right" class="size-4"></i>
            </a>
        </div>
    </div><!--end col-->
    <div class="col-span-12 border-yellow-300 sm:col-span-6 xl:col-span-3 card shadow-yellow-100 dark:border-yellow-500/30 dark:shadow-yellow-500/10">
        <div class="card-body">
            <h6 class="mb-2">Need a help in Claim?</h6>
            <p class="mb-3 text-gray-500 dark:text-dark-500">Go to this step by step guideline process on how to certify for your weekly benefits:</p>
            <a href="#" class="inline-flex items-center gap-2 font-medium text-yellow-500 hover:underline">
                See our guideline
                <i data-lucide="square-arrow-out-up-right" class="size-4"></i>
            </a>
        </div>
    </div><!--end col-->
    <div class="col-span-12 sm:col-span-6 xl:col-span-3 border-sky-300 card shadow-sky-100 dark:border-sky-500/30 dark:shadow-sky-500/10">
        <div class="card-body">
            <h6 class="mb-2">Need a help in Claim?</h6>
            <p class="mb-3 text-gray-500 dark:text-dark-500">Go to this step by step guideline process on how to certify for your weekly benefits:</p>
            <a href="#" class="inline-flex items-center gap-2 font-medium text-sky-500 hover:underline">
                See our guideline
                <i data-lucide="square-arrow-out-up-right" class="size-4"></i>
            </a>
        </div>
    </div><!--end col-->
    <div class="col-span-12 border-pink-300 sm:col-span-6 xl:col-span-3 card shadow-pink-100 dark:border-pink-500/30 dark:shadow-pink-500/10">
        <div class="card-body">
            <h6 class="mb-2">Need a help in Claim?</h6>
            <p class="mb-3 text-gray-500 dark:text-dark-500">Go to this step by step guideline process on how to certify for your weekly benefits:</p>
            <a href="#" class="inline-flex items-center gap-2 font-medium text-pink-500 hover:underline">
                See our guideline
                <i data-lucide="square-arrow-out-up-right" class="size-4"></i>
            </a>
        </div>
    </div><!--end col-->
    <div class="col-span-12 border-orange-300 sm:col-span-6 xl:col-span-3 card shadow-orange-100 dark:border-orange-500/30 dark:shadow-orange-500/10">
        <div class="card-body">
            <h6 class="mb-2">Need a help in Claim?</h6>
            <p class="mb-3 text-gray-500 dark:text-dark-500">Go to this step by step guideline process on how to certify for your weekly benefits:</p>
            <a href="#" class="inline-flex items-center gap-2 font-medium text-orange-500 hover:underline">
                See our guideline
                <i data-lucide="square-arrow-out-up-right" class="size-4"></i>
            </a>
        </div>
    </div><!--end col-->
    <div class="col-span-12 border-indigo-300 sm:col-span-6 xl:col-span-3 card shadow-indigo-100 dark:border-indigo-500/30 dark:shadow-indigo-500/10">
        <div class="card-body">
            <h6 class="mb-2">Need a help in Claim?</h6>
            <p class="mb-3 text-gray-500 dark:text-dark-500">Go to this step by step guideline process on how to certify for your weekly benefits:</p>
            <a href="#" class="inline-flex items-center gap-2 font-medium text-indigo-500 hover:underline">
                See our guideline
                <i data-lucide="square-arrow-out-up-right" class="size-4"></i>
            </a>
        </div>
    </div><!--end col-->
</div><!--end grid-->

<h5 class="mt-2 mb-5 underline">Fancy Card:</h5>

<div class="grid grid-cols-12 gap-x-space">
    <div class="col-span-12 !bg-transparent border-0 shadow-none sm:col-span-6 xl:col-span-4 card">
        <a href="#">
            <img class="rounded-md" src="assets/images/gallery/img-04.jpg" alt="Gallery">
        </a>
        <div class="relative mx-10 -mt-16 bg-white rounded-md card-body dark:bg-dark-950">
            <h5 class="mb-3"><a href="#" class="transition duration-500 ease-in-out hover:text-primary-500">The white and blue boat on river under blue sky during daytime</a></h5>
            <p class="text-gray-500 dark:text-dark-500">Today, I’m covering one of my favorite parts of the Nordstrom Anniversary Sale: the activewear!</p>
            <p class="mt-4 text-sm text-gray-500 dark:text-dark-500">
                By
                <a href="#" class="transition duration-500 ease-in-out text-primary-500">
                    Daniel
                </a> | in <a href="#" class="transition duration-500 ease-in-out text-primary-500">
                    Traveling
                </a>, <a href="#" class="transition duration-500 ease-in-out text-primary-500">
                    Business
                </a>
            </p>
        </div>
    </div><!--end col-->
    <div class="col-span-12 !bg-transparent border-0 shadow-none sm:col-span-6 xl:col-span-4 card">
        <a href="#">
            <img class="rounded-md" src="assets/images/gallery/img-01.jpg" alt="Gallery">
        </a>
        <div class="relative mx-10 -mt-16 bg-white rounded-md card-body dark:bg-dark-950">
            <h5 class="mb-3"><a href="#" class="transition duration-500 ease-in-out hover:text-primary-500">A heritage Shophouse at Little india SG</a></h5>
            <p class="text-gray-500 dark:text-dark-500">Today, I’m covering one of my favorite parts of the Nordstrom Anniversary Sale: the activewear!</p>
            <p class="mt-4 text-sm text-gray-500 dark:text-dark-500">
                By
                <a href="#" class="transition duration-500 ease-in-out text-primary-500">
                    Daniel
                </a> | in <a href="#" class="transition duration-500 ease-in-out text-primary-500">
                    Traveling
                </a>, <a href="#" class="transition duration-500 ease-in-out text-primary-500">
                    Business
                </a>
            </p>
        </div>
    </div><!--end col-->
    <div class="col-span-12 border-0 shadow-none sm:col-span-6 xl:col-span-4 card !bg-transparent">
        <a href="#">
            <img class="rounded-md" src="assets/images/gallery/img-02.jpg" alt="Gallery">
        </a>
        <div class="relative mx-10 -mt-16 bg-white rounded-md card-body dark:bg-dark-950">
            <h5 class="mb-3"><a href="#" class="transition duration-500 ease-in-out hover:text-primary-500">Purple flowers with green leaves</a></h5>
            <p class="text-gray-500 dark:text-dark-500">Today, I’m covering one of my favorite parts of the Nordstrom Anniversary Sale: the activewear!</p>
            <p class="mt-4 text-sm text-gray-500 dark:text-dark-500">
                By
                <a href="#" class="transition duration-500 ease-in-out text-primary-500">
                    Daniel
                </a> | in <a href="#" class="transition duration-500 ease-in-out text-primary-500">
                    Traveling
                </a>, <a href="#" class="transition duration-500 ease-in-out text-primary-500">
                    Business
                </a>
            </p>
        </div>
    </div><!--end col-->
</div><!--end grid-->

<h5 class="mt-2 mb-5 underline">Overlay Card:</h5>

<div class="grid grid-cols-12 gap-x-space">
    <div class="relative col-span-12 overflow-hidden border-0 sm:col-span-6 xl:col-span-4 card">
        <img src="assets/images/gallery/img-02.jpg" alt="Gallery">
        <div class="absolute inset-0 bg-gradient-to-t from-primary-500/30 via-green-500/30"></div>
        <div class="absolute inset-0 z-10 flex items-end card-body">
            <div>
                <h3 class="mt-3 text-white">Water Lily</h3>
                <p class="text-white/75">Flowers of love</p>
            </div>
        </div>
    </div><!--end col-->
    <div class="relative col-span-12 overflow-hidden border-0 sm:col-span-6 xl:col-span-4 card">
        <img src="assets/images/gallery/img-03.jpg" alt="Gallery">
        <div class="absolute inset-0 bg-gradient-to-t from-primary-500/80 via-sky-500/30"></div>
        <div class="absolute inset-0 z-10 flex items-end card-body">
            <div>
                <h3 class="mt-3 text-white">Art Of Holiday</h3>
            </div>
        </div>
    </div><!--end col-->
    <div class="relative col-span-12 overflow-hidden border-0 sm:col-span-6 xl:col-span-4 card">
        <img src="assets/images/gallery/img-04.jpg" alt="Gallery">
        <div class="absolute inset-0 bg-gradient-to-t from-primary-500/50 via-sky-500/50"></div>
        <div class="absolute inset-0 z-10 flex items-end card-body">
            <div>
                <h3 class="mt-3 text-white">River Water</h3>
                <p class="text-white/75">City of love</p>
            </div>
        </div>
    </div><!--end col-->
</div><!--end grid-->

<h5 class="mt-2 mb-5 underline">Edit Cards</h5>

<div class="grid grid-cols-12 gap-x-space">
    <div class="col-span-12 sm:col-span-6 xl:col-span-4">
        <div class="relative flex flex-col items-center justify-center w-full mb-5">
            <div class="flex flex-col w-full p-4 transition-colors ease-in rounded-lg shadow-md bg-primary-500" :class="bgColor" x-data="profileCard()" @click.away="showSettings=false">
                <div class="z-10 flex-col max-w-full p-5 text-center transition-all origin-top-left transform w-70 h-70" :class="{'flex': showSettings, 'hidden': !showSettings}">
                    <span class="text-2xl font-bold text-white">Settings</span>
                    <div class="flex flex-col mt-3 space-y-2 grow">
                        <span class="mb-2 font-bold text-white text-md">Background color:</span>
                        <div class="flex justify-center w-full space-x-2">
                            <template x-for="c in bgColors">
                                <button class="w-8 h-8 border-4 border-white rounded-full cursor-pointer flex-inline" :class="c" @click="selectColor(c)" :disabled="bgColor == c"></button>
                            </template>
                        </div>
                    </div>
                    <div class="flex justify-center flex-shrink mt-4 align-end">
                        <button class="text-lg font-bold text-white bg-transparent rounded fill-current" @click="showSettings = false">Close</button>
                    </div>
                </div>
                <div x-show="!showSettings">
                    <div class="flex justify-between w-full p-2">
                        <button class="flex items-center space-x-1 font-semibold text-white bg-transparent cursor-pointer fill-current group/items" @click="showSettings=true">
                            <i data-lucide="settings"></i>
                            <span class="text-lg transition-transform ease-in origin-left transform scale-x-0 select-none group-focus/items:scale-x-100 group-hover/items:scale-x-100">settings</span>
                        </button>
                        <button class="flex items-center space-x-1 font-semibold text-white bg-transparent cursor-pointer fill-current group">
                            <span class="text-lg transition-transform ease-in origin-right transform scale-x-0 select-none group-focus/items:scale-x-100 group-hover/items:scale-x-100">logout</span>
                            <i data-lucide="log-out"></i>
                        </button>
                    </div>
                    <div class="flex flex-col w-full h-full text-center">
                        <div class="flex flex-col items-center mb-3">
                            <img :src="photoUrl" alt="" class="rounded-full select-none size-28">
                            <div class="relative mt-4 text-center group/items">
                                <span class="p-0 px-2 m-0 font-sans text-xl font-semibold text-white break-words select-none" x-text="fullName" x-show="editing != 'fullName'" @dblclick="edit('fullName')" title="Double click to edit"></span>
                                <i data-lucide="pencil" class="absolute inline w-4 h-4 transition-transform transform scale-0 cursor-pointer text-gray-50 right-2 -top-3 group-hover/items:scale-100" x-show="editing != 'fullName'"></i>
                                <input type="text" name="" id="" class="w-full p-0 px-2 m-0 font-sans text-xl font-semibold text-center text-white bg-transparent focus:outline-hidden focus:animate-pulse" :value="fullName" spellcheck="false" x-show="editing == 'fullName'" x-ref="fullNameinput" @keydown.enter="saveEdit('fullName')" @click.away="discard('fullName')" @keydown.escape="discard('fullName')" title="Enter to save, click outside to discard." maxlength="20">
                            </div>
                            <div class="relative mt-1 text-center group/items">
                                <span class="p-0 m-0 font-sans text-sm font-semibold select-none text-white/75 text-wrap " x-text="username" x-show="editing != 'username'" @dblclick="edit('username')" title="Double click to edit"></span>
                                <i data-lucide="pencil" class="absolute inline w-4 h-4 ml-1 transition-transform transform scale-0 cursor-pointer text-gray-50 -top-1 group-hover/items:scale-100" x-show="editing != 'username'"></i>
                                <input type="text" name="" id="" class="inline-block w-auto p-0 m-0 font-sans text-sm font-semibold text-center bg-transparent text-white/75 focus:outline-hidden focus:animate-pulse" :value="username" spellcheck="false" x-show="editing == 'username'" x-ref="usernameinput" @keydown.enter="saveEdit('username')" @click.away="discard('username')" @keydown.escape="discard('username')" title="Enter to save, click outside to discard." maxlength="15">
                            </div>
                        </div>
                        <div class="flex flex-row justify-evenly">
                            <div class="flex flex-col cursor-pointer hover:opacity-80">
                                <span class="text-lg font-bold text-white">11</span>
                                <span class="text-sm text-white/75">Followers</span>
                            </div>
                            <div class="flex flex-col cursor-pointer hover:opacity-80">
                                <span class="text-lg font-bold text-white">52</span>
                                <span class="text-sm text-white/75">Following</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div><!--end col-->
</div><!--end grid-->

</div>
{{> partials/footer }}
</div>


{{> partials/vendor-scripts }}
<script type="module" src="assets/js/ui/cards.init.js"></script>
<script type="module" src="assets/js/main.js"></script>

</body>
</html>