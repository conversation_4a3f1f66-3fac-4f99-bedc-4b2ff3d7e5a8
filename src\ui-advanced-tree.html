{{> partials/main }}

<head>

    {{> partials/title-meta title="Tree" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}

{{> partials/page-heading title="Tree" sub-title="UI Advanced" }}

<div class="grid grid-cols-12 gap-x-space">
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Folder Structure Tree</h6>
        </div>
        <div class="card-body">
            <div class="-mx-5" x-data="fileTree()">
                <ul>
                    <template x-for="(level,i) in levels">
                        <li x-html="renderLevel(level,i)"></li>
                    </template>
                </ul>
            </div>
        </div>
    </div><!--end col-->
</div><!--end grid-->


</div>
{{> partials/footer }}
</div>

{{> partials/vendor-scripts }}
<script type="module" src="assets/js/main.js"></script>

<script type="module" src="assets/js/ui/advanced-tree.init.js"></script>

</body>
</html>