{{> partials/main }}

<head>

    {{> partials/title-meta title="Drawer" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}

{{> partials/page-heading title="Drawer" sub-title="UI" }}

<div class="grid grid-cols-12 gap-x-space">
    <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Basic</h6>
        </div>
        <div class="card-body">
            <button data-drawer-target="basicEnd" type="button" drawer-end class="btn btn-primary">Default Drawer</button>
            <div id="basicEnd" drawer-end class="drawer show">
                <div class="drawer-header">
                    <h6>Drawer Heading</h6>
                    <button data-drawer-close="basicEnd"><i data-lucide="x" class="link link-red"></i></button>
                </div>
                <div class="drawer-content">
                    <h6 class="mb-4 text-15">Drawer Content</h6>
                    <p class="text-slate-500 dark:text-dark-500">They all have something to say beyond the words on the page. They can come across as casual or neutral, exotic or graphic.</p>
                </div>
                <div class="drawer-footer">
                    <h6>Drawer Footer</h6>
                </div>
            </div>
        </div>
    </div><!--end -->
    <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Drawer Position</h6>
        </div>
        <div class="card-body">
            <div class="flex flex-wrap items-center gap-2">
                <button data-drawer-target="drawerEnd" type="button" class="btn btn-sub-gray">End Drawer</button>
                <button data-drawer-target="drawerStart" type="button" class="btn btn-sub-gray">Start Drawer</button>
                <button data-drawer-target="drawerTop" type="button" class="btn btn-sub-gray">Top Drawer</button>
                <button data-drawer-target="drawerBottom" type="button" class="btn btn-sub-gray">Bottom Drawer</button>
            </div>
            <div id="drawerEnd" drawer-end class="drawer show">
                <div class="drawer-header">
                    <h6>End Drawer Heading</h6>
                    <button data-drawer-close="drawerEnd"><i data-lucide="x" class="link link-red"></i></button>
                </div>
                <div class="drawer-content">
                    <h6 class="mb-4 text-15">Drawer Content</h6>
                    <p class="text-slate-500 dark:text-dark-500">They all have something to say beyond the words on the page. They can come across as casual or neutral, exotic or graphic.</p>
                </div>
                <div class="drawer-footer">
                    <h6>Drawer Footer</h6>
                </div>
            </div><!--end-->

            <div id="drawerStart" drawer-start class="drawer show">
                <div class="drawer-header">
                    <h6>Start Drawer Heading</h6>
                    <button data-drawer-close="drawerStart"><i data-lucide="x" class="link link-red"></i></button>
                </div>
                <div class="drawer-content">
                    <h6 class="mb-4 text-15">Drawer Content</h6>
                    <p class="text-slate-500 dark:text-dark-500">They all have something to say beyond the words on the page. They can come across as casual or neutral, exotic or graphic.</p>
                </div>
                <div class="drawer-footer">
                    <h6>Drawer Footer</h6>
                </div>
            </div><!--end-->

            <div id="drawerTop" drawer-top class="drawer show">
                <div class="drawer-header">
                    <h6>Top Drawer Heading</h6>
                    <button data-drawer-close="drawerTop"><i data-lucide="x" class="link link-red"></i></button>
                </div>
                <div class="drawer-content">
                    <h6 class="mb-4 text-15">Drawer Content</h6>
                    <p class="text-slate-500 dark:text-dark-500">They all have something to say beyond the words on the page. They can come across as casual or neutral, exotic or graphic.</p>
                </div>
                <div class="drawer-footer">
                    <h6>Drawer Footer</h6>
                </div>
            </div><!--end-->

            <div id="drawerBottom" drawer-bottom class="drawer show">
                <div class="drawer-header">
                    <h6>Bottom Drawer Heading</h6>
                    <button data-drawer-close="drawerBottom"><i data-lucide="x" class="link link-red"></i></button>
                </div>
                <div class="drawer-content">
                    <h6 class="mb-4 text-15">Drawer Content</h6>
                    <p class="text-slate-500 dark:text-dark-500">They all have something to say beyond the words on the page. They can come across as casual or neutral, exotic or graphic.</p>
                </div>
                <div class="drawer-footer">
                    <h6>Drawer Footer</h6>
                </div>
            </div><!--end-->
        </div>
    </div><!--end -->
    <div class="col-span-12 md:col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Size Drawer</h6>
        </div>
        <div class="card-body">
            <div class="flex flex-wrap items-center gap-2">
                <button data-drawer-target="smallDrawer" type="button" class="btn btn-sub-gray">Extra small</button>
                <button data-drawer-target="largeDrawer" type="button" class="btn btn-sub-gray">Large</button>
                <button data-drawer-target="halfScreenDrawer" type="button" class="btn btn-sub-gray">Half Screen</button>
            </div>

            <div id="smallDrawer" drawer-end class="drawer drawer-sm show">
                <div class="drawer-content" data-simplebar>
                    <div class="*:relative *:rounded-full *:block space-y-3">
                        <a href="#!">
                            <img src="assets/images/avatar/user-4.png" alt="" class="mx-auto rounded-full size-14">
                            <div class="absolute bottom-0 bg-green-500 border-2 border-white rounded-full dark:border-dark-900 size-4 right-0.5"></div>
                        </a>
                        <a href="#!">
                            <img src="assets/images/avatar/user-5.png" alt="" class="mx-auto rounded-full size-14">
                            <div class="absolute bottom-0 bg-green-500 border-2 border-white rounded-full dark:border-dark-900 size-4 right-0.5"></div>
                        </a>
                        <a href="#!">
                            <img src="assets/images/avatar/user-6.png" alt="" class="mx-auto rounded-full size-14">
                            <div class="absolute bottom-0 bg-green-500 border-2 border-white rounded-full dark:border-dark-900 size-4 right-0.5"></div>
                        </a>
                        <a href="#!">
                            <img src="assets/images/avatar/user-11.png" alt="" class="mx-auto rounded-full size-14">
                            <div class="absolute bottom-0 bg-green-500 border-2 border-white rounded-full dark:border-dark-900 size-4 right-0.5"></div>
                        </a>
                        <a href="#!">
                            <img src="assets/images/avatar/user-8.png" alt="" class="mx-auto rounded-full size-14">
                            <div class="absolute bottom-0 bg-green-500 border-2 border-white rounded-full dark:border-dark-900 size-4 right-0.5"></div>
                        </a>
                        <a href="#!">
                            <img src="assets/images/avatar/user-12.png" alt="" class="mx-auto rounded-full size-14">
                            <div class="absolute bottom-0 bg-green-500 border-2 border-white rounded-full dark:border-dark-900 size-4 right-0.5"></div>
                        </a>
                        <a href="#!">
                            <img src="assets/images/avatar/user-13.png" alt="" class="mx-auto rounded-full size-14">
                            <div class="absolute bottom-0 bg-green-500 border-2 border-white rounded-full dark:border-dark-900 size-4 right-0.5"></div>
                        </a>
                        <a href="#!">
                            <img src="assets/images/avatar/user-14.png" alt="" class="mx-auto rounded-full size-14">
                            <div class="absolute bottom-0 bg-green-500 border-2 border-white rounded-full dark:border-dark-900 size-4 right-0.5"></div>
                        </a>
                        <a href="#!">
                            <img src="assets/images/avatar/user-15.png" alt="" class="mx-auto rounded-full size-14">
                            <div class="absolute bottom-0 bg-green-500 border-2 border-white rounded-full dark:border-dark-900 size-4 right-0.5"></div>
                        </a>
                        <a href="#!">
                            <img src="assets/images/avatar/user-17.png" alt="" class="mx-auto rounded-full size-14">
                            <div class="absolute bottom-0 bg-green-500 border-2 border-white rounded-full dark:border-dark-900 size-4 right-0.5"></div>
                        </a>
                        <a href="#!">
                            <img src="assets/images/avatar/user-18.png" alt="" class="mx-auto rounded-full size-14">
                            <div class="absolute bottom-0 bg-green-500 border-2 border-white rounded-full dark:border-dark-900 size-4 right-0.5"></div>
                        </a>
                        <a href="#!">
                            <img src="assets/images/avatar/user-19.png" alt="" class="mx-auto rounded-full size-14">
                            <div class="absolute bottom-0 bg-green-500 border-2 border-white rounded-full dark:border-dark-900 size-4 right-0.5"></div>
                        </a>
                        <a href="#!">
                            <img src="assets/images/avatar/user-20.png" alt="" class="mx-auto rounded-full size-14">
                            <div class="absolute bottom-0 bg-green-500 border-2 border-white rounded-full dark:border-dark-900 size-4 right-0.5"></div>
                        </a>
                        <a href="#!">
                            <img src="assets/images/avatar/user-21.png" alt="" class="mx-auto rounded-full size-14">
                            <div class="absolute bottom-0 bg-green-500 border-2 border-white rounded-full dark:border-dark-900 size-4 right-0.5"></div>
                        </a>
                        <a href="#!">
                            <img src="assets/images/avatar/user-22.png" alt="" class="mx-auto rounded-full size-14">
                            <div class="absolute bottom-0 bg-green-500 border-2 border-white rounded-full dark:border-dark-900 size-4 right-0.5"></div>
                        </a>
                        <a href="#!">
                            <img src="assets/images/avatar/user-24.png" alt="" class="mx-auto rounded-full size-14">
                            <div class="absolute bottom-0 bg-green-500 border-2 border-white rounded-full dark:border-dark-900 size-4 right-0.5"></div>
                        </a>
                        <a href="#!">
                            <img src="assets/images/avatar/user-25.png" alt="" class="mx-auto rounded-full size-14">
                            <div class="absolute bottom-0 bg-green-500 border-2 border-white rounded-full dark:border-dark-900 size-4 right-0.5"></div>
                        </a>
                    </div>
                </div>
            </div><!--end-->

            <div id="largeDrawer" drawer-end class="drawer drawer-lg show">
                <div class="drawer-header">
                    <h6>Large Drawer</h6>
                    <button data-drawer-close="drawerBottom"><i data-lucide="x" class="link link-red"></i></button>
                </div>
                <div class="drawer-content">
                    <h6 class="mb-3 text-15">Drawer Content</h6>
                    <p class="text-slate-500 dark:text-dark-500">They all have something to say beyond the words on the page. They can come across as casual or neutral, exotic or graphic.</p>
                </div>
                <div class="drawer-footer">
                    <h6>Drawer Footer</h6>
                </div>
            </div><!--end-->

            <div id="halfScreenDrawer" drawer-start class="drawer-half drawer show">
                <div class="drawer-header">
                    <h6>Half Screen Drawer</h6>
                    <button data-drawer-close="halfScreenDrawer"><i data-lucide="x" class="link link-red"></i></button>
                </div>
                <div class="drawer-content">
                    <h6 class="mb-3 text-15">Drawer Content</h6>
                    <p class="text-slate-500 dark:text-dark-500">They all have something to say beyond the words on the page. They can come across as casual or neutral, exotic or graphic.</p>
                </div>
                <div class="drawer-footer">
                    <h6>Drawer Footer</h6>
                </div>
            </div><!--end-->
        </div>
    </div><!--end -->
</div><!--end grid-->

</div>
{{> partials/footer }}
</div>


{{> partials/vendor-scripts }}
<script type="module" src="assets/js/main.js"></script>

</body>
</html>