Stack trace:
Frame         Function      Args
0007FFFFB740  00021006118E (00021028DEE8, 000210272B3E, 0007FFFFB740, 0007FFFFA640) msys-2.0.dll+0x2118E
0007FFFFB740  0002100469BA (000000000000, 000000000000, 00021005B234, 0007FFFFBA18) msys-2.0.dll+0x69BA
0007FFFFB740  0002100469F2 (00021028E048, 0007FFFFB5F8, 0007FFFFB740, 0007FFFFBA10) msys-2.0.dll+0x69F2
0007FFFFB740  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFFB740  00021006A60D (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A60D
0007FFFFBA20  00021006B9A5 (000000004000, 000700000000, 000000000008, 000000000008) msys-2.0.dll+0x2B9A5
0007FFFFBA20  00021006BC2A (0007FFFFBAC8, 000000000001, 0007FFFFBCA4, 000000000000) msys-2.0.dll+0x2BC2A
0007FFFFBA20  000210198A2B (0007FFFFBAC8, 000000000001, 0007FFFFBCA4, 000000000000) msys-2.0.dll+0x158A2B
0007FFFFBA20  00010042DB45 (000A00000004, 0007FFFFBCB4, 00010040DD62, 000A0008E800) bash.exe+0x2DB45
0000FFFFFFFF  00010043C4D8 (0000000000C2, 000A00000000, 000A0008EF70, 000A00081D30) bash.exe+0x3C4D8
000000000070  00010043E69E (0002102706C0, 000000000001, 00021017D98A, 0007FFFFBCB0) bash.exe+0x3E69E
000000000070  000100441AE6 (000700000001, 000000000000, 0007FFFFBDA0, 000000000000) bash.exe+0x41AE6
000000000070  000100441D16 (000000000000, 000000000000, 000000000000, 000000000000) bash.exe+0x41D16
000000000019  0001004449B8 (000000000010, 000000000000, 000000000000, 000A0008EDD0) bash.exe+0x449B8
000000000019  00010043D677 (00000000001F, 000000000000, 000210198A2B, 000A0008ED50) bash.exe+0x3D677
00000000000E  00010043DC43 (000A0008ED70, 00000000002D, 000A00099590, 000A000994E0) bash.exe+0x3DC43
0001004F6F60  000100433766 (0000102706C0, 000000000001, 000000000000, 000000000000) bash.exe+0x33766
000000000001  0001004440BE (0002102706C0, 00010061F274, 00021017D98A, 000000000020) bash.exe+0x440BE
0000FFFFFFFF  000100419C92 (000210198A2B, 0000FFFFFFFF, 000000000001, 000A0008ECB0) bash.exe+0x19C92
000A0008EC00  00010041AC6A (00021017DA47, 000000000000, 0001004EB4A0, 000100000000) bash.exe+0x1AC6A
000A0008EC00  00010041C4EF (000210198A2B, 000000000000, 000A00097F80, 000A0008EC00) bash.exe+0x1C4EF
0000FFFFFFFF  0001004179C7 (000210198A2B, 000A00132520, 000A00098270, 000A0008EC00) bash.exe+0x179C7
0000000001AE  00010041AC6A (000A00098310, 000A000982E0, 000000000001, 000000000020) bash.exe+0x1AC6A
0000000001AE  00010041813E (0007FFFFC610, 000A00028830, 0002100AE898, 000A0008EA40) bash.exe+0x1813E
000000000001  00010046F3A5 (000A00171670, 000000000414, 0000CE000000, 0000000000BE) bash.exe+0x6F3A5
000A000288E0  00010046E127 (000000000009, 000A00022180, 000000000000, 0007FFFFCBEF) bash.exe+0x6E127
000000000000  00010046E2D5 (000000000000, 000000000000, 0002101EB9F7, 000000000000) bash.exe+0x6E2D5
000000000000  0001004EA70C (000A00002130, 000A00000160, FFFFFFFFFFFFFF36, 000000000000) bash.exe+0xEA70C
0007FFFFCD30  000210048142 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x8142
0007FFFFFFF0  000210045C86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x5C86
0007FFFFFFF0  000210045D34 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x5D34
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF9C7FF0000 ntdll.dll
7FF9C6750000 KERNEL32.DLL
7FF9C53C0000 KERNELBASE.dll
7FF9C5DB0000 USER32.dll
7FF9C5390000 win32u.dll
7FF9C5C80000 GDI32.dll
7FF9C58F0000 gdi32full.dll
7FF9C5A20000 msvcp_win.dll
7FF9C57D0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF9C7ED0000 advapi32.dll
7FF9C7E20000 msvcrt.dll
7FF9C60E0000 sechost.dll
7FF9C57A0000 bcrypt.dll
7FF9C6630000 RPCRT4.dll
7FF9C4960000 CRYPTBASE.DLL
7FF9C5C00000 bcryptPrimitives.dll
7FF9C5D60000 IMM32.DLL
