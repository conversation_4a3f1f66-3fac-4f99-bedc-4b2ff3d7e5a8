{{> partials/main }}

<head>

    {{> partials/title-meta title="Admission Form" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}
{{> partials/page-heading title="Admission Form" sub-title="Students" }}

<div class="grid grid-cols-12 gap-x-space">
    <div class="col-span-12 xl:col-span-8 2xl:col-span-9" x-data="formData()">
        <div class="card" x-data="{}">
            <div class="card-header">
                <ul class="overflow-x-auto tabs-pills">
                    <li @click="openTab = 1">
                        <a href="javascript:void(0)" :class="openTab === 1 ? activeClasses : inactiveClasses" class="nav-item [&.active]:bg-primary-500 [&.active]:text-primary-50">
                            Personal Details
                        </a>
                    </li>
                    <li @click="openTab = 2">
                        <a href="javascript:void(0)" :class="openTab === 2 ? activeClasses : inactiveClasses" class="nav-item [&.active]:bg-primary-500 [&.active]:text-primary-50">
                            Guardian Details
                        </a>
                    </li>
                    <li @click="openTab = 3">
                        <a href="javascript:void(0)" :class="openTab === 3 ? activeClasses : inactiveClasses" class="nav-item [&.active]:bg-primary-500 [&.active]:text-primary-50">
                            Educational Background
                        </a>
                    </li>
                    <li @click="openTab = 4">
                        <a href="javascript:void(0)" :class="openTab === 4 ? activeClasses : inactiveClasses" class="nav-item [&.active]:bg-primary-500 [&.active]:text-primary-50">
                            Documents
                        </a>
                    </li>
                    <li @click="openTab = 5">
                        <a href="javascript:void(0)" :class="openTab === 5 ? activeClasses : inactiveClasses" class="nav-item [&.active]:bg-primary-500 [&.active]:text-primary-50">
                            Application Overview
                        </a>
                    </li>
                </ul>
            </div>
            <div class="card-body">
                <div class="w-full">
                    <div x-show="openTab === 1">
                        <form action="#!" id="personalDetailsForm" x-ref="personalDetailsForm">
                            <h6 class="mb-3">Personal Details</h6>
                            <div class="grid grid-cols-12 gap-space">
                                <div class="col-span-12 sm:col-span-6 2xl:col-span-4">
                                    <label for="firstNameInput" class="form-label">First Name</label>
                                    <input type="text" id="firstNameInput" class="form-input" placeholder="Enter your first name" x-model="personalForm.firstName"  >
                                </div>
                                <div class="col-span-12 sm:col-span-6 2xl:col-span-4">
                                    <label for="middleNameInput" class="form-label">Middle Name</label>
                                    <input type="text" id="middleNameInput" class="form-input" placeholder="Enter your middle name" x-model="personalForm.middleName" >
                                </div>
                                <div class="col-span-12 sm:col-span-6 2xl:col-span-4">
                                    <label for="lastNameInput" class="form-label">Last Name</label>
                                    <input type="text" id="lastNameInput" class="form-input" placeholder="Enter your last name" x-model="personalForm.lastName" >
                                </div>
                                <div class="col-span-12 sm:col-span-6 2xl:col-span-4">
                                    <label for="genderSelect" class="form-label">Gender</label>
                                    <div id="genderSelect" placeholder="Select Gender" x-model="personalForm.gender" @input="personalFormErrors.gender = document.querySelector('#genderSelect').value ? '' : 'Gender is required'"></div>

                                </div>
                                <div class="col-span-12 sm:col-span-6 2xl:col-span-4">
                                    <label for="ageInput" class="form-label">Age</label>
                                    <input type="number" id="ageInput" class="form-input" placeholder="Enter your age" required>
                                </div>
                                <div class="col-span-12 sm:col-span-6 2xl:col-span-4">
                                    <label for="dateOfBirthInput" class="form-label">Date of Birth</label>
                                    <input type="text" id="dateOfBirthInput" class="form-input" placeholder="Select date" data-provider="flatpickr" data-date-format="d M, Y" type="text" placeholder="YYYY-MM-DD">
                                </div>
                            </div>

                            <h6 class="mt-6 mb-3">Contact Details</h6>
                            <div class="grid grid-cols-12 gap-space">
                                <div class="col-span-12 md:col-span-6">
                                    <label for="mobileNumberInput" class="form-label">Mobile Number</label>
                                    <input type="number" id="mobileNumberInput" class="form-input" placeholder="Enter your mobile number" required>
                                </div>
                                <div class="col-span-12 md:col-span-6">
                                    <label for="alternativeMobileNumberInput" class="form-label">Alternative Mobile Number</label>
                                    <input type="number" id="alternativeMobileNumberInput" class="form-input" placeholder="Enter your mobile number">
                                </div>
                                <div class="col-span-12 md:col-span-6">
                                    <label for="emailIDInput" class="form-label">Email ID</label>
                                    <input type="email" id="emailIDInput" class="form-input" placeholder="<EMAIL>" required>
                                </div>
                                <div class="col-span-12 md:col-span-6">
                                    <label for="nationalityInput" class="form-label">Nationality</label>
                                    <input type="text" id="nationalityInput" class="form-input" placeholder="Enter your nationality" required>
                                </div>
                                <div class="col-span-12">
                                    <label for="addressInput" class="form-label">Parament Address</label>
                                    <input type="text" id="addressInput" class="form-input" placeholder="Enter your address" required>
                                </div>
                                <div class="col-span-12 md:col-span-4">
                                    <label for="cityInput" class="form-label">City</label>
                                    <input type="text" id="cityInput" class="form-input" placeholder="Enter your city" required>
                                </div>
                                <div class="col-span-12 md:col-span-4">
                                    <label for="countryInput" class="form-label">Country</label>
                                    <input type="text" id="countryInput" class="form-input" placeholder="Enter your country" required>
                                </div>
                                <div class="col-span-12 md:col-span-4">
                                    <label for="pinCodeInput" class="form-label">Pin Code</label>
                                    <input type="number" id="pinCodeInput" class="form-input" placeholder="Enter your pincode" required>
                                </div>
                            </div>
                            <div class="flex flex-wrap items-center gap-2 mt-5 ltr:justify-end rtl:justify-start">
                                <button type="button" class="btn btn-primary" @click="if(validatePersonalDetails()){openTab = 2}">
                                    Save to Next
                                    <i data-lucide="move-right" class="ltr:inline-block rtl:hidden ltr:ml-1 rtl:mr-1 size-4"></i>
                                    <i data-lucide="move-left" class="ltr:hidden rtl:inline-block ltr:ml-1 rtl:mr-1 size-4"></i>
                                </button>
                            </div>
                        </form>
                    </div>
                    <div x-show="openTab === 2">
                        <form action="#!" id="guardianDetailsForm" x-ref="guardianDetailsForm">
                            <div class="grid grid-cols-12 gap-space">
                                <div class="col-span-12 sm:col-span-4">
                                    <label for="fatherNameInput" class="form-label">Father Name</label>
                                    <input type="text" id="fatherNameInput" class="form-input" placeholder="Enter your father name" required>
                                </div>
                                <div class="col-span-12 sm:col-span-4">
                                    <label for="montherNameInput" class="form-label">Mother Name</label>
                                    <input type="text" id="montherNameInput" class="form-input" placeholder="Enter your mother name" required>
                                </div>
                                <div class="col-span-12 sm:col-span-4">
                                    <label for="otherRelativeNameInput" class="form-label">Others Relative Name</label>
                                    <input type="text" id="otherRelativeNameInput" class="form-input" placeholder="Enter your relative name" required>
                                </div>
                                <div class="col-span-12 sm:col-span-6">
                                    <label for="gdmobileNumberInput" class="form-label">Mobile Number</label>
                                    <input type="number" id="gdmobileNumberInput" class="form-input" placeholder="Enter your mobile number" required>
                                </div>
                                <div class="col-span-12 sm:col-span-6">
                                    <label for="alternativegdMobileNumberInput" class="form-label">Alternative Mobile Number</label>
                                    <input type="number" id="alternativegdMobileNumberInput" class="form-input" placeholder="Enter your mobile number">
                                </div>
                            </div>
                            <div class="flex flex-wrap items-center gap-2 mt-5 ltr:justify-end rtl:justify-start">
                                <button type="button" class="btn btn-sub-gray" @click="openTab = 1"><i data-lucide="move-left" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> Previous</button>
                                <button type="button" class="btn btn-primary" @click="if(validateGuardianDetails()){openTab = 3}">Save to Next <i data-lucide="move-right" class="inline-block ltr:ml-1 rtl:mr-1 size-4"></i></button>
                            </div>
                        </form>
                    </div>
                    <div x-show="openTab === 3">
                        <form action="#!" id="educationalBackgroundForm" x-ref="educationalBackgroundForm">
                            <h6 class="mb-3">High School</h6>
                            <div class="grid grid-cols-12 gap-space">
                                <div class="col-span-12 sm:col-span-4">
                                    <label for="highSchoolNameInput" class="form-label">Name</label>
                                    <input type="text" id="highSchoolNameInput" class="form-input" placeholder="High school name" required>
                                </div>
                                <div class="col-span-12 sm:col-span-4">
                                    <label for="graduationYearInput" class="form-label">Graduation Year</label>
                                    <input type="number" id="graduationYearInput" class="form-input" placeholder="Years" required>
                                </div>
                                <div class="col-span-12 sm:col-span-4">
                                    <label for="gpaInput" class="form-label">GPA</label>
                                    <input type="number" id="gpaInput" class="form-input" placeholder="GPA" required>
                                </div>
                                <div class="col-span-12">
                                    <label for="majorFocusInput" class="form-label">Major/Focus</label>
                                    <input type="text" id="majorFocusInput" class="form-input" placeholder="Major/Focus" required>
                                </div>
                            </div>
                            <h6 class="mt-5 mb-3">Undergraduate Institution</h6>
                            <div class="grid grid-cols-12 gap-space">
                                <div class="col-span-12 sm:col-span-4">
                                    <label for="instituteNameInput" class="form-label">Institute Name</label>
                                    <input type="text" id="instituteNameInput" class="form-input" placeholder="Institute name" required>
                                </div>
                                <div class="col-span-12 sm:col-span-4">
                                    <label for="underGraduationYearInput" class="form-label">Graduation Year</label>
                                    <input type="number" id="underGraduationYearInput" class="form-input" placeholder="Years" required>
                                </div>
                                <div class="col-span-12 sm:col-span-4">
                                    <label for="underGpaInput" class="form-label">GPA</label>
                                    <input type="number" id="underGpaInput" class="form-input" placeholder="GPA" required>
                                </div>
                                <div class="col-span-12">
                                    <label for="underMajorFocusInput" class="form-label">Major/Focus</label>
                                    <input type="text" id="underMajorFocusInput" class="form-input" placeholder="Major/Focus" required>
                                </div>
                            </div>

                            <h6 class="mt-5 mb-3">Academic Achievements</h6>
                            <div class="grid grid-cols-12 gap-space">
                                <div class="col-span-12 sm:col-span-4">
                                    <label for="honorsAwardsInput" class="form-label">Honors/Awards</label>
                                    <input type="text" id="honorsAwardsInput" class="form-input">
                                </div>
                                <div class="col-span-12 sm:col-span-4">
                                    <label for="extracurricularActivitiesInput" class="form-label">Extracurricular Activities</label>
                                    <input type="text" id="extracurricularActivitiesInput" class="form-input">
                                </div>
                                <div class="col-span-12 sm:col-span-4">
                                    <label for="leadershipRolesInput" class="form-label">Leadership Roles</label>
                                    <input type="text" id="leadershipRolesInput" class="form-input">
                                </div>
                                <div class="col-span-12">
                                    <label for="publicationsResearchInput" class="form-label">Publications/Research</label>
                                    <input type="text" id="publicationsResearchInput" class="form-input">
                                </div>
                            </div>

                            <div class="flex flex-wrap items-center justify-end gap-2 mt-5">
                                <button type="button" class="btn btn-sub-gray" @click="openTab = 2">
                                    <i data-lucide="move-left" class="mr-1 ltr:inline-block rtl:hidden size-4"></i>
                                    <i data-lucide="move-right" class="ml-1 ltr:hidden rtl:inline-block size-4"></i>
                                    Previous
                                </button>
                                <button type="button" class="btn btn-primary" @click=" if(validateEducationalBackground()){openTab = 4}">
                                    Save to Next
                                    <i data-lucide="move-right" class="ml-1 ltr:inline-block rtl:hidden size-4"></i>
                                    <i data-lucide="move-left" class="mr-1 ltr:hidden rtl:inline-block size-4"></i>
                                </button>
                            </div>
                        </form>
                    </div>
                    <div x-show="openTab === 4">
                        <form action="#!">
                            <h6 class="mb-3">Passport-sized Photograph</h6>
                            <div class="mb-5">
                                <label for="passportPhotoInput">
                                    <div class="flex items-center justify-center overflow-hidden bg-gray-100 border border-gray-200 rounded-sm cursor-pointer dark:bg-dark-850 dark:border-dark-800 size-36">
                                        <img x-show="passportImageUrl" :src="passportImageUrl" class="object-cover w-full h-full">
                                        <div x-show="!passportImageUrl" class="flex flex-col items-center text-gray-500 dark:text-dark-500">
                                            <i data-lucide="upload"></i>
                                            <div class="mt-2 mb-1">Passport Size</div>
                                            <p>144 x 144</p>
                                        </div>
                                    </div>
                                </label>
                                <label class="block">
                                    <input type="file" name="passportPhoto" id="passportPhotoInput" @change="handleFileChosen('passportPhoto', $event)" class="hidden" />
                                </label>
                                <p x-show="passportError" class="mt-1 text-sm text-red-500" x-text="passportError"></p>
                            </div>

                            <h6 class="mb-3">High School Transcript</h6>
                            <div>
                                <label for="transcriptInput">
                                    <div class="flex items-center justify-center p-4 overflow-hidden bg-gray-100 border border-gray-200 rounded-sm cursor-pointer dark:bg-dark-850 dark:border-dark-800 h-28">
                                        <img x-show="transcriptImageUrl" :src="transcriptImageUrl" class="object-cover w-full h-full">
                                        <div x-show="!transcriptImageUrl" class="flex flex-col items-center text-gray-500 dark:text-dark-500">
                                            <i data-lucide="upload"></i>
                                            <div class="mt-2 mb-1">Drag and drop your certificate</div>
                                            <p>only allowed pdf, png files.</p>
                                        </div>
                                    </div>
                                </label>
                                <label class="block">
                                    <input type="file" name="transcript" id="transcriptInput" @change="handleFileChosen('transcript', $event)" class="hidden" />
                                </label>
                                <p x-show="transcriptError" class="mt-1 text-sm text-red-500" x-text="transcriptError"></p>
                            </div>

                            <div class="flex flex-wrap items-center gap-2 mt-5 ltr:justify-end rtl:justify-start">
                                <button type="button" class="btn btn-sub-gray" @click="openTab = 3">
                                    <i data-lucide="move-left" class="mr-1 ltr:inline-block rtl:hidden size-4"></i>
                                    <i data-lucide="move-right" class="ml-1 ltr:hidden rtl:inline-block size-4"></i>
                                    Previous
                                </button>
                                <button type="button" class="btn btn-primary" @click="validateAndSave">
                                    Save to Next
                                    <i data-lucide="move-right" class="ml-1 ltr:inline-block rtl:hidden size-4"></i>
                                    <i data-lucide="move-left" class="mr-1 ltr:hidden rtl:inline-block size-4"></i>
                                </button>
                            </div>
                        </form>

                    </div>
                    <div x-show="openTab === 5">

                        <form action="#!">
                            <h6 class="mb-3">Application Overview</h6>
                            <div class="grid grid-cols-12 gap-space">
                                <div class="col-span-12 sm:col-span-6">
                                    <label for="applicationIDInput" class="form-label">Application ID</label>
                                    <input type="text" id="applicationIDInput" class="form-input" value="#PEA-1478A5487956236" readonly>
                                </div>
                                <div class="col-span-12 sm:col-span-6">
                                    <label for="referenceNumberInput" class="form-label">Reference Number</label>
                                    <input type="text" id="referenceNumberInput" class="form-input" placeholder="#PEA-000A0000000000">
                                </div>
                                <div class="col-span-12 sm:col-span-6">
                                    <label for="dateOfaAplicationSelect" class="form-label">Date of Application Submission</label>
                                    <input type="text" id="dateOfaAplicationSelect" class="form-input" value="23 June, 2024" readonly>
                                </div>
                                <div class="col-span-12 sm:col-span-6">
                                    <label for="dateOfaAplicationSelect" class="form-label">Current Status</label>
                                    <span class="badge badge-green">Submitted</span>
                                </div>
                            </div>

                            <div class="flex flex-wrap items-center gap-2 mt-5 ltr:justify-end rtl:justify-start">
                                <button type="button" class="btn btn-sub-gray" @click="openTab = 4">
                                    <i data-lucide="move-left" class="mr-1 ltr:inline-block rtl:hidden size-4"></i>
                                    <i data-lucide="move-right" class="ml-1 ltr:hidden rtl:inline-block size-4"></i>
                                    Previous
                                </button>
                                <button type="button" class="btn btn-primary" @click="submitAdmissionForm">
                                    Submitted Form
                                    <i data-lucide="move-right" class="ml-1 ltr:inline-block rtl:hidden size-4"></i>
                                    <i data-lucide="move-left" class="mr-1 ltr:hidden rtl:inline-block size-4"></i>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-span-12 xl:col-span-4 2xl:col-span-3">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title">Fees Structure</h6>
            </div>
            <div class="card-body">
                <div class="mb-4 alert-red alert">
                    <span>If the fees are not paid before 01 Aug, 2024, the application will be automatically rejected</span>
                </div>
                <form action="#!">
                    <div class="grid items-center grid-cols-12 gap-3">
                        <div class="col-span-12 sm:col-span-4">
                            <label for="courseFrees" class="mb-0 form-label">Course Frees</label>
                        </div>
                        <div class="col-span-12 sm:col-span-8">
                            <input type="number" id="courseFrees" class="form-input" placeholder="$0">
                        </div>
                        <div class="col-span-12 sm:col-span-4">
                            <label for="paidAmount" class="mb-0 form-label">Paid Amount</label>
                        </div>
                        <div class="col-span-12 sm:col-span-8">
                            <input type="number" id="paidAmount" class="form-input" placeholder="$0">
                        </div>
                        <div class="col-span-12 sm:col-span-4">
                            <label for="statuSelect" class="mb-0 form-label">Status</label>
                        </div>
                        <div class="col-span-12 sm:col-span-8">
                            <div id="statusSelect" placeholder="Select Status"></div>
                        </div>
                        <div class="col-span-12 sm:col-span-4">
                            <label for="paidVia" class="mb-0 form-label">Paid Via</label>
                        </div>
                        <div class="col-span-12 sm:col-span-8">
                            <input type="text" id="paidVia" class="form-input" placeholder="N/A">
                        </div>
                    </div>
                    <div class="flex flex-wrap gap-2 mt-5">
                        <button class="btn btn-sub-gray">View Receive</button>
                        <button class="btn btn-primary">Add Account</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

</div>
{{> partials/footer }}
</div>


{{> partials/vendor-scripts }}

<script type="module" src="assets/js/apps/school/students/admission.init.js"></script>

<script type="module" src="assets/js/main.js"></script>

</body>
</html>