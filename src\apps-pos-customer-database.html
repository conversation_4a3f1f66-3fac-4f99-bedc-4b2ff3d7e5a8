{{> partials/main }}

<head>

    {{> partials/title-meta title="Customer Database" }}

    {{> partials/head-css }}

    <script src="https://cdn.sheetjs.com/xlsx-0.20.1/package/dist/xlsx.full.min.js"></script>

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}
{{> partials/page-heading title="Customer Database" sub-title="POS" }}

<div x-data="employeeTable" x-init="showAddress=false; showCity=false; showCountry=false; showRegistrationDate=false; showNotes=false">
    <div class="grid grid-cols-1 sm:grid-cols-2 2xl:grid-cols-4 gap-5">
        <div class="card">
            <div class="card-body">
                <div class="flex items-center gap-3 mb-4">
                    <div class="bg-primary-500/15 text-primary-500 rounded-full p-2 shrink-0">
                        <i data-lucide="users" class="size-5"></i>
                    </div>
                    <h6 class="text-gray-500 font-medium dark:text-dark-500 grow">Total Customers</h6>
                    <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown shrink-0">
                        <button x-ref="button" x-on:click="toggle()" title="dropdown-button" :aria-expanded="open.toString()" type="button" class="flex items-center text-gray-500 dark:text-dark-500">
                            <i data-lucide="ellipsis" class="size-5"></i>
                        </button>
                        <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden" dropdown-position="right">
                            <ul>
                                <li>
                                    <a href="#!" class="dropdown-item">
                                        <span>Weekly</span>
                                    </a>
                                </li>
                                <li>
                                    <a href="#!" class="dropdown-item">
                                        <span>Monthly</span>
                                    </a>
                                </li>
                                <li>
                                    <a href="#!" class="dropdown-item">
                                        <span>Yearly</span>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <h4 class="mb-3 font-bold">29,710</h4>
                <div class="flex items-center gap-1 text-sm text-green-600">
                    <i data-lucide="chevron-up" class="size-4"></i>
                    +2.3% <span class="text-gray-500 dark:text-dark-500 ltr:ml-1 rtl:mr-1">compared to last month</span>
                </div>
            </div>
        </div>
        <div class="card">
            <div class="card-body">
                <div class="flex items-center gap-3 mb-4">
                    <div class="bg-yellow-500/15 text-yellow-500 rounded-full p-2 shrink-0">
                        <i data-lucide="sparkles" class="size-5"></i>
                    </div>
                    <h6 class="text-gray-500 font-medium dark:text-dark-500 grow">New Customer</h6>
                    <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown shrink-0">
                        <button x-ref="button" x-on:click="toggle()" title="dropdown-button" :aria-expanded="open.toString()" type="button" class="flex items-center text-gray-500 dark:text-dark-500">
                            <i data-lucide="ellipsis" class="size-5"></i>
                        </button>
                        <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden" dropdown-position="right">
                            <ul>
                                <li>
                                    <a href="#!" class="dropdown-item">
                                        <span>Weekly</span>
                                    </a>
                                </li>
                                <li>
                                    <a href="#!" class="dropdown-item">
                                        <span>Monthly</span>
                                    </a>
                                </li>
                                <li>
                                    <a href="#!" class="dropdown-item">
                                        <span>Yearly</span>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <h4 class="mb-3 font-bold">3,361</h4>
                <div class="flex items-center gap-1 text-sm text-red-500">
                    <i data-lucide="chevron-down" class="size-4"></i>
                    -2.3% <span class="text-gray-500 dark:text-dark-500 ltr:ml-1 rtl:mr-1">compared to last month</span>
                </div>
            </div>
        </div>
        <div class="card">
            <div class="card-body">
                <div class="flex items-center gap-3 mb-4">
                    <div class="bg-green-500/15 text-green-500 rounded-full p-2 shrink-0">
                        <i data-lucide="repeat-2" class="size-5"></i>
                    </div>
                    <h6 class="text-gray-500 font-medium dark:text-dark-500 grow">Return Customer Rate</h6>
                    <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown shrink-0">
                        <button x-ref="button" x-on:click="toggle()" title="dropdown-button" :aria-expanded="open.toString()" type="button" class="flex items-center text-gray-500 dark:text-dark-500">
                            <i data-lucide="ellipsis" class="size-5"></i>
                        </button>
                        <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden" dropdown-position="right">
                            <ul>
                                <li>
                                    <a href="#!" class="dropdown-item">
                                        <span>Weekly</span>
                                    </a>
                                </li>
                                <li>
                                    <a href="#!" class="dropdown-item">
                                        <span>Monthly</span>
                                    </a>
                                </li>
                                <li>
                                    <a href="#!" class="dropdown-item">
                                        <span>Yearly</span>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <h4 class="mb-3 font-bold">11.23%</h4>
                <div class="flex items-center gap-1 text-sm text-green-600">
                    <i data-lucide="chevron-up" class="size-4"></i>
                    +1.8% <span class="text-gray-500 dark:text-dark-500 ltr:ml-1 rtl:mr-1">compared to last month</span>
                </div>
            </div>
        </div>
        <div class="card">
            <div class="card-body">
                <div class="flex items-center gap-3 mb-4">
                    <div class="bg-purple-500/15 text-purple-500 rounded-full p-2 shrink-0">
                        <i data-lucide="briefcase" class="size-5"></i>
                    </div>
                    <h6 class="text-gray-500 font-medium dark:text-dark-500 grow">Avg. Order Revenue</h6>
                    <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown shrink-0">
                        <button x-ref="button" x-on:click="toggle()" title="dropdown-button" :aria-expanded="open.toString()" type="button" class="flex items-center text-gray-500 dark:text-dark-500">
                            <i data-lucide="ellipsis" class="size-5"></i>
                        </button>
                        <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden" dropdown-position="right">
                            <ul>
                                <li>
                                    <a href="#!" class="dropdown-item">
                                        <span>Weekly</span>
                                    </a>
                                </li>
                                <li>
                                    <a href="#!" class="dropdown-item">
                                        <span>Monthly</span>
                                    </a>
                                </li>
                                <li>
                                    <a href="#!" class="dropdown-item">
                                        <span>Yearly</span>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <h4 class="mb-3 font-bold">$39,643</h4>
                <div class="flex items-center gap-1 text-sm text-red-500">
                    <i data-lucide="chevron-down" class="size-4"></i>
                    -8% <span class="text-gray-500 dark:text-dark-500 ltr:ml-1 rtl:mr-1">compared to last month</span>
                </div>
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-header flex lg:items-center flex-col lg:flex-row gap-3">
            <h6 class="card-title grow">Customer Database</h6>
            <div class="flex items-center gap-3 shrink-0 flex-wrap">
                <button class="btn btn-sub-gray" @click="exportTable()">Export CSV</button>
                <button class="btn btn-sub-gray" @click="exportExcel()">Export Excel</button>
                <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown shrink-0">
                    <button x-ref="button" x-on:click="toggle()" title="dropdown-button" :aria-expanded="open.toString()" type="button" class="flex items-center btn btn-primary">
                        Manage Table Column
                        <svg :class="{ 'transform rotate-180': open }" class="size-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                        </svg>
                    </button>
                    <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-4 dropdown-menu hidden" dropdown-position="right">
                        <ul class="flex flex-col gap-3">
                            <li>
                                <div class="input-check-group">
                                    <input id="lastOrderCheckbox" class="input-check input-check-primary" type="checkbox" checked x-model="showLastOrder">
                                    <label for="lastOrderCheckbox" class="input-check-label">Last Order</label>
                                </div>
                            </li>
                            <li>
                                <div class="input-check-group">
                                    <input id="addressCheckbox" class="input-check input-check-primary" type="checkbox" x-model="showAddress">
                                    <label for="addressCheckbox" class="input-check-label">Address</label>
                                </div>
                            </li>
                            <li>
                                <div class="input-check-group">
                                    <input id="cityCheckbox" class="input-check input-check-primary" type="checkbox" x-model="showCity">
                                    <label for="cityCheckbox" class="input-check-label">City</label>
                                </div>
                            </li>
                            <li>
                                <div class="input-check-group">
                                    <input id="countryCheckbox" class="input-check input-check-primary" type="checkbox" x-model="showCountry">
                                    <label for="countryCheckbox" class="input-check-label">Country</label>
                                </div>
                            </li>
                            <li>
                                <div class="input-check-group">
                                    <input id="notesCheckbox" class="input-check input-check-primary" type="checkbox" x-model="showNotes">
                                    <label for="notesCheckbox" class="input-check-label">Notes</label>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body pt-0">
            <div class="overflow-x-auto table-box">
                <table class="table whitespace-nowrap text-sm">
                    <tbody>
                        <tr class="text-gray-500 bg-gray-100 dark:bg-dark-850 dark:text-dark-500">
                            <th x-on:click="sort('customerID')" class="cursor-pointer font-medium">
                                Customer ID
                                <span x-show="sortBy === 'customerID'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span>
                            </th>
                            <th x-on:click="sort('name')" class="cursor-pointer font-medium">
                                Customer
                                <span x-show="sortBy === 'name'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span>
                            </th>
                            <th x-on:click="sort('email')" class="cursor-pointer font-medium">
                                Email
                                <span x-show="sortBy === 'email'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span>
                            </th>
                            <th x-on:click="sort('phone')" class="cursor-pointer font-medium">
                                Phone
                                <span x-show="sortBy === 'phone'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span>
                            </th>
                            <th x-on:click="sort('totalOrders')" class="cursor-pointer font-medium">
                                Total Orders
                                <span x-show="sortBy === 'totalOrders'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span>
                            </th>
                            <th x-show="showLastOrder" x-on:click="sort('lastOrder')" class="cursor-pointer font-medium">
                                Last Order
                                <span x-show="sortBy === 'lastOrder'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span>
                            </th>
                            <th x-on:click="sort('outstanding')" class="cursor-pointer font-medium">
                                Outstanding
                                <span x-show="sortBy === 'outstanding'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span>
                            </th>
                            <th x-on:click="sort('status')" class="cursor-pointer font-medium">
                                Status
                                <span x-show="sortBy === 'status'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span>
                            </th>
                            <th x-show="showAddress" x-on:click="sort('address')" class="cursor-pointer font-medium">Address <span x-show="sortBy === 'address'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                            <th x-show="showCity" x-on:click="sort('city')" class="cursor-pointer font-medium">City <span x-show="sortBy === 'city'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                            <th x-show="showCountry" x-on:click="sort('country')" class="cursor-pointer font-medium">Country <span x-show="sortBy === 'country'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                            <th x-show="showNotes" x-on:click="sort('notes')" class="cursor-pointer font-medium">Notes <span x-show="sortBy === 'notes'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                            <th class="cursor-pointer font-medium">Actions</th>
                        </tr>

                        <template x-if="displayedEmployee.length > 0">
                            <template x-for="(employee, index) in displayedEmployee" :key="index">
                                <tr>
                                    <td>
                                        <a href="#!" x-text="employee.customerID" class="link link-primary"></a>
                                    </td>
                                    <td>
                                        <div class="flex items-center gap-2">
                                            <template x-if="employee.avatar && employee.avatar.includes('assets')">
                                                <img :src="employee.avatar" :alt="employee.name" class="size-8 rounded-full">
                                            </template>
                                            <template x-if="!employee.avatar || !employee.avatar.includes('assets')">
                                                <div class="size-8 flex items-center justify-center rounded-full bg-gray-100 text-gray-500 dark:bg-dark-850 dark:text-dark-500 shrink-0">
                                                    <span x-text="employee.name ? employee.name.charAt(0) : 'U'"></span>
                                                </div>
                                            </template>
                                            <div class="grow">
                                                <h6 class="mb-0 text-sm">
                                                    <a href="#!" data-drawer-target="customerDetailsModal" class="link link-primary text-current" x-text="employee.name"></a>
                                                </h6>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <p class="text-gray-500 dark:text-dark-500" x-text="employee.email"></p>
                                    </td>
                                    <td>
                                        <p class="text-gray-500 dark:text-dark-500" x-text="employee.phone"></p>
                                    </td>
                                    <td>
                                        <p class="text-gray-500 dark:text-dark-500" x-text="employee.totalOrders"></p>
                                    </td>
                                    <td x-show="showLastOrder">
                                        <p class="text-gray-500 dark:text-dark-500" x-text="employee.lastOrder"></p>
                                    </td>
                                    <td>
                                        <p class="text-gray-500 dark:text-dark-500" x-text="employee.outstanding"></p>
                                    </td>
                                    <td>
                                        <span x-text="employee.status" :class="{
                                            'badge badge-green': employee.status === 'Active',
                                            'badge badge-red': employee.status === 'Inactive',
                                            'badge badge-orange': employee.status === 'Overdue',
                                            'badge badge-yellow': employee.status === 'Pending'
                                        }"></span>
                                    </td>
                                    <td x-show="showAddress">
                                        <p class="text-gray-500 dark:text-dark-500" x-text="employee.address"></p>
                                    </td>
                                    <td x-show="showCity">
                                        <p class="text-gray-500 dark:text-dark-500" x-text="employee.city"></p>
                                    </td>
                                    <td x-show="showCountry">
                                        <p class="text-gray-500 dark:text-dark-500" x-text="employee.country"></p>
                                    </td>
                                    <td x-show="showNotes">
                                        <p class="text-gray-500 dark:text-dark-500" x-text="employee.notes"></p>
                                    </td>
                                    <td>
                                        <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown">
                                            <button x-ref="button" x-on:click="toggle()" title="dropdown-button" :aria-expanded="open.toString()" type="button" class="btn btn-sub-sky btn-icon size-8">
                                                <i data-lucide="more-horizontal" class="size-4"></i>
                                            </button>
                                            <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden" dropdown-position="right">
                                                <ul>
                                                    <li><a href="#!" class="dropdown-item" data-drawer-target="customerDetailsModal">Overview</a></li>
                                                    <li><a href="#!" class="dropdown-item" data-modal-target="deleteModal">Delete</a></li>
                                                </ul>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            </template>
                        </template>

                        <template x-if="displayedEmployee.length == 0">
                            <tr>
                                <td :colspan="visibleColumnsCount" class="p-8 text-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto w-12 h-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                    </svg>
                                    <p class="mt-2 text-center text-gray-500">No matching records found</p>
                                </td>
                            </tr>
                        </template>
                    </tbody>
                </table>
            </div>

            <div class="grid items-center grid-cols-12 gap-space mt-space" x-show="displayedEmployee.length > 0">
                <div class="col-span-12 text-center lg:col-span-6 lg:ltr:text-left lg:rtl:text-right">
                    <p class="text-gray-500 dark:text-dark-500">Showing <b x-text="showingStart"></b> - <b x-text="showingEnd"></b> of <b x-text="filterEmployee.length"></b> Results</p>
                </div>
                <div class="col-span-12 lg:col-span-6">
                    <div class="flex justify-center gap-2 lg:justify-end pagination pagination-primary">
                        <button @click="prevPage()" :disabled="currentPage === 1" class="pagination-pre">
                            <i data-lucide="chevron-left" class="mr-1 ltr:inline-block rtl:hidden size-4"></i>
                            <i data-lucide="chevron-right" class="ml-1 rtl:inline-block ltr:hidden size-4"></i>
                            Prev
                        </button>
                        <template x-for="page in totalPages" :key="page">
                            <button @click="gotoPage(page)" :class="{ 'active': currentPage === page }" class="pagination-item">
                                <span x-text="page"></span>
                            </button>
                        </template>
                        <button @click="nextPage()" :disabled="currentPage === totalPages" class="pagination-next">
                            Next
                            <i data-lucide="chevron-right" class="ml-1 ltr:inline-block rtl:hidden size-4"></i>
                            <i data-lucide="chevron-left" class="mr-1 rtl:inline-block ltr:hidden size-4"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="customerDetailsModal" drawer-end class="drawer show">
    <div class="bg-primary-500 text-white text-center py-8 px-6">
        <button data-drawer-close="customerDetailsModal" class="absolute top-4 right-4"><i data-lucide="x" class="link text-white/50 hover:text-white size-5"></i></button>
        <div class="w-20 h-20 rounded-full mx-auto mb-4 overflow-hidden border-4 border-white/20">
            <img src="assets/images/avatar/user-18.png" alt="Alex Smith" class="size-full object-cover">
        </div>
        <h4 class="mb-1">William Scott</h4>
        <p class="mb-4 text-white/75">ID: SRBCU31866</p>
        <button type="button" class="btn bg-white text-primary-500 w-full">
            Send offer
        </button>
        <p class="text-white/75 text-sm mt-4">Last activity: Today 08:20 PM</p>
    </div>
    <div class="drawer-content">
        <div class="mb-6">
            <h3 class="text-sm font-medium mb-3">
                Contact Info
            </h3>
            <div class="flex flex-col gap-3">
                <div class="flex items-center text-sm gap-3">
                    <i data-lucide="mail" class="size-4 text-gray-500"></i>
                    <span class="text-primary-500"><EMAIL></span>
                </div>
                <div class="flex items-center text-sm gap-3">
                    <i data-lucide="phone" class="size-4 text-gray-500"></i>
                    <span>******-567-8914</span>
                </div>
                <div class="flex items-center text-sm gap-3">
                    <i data-lucide="map-pin" class="size-4 text-gray-500"></i>
                    <span>5050 Hill St, Dublin, Ireland</span>
                </div>
            </div>
        </div>
        <div>
            <h3 class="text-sm font-medium mb-3">Other</h3>
            <div class="space-y-2 text-sm">
                <div class="flex justify-between">
                    <span class="text-gray-500 dark:text-dark-500">Customer ID</span>
                    <span>SRBCU31866</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-500 dark:text-dark-500">Total Orders</span>
                    <span>6</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-500 dark:text-dark-500">Outstanding</span>
                    <span>$120</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-500 dark:text-dark-500">Last Order</span>
                    <span>Jul 12, 2024</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-500 dark:text-dark-500">Status</span>
                    <span class="badge badge-green">Active</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!--delete modal-->
<div id="deleteModal" class="!hidden modal show">
    <div class="modal-wrap modal-xs modal-center">
        <div class="text-center modal-content p-7">
            <div class="flex items-center justify-center mx-auto mb-4 text-red-500 rounded-full bg-red-500/10 size-14 backdrop-blur-xl">
                <i data-lucide="trash-2" class="size-6"></i>
            </div>
            <h5 class="mb-4">Are you sure you want to delete this Customer?</h5>
            <div class="flex items-center justify-center gap-2">
                <button class="btn btn-red" data-modal-close="deleteModal">Delete</button>
                <button data-modal-close="deleteModal" class="btn link link-primary">Cancel</button>
            </div>
        </div>
    </div>
</div>

</div>
{{> partials/footer }}
</div>

{{> partials/vendor-scripts }}

<script type="module" src="assets/js/apps/pos/customer-database.init.js"></script>

<script type="module" src="assets/js/main.js"></script>

</body>

</html>