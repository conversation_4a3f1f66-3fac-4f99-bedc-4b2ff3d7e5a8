{{> partials/main }}

<head>

    {{> partials/title-meta title="List View" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}
{{> partials/page-heading title="List View" sub-title="Students" }}

<div class="grid grid-cols-12 gap-x-space" x-data="studentsTable()">
    <div class="col-span-12 card">
        <div class="card-header">
            <div class="flex flex-wrap justify-between gap-5">
                <div>
                    <div class="relative group/form grow">
                        <input type="text" class="ltr:pl-9 rtl:pr-9 form-input ltr:group-[&.right]/form:pr-9 rtl:group-[&.right]/form:pl-9 ltr:group-[&.right]/form:pl-4 rtl:group-[&.right]/form:pr-4" placeholder="Search student, class etc. ..." x-model="searchStudent" @input="filterStudents" @input="filterStudents">
                        <button class="absolute inset-y-0 flex items-center ltr:left-3 rtl:right-3 ltr:group-[&.right]/form:right-3 rtl:group-[&.right]/form:left-3 ltr:group-[&.right]/form:left-auto rtl:group-[&.right]/form:right-auto focus:outline-hidden">
                            <i data-lucide="search" class="text-gray-500 size-4 fill-gray-100 dark:text-dark-500 dark:fill-dark-850"></i>
                        </button>
                    </div>
                </div>
                <div>
                    <div class="items-center gap-5 sm:flex">
                        <div id="sortingByClass" class="w-full" placeholder="Sorting by class" @change="filterStudents"></div>
                        <a href="apps-school-students-admission.html" class="mt-5 btn btn-primary shrink-0 sm:mt-0"><i data-lucide="circle-plus" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> Add Student</a>
                    </div>
                </div>
            </div>
        </div>
        <div class="pt-0 card-body">
            <div>
                <div class="overflow-x-auto table-box whitespace-nowrap">
                    <table class="table flush">
                        <tbody>
                            <tr class="text-gray-500 bg-gray-100 dark:bg-dark-850 dark:text-dark-500">
                                <th x-on:click="sort('studentID')" class="!font-medium cursor-pointer">ID <span x-show="sortBy === 'studentID'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th x-on:click="sort('studentName')" class="!font-medium cursor-pointer">Student Name <span x-show="sortBy === 'studentName'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th x-on:click="sort('gender')" class="!font-medium cursor-pointer">Gender <span x-show="sortBy === 'gender'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th x-on:click="sort('rollNo')" class="!font-medium cursor-pointer">Roll No <span x-show="sortBy === 'rollNo'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th x-on:click="sort('class')" class="!font-medium cursor-pointer">Class <span x-show="sortBy === 'class'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th x-on:click="sort('email')" class="!font-medium cursor-pointer">Email <span x-show="sortBy === 'email'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th x-on:click="sort('phone')" class="!font-medium cursor-pointer">Phone <span x-show="sortBy === 'phone'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th x-on:click="sort('birthDate')" class="!font-medium cursor-pointer">Birth Of Date <span x-show="sortBy === 'birthDate'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th x-on:click="sort('date')" class="!font-medium cursor-pointer">Joining Date <span x-show="sortBy === 'date'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th class="!font-medium">Action</th>
                            </tr>
                            <template x-if="displayedStudents.length > 0">
                            <template x-for="(student, index) in displayedStudents" :key="index">
                                <tr class="*:px-3 *:py-2.5">
                                    <td x-text="student.studentID"></td>
                                    <td>
                                        <div class="flex items-center gap-3">
                                            <div class="relative text-gray-500 bg-gray-100 rounded-full dark:text-dark-500 dark:bg-dark-850 size-8">
                                                <img x-show="student.image" :src="student.image" alt="" class="rounded-full" @error="removeImage($event)">
                                                <span x-show="!student.image" x-text="student.avatarText" class="absolute inset-0 flex items-center justify-center text-xs font-semibold text-gray-500 bg-gray-100 rounded-full dark:text-dark-500 dark:bg-dark-850"></span>
                                            </div>
                                            <div>
                                                <h6><a href="apps-school-students-overview.html" x-text="student.studentName"></a></h6>
                                            </div>
                                        </div>
                                    </td>
                                    <td x-text="student.gender"></td>
                                    <td x-text="student.rollNo"></td>
                                    <td x-text="student.class"></td>
                                    <td x-text="student.email"></td>
                                    <td x-text="student.phone"></td>
                                    <td x-text="student.birthDate"></td>
                                    <td x-text="student.formattedDate"></td>
                                    <td>
                                        <div class="flex items-center gap-2">
                                            <button class="btn btn-sub-primary btn-icon !size-8" data-modal-target="addStaffModal"><i class="ri-pencil-line"></i></button>
                                            <button class="btn btn-sub-red btn-icon !size-8" @click="deleteStudent = student.studentID" data-modal-target="deleteModal"><i class="ri-delete-bin-line"></i></button>
                                        </div>
                                    </td>
                                </tr>
                            </template>
                        </template>
                        </tr>
                        <tr>
                            <template x-if="displayedStudents.length == 0">
                                <td colspan="10" class="!p-8">
                                    <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" class="mx-auto size-12" viewBox="0 0 48 48">
                                        <linearGradient id="SVGID_1__h35ynqzIJzH4_gr1" x1="34.598" x2="15.982" y1="15.982" y2="34.598" gradientUnits="userSpaceOnUse">
                                            <stop offset="0" stop-color="#60e8fe"></stop>
                                            <stop offset=".033" stop-color="#6ae9fe"></stop>
                                            <stop offset=".197" stop-color="#97f0fe"></stop>
                                            <stop offset=".362" stop-color="#bdf5ff"></stop>
                                            <stop offset=".525" stop-color="#dafaff"></stop>
                                            <stop offset=".687" stop-color="#eefdff"></stop>
                                            <stop offset=".846" stop-color="#fbfeff"></stop>
                                            <stop offset="1" stop-color="#fff"></stop>
                                        </linearGradient>
                                        <path fill="url(#SVGID_1__h35ynqzIJzH4_gr1)" d="M40.036,33.826L31.68,25.6c0.847-1.739,1.335-3.684,1.335-5.748c0-7.27-5.894-13.164-13.164-13.164	S6.688,12.582,6.688,19.852c0,7.27,5.894,13.164,13.164,13.164c2.056,0,3.995-0.485,5.728-1.326l3.914,4.015l4.331,4.331	c1.715,1.715,4.496,1.715,6.211,0C41.751,38.321,41.751,35.541,40.036,33.826z"></path>
                                        <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M31.95,25.739l8.086,8.086c1.715,1.715,1.715,4.496,0,6.211l0,0c-1.715,1.715-4.496,1.715-6.211,0	l-4.331-4.331"></path>
                                        <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M7.525,24.511c-1.771-4.694-0.767-10.196,3.011-13.975c3.847-3.847,9.48-4.817,14.228-2.912"></path>
                                        <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M30.856,12.603c3.376,5.114,2.814,12.063-1.688,16.565c-4.858,4.858-12.565,5.129-17.741,0.814"></path>
                                    </svg>
                                    <p class="mt-2 text-center text-gray-500 dark:text-dark-500">No matching records found</p>
                                </td>
                            </template>
                        </tr>
                        </tbody>
                    </table>
                </div>
                <div class="grid grid-cols-12 mt-space gap-space items-center" x-show="displayedStudents.length > 0">
                    <div class="col-span-12 xl:col-span-6 justify-center xl:justify-start flex flex-wrap">
                        <p class="text-gray-500 dark:text-dark-500">Showing <b x-text="showingStart"></b> - <b x-text="showingEnd"></b> of <b x-text="filteredStudents.length"></b> Results</p>
                    </div>
                    <div class="col-span-12 xl:col-span-6">
                        <div class="flex justify-center xl:justify-end pagination pagination-primary">
                            <button @click="prevPage()" :disabled="currentPage === 1" class="pagination-pre">
                                <i data-lucide="chevron-left" class="mr-1 ltr:inline-block rtl:hidden size-4"></i>
                                <i data-lucide="chevron-right" class="ml-1 ltr:hidden rtl:inline-block size-4"></i>
                                 Prev
                            </button>
                            <template x-for="page in totalPages" :key="page">
                                <button @click="gotoPage(page)" :class="{ 'active': currentPage === page }" class="pagination-item">
                                    <span x-text="page"></span>
                                </button>
                            </template>
                            <button @click="nextPage()" :disabled="currentPage === totalPages" class="pagination-next">
                                Next
                                <i data-lucide="chevron-right" class="ml-1 ltr:inline-block rtl:hidden size-4"></i>
                                <i data-lucide="chevron-left" class="mr-1 ltr:hidden rtl:inline-block size-4"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div><!--end col-->

    <!--delete modal-->
    <div id="deleteModal" class="!hidden modal show">
        <div class="modal-wrap modal-xs modal-center">
            <div class="text-center modal-content p-7">
                <div class="flex items-center justify-center mx-auto mb-4 text-red-500 rounded-full bg-red-500/10 size-14 backdrop-blur-xl">
                    <i data-lucide="trash-2" class="size-6"></i>
                </div>
                <h5 class="mb-4">Are you sure you want to delete this parents ?</h5>
                <div class="flex items-center justify-center gap-2">
                    <button class="btn btn-red" @click="deletedStudent()" data-modal-close="deleteModal">Delete</button>
                    <button data-modal-close="deleteModal" class="btn link link-primary">Cancel</button>
                </div>
            </div>
        </div>
    </div><!--end-->
</div><!--end grid-->

</div>
{{> partials/footer }}
</div>



{{> partials/vendor-scripts }}

<script type="module" src="assets/js/apps/school/students/list-view.init.js"></script>

<script type="module" src="assets/js/main.js"></script>

</body>
</html>