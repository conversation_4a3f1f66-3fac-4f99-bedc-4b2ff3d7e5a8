{{> partials/main }}

<head>

    {{> partials/title-meta title="Question" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}
{{> partials/page-heading title="Question" sub-title="Exam" }}
<div x-data="questions()">
    <div class="grid grid-cols-12 gap-5 gap-x-space mb-space">
        <div class="col-span-12 md:col-span-6 xl:col-span-4">
            <div class="relative flex items-center">
                <input type="text" class="!border-r-0 !rounded-r-none form-input grow focus:!border-green-500/20"
                    placeholder="Search for ..." x-model="searchTerm" @input="filterQuestions">
                <button class="border-green-200 rounded-l-none btn btn-sub-green btn-icon shrink-0"><i
                        data-lucide="search" class="size-5"></i></button>
            </div>
        </div><!--end col-->
        <button class="btn btn-red btn-icon " x-show="selectedItems.length > 0" @click="deleteSelectedItems()">
            <i data-lucide="trash" class="inline-block size-4"></i>
        </button>
        <div class="col-span-12 sm:col-span-6 md:col-span-2 xl:col-start-9">
            <div id="sortBySelect" placeholder="Sort By" @change="filterQuestions"></div>
        </div>
        <div class="col-span-12 sm:col-span-6 md:col-span-4 xl:col-span-2">
            <button type="button" class="w-full btn btn-primary" @click="handleModal('showAddExamForm')" data-modal-target="addQuestionModal">Create New
                Question</button>
        </div>
    </div><!--end grid-->

    <div class="mb-5">
        <div class="overflow-x-auto">
            <table class="table !border-separate !border-spacing-y-2 whitespace-nowrap">
                <tr class="font-medium text-gray-500">
                    <td class="w-11">
                        <div class="input-check-group">
                            <label for="checkboxAll" class="hidden input-check-label"></label>
                            <input id="checkboxAll" class="input-check input-check-primary" type="checkbox" x-model="selectAll" x-on:click="toggleAll">
                        </div>
                    </td>
                    <td>Questions</td>
                    <td>Option</td>
                    <td>Item Type</td>
                    <td>Difficult</td>
                    <td>Status</td>
                    <td>Action</td>
                </tr>
                <template x-for="question in displayedQuestions" :key="question.id">
                    <tr x-data="{ showAll: false }">
                        <td
                            class="self-start align-top border-t ltr:last:border-r rtl:last:border-l ltr:first:border-l rtl:first:border-r ltr:last:rounded-r-md rtl:last:rounded-l-md ltr:first:rounded-l-md rtl:first:rounded-r-md">
                            <div class="input-check-group">
                                <label :for="`question${question.id}`" class="hidden input-check-label"></label>
                                <input :id="`question${question.id}`" class="input-check input-check-primary" type="checkbox" @click="toggleItem(question)" :checked="selectedItems.includes(question)">
                            </div>
                        </td>
                        <td class="border-t ltr:last:border-r rtl:last:border-l ltr:first:border-l rtl:first:border-r ltr:last:rounded-r-md rtl:last:rounded-l-md ltr:first:rounded-l-md rtl:first:rounded-r-md">
                            <h6><a href="#!" x-on:click.prevent="showAll = !showAll" x-text="question.question"></a>
                            </h6>
                            <template x-if="showAll" x-transition>
                                <div class="mt-3 space-y-2">
                                    <template x-for="(option, index) in question.options" :key="index">
                                        <div class="input-radio-group">
                                            <input :id="'qOption' + question.id + index" class="hidden input-radio peer"
                                                type="radio" :name="'optionQ' + question.id">
                                            <label :for="'qOption' + question.id + index"
                                                class="flex items-center justify-center border border-gray-200 rounded-md text-15 size-9 peer-checked:bg-primary-500 peer-checked:border-primary-500 peer-checked:text-white"
                                                x-text="String.fromCharCode(65 + index)"></label>
                                            <label :for="'qOption' + question.id + index"
                                                class="py-2.5 px-3 rounded-md border border-gray-200 input-radio-label grow"
                                                x-text="option"></label>
                                        </div>
                                    </template>
                                </div>
                            </template>
                        </td>
                        <td
                            class="align-top border-t ltr:last:border-r rtl:last:border-l ltr:first:border-l rtl:first:border-r ltr:last:rounded-r-md rtl:last:rounded-l-md ltr:first:rounded-l-md rtl:first:rounded-r-md">
                            <a href="#!" class="link link-primary" x-on:click.prevent="showAll = !showAll">
                                <span x-text="showAll ? 'Hide All' : 'Show All'"></span>
                            </a>
                        </td>
                        <td class="align-top border-t ltr:last:border-r rtl:last:border-l ltr:first:border-l rtl:first:border-r ltr:last:rounded-r-md rtl:last:rounded-l-md ltr:first:rounded-l-md rtl:first:rounded-r-md"
                            x-text="question.type"></td>
                        <td class="align-top border-t ltr:last:border-r rtl:last:border-l ltr:first:border-l rtl:first:border-r ltr:last:rounded-r-md rtl:last:rounded-l-md ltr:first:rounded-l-md rtl:first:rounded-r-md"
                            x-text="question.difficulty"></td>
                        <td
                            class="align-top border-t ltr:last:border-r rtl:last:border-l ltr:first:border-l rtl:first:border-r ltr:last:rounded-r-md rtl:last:rounded-l-md ltr:first:rounded-l-md rtl:first:rounded-r-md">
                            <span class="badge" :class="question.status == 'New' ?  'badge-green ':  'badge-gray'"
                                x-text="question.status"></span></td>
                        <td
                            class="align-top border-t ltr:last:border-r rtl:border-l ltr:first:border-l rtl:first:border-r ltr:last:rounded-r-md rtl:last:rounded-l-md ltr:first:rounded-l-md rtl:first:rounded-r-md">
                            <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()"
                                x-init="calculatePosition()" class="relative">
                                <button x-ref="button" x-on:click="toggle()" :aria-expanded="open.toString()"
                                    type="button" class="flex items-center text-gray-500">
                                    <i class="ri-more-2-fill"></i>
                                </button>
                                <div x-ref="dropdown" x-show="open" x-transition.origin.top.right
                                    x-on:click.outside="close()"
                                    class="fixed z-50 py-2 bg-white rounded-md shadow-lg w-36 dropdown-menu">
                                    <ul>
                                        <li>
                                            <a href="#!" data-modal-target="overviewQuestionModal"
                                            @click="setReviewQuestion(question)"
                                                class="flex items-center px-4 py-1.5 text-gray-500 hover:text-primary-500 text-14 transition duration-300 ease-linear">
                                                <i class="align-middle ltr:mr-2 rtl:ml-2 ri-eye-line"></i>
                                                <span>Overview</span>
                                            </a>
                                        </li>
                                        <li>
                                            <a href="#!" data-modal-target="addQuestionModal"
                                                @click="editExam(question.id)"
                                                class="flex items-center px-4 py-1.5 text-gray-500 hover:text-primary-500 text-14 transition duration-300 ease-linear">
                                                <i class="align-middle ltr:mr-2 rtl:ml-2 ri-pencil-line"></i>
                                                <span>Edit</span>
                                            </a>
                                        </li>
                                        <li>
                                            <a href="#!" data-modal-target="deleteModal"
                                            @click="setReviewQuestion(question)"
                                                class="flex items-center px-4 py-1.5 text-gray-500 hover:text-red-500 text-14 transition duration-300 ease-linear">
                                                <i class="align-middle ltr:mr-2 rtl:ml-2 ri-delete-bin-line"></i>
                                                <span>Delete</span>
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </td>
                    </tr>
                </template>
               
                    <template x-if="filteredQuestions.length == 0">
                        <td colspan="10" class="!p-8">
                            <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" class="mx-auto size-12" viewBox="0 0 48 48">
                                <linearGradient id="SVGID_1__h35ynqzIJzH4_gr1" x1="34.598" x2="15.982" y1="15.982" y2="34.598" gradientUnits="userSpaceOnUse">
                                    <stop offset="0" stop-color="#60e8fe"></stop>
                                    <stop offset=".033" stop-color="#6ae9fe"></stop>
                                    <stop offset=".197" stop-color="#97f0fe"></stop>
                                    <stop offset=".362" stop-color="#bdf5ff"></stop>
                                    <stop offset=".525" stop-color="#dafaff"></stop>
                                    <stop offset=".687" stop-color="#eefdff"></stop>
                                    <stop offset=".846" stop-color="#fbfeff"></stop>
                                    <stop offset="1" stop-color="#fff"></stop>
                                </linearGradient>
                                <path fill="url(#SVGID_1__h35ynqzIJzH4_gr1)" d="M40.036,33.826L31.68,25.6c0.847-1.739,1.335-3.684,1.335-5.748c0-7.27-5.894-13.164-13.164-13.164	S6.688,12.582,6.688,19.852c0,7.27,5.894,13.164,13.164,13.164c2.056,0,3.995-0.485,5.728-1.326l3.914,4.015l4.331,4.331	c1.715,1.715,4.496,1.715,6.211,0C41.751,38.321,41.751,35.541,40.036,33.826z"></path>
                                <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M31.95,25.739l8.086,8.086c1.715,1.715,1.715,4.496,0,6.211l0,0c-1.715,1.715-4.496,1.715-6.211,0	l-4.331-4.331"></path>
                                <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M7.525,24.511c-1.771-4.694-0.767-10.196,3.011-13.975c3.847-3.847,9.48-4.817,14.228-2.912"></path>
                                <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M30.856,12.603c3.376,5.114,2.814,12.063-1.688,16.565c-4.858,4.858-12.565,5.129-17.741,0.814"></path>
                            </svg>
                            <p class="mt-2 text-center text-gray-500 dark:text-dark-500">No matching records found</p>
                        </td>
                    </template>
            
            </table>
        </div>
    </div>

    <!--Overview question-->
    <div id="overviewQuestionModal" class="!hidden modal show">
        <div class="modal-wrap modal-center">
            <div class="modal-header">
                <h6 class="modal-title">Review Question</h6>
                <button data-modal-close="overviewQuestionModal" class="link link-red"><i data-lucide="x"
                        class="size-5"></i></button>
            </div>
            <div class="modal-content">
                <h6 x-text="`Q${reviewQuestion.id}. ${reviewQuestion.question}`">Q1. Which organization defines the Web
                    Standards?</h6>
                <div class="mt-3 space-y-2">
                    <template x-for="(option, index) in reviewQuestion.options" :key="index">
                        <div class="input-radio-group">
                            <input :id="'qOption' + reviewQuestion.id + index" class="hidden input-radio peer" type="radio"
                                :name="'optionQ' + reviewQuestion.id">
                            <label :for="'qOption' + reviewQuestion.id + index"
                                class="flex items-center justify-center border border-gray-200 rounded-md text-15 size-9 peer-checked:bg-primary-500 peer-checked:border-primary-500 peer-checked:text-white"
                                x-text="String.fromCharCode(65 + index)"></label>
                            <label :for="'qOption' + reviewQuestion.id + index"
                                class="py-1.5 px-3 rounded-md border border-gray-200 input-radio-label grow"
                                x-text="option"></label>
                        </div>
                    </template>
                </div>
                <div class="flex justify-end gap-2 mt-4">
                    <button type="button" class="btn btn-active-red" data-modal-close="overviewQuestionModal">
                        <i data-lucide="x" class="inline-block size-4"></i>
                        <span class="align-baseline">Close</span>
                    </button>
                    <button type="button" class="btn btn-primary">Send Ans</button>
                </div>
            </div>
        </div>
    </div>

    <!--Create question-->
    <div id="addQuestionModal" class="!hidden modal show" :class="{ 'show d-block': showAddExamForm || showEditExamForm }" x-show="showAddExamForm || showEditExamForm">
        <div class="modal-wrap modal-center">
            <div class="modal-header">
                <h6 class="modal-title" x-text="showAddExamForm ? 'Add Question' : 'Edit Question'">Add Question</h6>
                <button data-modal-close="addQuestionModal" class="link link-red"><i data-lucide="x"
                        class="size-5"></i></button>
            </div>
            <div class="modal-content">
                <form action="#!">
                    <div class="grid grid-cols-12 gap-4">
                        <div class="col-span-12">
                            <label for="questionInput" class="form-label">Question</label>
                            <input type="text" id="questionInput" class="form-input" placeholder="Enter question" x-model="form.question" @input="validateField('question' , form.question , 'Question is required')">
                            <span class="text-red-500" x-text="errors.question" x-show="errors.question"></span>
                        </div>
                        <div class="col-span-6" >
                            <label for="option1Input" class="form-label">Option 1</label>
                            <input type="text" id="option1Input" class="form-input" placeholder="Option one" x-model="form.options[0]" :disabled="form.checkbox || document.querySelector('#itemTypeSelect').value == 'Q & A'" @input="validateField('option1' , form.options[0] , 'Option 1 is required')">
                            <span class="text-red-500" x-text="errors.option1" x-show="errors.option1"></span>
                        </div>
                        <div class="col-span-6">
                            <label for="option2Input" class="form-label">Option 2</label>
                            <input type="text" id="option2Input" class="form-input" placeholder="Option two" x-model="form.options[1]" :disabled="form.checkbox || document.querySelector('#itemTypeSelect').value == 'Q & A'" @input="validateField('option2' , form.options[1] , 'Option 2 is required')">
                            <span class="text-red-500" x-text="errors.option2" x-show="errors.option2"></span>
                        </div>
                        <div class="col-span-6">
                            <label for="option3Input" class="form-label">Option 3</label>
                            <input type="text" id="option3Input" class="form-input" placeholder="Option three"  x-model="form.options[2]" :disabled="form.checkbox || document.querySelector('#itemTypeSelect').value == 'Q & A'" @input="validateField('option3' , form.options[2] , 'Option 3 is required')">
                            <span class="text-red-500" x-text="errors.option3" x-show="errors.option3"></span>
                        </div>
                        <div class="col-span-6">
                            <label for="option4Input" class="form-label">Option 4</label>
                            <input type="text" id="option4Input" class="form-input" placeholder="Option four" x-model="form.options[3]" :disabled="form.checkbox || document.querySelector('#itemTypeSelect').value == 'Q & A'" @input="validateField('option4' , form.options[3] , 'Option 4 is required')">
                            <span class="text-red-500" x-text="errors.option4" x-show="errors.option4"></span>
                        </div>
                        <div class="col-span-12">
                            <div class="input-check-group">
                                <input id="optionOffInput" class="input-check input-check-primary" type="checkbox" x-model="form.checkbox" :checked="form.checkbox"/>
                                <label for="optionOffInput" class="input-check-label">Not a MCQ</label>
                            </div>
                        </div>
                        <div class="col-span-6">
                            <label for="itemTypeSelect" class="form-label">Item Type</label>
                            <div id="itemTypeSelect" placeholder="Select Item Type" x-model="form.type" @change="validateField('type' , document.querySelector('#itemTypeSelect') , 'Item Type is required')"></div>
                            <span x-show="errors.type" class="text-red-500" x-text="errors.type"></span>
                        </div>
                        <div class="col-span-6">
                            <label for="difficultLevelSelect" class="form-label">Difficult Level</label>
                            <div id="difficultLevelSelect" placeholder="Select Type" x-model="form.difficulty" @change="validateField('difficulty' , document.querySelector('#difficultLevelSelect') , 'Difficult Level is required')"></div>
                            <span x-show="errors.difficulty" class="text-red-500" x-text="errors.difficulty"></span>
                        </div>
                        <div class="col-span-12">
                            <label for="statusSelect" class="form-label">Status</label>
                            <div id="statusSelect" placeholder="Select Status" x-model="form.status" @change="validateField('status' , document.querySelector('#statusSelect') , 'Status is required')"></div>
                            <span x-show="errors.status" class="text-red-500" x-text="errors.status"></span>
                        </div>
                    </div>
                    <div class="flex items-center justify-end gap-2 mt-5">
                        <button type="button" class="btn btn-active-red" data-modal-close="addQuestionModal">
                            <i data-lucide="x" class="inline-block size-4"></i>
                            <span class="align-baseline">Close</span>
                        </button>
                        <button type="button" class="btn btn-primary" x-text="showAddExamForm ? 'Add Question' : 'Edit Question'" @click="submitForm()">Add Question</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!--delete modal-->
    <div id="deleteModal" class="!hidden modal show">
        <div class="modal-wrap modal-xs modal-center">
            <div class="text-center modal-content p-7">
                <div
                    class="flex items-center justify-center mx-auto mb-4 text-red-500 rounded-full bg-red-500/10 size-14 backdrop-blur-xl">
                    <i data-lucide="trash-2" class="size-6"></i>
                </div>
                <h5 class="mb-4">Are you sure you want to delete this question ?</h5>
                <div class="flex items-center justify-center gap-2">
                    <button class="btn btn-red" @click="DeleteQuestion()" data-modal-close="deleteModal">Delete</button>
                    <button data-modal-close="deleteModal" class="btn link link-primary">Cancel</button>
                </div>
            </div>
        </div>
    </div><!--end-->
</div>
</div>
{{> partials/footer }}
</div>

{{> partials/vendor-scripts }}

<script type="module" src="assets/js/apps/school/exam/question.init.js"></script>

<script type="module" src="assets/js/main.js"></script>

</body>
</html>