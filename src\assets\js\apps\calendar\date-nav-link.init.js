/*
Template Name: <PERSON><PERSON>x - Admin & Dashboard Template
Author: SRBThemes
Version: 1.0.0
File: date nav link calendar init Js File
*/

import { Calendar } from '@fullcalendar/core'
import interactionPlugin from '@fullcalendar/interaction'
import dayGridPlugin from '@fullcalendar/daygrid'
import timeGridPlugin from '@fullcalendar/timegrid'

const calendarEl = document.getElementById('dateNavLinkCalendar')
const calendar = new Calendar(calendarEl, {
    buttonText: {
        today: 'Today',
        year: 'Year',
        month: 'Month',
        week: 'Week',
        day: 'Day',
        list: 'List'
    },
    plugins: [
        interactionPlugin,
        dayGridPlugin,
        timeGridPlugin
    ],
    editable: true,
    navLinks: true,
    headerToolbar: {
        left: 'prev,next today',
        center: 'title',
        right: 'dayGridMonth,timeGridWeek,timeGridDay'
    },
    events: 'https://fullcalendar.io/api/demo-feeds/events.json?single-day&for-resource-timeline'
})

calendar.render()