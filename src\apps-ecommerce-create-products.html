{{> partials/main }}

<head>

    {{> partials/title-meta title="Product Create" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}
{{> partials/page-heading title="Product Create" sub-title="Ecommerce" }}

<div class="flex flex-wrap items-center gap-5 mb-5">
    <div class="grow">
        <h6 class="mb-1 card-title">Add New Products</h6>
        <p class="text-gray-500 dark:text-dark-500">Please fill the below form to create a new product.</p>
    </div>
    <div class="flex gap-2 shrink-0">
        <button class="btn btn-sub-gray"><i data-lucide="trash-2" class="inline-block ltr:mr-1 rtl:ml-1 align-center size-4"></i> <span class="align-middle">Delete</span></button>
        <button class="btn btn-primary"><i data-lucide="plus" class="inline-block ltr:mr-1 rtl:ml-1 align-center size-4"></i> <span class="align-middle">Add Products</span></button>
    </div>
</div>

<div class="grid grid-cols-12 gap-5">
    <div class="col-span-12 xl:col-span-8">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title">Products Description</h6>
            </div>
            <div class="card-body">
                <form action="#!">
                    <div class="grid grid-cols-12 gap-5">
                        <div class="col-span-12">
                            <label for="productNameInput" class="form-label">Product Name</label>
                            <input type="text" id="productNameInput" class="form-input" placeholder="Enter product name">
                        </div>
                        <div class="col-span-12">
                            <label for="descriptionTextarea" class="form-label">Description</label>
                            <textarea name="descriptionTextarea" id="descriptionTextarea" rows="3" class="h-auto form-input" placeholder="Enter your description"></textarea>
                        </div>
                        <div class="col-span-4">
                            <label for="categorySelect" class="form-label">Category</label>
                            <div id="categorySelect"></div>
                        </div>
                        <div class="col-span-4">
                            <label for="brandTypeSelect" class="form-label">Brand Type</label>
                            <div id="brandTypeSelect"></div>
                        </div>
                    </div>
                </form>
            </div>
        </div><!--end card-->
        <div class="card">
            <div class="card-header">
                <h6 class="card-title">Products Images</h6>
            </div>
            <div class="card-body">
                <div class="grid grid-cols-12 gap-5">
                    <div class="col-span-12 md:col-span-7 md:row-span-2">
                        <div x-data="previewImage()" class="h-full">
                            <label for="logo1" class="flex items-center justify-center h-full p-5 text-center border border-gray-200 border-dashed cursor-pointer dark:border-dark-800">
                                <img x-show="imageUrl" :src="imageUrl" class="mx-auto h-60">
                                <div x-show="!imageUrl" class="text-gray-500 dark:text-dark-500">
                                    <i data-lucide="image-plus" class="mx-auto"></i>
                                    <div class="mt-3">Product Image 1</div>
                                </div>
                            </label>
                            <div class="hidden mt-4">
                                <input type="file" name="logo1" id="logo1" @change="fileChosen" class="block w-full text-sm file:rounded-md focus:outline-0 text-slate-500 dark:text-dark-500 file:mr-4 file:py-2 file:px-4 file:border-0 file:text-sm file:font-semibold file:bg-violet-50 file:text-violet-700 hover:file:bg-violet-100 " />
                            </div>
                        </div>
                    </div><!--end col-->
                    <div class="col-span-12 md:col-span-5">
                        <div x-data="previewImage()">
                            <label for="logo2" class="flex items-center justify-center h-56 p-5 text-center border border-gray-200 border-dashed cursor-pointer dark:border-dark-800">
                                <img x-show="imageUrl" :src="imageUrl" class="mx-auto h-44">
                                <div x-show="!imageUrl" class="text-gray-500 dark:text-dark-500">
                                    <i data-lucide="image-plus" class="mx-auto"></i>
                                    <div class="mt-3">Product Image 2</div>
                                </div>
                            </label>
                            <div class="hidden mt-4">
                                <input type="file" name="logo2" id="logo2" @change="fileChosen" class="block w-full text-sm file:rounded-md focus:outline-0 text-slate-500 dark:text-dark-500 file:mr-4 file:py-2 file:px-4 file:border-0 file:text-sm file:font-semibold file:bg-violet-50 file:text-violet-700 hover:file:bg-violet-100 " />
                            </div>
                        </div>
                    </div>
                    <div class="col-span-12 md:col-span-5">
                        <div x-data="previewImage()">
                            <label for="logo3" class="flex items-center justify-center h-56 p-5 text-center border border-gray-200 border-dashed cursor-pointer dark:border-dark-800">
                                <img x-show="imageUrl" :src="imageUrl" class="mx-auto h-44">
                                <div x-show="!imageUrl" class="text-gray-500 dark:text-dark-500">
                                    <i data-lucide="image-plus" class="mx-auto"></i>
                                    <div class="mt-3">Product Image 3</div>
                                </div>
                            </label>
                            <div class="hidden mt-4">
                                <input type="file" name="logo3" id="logo3" @change="fileChosen" class="block w-full text-sm file:rounded-md focus:outline-0 text-slate-500 dark:text-dark-500 file:mr-4 file:py-2 file:px-4 file:border-0 file:text-sm file:font-semibold file:bg-violet-50 file:text-violet-700 hover:file:bg-violet-100 " />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div><!--end card-->
        <div class="card">
            <div class="card-header">
                <h6 class="card-title">Advance Description</h6>
            </div>
            <div class="card-body">
                <div class="grid grid-cols-12 gap-5">
                    <div class="col-span-12 md:col-span-4">
                        <h6 class="form-label">Select Gender</h6>
                        <div class="flex items-center gap-3">
                            <div class="input-radio-group">
                                <input id="maleRadio" name="selectGender" class="input-radio input-radio-primary" type="radio" />
                                <label for="maleRadio" class="input-radio-label">Male</label>
                            </div>
                            <div class="input-radio-group">
                                <input id="femaleRadio" name="selectGender" class="input-radio input-radio-primary" type="radio" />
                                <label for="femaleRadio" class="input-radio-label">Female</label>
                            </div>
                            <div class="input-radio-group">
                                <input id="unisexRadio" name="selectGender" class="input-radio input-radio-primary" type="radio" />
                                <label for="unisexRadio" class="input-radio-label">Unisex</label>
                            </div>
                        </div>
                    </div>
                    <div class="col-span-12 md:col-span-4">
                        <label for="stockInput" class="form-label">Stock</label>
                        <div x-data="{ count: 0 }">
                            <div class="flex input-spin-group input-spin-primary">
                                <button @click="if(count > 0) count--" class="input-spin-minus"><i class="size-4" data-lucide="minus"></i></button>
                                <input type="text" x-model="count" class="input-spin form-input" readonly id="stockInput">
                                <button @click="count++" class="input-spin-plus"><i class="size-4" data-lucide="plus"></i></button>
                            </div>
                        </div>
                    </div>
                    <div class="col-span-12 md:col-span-4">
                        <label for="quantityInput" class="form-label">Quantity</label>
                        <div x-data="{ count: 0 }">
                            <div class="flex input-spin-group input-spin-primary">
                                <button @click="if(count > 0) count--" class="input-spin-minus"><i class="size-4" data-lucide="minus"></i></button>
                                <input type="text" x-model="count" class="input-spin form-input" readonly id="quantityInput">
                                <button @click="count++" class="input-spin-plus"><i class="size-4" data-lucide="plus"></i></button>
                            </div>
                        </div>
                    </div>
                    <div class="col-span-12 md:col-span-6">
                        <label for="sizeSelect" class="form-label">Size</label>
                        <div id="sizeSelect"></div>
                    </div>
                    <div class="col-span-12 md:col-span-6">
                        <label for="colorsSelect" class="form-label">Colors</label>
                        <div id="colorsSelect"></div>
                    </div>
                </div>
            </div>
        </div><!--end card-->
        <div class="card">
            <div class="card-header">
                <h6 class="card-title">Pricing & Sale</h6>
            </div>
            <div class="card-body">
                <div class="grid grid-cols-12 gap-5" x-data="priceCalculator()">
                    <div class="col-span-12 md:col-span-3">
                        <label for="priceInput" class="form-label">Price</label>
                        <input type="text" id="priceInput" class="form-input" placeholder="$00.00" x-model="price" @input="validateNumber">
                    </div>
                    <div class="col-span-12 md:col-span-3">
                        <label for="discountInput" class="form-label">Discount</label>
                        <input type="text" id="discountInput" class="form-input" placeholder="0%" x-model="discount" @input="validateNumber">
                    </div>
                    <div class="col-span-12 md:col-span-3">
                        <label for="sellingPrice" class="form-label">Selling Price</label>
                        <input type="text" id="sellingPrice" class="form-input" placeholder="$00.00" x-model="sellingPrice" disabled>
                    </div>
                </div>
            </div>
        </div><!--end card-->

        <div class="card">
            <div class="card-header">
                <h6 class="card-title">Payment Method</h6>
            </div>
            <div class="card-body">
                <div class="grid grid-cols-12 gap-5" x-data="priceCalculator()">
                    <div class="col-span-12 md:col-span-4">
                        <label for="cashOnDelivery" class="relative block mb-0 cursor-pointer card">
                            <span class="flex items-center gap-3 card-body">
                                <img src="assets/images/others/money.png" alt="" class="size-8 shrink-0">
                                <span class="block text-base font-semibold grow">Cash on Delivery</span>
                                <input id="cashOnDelivery" class="input-check input-check-primary shrink-0" type="checkbox" />
                            </span>
                        </label>
                    </div>
                    <div class="col-span-12 md:col-span-4">
                        <label for="masterVisaCard" class="relative block mb-0 cursor-pointer card">
                            <span class="flex items-center gap-3 card-body">
                                <img src="assets/images/payment/mastercard.png" alt="" class="h-8 shrink-0">
                                <span class="block text-base font-semibold grow">Visa & Master Card</span>
                                <input id="masterVisaCard" class="input-check input-check-primary shrink-0" type="checkbox" />
                            </span>
                        </label>
                    </div>
                    <div class="col-span-12 md:col-span-4">
                        <label for="bankCard" class="relative block mb-0 cursor-pointer card">
                            <span class="flex items-center gap-3 card-body">
                                <img src="assets/images/others/bank.png" alt="" class="h-8 shrink-0">
                                <span class="block text-base font-semibold grow">Bank Transfer</span>
                                <input id="bankCard" class="input-check input-check-primary shrink-0" type="checkbox" />
                            </span>
                        </label>
                    </div>
                </div>
            </div>
        </div><!--end card-->
    </div><!--end col-->
    <div class="col-span-12 xl:col-span-4">
        <div class="sticky top-24 card">
            <div class="card-header">
                <h6 class="card-title"><i data-lucide="eye" class="inline-block mr-1 size-4"></i> <span class="align-middle">Product Card Preview</span></h6>
            </div>
            <div class="bg-gray-100 card-body dark:bg-dark-850">
                <div class="mb-0 card">
                    <div class="card-body">
                        <div class="relative p-5 bg-gray-100 dark:bg-dark-850">
                            <a href="#!" class="absolute z-10 flex items-center justify-center bg-white rounded-full link link-red size-10 shrink-0 top-5 dark:bg-dark-850 ltr:right-5 rtl:left-5"><i class="text-lg ri-heart-line"></i></a>
                            <div class="swiper productSlider" dir="ltr">
                                <div class="swiper-wrapper">
                                    <div class="swiper-slide">
                                        <img src="assets/images/products/img-06.png" alt="" class="w-3/4 mx-auto">
                                    </div>
                                    <div class="swiper-slide">
                                        <img src="assets/images/products/img-04.png" alt="" class="w-3/4 mx-auto">
                                    </div>
                                    <div class="swiper-slide">
                                        <img src="assets/images/products/img-05.png" alt="" class="w-3/4 mx-auto">
                                    </div>
                                </div>
                                <div class="swiper-pagination"></div>
                            </div>
                        </div>
                        <div class="mt-5">
                            <h5 class="mb-2">$36.87</h5>
                            <h6 class="mb-1"><a href="#!" class="text-current link link-primary">Bra Lace Crop top</a></h6>
                            <p class="text-gray-500 dark:text-dark-500">Fashion</p>

                            <div class="flex flex-wrap items-center gap-2">
                                <div class="flex items-center gap-2 mt-3 grow" x-data="{ activeLink: 'primary' }">
                                    <a href="#!" class="flex items-center justify-center text-white rounded-sm size-5 bg-primary-500 group/item" x-bind:class="{ 'active': activeLink === 'primary' }" @click.prevent="activeLink = 'primary'"><i data-lucide="check" class="size-4 hidden group-[&.active]/item:block"></i></a>
                                    <a href="#!" class="flex items-center justify-center text-white bg-pink-500 rounded-sm size-5 group/item" x-bind:class="{ 'active': activeLink === 'pink' }" @click.prevent="activeLink = 'pink'"><i data-lucide="check" class="size-4 hidden group-[&.active]/item:block"></i></a>
                                    <a href="#!" class="flex items-center justify-center text-white bg-green-500 rounded-sm size-5 group/item" x-bind:class="{ 'active': activeLink === 'green' }" @click.prevent="activeLink = 'green'"><i data-lucide="check" class="size-4 hidden group-[&.active]/item:block"></i></a>
                                    <a href="#!" class="flex items-center justify-center text-white bg-red-500 rounded-sm size-5 group/item" x-bind:class="{ 'active': activeLink === 'red' }" @click.prevent="activeLink = 'red'"><i data-lucide="check" class="size-4 hidden group-[&.active]/item:block"></i></a>
                                </div>

                                <div class="flex items-center gap-2 mt-3 font-medium shrink-0" x-data="{ activeLink: 'primary' }">
                                    <a href="#!" class="text-gray-500 dark:text-dark-500 [&.active]:text-green-500" x-bind:class="{ 'active': activeLink === 'primary' }" @click.prevent="activeLink = 'primary'">S</a>
                                    <a href="#!" class="text-gray-500 dark:text-dark-500 [&.active]:text-green-500" x-bind:class="{ 'active': activeLink === 'pink' }" @click.prevent="activeLink = 'pink'">M</a>
                                    <a href="#!" class="text-gray-500 dark:text-dark-500 [&.active]:text-green-500" x-bind:class="{ 'active': activeLink === 'green' }" @click.prevent="activeLink = 'green'">L</a>
                                    <a href="#!" class="text-gray-500 dark:text-dark-500 [&.active]:text-green-500" x-bind:class="{ 'active': activeLink === 'sky' }" @click.prevent="activeLink = 'sky'">XL</a>
                                    <a href="#!" class="text-gray-500 dark:text-dark-500 [&.active]:text-green-500" x-bind:class="{ 'active': activeLink === 'red' }" @click.prevent="activeLink = 'red'">2XL</a>
                                </div>
                            </div>

                            <div class="flex gap-2 mt-4">
                                <a href="apps-ecommerce-shop-cart.html" class="w-full btn btn-primary">Buy Now</a>
                                <a href="#!" class="btn btn-sub-gray btn-icon shrink-0"><i data-lucide="shopping-basket" class="size-5"></i></a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div><!--end col-->
</div><!--end grid-->

</div>
{{> partials/footer }}
</div>

{{> partials/vendor-scripts }}

<script src="assets/libs/swiper/swiper-bundle.min.js"></script>

<script type="module" src="assets/js/apps/ecommerce/product-create.init.js"></script>

<script type="module" src="assets/js/main.js"></script>

</body>
</html>