{{> partials/main }}

<head>

    {{> partials/title-meta title="RoadMap" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}
{{> partials/page-heading title="RoadMap" sub-title="Projects" }}

<div class="card">
    <div class="relative overflow-hidden rounded-md-t h-44 bg-primary-500/10">
        <div class="border-[60px] border-t-primary-500 border-l-primary-500 absolute opacity-10 -top-2 ltr:right-0 rtl:left-0 rotate-45 size-96"></div>
        <div class="border-[60px] border-green-500 absolute opacity-10 top-20 ltr:right-8 rtl:left-8 rotate-45 size-80"></div>
        <div class="border-[60px] border-pink-500 absolute opacity-10 top-36 ltr:right-28 rtl:left-28 rotate-45 size-40"></div>
    </div>
    <div class="card-body">
        <div class="relative mb-6">
            <div class="flex flex-wrap gap-5">
                <div class="relative flex items-center justify-center bg-white border border-gray-200 rounded-md shadow-lg dark:bg-dark-900 dark:border-dark-800 shadow-gray-100 dark:shadow-dark-850 -mt-14 size-28 shrink-0">
                    <img src="assets/images/brands/img-02.png" alt="" class="mx-auto size-16">
                </div>
                <div class="grow">
                    <h5 class="mb-1">AI Model Development <span class="leading-normal ltr:ml-1 rtl:mr-1 badge badge-yellow">In Progress</span></h5>
                    <p class="text-gray-500 dark:text-dark-500">Create Date: 25 Jan, 2024</p>
                </div>
                <div class="flex items-center gap-3 shrink-0">
                    <button type="button" class="btn btn-primary" data-modal-target="shareProjectModal">Add User</button>
                    <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown">
                        <button x-ref="button" x-on:click="toggle()" :aria-expanded="open.toString()" type="button" title="dropdown-button" class="btn-icon-text btn-icon btn-sub-gray btn">
                            <i data-lucide="ellipsis" class="size-5"></i>
                        </button>

                        <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden" dropdown-position="right">
                            <a href="#" class="dropdown-item">
                                Create Invoice
                            </a>

                            <a href="#" class="dropdown-item">
                                Generate Billing
                            </a>

                            <a href="#" class="dropdown-item">
                                Delete Project
                            </a>
                            <a href="#" class="dropdown-item">
                                Settings
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-12 gap-5 mb-5">
            <div class="col-span-12 p-3 text-center border border-gray-200 border-dashed rounded-md dark:border-dark-800 sm:col-span-6 md:col-span-3 xl:col-span-2">
                <h6 class="mb-1">31 May, 2024</h6>
                <p class="text-gray-500 dark:text-dark-500">Due Date</p>
            </div>
            <div class="col-span-12 p-3 text-center border border-gray-200 border-dashed rounded-md dark:border-dark-800 sm:col-span-6 md:col-span-3 xl:col-span-2">
                <h6 class="mb-1">$25,000</h6>
                <p class="text-gray-500 dark:text-dark-500">Budget ($)</p>
            </div>
            <div class="col-span-12 p-3 text-center border border-gray-200 border-dashed rounded-md dark:border-dark-800 sm:col-span-6 md:col-span-3 xl:col-span-2">
                <h6 class="mb-1">$8,000</h6>
                <p class="text-gray-500 dark:text-dark-500">Total Spend ($)</p>
            </div>
            <div class="col-span-12 p-3 text-center border border-gray-200 border-dashed rounded-md dark:border-dark-800 sm:col-span-6 md:col-span-3 xl:col-span-2">
                <div x-data="dropdown" x-on:keydown.escape.prevent.stop="close($refs.button)" x-on:focusin.window="!$refs.panel.contains($event.target) && close()" x-id="['dropdown-button']" class="dropdown">

                    <button x-ref="button" x-on:click="toggle()" :aria-expanded="open" :aria-controls="$id('dropdown-button')" type="button" class="flex gap-2 p-0 btn text-start">
                        <img :src="currentUser.image" alt="" class="rounded-full size-10 shrink-0">
                        <span class="block grow">
                            <span class="text-gray-500 dark:text-dark-500" x-text="currentUser.role"></span>
                            <span class="block font-medium" x-text="currentUser.name"></span>
                        </span>
                        <svg :class="{ 'transform rotate-180': open }" class="text-gray-400 transition-transform duration-300 size-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                        </svg>
                    </button>

                    <div x-ref="panel" x-show="open" x-transition.origin.top.left x-on:click.outside="close($refs.button)" :id="$id('dropdown-button')" style="display: none;" class="p-2 dropdown-menu dropdown-right !w-52">
                        <template x-for="user in users" :key="user.name">
                            <a href="#!" @click.prevent="selectUser(user)" :class="{ 'active': isActive(user) }" class="dropdown-item">
                                <img :src="user.image" alt="" class="rounded-full size-8 shrink-0">
                                <h6 x-text="user.name"></h6>
                            </a>
                        </template>
                    </div>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-12 gapo-space mb-space">
            <div class="col-span-12 md:col-span-4">
                <h6 class="mb-1">Assigned To:</h6>
                <div class="flex -space-x-3 rtl:space-x-reverse">
                    <a href="#!" title="avatar link" class="transition duration-300 ease-linear hover:z-10" x-data="{ tooltip: 'Ina Payne' }"><img class="border-2 border-white rounded-full dark:border-dark-900 size-10" x-tooltip.placement.top.no-flip="tooltip" src="assets/images/avatar/user-5.png" alt=""></a>
                    <a href="#!" title="avatar link" class="transition duration-300 ease-linear hover:z-10" x-data="{ tooltip: 'Robert Freeman' }"><img class="border-2 border-white rounded-full dark:border-dark-900 size-10" x-tooltip.placement.top.no-flip="tooltip" src="assets/images/avatar/user-11.png" alt=""></a>
                    <a href="#!" title="avatar link" class="transition duration-300 ease-linear hover:z-10" x-data="{ tooltip: 'Michelle Wile' }"><img class="border-2 border-white rounded-full dark:border-dark-900 size-10" x-tooltip.placement.top.no-flip="tooltip" src="assets/images/avatar/user-13.png" alt=""></a>
                    <a href="#!" title="avatar link" class="transition duration-300 ease-linear hover:z-10" x-data="{ tooltip: 'William Keen' }"><img class="border-2 border-white rounded-full dark:border-dark-900 size-10" x-tooltip.placement.top.no-flip="tooltip" src="assets/images/avatar/user-14.png" alt=""></a>
                    <a href="#!" title="avatar link" class="transition duration-300 ease-linear hover:z-10" x-data="{ tooltip: 'Carol Kincaid' }"><img class="border-2 border-white rounded-full dark:border-dark-900 size-10" x-tooltip.placement.top.no-flip="tooltip" src="assets/images/avatar/user-16.png" alt=""></a>
                    <a href="#!" title="avatar link" class="transition duration-300 ease-linear hover:z-10" x-data="{ tooltip: 'Rachel Jackson' }"><img class="border-2 border-white rounded-full dark:border-dark-900 size-10" x-tooltip.placement.top.no-flip="tooltip" src="assets/images/avatar/user-17.png" alt=""></a>
                </div>
            </div>
            <div class="col-span-12 md:col-span-4">
                <h6 class="mb-1">Report To:</h6>
                <div class="flex -space-x-3 rtl:space-x-reverse">
                    <a href="#!" title="avatar link" class="transition duration-300 ease-linear hover:z-10" x-data="{ tooltip: 'Leal Bureau' }"><img class="border-2 border-white rounded-full dark:border-dark-900 size-10" x-tooltip.placement.top.no-flip="tooltip" src="assets/images/avatar/user-20.png" alt=""></a>
                    <a href="#!" title="avatar link" class="transition duration-300 ease-linear hover:z-10" x-data="{ tooltip: 'Julie Seltzer' }"><img class="border-2 border-white rounded-full dark:border-dark-900 size-10" x-tooltip.placement.top.no-flip="tooltip" src="assets/images/avatar/user-18.png" alt=""></a>
                </div>
            </div>
        </div>

        <ul class="overflow-x-auto whitespace-normal tabs-pills">
            <li>
                <a href="apps-projects-overview.html" class="nav-item [&.active]:bg-primary-500 [&.active]:text-primary-50">
                    <i data-lucide="eye" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-middle">Overview</span>
                </a>
            </li>
            <li>
                <a href="apps-projects-roadmap.html" class="nav-item [&.active]:bg-primary-500 [&.active]:text-primary-50 active">
                    <i data-lucide="sparkles" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-middle">RoadMap</span>
                </a>
            </li>
            <li>
                <a href="apps-projects-task.html" class="nav-item [&.active]:bg-primary-500 [&.active]:text-primary-50">
                    <i data-lucide="align-left" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-middle">Tasks</span>
                </a>
            </li>
            <li>
                <a href="apps-projects-files.html" class="nav-item [&.active]:bg-primary-500 [&.active]:text-primary-50">
                    <i data-lucide="file-text" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-middle">Files</span>
                </a>
            </li>
            <li>
                <a href="apps-projects-users.html" class="nav-item [&.active]:bg-primary-500 [&.active]:text-primary-50">
                    <i data-lucide="user-round" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-middle">Users</span>
                </a>
            </li>
        </ul>
    </div>
</div>

<div class="card">
    <div class="card-body">
        <div class="relative before:absolute space-y-6 before:w-2 before:h-[calc(100%_-_5px)] before:bg-gray-100 dark:before:bg-dark-850 ltr:before:left-3.5 rtl:before:right-3.5 before:top-2 before:rounded-md">
            <div class="relative inline-block px-5 py-2 bg-gray-100 rounded-full dark:bg-dark-850 timeline-month">
                <i class="text-gray-400 ri-circle-fill dark:text-dark-500"></i> Project RoadMap
            </div>
            <div class="relative ltr:pl-10 rtl:pr-10 timeline-section before:absolute before:w-7 before:h-px before:bg-gray-200 dark:before:bg-dark-800 before:top-3.5 ltr:before:left-5 rtl:before:right-5 after:absolute after:top-2 after:left-3 rtl:after:right-3 after:bg-primary-500 after:border after:border-gray-200 dark:after:border-dark-800 after:size-3 after:rounded-full">
                <div class="mb-5 timeline-date py-1 px-3.5 bg-gray-100 dark:bg-dark-850 dark:text-dark-500 text-gray-500 relative inline-block rounded-md border border-gray-200 dark:border-dark-800">
                    Phase 1: Data Collection and Preparation (Month 1-2)
                </div>
                <div class="grid grid-cols-1 gap-3">
                    <div class="relative overflow-hidden bg-gray-100 border border-gray-200 rounded-md timeline-box dark:border-dark-800 dark:bg-dark-850">
                        <div class="box-content p-5 bg-gray-100 dark:bg-dark-850">
                            <h6 class="mb-3">Define Data Requirements:</h6>
                            <ul class="space-y-2 list-disc list-inside">
                                <li>Identify the specific data needed for the project, including customer demographics, transaction history, web interaction logs, marketing campaign data, and product catalog details.</li>
                                <li>Collaborate with stakeholders to understand their data needs and ensure all relevant information is collected.</li>
                            </ul>
                        </div>
                    </div>
                    <div class="relative overflow-hidden bg-gray-100 border border-gray-200 rounded-md timeline-box dark:bg-dark-850 dark:border-dark-800">
                        <div class="box-content p-5 bg-gray-100 dark:bg-dark-850">
                            <h6 class="mb-3">Data Source Identification and Access:</h6>
                            <ul class="space-y-2 list-disc list-inside">
                                <li>Determine the internal and external data sources that will provide the required data.</li>
                                <li>Ensure access to databases, data warehouses, and third-party data providers.</li>
                                <li>Set up necessary data pipelines for continuous data flow if required.</li>
                            </ul>
                        </div>
                    </div>
                    <div class="relative overflow-hidden bg-gray-100 border border-gray-200 rounded-md timeline-box dark:border-dark-800 dark:bg-dark-850">
                        <div class="box-content p-5 bg-gray-100 dark:bg-dark-850">
                            <h6 class="mb-3">Data Extraction:</h6>
                            <ul class="space-y-2 list-disc list-inside">
                                <li>Extract data from identified sources using appropriate tools and techniques.</li>
                                <li>Use SQL, APIs, web scraping, or ETL (Extract, Transform, Load) tools to gather the data.</li>
                                <li>Ensure extraction processes are efficient and minimize disruptions to live systems.</li>
                            </ul>
                        </div>
                    </div>
                    <div class="relative overflow-hidden bg-gray-100 border border-gray-200 rounded-md timeline-box dark:bg-dark-850 dark:border-dark-800">
                        <div class="box-content p-5 bg-gray-100 dark:bg-dark-850">
                            <h6 class="mb-3">Data Cleaning:</h6>
                            <ul class="space-y-2 list-disc list-inside">
                                <li>Remove duplicates, correct errors, and handle missing values in the datasets.</li>
                                <li>Standardize data formats and units to ensure consistency.</li>
                                <li>Identify and resolve any discrepancies or anomalies in the data.</li>
                            </ul>
                        </div>
                    </div>
                    <div class="relative overflow-hidden bg-gray-100 border border-gray-200 rounded-md timeline-box dark:bg-dark-850 dark:border-dark-800">
                        <div class="box-content p-5 bg-gray-100 dark:bg-dark-850">
                            <h6 class="mb-3">Data Quality Assurance:</h6>
                            <ul class="space-y-2 list-disc list-inside">
                                <li>Conduct thorough quality checks to ensure the integrity and reliability of the data.</li>
                                <li>Implement validation rules and perform audits to confirm data accuracy.</li>
                                <li>Document the data cleaning and transformation processes for transparency and reproducibility.</li>
                            </ul>
                        </div>
                    </div>
                    <div class="relative overflow-hidden bg-gray-100 border border-gray-200 rounded-md timeline-box dark:bg-dark-850 dark:border-dark-800">
                        <div class="box-content p-5 bg-gray-100 dark:bg-dark-850">
                            <h6 class="mb-3">Data Privacy and Security:</h6>
                            <ul class="space-y-2 list-disc list-inside">
                                <li>Implement data encryption and access control mechanisms to protect sensitive information.</li>
                                <li>Ensure compliance with relevant data protection regulations such as GDPR or CCPA.</li>
                                <li>Anonymize or pseudonymize personal data where necessary to safeguard user privacy.</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>  
            <div class="relative ltr:pl-10 rtl:pr-10 timeline-section before:absolute before:w-7 before:h-px before:bg-gray-200 dark:before:bg-dark-800 before:top-3.5 ltr:before:left-5 rtl:before:right-5 after:absolute after:top-2 after:left-3 rtl:after:right-3 after:bg-primary-500 after:border after:size-3 after:rounded-full">
                <div class="mb-5 timeline-date py-1 px-3.5 bg-gray-100 text-gray-500 relative inline-block rounded-md border border-gray-200 dark:border-dark-800 dark:text-dark-500 dark:bg-dark-850">
                    Phase 2: Model Development (Month 3-5)
                </div>
                <div class="grid grid-cols-1 gap-3">
                    <div class="relative overflow-hidden bg-gray-100 border border-gray-200 rounded-md timeline-box dark:bg-dark-850 dark:border-dark-800">
                        <div class="box-content p-5 bg-gray-100 dark:bg-dark-850">
                            <h6 class="mb-3">Define Problem Statements:</h6>
                            <ul class="mb-3 space-y-2 list-disc list-inside">
                                <li>Clearly define the specific problems each model aims to solve (e.g., customer behavior prediction, sales forecasting, recommendation system).</li>
                            </ul>
                            <h6 class="mb-3">Select Appropriate Algorithms:</h6>
                            <ul class="mb-3 space-y-2 list-disc list-inside">
                                <li>Research and select suitable machine learning algorithms for each problem statement.</li>
                                <li>Consider a variety of models such as regression, classification, clustering, and recommendation algorithms.</li>
                            </ul>
                            <h6 class="mb-3">Design Model Architecture:</h6>
                            <ul class="mb-3 space-y-2 list-disc list-inside">
                                <li>Design the architecture for each model, including input features, layers (for neural networks), and output formats.</li>
                                <li>Document the rationale behind the chosen architectures.</li>
                            </ul>
                            <h6 class="mb-3">Prepare Training and Validation Datasets:</h6>
                            <ul class="mb-3 space-y-2 list-disc list-inside">
                                <li>Split the preprocessed data into training, validation, and test sets.</li>
                                <li>Ensure data splits maintain representative distributions and avoid data leakage.</li>
                            </ul>
                            <h6 class="mb-3">Develop Model Training Pipelines:</h6>
                            <ul class="space-y-2 list-disc list-inside">
                                <li>Implement training pipelines using selected machine learning frameworks (e.g., TensorFlow, PyTorch, Scikit-Learn).</li>
                                <li>Automate data preprocessing steps within the training pipelines.</li>
                            </ul>
                        </div>
                    </div>
                    <div class="relative overflow-hidden bg-gray-100 border border-gray-200 rounded-md timeline-box dark:bg-dark-850 dark:border-dark-800">
                        <div class="box-content p-5 bg-gray-100 dark:bg-dark-850">
                            <h6 class="mb-3">Tools and Technologies:</h6>
                            <ul class="space-y-2 list-disc list-inside">
                                <li>Machine Learning Frameworks (e.g., TensorFlow, PyTorch, Scikit-Learn)</li>
                                <li>Data Processing Tools (e.g., Pandas, NumPy)</li>
                                <li>Model Training Infrastructure (e.g., GPUs, cloud computing resources)</li>
                                <li>Version Control Systems (e.g., Git)</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="relative ltr:pl-10 rtl:pr-10 timeline-section before:absolute before:w-7 before:h-px before:bg-gray-200 dark:before:bg-dark-800 before:top-3.5 ltr:before:left-5 rtl:before:right-5 after:absolute after:top-2 after:left-3 rtl:after:right-3 after:bg-primary-500 after:border after:size-3 after:rounded-full">
                <div class="mb-5 timeline-date py-1 px-3.5 bg-gray-100 text-gray-500 relative inline-block rounded-md border border-gray-200 dark:bg-dark-850 dark:border-dark-800 dark:text-dark-500">
                    Phase 3: Integration and Deployment (Month 6-7)
                </div>
                <div class="grid grid-cols-1 gap-3">
                    <div class="relative overflow-hidden bg-gray-100 border border-gray-200 rounded-md timeline-box dark:bg-dark-850 dark:border-dark-800">
                        <div class="box-content p-5 bg-gray-100 dark:bg-dark-850">
                            <h6 class="mb-3">Integration Planning and Initial Setup</h6>
                            <ul class="mb-3 space-y-2 list-decimal list-inside">
                                <li>Develop Integration Plan</li>
                                <li>Collaborate with Development Team</li>
                                <li>Setup Development and Testing Environments</li>
                                <li>API Development</li>
                            </ul>
                            <h6 class="mb-3">Model Integration and Testing</h6>
                            <ul class="mb-3 space-y-2 list-decimal list-inside">
                                <li>Integrate Models into the Platform</li>
                                <li>Functional Testing</li>
                                <li>User Interface Integration</li>
                                <li>Security and Access Control</li>
                            </ul>
                            <h6 class="mb-3">Deployment in Staging Environment</h6>
                            <ul class="mb-3 space-y-2 list-decimal list-inside">
                                <li>Deploy Models in Staging</li>
                                <li>Conduct Performance Testing</li>
                                <li>User Acceptance Testing (UAT)</li>
                                <li>Finalize Documentation</li>
                            </ul>
                            <h6 class="mb-3">Production Deployment and Monitoring</h6>
                            <ul class="space-y-2 list-decimal list-inside">
                                <li>Final Review and Approval</li>
                                <li>Production Deployment</li>
                                <li>Monitoring and Maintenance Setup</li>
                                <li>Post-Deployment Support</li>
                                <li>Iterative Improvements</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="relative ltr:pl-10 rtl:pr-10 timeline-section before:absolute before:w-7 before:h-px before:bg-gray-200 dark:before:bg-dark-800 before:top-3.5 ltr:before:left-5 rtl:before:right-5 after:absolute after:top-2 after:left-3 rtl:after:right-3 after:bg-primary-500 after:border after:size-3 after:rounded-full">
                <div class="mb-5 timeline-date py-1 px-3.5 bg-gray-100 text-gray-500 relative inline-block rounded-md border border-gray-200 dark:bg-dark-850 dark:border-dark-800 dark:text-dark-500">
                    Phase 4: User Interface and Reporting (Month 8-9)
                </div>
                <div class="grid grid-cols-1 gap-3">
                    <div class="relative overflow-hidden bg-gray-100 border border-gray-200 rounded-md timeline-box dark:bg-dark-850 dark:border-dark-800">
                        <div class="box-content p-5 bg-gray-100 dark:bg-dark-850">
                            <h6 class="mb-3">Requirements Gathering and Design Planning</h6>
                            <ul class="mb-3 space-y-2 list-decimal list-inside">
                                <li>Stakeholder Interviews and Surveys</li>
                                <li>Define UI/UX Requirements</li>
                                <li>Design Mockups and Prototypes</li>
                                <li>Review and Feedback</li>
                            </ul>
                            <h6 class="mb-3">User Interface Development</h6>
                            <ul class="space-y-2 list-decimal list-inside">
                                <li>Front-End Development</li>
                                <li>Integration with AI Models</li>
                                <li>User Authentication and Access Control</li>
                                <li>UI Testing</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

</div>
{{> partials/footer }}
</div>

<div id="shareProjectModal" class="!hidden modal show">
    <div class="modal-wrap modal-center">
        <div class="modal-header">
            <h6>Share Project</h6>
            <button data-modal-close="shareProjectModal" class="link link-red"><i data-lucide="x" class="size-5"></i></button>
        </div>
        <div class="modal-content">
            <div class="flex items-center gap-3">
                <input type="text" id="basicInput1" class="form-input grow" placeholder="<EMAIL>">
                <button type="button" class="btn btn-primary shrink-0">Send Invite</button>
            </div>
            <div class="mt-5">
                <div class="space-y-3">
                    <div class="flex items-center gap-2">
                        <img src="assets/images/avatar/user-5.png" alt="" class="rounded-full size-7 shrink-0">
                        <h6 class="grow">Ina Payne</h6>
                        <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown shrink-0">
                            <button x-ref="button" x-on:click="toggle()" :aria-expanded="open.toString()" type="button" class="flex items-center gap-2 text-gray-500 dark:text-dark-500">
                                Admin
                                <svg :class="{ 'transform rotate-180': open }" class="transition-transform duration-300 size-5" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                </svg>
                            </button>

                            <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden" dropdown-position="right">
                                <a href="#" class="dropdown-item">
                                    Edit
                                </a>
                                <a href="#" class="dropdown-item">
                                    View Only
                                </a>
                                <a href="#" class="dropdown-item">
                                    Admin
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center gap-2">
                        <img src="assets/images/avatar/user-11.png" alt="" class="rounded-full size-7 shrink-0">
                        <h6 class="grow">Robert Freeman</h6>
                        <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown shrink-0">
                            <button x-ref="button" x-on:click="toggle()" :aria-expanded="open.toString()" type="button" class="flex items-center gap-2 text-gray-500 dark:text-dark-500">
                                View Only
                                <svg :class="{ 'transform rotate-180': open }" class="transition-transform duration-300 size-5" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                </svg>
                            </button>

                            <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden" dropdown-position="right">
                                <a href="#" class="dropdown-item">
                                    Edit
                                </a>
                                <a href="#" class="dropdown-item">
                                    View Only
                                </a>
                                <a href="#" class="dropdown-item">
                                    Admin
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center gap-2">
                        <img src="assets/images/avatar/user-13.png" alt="" class="rounded-full size-7 shrink-0">
                        <h6 class="grow">Michelle Wile</h6>
                        <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown shrink-0">
                            <button x-ref="button" x-on:click="toggle()" :aria-expanded="open.toString()" type="button" class="flex items-center gap-2 text-gray-500 dark:text-dark-500">
                                Edit
                                <svg :class="{ 'transform rotate-180': open }" class="transition-transform duration-300 size-5" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                </svg>
                            </button>

                            <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden" dropdown-position="right">
                                <a href="#" class="dropdown-item">
                                    Edit
                                </a>
                                <a href="#" class="dropdown-item">
                                    View Only
                                </a>
                                <a href="#" class="dropdown-item">
                                    Admin
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center gap-2">
                        <img src="assets/images/avatar/user-14.png" alt="" class="rounded-full size-7 shrink-0">
                        <h6 class="grow">William Keen</h6>
                        <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown shrink-0">
                            <button x-ref="button" x-on:click="toggle()" :aria-expanded="open.toString()" type="button" class="flex items-center gap-2 text-gray-500 dark:text-dark-500">
                                Edit
                                <svg :class="{ 'transform rotate-180': open }" class="transition-transform duration-300 size-5" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                </svg>
                            </button>

                            <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden" dropdown-position="right">
                                <a href="#" class="dropdown-item">
                                    Edit
                                </a>
                                <a href="#" class="dropdown-item">
                                    View Only
                                </a>
                                <a href="#" class="dropdown-item">
                                    Admin
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center gap-2">
                        <img src="assets/images/avatar/user-16.png" alt="" class="rounded-full size-7 shrink-0">
                        <h6 class="grow">Carol Kincaid</h6>
                        <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown shrink-0">
                            <button x-ref="button" x-on:click="toggle()" :aria-expanded="open.toString()" type="button" class="flex items-center gap-2 text-gray-500 dark:text-dark-500">
                                View Only
                                <svg :class="{ 'transform rotate-180': open }" class="transition-transform duration-300 size-5" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                </svg>
                            </button>

                            <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden" dropdown-position="right">
                                <a href="#" class="dropdown-item">
                                    Edit
                                </a>
                                <a href="#" class="dropdown-item">
                                    View Only
                                </a>
                                <a href="#" class="dropdown-item">
                                    Admin
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center gap-2">
                        <img src="assets/images/avatar/user-17.png" alt="" class="rounded-full size-7 shrink-0">
                        <h6 class="grow">Rachel Jackson</h6>
                        <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown shrink-0">
                            <button x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" type="button" class="flex items-center gap-2 text-gray-500 dark:text-dark-500">
                                Edit
                                <svg :class="{ 'transform rotate-180': open }" class="transition-transform duration-300 size-5" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                </svg>
                            </button>

                            <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden" dropdown-position="right">
                                <a href="#" class="dropdown-item">
                                    Edit
                                </a>
                                <a href="#" class="dropdown-item">
                                    View Only
                                </a>
                                <a href="#" class="dropdown-item">
                                    Admin
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <p><a href="#!" class="link link-primary"><i data-lucide="link" class="inline-block align-middle ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-baseline">Copy URL</span></a></p>
        </div>
    </div>
</div><!--end-->

{{> partials/vendor-scripts }}

<script type="module" src="assets/js/apps/projects/overview.init.js"></script>

<script type="module" src="assets/js/main.js"></script>

</body>
</html>