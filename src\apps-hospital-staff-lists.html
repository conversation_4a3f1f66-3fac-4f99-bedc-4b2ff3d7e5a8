{{> partials/main }}

<head>

    {{> partials/title-meta title="List View" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}
{{> partials/page-heading title="List View" sub-title="Staff" }}
    <div x-data="staffListTable()">
        <div class="grid grid-cols-12 gap-x-space">
            <div class="col-span-12 card">
                <div class="flex items-center gap-3 card-header">
                    <h6 class="card-title grow">Staff List</h6>
                    <button type="button" class="btn btn-primary shrink-0" @click="handleModal('showAddStaffForm')" data-modal-target="addStaffModal"><i data-lucide="circle-plus" class="inline-block mr-1 size-4"></i> Add Staff</button>
                </div>
                <div class="pt-0 card-body">
                    <div>
                        <div data-simplebar class="table-box whitespace-nowrap">
                            <table class="table flush">
                                <tbody>
                                    <tr class="text-gray-500 bg-gray-100 dark:bg-dark-850 dark:text-dark-500">
                                        <th x-on:click="sort('staffID')" class="!font-medium cursor-pointer">ID <span x-show="sortBy === 'staffID'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                        <th x-on:click="sort('name')" class="!font-medium cursor-pointer">Staff Name <span x-show="sortBy === 'name'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                        <th x-on:click="sort('department')" class="!font-medium cursor-pointer">Department <span x-show="sortBy === 'department'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                        <th x-on:click="sort('email')" class="!font-medium cursor-pointer">Email <span x-show="sortBy === 'email'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                        <th x-on:click="sort('phone')" class="!font-medium cursor-pointer">Phone <span x-show="sortBy === 'phone'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                        <th x-on:click="sort('date')" class="!font-medium cursor-pointer">Joining Date <span x-show="sortBy === 'date'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                        <th class="!font-medium">Action</th>
                                    </tr>
                                    <template x-for="(staff, index) in displayedStaff" :key="index">
                                        <tr class="*:px-3 *:py-2.5">
                                            <td x-text="staff.staffID"></td>
                                            <td>
                                                <div class="flex items-center gap-3">
                                                    <div class="relative text-gray-500 bg-gray-100 rounded-full dark:text-dark-500 dark:bg-dark-850 size-10">
                                                        <img x-show="staff.image" :src="staff.image" alt="" class="rounded-full" @error="removeImage($event)">
                                                        <span x-show="!staff.image" x-text="staff.avatarText" class="absolute inset-0 flex items-center justify-center text-sm font-semibold text-gray-500 bg-gray-100 rounded-full dark:text-dark-500 dark:bg-dark-850"></span>
                                                    </div>
                                                    <div>
                                                        <h6 class="mb-1"><a href="#!" x-text="staff.name"></a></h6>
                                                        <p x-text="staff.role" class="text-sm text-gray-500 dark:text-dark-500"></p>
                                                    </div>
                                                </div>
                                            </td>
                                            <td x-text="staff.department"></td>
                                            <td x-text="staff.email"></td>
                                            <td x-text="staff.phone"></td>
                                            <td x-text="staff.formattedDate"></td>
                                            <td>
                                                <div class="flex items-center gap-2">
                                                    <button class="btn btn-sub-gray btn-icon !size-8 rounded-md" title="edit" data-modal-target="addStaffModal" @click="editStaff(staff.staffID)"><i class="ri-pencil-line"></i></button>
                                                    <button class="btn btn-sub-red btn-icon !size-8 rounded-md" title="delete" @click=" deleteItem = staff.staffID" data-modal-target="deleteModal"><i class="ri-delete-bin-line"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                    </template>
                                </tbody>
                            </table>
                        </div>
                        <div class="grid grid-cols-12 gap-5 mt-5 items-center">
                            <div class="col-span-12 md:col-span-6 justify-center md:justify-start flex flex-wrap">
                                <p class="text-gray-500 dark:text-dark-500">Showing <b x-text="showingStart"></b> - <b x-text="showingEnd"></b> of <b x-text="staffs.length"></b> Results</p>
                            </div>
                            <div class="col-span-12 md:col-span-6">
                                <div class="flex justify-center md:justify-end pagination pagination-primary">
                                    <button @click="prevPage()" :disabled="currentPage === 1" class="pagination-pre">
                                        <i data-lucide="chevron-left" class="mr-1 rtl:hidden ltr:inline-block size-4"></i>
                                        <i data-lucide="chevron-right" class="ml-1 ltr:hidden rtl:inline-block size-4"></i>
                                        Prev
                                    </button>
                                    <template x-for="page in totalPages" :key="page">
                                        <button @click="gotoPage(page)" :class="{ 'active': currentPage === page }" class="pagination-item">
                                            <span x-text="page"></span>
                                        </button>
                                    </template>
                                    <button @click="nextPage()" :disabled="currentPage === totalPages" class="pagination-next">
                                        Next
                                        <i data-lucide="chevron-right" class="ml-1 ltr:inline-block rtl:hidden size-4"></i>
                                        <i data-lucide="chevron-left" class="mr-1 ltr:hidden rtl:inline-block size-4"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div><!--end col-->
        </div><!--end grid-->

        <div id="addStaffModal" class="!hidden modal show" :class="{'show d-block': showAddStaffForm || showEditStaffForm}" x-show="showAddStaffForm || showEditStaffForm">
            <div class="modal-wrap modal-center">
                <div class="p-2 modal-content">
                    <div class="h-24 p-5 rounded-t-sm bg-gradient-to-r from-primary-500/20 via-pink-500/20 to-green-500/20"></div>
                    <div class="p-4">
                        <div class="-mt-16">
                            <label for="logo">
                                <div class="inline-flex items-center justify-center overflow-hidden bg-gray-100 border-2 border-white border-solid rounded-full cursor-pointer dark:border-dark-900 dark:bg-dark-850 size-24">
                                    <img x-show="staffForm.image" :src="staffForm.image" class="object-cover w-full h-full rounded-full">
                                    <div x-show="!staffForm.image" class="flex flex-col items-center text-gray-500 dark:text-dark-500">
                                        <i data-lucide="upload"></i>
                                    </div>
                                </div>
                            </label>
                            <div class="hidden mt-4">
                                <label class="block">
                                    <span class="sr-only">Choose profile photo</span>
                                    <input type="file" name="logo" id="logo" @change="fileChosen" class="block w-full text-sm file:rounded-md focus:outline-0 text-slate-500 dark:text-dark-500 file:mr-4 file:py-2 file:px-4 file:border-0 file:text-sm file:font-semibold file:bg-violet-50 file:text-violet-700 hover:file:bg-violet-100" />
                                </label>
                            </div>
                        </div>
                        <div class="grid grid-cols-12 gap-4 mt-4">
                            <div class="col-span-12">
                                <label for="fullNameInput" class="form-label">Full Name</label>
                                <input type="text" id="fullNameInput" class="form-input" placeholder="Full name" x-model="staffForm.name" @input="validateField('name', staffForm.name, 'Full name is required.')">
                                <span x-show="errors.name" class="text-red-500" x-text="errors.name"></span>
                            </div>
                            <div class="col-span-12">
                                <label for="roleInput" class="form-label">Role</label>
                                <input type="text" id="roleInput" class="form-input" placeholder="Role" x-model="staffForm.role" @input="validateField('role', staffForm.role, 'Role is required.')">
                                <span x-show="errors.role" class="text-red-500" x-text="errors.role"></span>
                            </div>
                            <div class="col-span-12">
                                <label for="departmentSelect" class="form-label">Department</label>
                                <div id="departmentSelect" placeholder="Select Department" x-model="staffForm.department" @change="validateField('department', document.querySelector('#departmentSelect') , 'Department is required.')"></div>
                                <span x-show="errors.department" class="text-red-500" x-text="errors.department"></span>
                            </div>
                            <div class="col-span-12">
                                <label for="emailInput" class="form-label">Email</label>
                                <input type="email" id="emailInput" class="form-input" placeholder="<EMAIL>" x-model="staffForm.email" @input="validateEmailField()">
                                <span x-show="errors.email" class="text-red-500" x-text="errors.email"></span>
                            </div>
                            <div class="col-span-12">
                                <label for="phoneNumberInput" class="form-label">Phone Number</label>
                                <input type="text" id="phoneNumberInput" class="form-input" placeholder="+(00) 0000 000" x-model="staffForm.phone" @input="formatPhoneNumber()">
                                <span x-show="errors.phone" class="text-red-500" x-text="errors.phone"></span>
                            </div>
                            <div class="col-span-12">
                                <label for="joiningDateInput" class="form-label">Joining Date</label>
                                <input type="text" id="joiningDateInput" class="form-input" placeholder="Select date" data-provider="flatpickr" data-date-format="d M, Y" x-model="staffForm.date" @input="validateField('date', staffForm.date, 'Date is required.')">
                                <span x-show="errors.date" class="text-red-500" x-text="errors.date"></span>
                            </div>
                        </div>
                        <div class="flex items-center justify-end gap-2 mt-5">
                            <button type="button" class="btn btn-active-red" data-modal-close="addStaffModal" @click="resetForm()">
                                <i data-lucide="x" class="inline-block size-4"></i>
                                <span class="align-baseline">Close</span>
                            </button>
                            <button type="button" class="btn btn-primary" x-text="showAddStaffForm ? 'Add Staff' : 'Update Staff'" @click="submitForm()">
                                <i data-lucide="plus" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i>
                                Add Staff
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>


        <!--delete modal-->
        <div id="deleteModal" class="!hidden modal show">
            <div class="modal-wrap modal-xs modal-center">
                <div class="text-center modal-content p-7">
                    <div class="flex items-center justify-center mx-auto mb-4 text-red-500 rounded-full bg-red-500/10 size-14 backdrop-blur-xl">
                        <i data-lucide="trash-2" class="size-6"></i>
                    </div>
                    <h5 class="mb-4">Are you sure you want to delete this staff ?</h5>
                    <div class="flex items-center justify-center gap-2">
                        <button class="btn btn-red" @click="deleteStaff()" data-modal-close="deleteModal"> Delete</button>
                        <button data-modal-close="deleteModal" class="btn link link-primary">Cancel</button>
                    </div>
                </div>
            </div>
        </div><!--end-->
    </div>
    </div>
    {{> partials/footer }}
</div>

{{> partials/vendor-scripts }}

<script type="module" src="assets/js/apps/hospital/staff/list.init.js"></script>

<script type="module" src="assets/js/main.js"></script>

</body>
</html>