{{> partials/main }}

<head>

    {{> partials/title-meta title="ApexSankey" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}

{{> partials/page-heading title="ApexSankey" sub-title="Charts" }}

<div class="grid grid-cols-12 gap-x-space">
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Basic</h6>
        </div>
        <div class="card-body" x-data="apexSankeyApp()" x-init="init()">
            <div x-ref="apexSankeyContainer" data-chart-colors="[bg-gray-800, bg-white, bg-gray-200]" data-chart-dark-colors="[bg-dark-100, bg-dark-900, bg-dark-800]"></div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Node Customization</h6>
        </div>
        <div class="card-body" x-data="apexSankeyNodeApp()" x-init="init()">
            <div x-ref="apexSankeyNodeContainer" data-chart-colors="[bg-gray-800, bg-white, bg-gray-200]" data-chart-dark-colors="[bg-dark-100, bg-dark-900, bg-dark-800]"></div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Edge Customization</h6>
        </div>
        <div class="card-body" x-data="apexSankeyEdgeApp()" x-init="init()">
            <div x-ref="apexSankeyEdgeContainer" data-chart-colors="[bg-gray-800, bg-white, bg-gray-200]" data-chart-dark-colors="[bg-dark-100, bg-dark-900, bg-dark-800]"></div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Font Options</h6>
        </div>
        <div class="card-body" x-data="apexSankeyFontApp()" x-init="init()">
            <div x-ref="apexSankeyFontContainer" data-chart-colors="[bg-gray-800, bg-white, bg-gray-200]" data-chart-dark-colors="[bg-dark-100, bg-dark-900, bg-dark-800]"></div>
        </div>
    </div><!--end col-->
</div><!--end grid-->

</div>
{{> partials/footer }}
</div>

{{> partials/vendor-scripts }}

<script type="module" src="assets/js/charts/apexsankey.init.js"></script>

<script type="module" src="assets/js/main.js"></script>

</body>
</html>