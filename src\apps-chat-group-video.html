{{> partials/main }}

<head>

    {{> partials/title-meta title="Group Video" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}
{{> partials/page-heading title="Group Video" sub-title="Chats" }}

<div data-simplebar>
    <div class="flex items-center gap-x-space">
        <a href="#!" class="relative block w-64 overflow-hidden card shrink-0">
            <div class="absolute flex items-center gap-2 bottom-3 left-3">
                <div class="rounded-full btn bg-gray-900/40 [&.active]:bg-red-500 text-white flex items-center justify-center p-0 size-9"><i data-lucide="mic" class="size-4"></i></div>
                <div class="px-3 py-2 text-xs leading-none text-white rounded-full bg-gray-900/40">
                    You
                </div>
            </div>
            <img src="assets/images/chat/video-7.png" alt="">
        </a>
        <a href="#!" class="relative block w-64 overflow-hidden card shrink-0">
            <div class="absolute flex items-center gap-2 bottom-3 left-3">
                <div class="rounded-full btn bg-gray-900/40 [&.active]:bg-red-500 text-white flex items-center justify-center p-0 size-9"><i data-lucide="mic" class="size-4"></i></div>
                <div class="px-3 py-2 text-xs leading-none text-white rounded-full bg-gray-900/40">
                    Michaela Sutton
                </div>
            </div>
            <img src="assets/images/chat/video-2.png" alt="">
        </a>
        <a href="#!" class="relative block w-64 overflow-hidden card shrink-0">
            <div class="absolute flex items-center gap-2 bottom-3 left-3">
                <div class="rounded-full btn bg-gray-900/40 [&.active]:bg-red-500 text-white flex items-center justify-center p-0 size-9"><i data-lucide="mic" class="size-4"></i></div>
                <div class="px-3 py-2 text-xs leading-none text-white rounded-full bg-gray-900/40">
                    James Davis
                </div>
            </div>
            <img src="assets/images/chat/video-3.png" alt="">
        </a>
        <a href="#!" class="relative block w-64 overflow-hidden card shrink-0">
            <div class="absolute flex items-center gap-2 bottom-3 left-3">
                <div class="rounded-full btn bg-gray-900/40 [&.active]:bg-red-500 text-white flex items-center justify-center p-0 size-9"><i data-lucide="mic" class="size-4"></i></div>
                <div class="px-3 py-2 text-xs leading-none text-white rounded-full bg-gray-900/40">
                    Wendy Dugan
                </div>
            </div>
            <img src="assets/images/chat/video-4.png" alt="">
        </a>
        <a href="#!" class="relative block w-64 overflow-hidden card shrink-0">
            <div class="absolute flex items-center gap-2 bottom-3 left-3">
                <div class="rounded-full btn bg-gray-900/40 [&.active]:bg-red-500 text-white flex items-center justify-center p-0 size-9"><i data-lucide="mic" class="size-4"></i></div>
                <div class="px-3 py-2 text-xs leading-none text-white rounded-full bg-gray-900/40">
                    Carlos Garcia
                </div>
            </div>
            <img src="assets/images/chat/video-5.png" alt="">
        </a>
        <a href="#!" class="relative block w-64 overflow-hidden card shrink-0">
            <div class="absolute flex items-center gap-2 bottom-3 left-3">
                <div class="rounded-full btn bg-gray-900/40 [&.active]:bg-red-500 text-white flex items-center justify-center p-0 size-9"><i data-lucide="mic" class="size-4"></i></div>
                <div class="px-3 py-2 text-xs leading-none text-white rounded-full bg-gray-900/40">
                    Lorna Yancey
                </div>
            </div>
            <img src="assets/images/chat/video-6.png" alt="">
        </a>
    </div>
</div>

<div class="grid grid-cols-12 gap-x-space">
    <div class="col-span-12 overflow-hidden xl:col-span-8 card" x-data="{showCaptions : true}">
        <div class="relative">
            <div class="absolute flex items-center gap-3 top-5 left-4">
                <button title="mic btn" class="rounded-full btn bg-gray-900/30 [&.active]:bg-red-500 text-white btn-icon"><i data-lucide="mic" class="size-4"></i></button>
                <div class="px-4 py-3 leading-none text-white rounded-full bg-gray-900/30">
                    John Powers
                </div>
            </div>
            <img src="assets/images/chat/video-1.png" alt="" class="object-cover w-full">
            <div x-show="showCaptions" class="absolute max-w-2xl px-4 py-2.5 mx-auto font-medium leading-none transform -translate-x-1/2 rounded-full bg-gray-900/15 left-1/2 bottom-5">
                Hello, Shopia Mia
            </div>
        </div>
        <div class="card-body">
            <div class="flex flex-wrap items-center justify-between gap-5">
                <div x-data="{ volume: 50 }" class="inline-flex items-center gap-2 px-3 bg-gray-100 dark:bg-dark-850 btn shrink-0">
                    <button title="volume - btn" @click="volume = Math.max(0, volume - 10)" class="p-1 bg-white rounded-sm dark:bg-dark-900">
                        <i data-lucide="volume-1" class="text-gray-500 size-4 dark:text-dark-500"></i>
                    </button>
                    <div class="!w-24 bg-white dark:bg-dark-900 progress-bar progress-1">
                        <div :style="{ width: volume + '%' }" class="h-2 text-white rounded-sm progress-bar-wrap bg-primary-500"></div>
                    </div>
                    <button title="volume + btn" @click="volume = Math.min(100, volume + 10)" class="p-1 bg-white rounded-sm dark:bg-dark-900">
                        <i data-lucide="volume-2" class="text-gray-500 dark:text-dark-500 size-4"></i>
                    </button>
                </div>
                <div class="flex items-center gap-2">
                    <button title="mic btn" class="rounded-full btn btn-sub-gray btn-icon"><i data-lucide="mic" class="size-4"></i></button>
                    <button title="video btn" class="rounded-full btn btn-sub-gray btn-icon"><i data-lucide="video" class="size-4"></i></button>
                    <button title="captions btn" class="rounded-full btn btn-sub-gray btn-icon" @click="showCaptions = !showCaptions">
                        <i x-show="showCaptions" data-lucide="captions" class="size-4"></i>
                        <i x-show="!showCaptions" data-lucide="captions-off" class="size-4"></i>
                    </button>
                    <button title="added btn" class="rounded-full btn btn-sub-gray btn-icon"><i data-lucide="circle-plus" class="size-4"></i></button>
                </div>
                <button title="leave call" class="btn btn-red"><i data-lucide="phone-missed" class="inline-block mr-1 size-4"></i> Leave Call</button>
            </div>
        </div>
    </div>
    <div class="col-span-12 xl:col-span-4">
        <div class="card" x-data="pinManager()">
            <div class="card-header">
                <h6 class="card-title">Key Moments</h6>
            </div>
            <div class="card-body">
                <div class="max-h-28 -mx-space px-space" data-simplebar>
                    <div class="flex flex-col gap-2">
                        <template x-for="pin in pins" :key="pin.time">
                            <a href="#!" title="text pin" class="flex items-center gap-3 text-gray-500 dark:text-dark-500">
                                <p class="w-28" x-text="pin.time"></p>
                                <p x-text="pin.text"></p>
                            </a>
                        </template>
                    </div>
                </div>
                <div class="flex items-center gap-2 mt-5">
                    <p class="font-medium text-green-500 shrink-0" x-text="formattedTime"></p>
                    <label for="newPinText" class="hidden">newPinText</label>
                    <input type="text" class="form-input" id="newPinText" x-model="newPinText" @keydown.enter="addPin">
                    <button @click="addPin" title="add pin btn" class="btn btn-sub-red btn-icon shrink-0">
                        <i data-lucide="pin" class="size-4"></i>
                    </button>
                </div>
            </div>
        </div>
        <div class="card" x-data="groupChat()">
            <div class="card-header">
                <h6 class="card-title">Group Chat</h6>
            </div>
            <div class="card-body">
                <div class="max-h-64 -mx-space px-space" data-simplebar x-ref="chatContainer">
                    <div class="flex flex-col gap-2" id="chat-messages">
                        <template x-for="message in messages" :key="index">
                            <div class="flex gap-2">
                                <div class="relative flex items-center justify-center font-semibold transition duration-200 ease-linear bg-gray-100 dark:bg-dark-850 rounded-full size-10 shrink-0 group-[&.right]/chat:order-2">
                                    <img :src="message.avatar" alt="" class="rounded-full">
                                </div>
                                <div class="grow">
                                    <h6 class="mb-1" x-text="message.name">Michaela Sutton</h6>
                                    <p class="text-gray-500 dark:text-dark-500" x-text="message.message">I think this SRBThemes will provide us with some great insights.</p>
                                </div>
                                <div class="self-end ml-3 text-gray-500 dark:text-dark-500 shrink-0" x-text="message.time">
                                    02:14
                                </div>
                            </div>
                        </template>
                    </div>
                </div>
            </div>
            <div class="flex items-center gap-2 pt-0 card-body">
                <label for="sendMessage" class="hidden">sendMessage</label>
                <input type="text" id="sendMessage" class="form-input" placeholder="Type something ..." x-model="newMessage" @keydown.enter="sendMessage()">
                <button title="send btn" class="btn btn-primary btn-icon shrink-0" @click="sendMessage()">
                    <i data-lucide="send-horizontal" class="size-4"></i>
                </button>
            </div>
        </div>
    </div>
</div>

</div>
{{> partials/footer }}
</div>

{{> partials/vendor-scripts }}

<script type="module" src="assets/js/apps/chat/video.init.js"></script>

<script type="module" src="assets/js/main.js"></script>

<script>
    document.addEventListener('alpine:init', () => {
        lucide.createIcons();
    });
</script>

</body>
</html>