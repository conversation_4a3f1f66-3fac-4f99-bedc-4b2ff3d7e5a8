{{> partials/main }}

<head>

    {{> partials/title-meta title="Order Track" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}
{{> partials/page-heading title="Order Track" sub-title="Orders" }}

<div class="card" x-data="{ 
            openTab: 2,
            activeClasses: 'active',
            inactiveClasses: 'text-gray-500 hover:text-primary-500 dark:text-dark-500 dark:hover:text-primary-500'
        }">
    <div class="tabs-pills card-header">
        <h6 class="card-title grow">Order Tracking</h6>
        <div class="shrink-0">
            <ul class="flex gap-2">
                <li @click="openTab = 1">
                    <a href="javascript:void(0)" :class="openTab === 1 ? activeClasses : inactiveClasses" class="nav-item [&.active]:bg-gray-100 dark:[&.active]:bg-dark-800 [&.active]:text-gray-800 dark:[&.active]:text-dark-50">
                        Overview
                    </a>
                </li>
                <li @click="openTab = 2">
                    <a href="javascript:void(0)" :class="openTab === 2 ? activeClasses : inactiveClasses" class="nav-item [&.active]:bg-gray-100 dark:[&.active]:bg-dark-800 [&.active]:text-gray-800 dark:[&.active]:text-dark-50">
                        Map View
                    </a>
                </li>
            </ul>
        </div>
    </div>
    <div class="card-body">
        <div class="w-full mt-4">
            <div x-show="openTab === 1">
                <div class="grid grid-cols-12 gap-space">
                    <div class="col-span-12 md:col-span-3 xl:col-span-2">
                        <p class="mb-1 text-gray-500 dark:text-dark-500">Order ID</p>
                        <h6><a href="#!" class="text-current link link-primary">PEO-14521</a></h6>
                    </div>
                    <div class="col-span-12 md:col-span-3 xl:col-span-2">
                        <p class="mb-1 text-gray-500 dark:text-dark-500">Delivery Date</p>
                        <h6>08 Sep, 2024</h6>
                    </div>
                    <div class="col-span-12 md:col-span-3 xl:col-span-2">
                        <p class="mb-1 text-gray-500 dark:text-dark-500">Payment Method</p>
                        <span class="badge badge-green">Paid</span>
                    </div>
                    <div class="col-span-12 md:col-span-3 xl:col-span-3">
                        <p class="mb-1 text-gray-500 dark:text-dark-500">Courier Partner</p>
                        <h6>SRBThemes Express</h6>
                    </div>
                    <div class="col-span-12 md:col-span-6 xl:col-span-3">
                        <p class="mb-1 text-gray-500 dark:text-dark-500">Delivery Address</p>
                        <h6>0588 Macey Ranch, Port Blake, China - 96153-1460</h6>
                    </div>
                </div>
                <div class="p-5 bg-gray-100 rounded-md mt-space dark:bg-dark-850" dir="ltr">
                    <div x-data="{ progress: 33 }" class="relative h-32">
                        <div class="absolute left-5 inset-y-0 h-32 w-[2px] bg-gray-200 dark:bg-dark-800 before:absolute before:size-3 before:border-4 before:border-white dark:before:border-dark-900 before:bg-green-500 before:rounded-full before:-left-[5px]"></div>
                        <div class="absolute right-5 inset-y-0 h-32 w-[2px] bg-gray-200 dark:bg-dark-800 before:absolute before:size-3 before:border-4 before:border-white dark:before:border-dark-900 before:bg-gray-100 dark:before:bg-dark-800 before:rounded-full before:-left-[5px]"></div>
                        <div class="relative mx-10">
                            <img src="assets/images/others/truck.png" alt="" class="absolute h-24 animate-pulse md:h-auto" :style="'left:' + (progress - 11) + '%'">
                        </div>
                        <div class="absolute inset-x-0 bottom-0 bg-gray-200 dark:bg-dark-800 progress-bar progress-2">
                            <div :style="'width:' + progress + '%'" class="text-white rounded-full progress-bar-wrap bg-primary-500"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div x-show="openTab === 2">
                <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-space">
                    <div>
                        <p class="mb-1 text-gray-500 dark:text-dark-500">Order ID</p>
                        <h6><a href="#!" class="text-current link link-primary">PEO-14521</a></h6>
                    </div>
                    <div>
                        <p class="mb-1 text-gray-500 dark:text-dark-500">Order Status</p>
                        <span class="badge badge-purple">Shipping</span>
                    </div>
                    <div>
                        <p class="mb-1 text-gray-500 dark:text-dark-500">Delivery Date</p>
                        <h6>08 Sep, 2024</h6>
                    </div>
                    <div>
                        <p class="mb-1 text-gray-500 dark:text-dark-500">Courier Partner</p>
                        <h6>SRBThemes Express</h6>
                    </div>
                    <div>
                        <p class="mb-1 text-gray-500 dark:text-dark-500">Delivery Address</p>
                        <h6>China</h6>
                    </div>
                </div>
                <div id="lineStyleMap" class="mt-space h-72"></div>
            </div>
        </div>
    </div>
</div>

<div class="flex items-center mb-5">
    <h6 class="text-16 grow">Recent Orders</h6>
    <div class="shrink-0">
        <a href="apps-ecommerce-orders-list.html" class="btn btn-green">
            <span class="align-center">View All Orders</span>
            <i data-lucide="move-right" class="ml-1 ltr:inline-block rtl:hidden size-5"></i>
            <i data-lucide="move-left" class="mr-1 rtl:inline-block ltr:hidden size-5"></i>
        </a>
    </div>
</div>

<div x-data="ordersTable()">
    <div class="grid grid-cols-12 gap-x-5">
        <template x-for="(product, index) in displayedProducts" :key="index">
            <div class="col-span-12 md:col-span-6 xl:col-span-3">
                <div class="card">
                    <div class="card-body">
                        <div class="flex items-center gap-2">
                            <p class="text-gray-500 dark:text-dark-500 grow">Orders ID: <a href="#!" class="link link-primary" x-text="product.ordersID"></a></p>
                            <div>
                                <span x-text="product.status" :class="{
                                    'badge badge-green': product.status === 'Delivered',
                                    'badge badge-primary': product.status === 'New',
                                    'badge badge-red': product.status === 'Cancelled',
                                    'badge badge-purple': product.status === 'Shipping',
                                    'badge badge-yellow': product.status === 'Pending'
                                }"></span>
                            </div>
                        </div>

                        <div class="flex items-center gap-3 mt-4">
                            <div class="p-1 border border-gray-200 rounded-md dark:border-dark-800 size-16 shrink-0">
                                <img :src="product.image" alt="">
                            </div>
                            <div class="overflow-hidden grow">
                                <h6 class="mb-1 truncate"><a href="apps-ecommerce-product-overview.html" x-text="product.productName"></a></h6>
                                <a href="apps-ecommerce-orders-overview.html" class="underline link link-red">
                                    Show Details
                                    <i data-lucide="move-right" class="ltr:inline-block rtl:hidden size-4"></i>
                                    <i data-lucide="move-left" class="rtl:inline-block ltr:hidden size-4"></i>
                                </a>
                            </div>
                        </div>

                        <p class="mt-3 text-gray-500 dark:text-dark-500">Order Expected Date</p>
                        <h6 x-text="product.deliveredDate"></h6>

                        <div class="flex flex-wrap items-center gap-2 mt-4 2xl:flex-nowrap">
                            <a href="apps-ecommerce-orders-overview.html" class="w-full btn btn-sub-gray"><i class="ltr:mr-0.5 rtl:ml-0.5 align-bottom ri-eye-line"></i> Overview</a>
                            <button class="w-full btn btn-primary">Track Order</button>
                        </div>
                    </div>
                </div>
            </div>
        </template>
    </div>

    <div class="grid grid-cols-12 gap-5 mb-5 items-center">
        <div class="col-span-12 md:col-span-6">
            <p class="text-gray-500 dark:text-dark-500">Showing <b x-text="showingStart"></b> - <b x-text="showingEnd"></b> of <b x-text="products.length"></b> Results</p>
        </div>
        <div class="col-span-12 md:col-span-6">
            <div class="flex justify-start md:justify-end pagination pagination-primary">
                <button @click="prevPage()" :disabled="currentPage === 1" class="pagination-pre">
                    <i data-lucide="chevron-left" class="mr-1 ltr:inline-block rtl:hidden size-4"></i>
                    <i data-lucide="chevron-right" class="ml-1 rtl:inline-block ltr:hidden size-4"></i>
                    Prev
                </button>
                <template x-for="page in totalPages" :key="page">
                    <button @click="gotoPage(page)" :class="{ 'active': currentPage === page }" class="pagination-item">
                        <span x-text="page"></span>
                    </button>
                </template>
                <button @click="nextPage()" :disabled="currentPage === totalPages" class="pagination-next">
                    Next
                    <i data-lucide="chevron-right" class="ml-1 ltr:inline-block rtl:hidden size-4"></i>
                    <i data-lucide="chevron-left" class="mr-1 rtl:inline-block ltr:hidden size-4"></i>
                </button>
            </div>
        </div>
    </div>
</div>
</div>
{{> partials/footer }}
</div>

{{> partials/vendor-scripts }}

<script src="assets/libs/jsvectormap/jsvectormap.min.js"></script>
<script src="assets/libs/jsvectormap/maps/world.js"></script>

<script type="module" src="assets/js/apps/ecommerce/orders-track-map.init.js"></script>

<script type="module" src="assets/js/main.js"></script>

</body>
</html>