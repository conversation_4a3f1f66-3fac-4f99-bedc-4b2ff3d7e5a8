import Swiper from 'swiper/bundle';

var swiper = new Swiper(".mySwiper", {
    slidesPerView: 1,
    spaceBetween: 22,
    autoplay: {
        delay: 2500,
        disableOnInteraction: false,
    },
    breakpoints: {
        768: {
            slidesPerView: 2,
        },
        1024: {
            slidesPerView: 2,
        },
    },
});


import ApexCharts from 'apexcharts';
import { getColorCodes } from "../helpers/helper";

//Stacked Columns Chart
document.addEventListener("alpine:init", () => {
    Alpine.data("stackedColumnApp", () => ({
        series: [],
        labels: [],
        activePeriod: 'thisMonth', // 'thisMonth' or 'lastMonth'
        colorCodes: [],
        stackedColumnChart: null,

        // Data for different time periods
        dataSets: {
            thisMonth: {
                series: [
                    { name: 'Sales', data: [44, 55, 41, 67, 22, 43, 44, 55, 41, 67, 22, 43] },
                    { name: 'Revenue', data: [13, 23, 20, 8, 13, 27, 13, 23, 20, 8, 13, 27] }
                ],
                labels: ['05/01/2025 GMT', '05/02/2025 GMT', '05/03/2025 GMT', '05/04/2025 GMT',
                    '05/05/2025 GMT', '05/06/2025 GMT', '05/07/2025 GMT', '05/08/2025 GMT',
                    '05/09/2025 GMT', '05/10/2025 GMT', '05/11/2025 GMT', '05/12/2025 GMT']
            },
            lastMonth: {
                series: [
                    { name: 'Sales', data: [30, 40, 35, 50, 20, 35, 30, 40, 35, 50, 20, 35] },
                    { name: 'Revenue', data: [10, 15, 12, 5, 8, 15, 10, 15, 12, 5, 8, 15] }
                ],
                labels: ['04/01/2025 GMT', '04/02/2025 GMT', '04/03/2025 GMT', '04/04/2025 GMT',
                    '04/05/2025 GMT', '04/06/2025 GMT', '04/07/2025 GMT', '04/08/2025 GMT',
                    '04/09/2025 GMT', '04/10/2025 GMT', '04/11/2025 GMT', '04/12/2025 GMT']
            }
        },

        init() {
            this.colorCodes = getColorCodes(this.$refs.stackedColumnChart.dataset);
            this.loadData(this.activePeriod);
            this.renderChart();
        },

        loadData(period) {
            this.activePeriod = period;
            this.series = this.dataSets[period].series;
            this.labels = this.dataSets[period].labels;
        },

        renderChart() {
            if (this.stackedColumnChart) {
                this.stackedColumnChart.destroy();
            }

            this.stackedColumnChart = new ApexCharts(
                this.$refs.stackedColumnChart,
                this.options
            );
            this.stackedColumnChart.render();
        },

        reloadChart() {
            this.renderChart();
        },

        get options() {
            return {
                series: this.series,
                chart: {
                    height: 310,
                    type: "bar",
                    stacked: true,
                    toolbar: { show: false },
                    zoom: { enabled: true }
                },
                responsive: [{
                    breakpoint: 480,
                    options: {
                        legend: { position: 'bottom', offsetX: -10, offsetY: 0 }
                    }
                }],
                dataLabels: { enabled: false },
                plotOptions: {
                    bar: {
                        horizontal: false,
                        borderRadius: 6,
                        columnWidth: '25%',
                    },
                },
                xaxis: {
                    categories: this.labels,
                    type: 'datetime'
                },
                legend: {
                    show: false,
                },
                colors: [this.colorCodes[2], this.colorCodes[1]],
                fill: {
                    type: 'gradient',
                    gradient: {
                        shade: 'dark',
                        type: 'vertical',
                        shadeIntensity: 0.5,
                        gradientToColors: [this.colorCodes[0], this.colorCodes[3]],
                        inverseColors: false,
                        opacityFrom: 0.8,
                        opacityTo: 0.8,
                        stops: [0, 100]
                    }
                }
            };
        },

        // Method to switch between periods
        switchPeriod(period) {
            this.loadData(period);
            this.stackedColumnChart.updateOptions({
                series: this.series,
                xaxis: {
                    categories: this.labels
                }
            });
        },

        // Helper to check active period
        isActive(period) {
            return this.activePeriod === period;
        }
    }));
});


//Customer Charts
document.addEventListener('alpine:init', () => {
    Alpine.store('customers', {
        period: 'monthly',
        setPeriod(period) {
            this.period = period;
        }
    });
});
document.addEventListener("alpine:init", () => {
    Alpine.data("customersApp", () => ({
        recentData: {
            values: [15, 25, 35, 45, 35, 25, 35],
            labels: ["Today", "Yesterday", "2 days ago", "3 days ago", "4 days ago", "5 days ago", "6 days ago"]
        },
        weeklyData: {
            values: [28, 50, 70, 90, 70, 50, 60],
            labels: ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"]
        },
        monthlyData: {
            values: [150, 180, 220, 250, 280, 300, 320, 350, 380, 400, 420, 450],
            labels: ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"]
        },
        yearlyData: {
            values: [1200, 1500, 1800, 2100, 2400, 2700],
            labels: ["2019", "2020", "2021", "2022", "2023", "2024"]
        },
        customersCharts: null,
        init() {
            this.renderChart();
            window.addEventListener('resize', this.reloadChart.bind(this));
            
            // Watch for period changes in the store
            this.$watch('$store.customers.period', (value) => {
                this.renderChart();
            });
        },
        renderChart() {
            if (this.customersCharts) {
                this.customersCharts.destroy();
            }

            this.customersCharts = new ApexCharts(this.$refs.customersCharts, this.options);
            this.customersCharts.render();
        },
        reloadChart() {
            this.renderChart();
        },
        getCurrentData() {
            switch(this.$store.customers.period) {
                case 'recent':
                    return this.recentData;
                case 'weekly':
                    return this.weeklyData;
                case 'monthly':
                    return this.monthlyData;
                case 'yearly':
                    return this.yearlyData;
                default:
                    return this.recentData;
            }
        },
        get options() {
            const data = this.getCurrentData();
            return {
                series: [
                    {
                        name: "Customers",
                        data: data.values
                    }
                ],
                chart: {
                    height: 180,
                    type: "area",
                    sparkline: { enabled: !0 },
                    zoom: {
                        enabled: true
                    },
                },
                dataLabels: {
                    enabled: false
                },
                stroke: {
                    curve: 'smooth',
                    width: 2
                },
                xaxis: {
                    categories: data.labels,
                    labels: {
                        style: {
                            fontSize: '12px'
                        }
                    }
                },
                fill: {
                    type: 'gradient',
                    gradient: {
                        shadeIntensity: 1,
                        inverseColors: false,
                        opacityFrom: 0.8,
                        opacityTo: 0,
                        stops: [0, 100]
                    },
                },
                colors: getColorCodes(this.$refs.customersCharts.dataset),
                grid: {
                    show: true,
                    strokeDashArray: 3,
                    position: 'back',
                    padding: {
                        top: 0,
                        right: 5,
                        bottom: 0,
                    },
                    xaxis: {
                        lines: {
                            show: true
                        }
                    },
                    yaxis: {
                        lines: {
                            show: false
                        }
                    },
                },
                tooltip: {
                    y: {
                        formatter: function(value) {
                            return value + " customers";
                        }
                    }
                }
            };
        }
    }));
});

//Payment Types Chart
document.addEventListener("alpine:init", () => {
    Alpine.data("paymentTypesApp", () => ({
        series: [974, 750, 510, 170, 150],
        labels: ['Cash', 'Credit Card', 'PayPal', 'Bank Transfer', 'Other'],
        init() {
            // Initial chart render
            this.renderChart();

            // Reload chart on window resize
            window.addEventListener('resize', this.reloadChart.bind(this));
        },
        renderChart() {
            // Destroy previous instance if exists
            if (this.paymentTypesChart)
                this.paymentTypesChart.destroy();

            // Initialize new chart
            this.paymentTypesChart = new ApexCharts(this.$refs.paymentTypesChart, this.options);
            this.paymentTypesChart.render();
        },
        reloadChart() {
            // Handle the logic for resizing
            this.renderChart(); // Re-render chart on resize
        },
        get options() {
            return {
                series: this.series,
                labels: this.labels,
                chart: {
                    height: 300,
                    type: "donut",
                    dropShadow: {
                        enabled: true,
                        color: '#111',
                        top: -1,
                        left: 3,
                        blur: 3,
                        opacity: 0.1
                    },
                    events: {
                        legendClick: function(chartContext, seriesIndex, config) {
                            // Handle legend click event
                            const series = chartContext.w.config.series;
                            const label = chartContext.w.config.labels[seriesIndex];
                            const isVisible = series[seriesIndex] !== 0;
                            
                            // Update the custom legend appearance
                            const legendItems = document.querySelectorAll('.payment-legend-item');
                            legendItems.forEach(item => {
                                if (item.getAttribute('data-label') === label) {
                                    item.classList.toggle('opacity-50', !isVisible);
                                }
                            });
                        }
                    }
                },
                plotOptions: {
                    pie: {
                        startAngle: -90,
                        endAngle: 270,
                        donut: {
                            size: '65%',
                            background: 'transparent',
                            labels: {
                                show: true,
                                name: {
                                    show: true,
                                    fontSize: '16px',
                                    fontWeight: 500,
                                    offsetY: 0,
                                    color: '#373d3f'
                                },
                                value: {
                                    show: true,
                                    fontSize: '20px',
                                    fontWeight: 600,
                                    offsetY: 12,
                                    color: '#373d3f',
                                    formatter: function (val) {
                                        return val + '%'
                                    }
                                },
                                total: {
                                    show: true,
                                    label: 'Total',
                                    fontSize: '16px',
                                    fontWeight: 400,
                                    color: '#373d3f',
                                    formatter: function (w) {
                                        return '$' + w.globals.seriesTotals.reduce((a, b) => a + b, 0)
                                    }
                                }
                            }
                        }
                    }
                },
                dataLabels: {
                    enabled: false
                },
                colors: getColorCodes(this.$refs.paymentTypesChart.dataset),
                fill: {
                    type: 'gradient',
                },
                stroke: {
                    width: 0,
                    lineCap: 'round'
                },
                legend: {
                    show: false // Hide default legend since we're using custom legend
                },
                tooltip: {
                    enabled: true,
                    theme: 'dark',
                    style: {
                        fontSize: '12px',
                        fontFamily: 'Helvetica, Arial, sans-serif',
                    },
                    y: {
                        formatter: function (val) {
                            return val + '%'
                        }
                    }
                },
                states: {
                    hover: {
                        filter: {
                            type: 'darken',
                            value: 0.9
                        }
                    }
                }
            };
        }
    }));
});


//Products tables
import orderList from "../../json/dashboards/pos-order-list";
function productsTable() {
    return {
        products: [],
        filterProducts: [],
        sortBy: '',
        searchTerm: '',
        sortDirection: 'asc',
        sortClasses: {
            'asc': '↑',
            'desc': '↓'
        },
        selectAll: false,
        selectedItems: [],
        currentPage: 1,
        itemsPerPage: 8,
        activeTab: 'Dine In',

        toggleAll() {
            this.selectedItems = this.selectAll ? [...this.filterProducts] : [];
        },

        get totalPages() {
            return Math.ceil(this.filterProducts.length / this.itemsPerPage);
        },

        get displayedProducts() {
            const start = (this.currentPage - 1) * this.itemsPerPage;
            const end = start + this.itemsPerPage;
            return this.filterProducts.slice(start, end);
        },

        get showingStart() {
            return Math.min((this.currentPage - 1) * this.itemsPerPage + 1, this.filterProducts.length);
        },

        get showingEnd() {
            return Math.min(this.currentPage * this.itemsPerPage, this.filterProducts.length);
        },

        init() {
            let startID = 61501;
            orderList.forEach((product, index) => {
                product.ordersID = "SRD-" + (startID + index).toString();
            });
            this.products = orderList;
            this.filteredProducts();
        },

        filteredProducts() {
            // First filter by active tab
            let filtered = this.products.filter(product => product.status === this.activeTab);
            
            // Then apply search filter if exists
            const searchTerm = this.searchTerm.trim().toLowerCase();
            if (searchTerm) {
                filtered = filtered.filter((product) => {
                    return Object.values(product).some(value => 
                        value.toString().toLowerCase().includes(searchTerm)
                    );
                });
            }
            
            this.filterProducts = filtered;
            this.currentPage = 1; // Reset to first page when filtering
        },

        // Add method to change tabs
        changeTab(tab) {
            this.activeTab = tab;
            this.filteredProducts();
        },

        sort(column) {
            if (column === this.sortBy)
                this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
            else {
                this.sortDirection = 'asc';
                this.sortBy = column;
            }

            this.filterProducts.sort((a, b) => {
                let valueA = a[column];
                let valueB = b[column];

                // Handle special cases for different column types
                if (column === 'tableNumber') {
                    // Extract numbers from table numbers (e.g., "B05" -> 5)
                    valueA = parseInt(valueA.replace(/[^0-9]/g, ''));
                    valueB = parseInt(valueB.replace(/[^0-9]/g, ''));
                } else if (column === 'pickupTime') {
                    // Convert time to comparable format (e.g., "15:30" -> 1530)
                    valueA = parseInt(valueA.replace(':', ''));
                    valueB = parseInt(valueB.replace(':', ''));
                } else if (column === 'price') {
                    // Remove $ and convert to number
                    valueA = parseFloat(valueA.replace('$', ''));
                    valueB = parseFloat(valueB.replace('$', ''));
                }

                let comparison = 0;
                if (valueA > valueB) {
                    comparison = 1;
                } else if (valueA < valueB) {
                    comparison = -1;
                }
                return this.sortDirection === 'desc' ? comparison * -1 : comparison;
            });
        },

        prevPage() {
            if (this.currentPage > 1)
                this.currentPage--;
        },

        nextPage() {
            if (this.currentPage < this.totalPages)
                this.currentPage++;
        },

        gotoPage(page) {
            this.currentPage = page;
        }
    };
}

// Initialize Alpine.js
document.addEventListener('alpine:init', () => {
    Alpine.data('productsTable', productsTable);
});