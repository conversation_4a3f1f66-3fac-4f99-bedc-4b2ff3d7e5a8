{{> partials/main }}

<head>

    {{> partials/title-meta title="Widgets Data" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}
{{> partials/page-heading title="Data" sub-title="Widgets" }}

<div class="grid grid-cols-12 gap-x-space">
    <div class="col-span-12 xl:col-span-6 card">
        <div class="flex items-center card-header">
            <h6 class="card-title grow">Top Products</h6>
            <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown shrink-0">
                <button x-ref="button" x-on:click="toggle()" :aria-expanded="open.toString()" type="button" class="flex px-3 py-1.5 text-xs border-gray-200 dark:border-dark-800 link link-primary btn">
                    Filters
                    <svg :class="{ 'transform rotate-180': open }" class="transition-transform duration-300 ltr:ml-1 rtl:mr-1 size-4" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                    </svg>
                </button>

                <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden" dropdown-position="right">
                    <a href="#" class="dropdown-item">
                        Amount
                    </a>

                    <a href="#" class="dropdown-item">
                        Revenue
                    </a>
                    <a href="#" class="dropdown-item">
                        Rating
                    </a>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div x-data="productsTable" x-init="init()">
                <div class="overflow-x-auto">
                    <table class="table flush">
                        <tbody>
                            <tr class="bg-gray-100 dark:bg-dark-850">
                                <th class="whitespace-nowrap !font-medium text-gray-500 dark:text-dark-500">Product</th>
                                <th class="whitespace-nowrap !font-medium text-gray-500 dark:text-dark-500">Sales</th>
                                <th class="whitespace-nowrap !font-medium text-gray-500 dark:text-dark-500">Price Unit</th>
                                <th class="whitespace-nowrap !font-medium text-gray-500 dark:text-dark-500">Stock</th>
                                <th class="whitespace-nowrap !font-medium text-gray-500 dark:text-dark-500">Revenue</th>
                                <th class="whitespace-nowrap !font-medium text-gray-500 dark:text-dark-500">Rating</th>
                            </tr>
                            <template x-for="(product, index) in products" :key="index">
                                <tr class="*:px-3 *:py-2.5">
                                    <td class="whitespace-nowrap">
                                        <div class="flex items-center gap-2">
                                            <div class="flex items-center justify-center p-1 border border-gray-200 rounded-sm size-9 dark:border-dark-800">
                                                <img :src="product.image" alt="" class="rounded-sm">
                                            </div>
                                            <h6 x-text="product.productName"></h6>
                                        </div>
                                    </td>
                                    <td class="whitespace-nowrap" x-text="product.salesUnit"></td>
                                    <td class="whitespace-nowrap" x-text="product.price"></td>
                                    <td class="whitespace-nowrap" x-text="product.stock"></td>
                                    <td class="whitespace-nowrap" x-text="product.revenue"></td>
                                    <td class="whitespace-nowrap">
                                        <i class="text-yellow-500 ri-star-line"></i>
                                        <span class="text-gray-500 align-middle dark:text-dark-500" x-text="product.rating"></span>
                                    </td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                </div>
            </div>

        </div>
    </div><!--end col-->
    <div class="col-span-12 xl:col-span-6 card">
        <div class="flex items-center card-header">
            <h6 class="card-title grow">Recent Invoice</h6>
            <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown shrink-0">
                <button x-ref="button" x-on:click="toggle()" :aria-expanded="open.toString()" type="button" class="flex px-3 py-1.5 text-xs border-gray-200 dark:border-dark-800 link link-primary btn">
                    Filters
                    <svg :class="{ 'transform rotate-180': open }" class="ml-1 transition-transform duration-300 size-4" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                    </svg>
                </button>

                <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden" dropdown-position="right">
                    <a href="#" class="dropdown-item">
                        Amount
                    </a>

                    <a href="#" class="dropdown-item">
                        Revenue
                    </a>
                    <a href="#" class="dropdown-item">
                        Rating
                    </a>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div x-data="invoiceTable()" x-init="init()">
                <div class="overflow-x-auto">
                    <table class="table flush">
                        <tbody>
                            <tr class="bg-gray-100 dark:bg-dark-850">
                                <th x-on:click="sort('invoiceID')" class="whitespace-nowrap !font-medium text-gray-500 dark:text-dark-500 cursor-pointer">Invoice ID <span x-show="sortBy === 'invoiceID'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th x-on:click="sort('client')" class="whitespace-nowrap !font-medium text-gray-500 dark:text-dark-500 cursor-pointer">Client <span x-show="sortBy === 'client'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th x-on:click="sort('dateDue')" class="whitespace-nowrap !font-medium text-gray-500 dark:text-dark-500 cursor-pointer">Due Date <span x-show="sortBy === 'dateDue'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th x-on:click="sort('totalAmount')" class="whitespace-nowrap !font-medium text-gray-500 dark:text-dark-500 cursor-pointer">Total <span x-show="sortBy === 'totalAmount'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th x-on:click="sort('status')" class="whitespace-nowrap !font-medium text-gray-500 dark:text-dark-500 cursor-pointer">Status <span x-show="sortBy === 'status'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                            </tr>
                            <template x-for="(product, index) in products" :key="index">
                                <tr class="*:px-3 *:py-2.5">
                                    <td class="whitespace-nowrap"><a href="#!" x-text="product.invoiceID"></a></td>
                                    <td class="whitespace-nowrap" x-text="product.client"></td>
                                    <td class="whitespace-nowrap" x-text="product.dateDue"></td>
                                    <td class="whitespace-nowrap" x-text="product.totalAmount"></td>
                                    <td class="whitespace-nowrap">
                                        <span x-text="product.status" :class="{
                                            'badge badge-yellow': product.status === 'Pending',
                                            'badge badge-green': product.status === 'Successful',
                                            'badge badge-blue': product.status === 'New'
                                        }"></span>
                                    </td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div><!--end col-->
</div><!--end grid-->

</div>

{{> partials/footer }}
</div>

{{> partials/vendor-scripts }}

<script type="module" src="assets/js/main.js"></script>

<script type="module" src="assets/js/pages/widgets.data.init.js"></script>

</body>
</html>