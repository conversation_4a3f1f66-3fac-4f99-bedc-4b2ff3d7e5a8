/*
Template Name: <PERSON>iex - Admin & Dashboard Template
Author: SRBThemes
Version: 1.0.0
File: list view calendar init Js File
*/

import { Calendar } from '@fullcalendar/core'
import timeGridPlugin from '@fullcalendar/timegrid'

const calendarEl = document.getElementById('listViewCalendar')
const calendar = new Calendar(calendarEl, {
    timeZone: 'America/New_York',
    buttonText: {
        today: 'Today',
        year: 'Year',
        month: 'Month',
        week: 'Week',
        day: 'Day',
        list: 'List'
    },
    plugins: [
        timeGridPlugin,
    ],
    initialView: 'timeGridWeek',
    headerToolbar: {
        left: 'prev,next',
        center: 'title',
        right: 'timeGridWeek,timeGridDay' // user can switch between the two
    }
})

calendar.render()