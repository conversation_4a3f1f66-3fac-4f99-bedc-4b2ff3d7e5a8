import product1 from "/assets/images/products/img-01.png";
import product2 from "/assets/images/products/img-02.png";
import product3 from "/assets/images/products/img-03.png";
import product4 from "/assets/images/products/img-04.png";
import product5 from "/assets/images/products/img-05.png";
import product6 from "/assets/images/products/img-06.png";
import product7 from "/assets/images/products/img-07.png";
import product8 from "/assets/images/products/img-08.png";
import product9 from "/assets/images/products/img-09.png";
import product10 from "/assets/images/products/img-10.png";
import product11 from "/assets/images/products/img-11.png";
import product13 from "/assets/images/products/img-13.png";

const productGridData = [
    {
        "productName": "Blouse Ruffle Tube top",
        "category": "Fashion",
        "price": "$14.99",
        "image": product1,
        "color": "bg-primary-500/15"
    },
    {
        "productName": "Gold-colored locket watch",
        "category": "Watch",
        "price": "$59.99",
        "image": product2,
        "color": "bg-green-500/15"
    },
    {
        "productName": "Spar Men Black Running Shoes",
        "category": "Footwear",
        "price": "$35.78",
        "image": product3,
        "color": "bg-indigo-500/15"
    },
    {
        "productName": "Crop top Sweater Clothing",
        "category": "Fashion",
        "price": "$29.49",
        "image": product4,
        "color": "bg-pink-500/15"
    },
    {
        "productName": "Sleeve Clothing Leggings",
        "category": "Fashion",
        "price": "$22.00",
        "image": product5,
        "color": "bg-pink-500/15"
    },
    {
        "productName": "Bra Lace Crop top",
        "category": "Fashion",
        "price": "$29.99",
        "image": product6,
        "color": "bg-gray-500/15"
    },
    {
        "productName": "Yellow women shoes",
        "category": "Footwear",
        "price": "$36.87",
        "image": product7,
        "color": "bg-yellow-500/15"
    },
    {
        "productName": "Tote bag Leather Handbag Dolce",
        "category": "Bags",
        "price": "$79.99",
        "image": product8,
        "color": "bg-red-500/15"
    },
    {
        "productName": "Hoodie Jacket Letterman Sleeve Coat",
        "category": "Fashion",
        "price": "$44.49",
        "image": product9,
        "color": "bg-sky-500/15"
    },
    {
        "productName": "Straw hat Cap Cowboy hat Sun hat",
        "category": "Accessories",
        "price": "$24.99",
        "image": product10,
        "color": "bg-orange-500/15"
    },
    {
        "productName": "Sneakers Shoe Nike Basketball",
        "category": "Footwear",
        "price": "$32.00",
        "image": product11,
        "color": "bg-green-500/15"
    },
    {
        "productName": "Jean jacket Denim Levi Strauss & Co. Jeans",
        "category": "Fashion",
        "price": "$39.49",
        "image": product13,
        "color": "bg-primary-500/15"
    }
]
export default productGridData