{{> partials/main }}

<head>

    {{> partials/title-meta title="Customers List" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}
        {{> partials/page-heading title="List View" sub-title="Customers" }}

        <div x-data="customerTable()">
            <div class="grid items-center grid-cols-12 gap-3 xl:gap-5 mb-3">
                <div class="col-span-12 xl:col-span-5 2xl:col-span-7">
                    <h6 class="card-title mb-0">Customer List</h6>
                </div>
                <div class="flex flex-wrap xl:flex-nowrap xl:justify-end col-span-12 gap-3 xl:col-span-7 2xl:col-span-5">
                    <button class="btn btn-red btn-icon" x-show="selectedItems.length > 0" @click="deleteSelectedItems()">
                        <i data-lucide="trash" class="inline-block size-4"></i>
                    </button>
                    <div class="relative group/form">
                        <input type="email" class="ltr:pl-9 rtl:pr-9 form-input ltr:group-[&.right]/form:pr-9 rtl:group-[&.right]/form:pl-9 ltr:group-[&.right]/form:pl-4 rtl:group-[&.right]/form:pr-4" placeholder="Search name, email, gender, etc..." x-model="searchTerm" @input="filterCustomers()">
                        <span class="absolute inset-y-0 flex items-center text-gray-500 dark:text-dark-500 ltr:left-3 rtl:right-3 ltr:group-[&.right]/form:right-3 rtl:group-[&.right]/form:left-3 ltr:group-[&.right]/form:left-auto rtl:group-[&.right]/form:right-auto focus:outline-hidden">
                            <i data-lucide="search" class="size-4"></i>
                        </span>
                    </div>
                    <button class="btn btn-primary" data-modal-target="addCustomerModals" @click="handleModal('showAddCustomerForm')"><i data-lucide="plus" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> New Customer</button>
                </div>
            </div>
            <div>
                <div class="overflow-x-auto">
                    <table class="table border-separate hovered flush border-spacing-y-2 whitespace-nowrap">
                        <tbody class="*:bg-gray-50 dark:*:bg-dark-900 *:rounded-md">
                            <tr class="text-gray-500 bg-gray-100 dark:bg-dark-800 dark:text-dark-500">
                                <th class="!font-medium">
                                    <div class="input-check-group">
                                        <label for="checkboxAll" class="hidden input-check-label"></label>
                                        <input id="checkboxAll" class="input-check input-check-primary" type="checkbox" x-model="selectAll" x-on:click="toggleAll" /></th>
                                    </div>
                                <th x-on:click="sort('customersID')" class="!font-medium cursor-pointer">ID <span x-show="sortBy === 'customersID'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th x-on:click="sort('customersName')" class="!font-medium cursor-pointer">Name <span x-show="sortBy === 'customersName'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th x-on:click="sort('email')" class="!font-medium cursor-pointer">Email <span x-show="sortBy === 'email'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th x-on:click="sort('phoneNumber')" class="!font-medium cursor-pointer">Phone Number <span x-show="sortBy === 'phoneNumber'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th x-on:click="sort('subscriber')" class="!font-medium cursor-pointer">Subscriber <span x-show="sortBy === 'subscriber'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th x-on:click="sort('gender')" class="!font-medium cursor-pointer">Gender <span x-show="sortBy === 'gender'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th x-on:click="sort('location')" class="!font-medium cursor-pointer">Location <span x-show="sortBy === 'location'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th x-on:click="sort('status')" class="!font-medium cursor-pointer">status <span x-show="sortBy === 'status'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th class="!font-medium">Action</th>
                            </tr>
                            <template x-if="displayedProducts.length > 0">
                            <template x-for="(product, index) in displayedProducts" :key="index">
                                <tr>
                                    <td>
                                        <div class="input-check-group">
                                            <label :for="`customer${product.customersID}`" class="hidden input-check-label"></label>
                                            <input :id="`customer${product.customersID}`" class="input-check input-check-primary" type="checkbox" @click="toggleItem(product)" :checked="selectedItems.includes(product)" />
                                        </div>
                                    </td>
                                    <td x-text="product.customersID"></td>
                                    <td>
                                        <div class="flex items-center gap-2">
                                            <img :src="product.image" alt="" class="rounded-full shrink-0 size-8">
                                            <a href="#!" class="text-current link link-primary grow" x-text="product.customersName"></a>
                                        </div>
                                    </td>
                                    <td x-text="product.email"></td>
                                    <td x-text="product.phoneNumber"></td>
                                    <td x-text="product.subscriber"></td>
                                    <td x-text="product.gender"></td>
                                    <td x-text="product.location"></td>
                                    <td>
                                        <span x-text="product.status" :class="{
                                            'badge badge-green': product.status === 'Active',
                                            'badge badge-red': product.status === 'Inactive'
                                        }"></span>
                                    </td>
                                    <td>
                                        <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown">
                                            <button x-ref="button" x-on:click="toggle()" :aria-expanded="open.toString()" type="button" class="flex items-center text-gray-500 dark:text-dark-500">
                                                <i class="ri-more-2-fill"></i>
                                            </button>
                                            <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden">
                                                <ul>
                                                    <li>
                                                        <a href="#!" data-modal-target="overviewCustomerModals" @click="reviewCustomer(product.customersID)" class="dropdown-item">
                                                            <i class="align-middle ltr:mr-2 rtl:ml-2 ri-eye-line"></i> <span>Overview</span>
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a href="#!" data-modal-target="addCustomerModals" @click="editCustomer(product.customersID)" class="dropdown-item">
                                                            <i class="align-middle ltr:mr-2 rtl:ml-2 ri-pencil-line"></i> <span>Edit</span>
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a href="#!" data-modal-target="deleteModal" @click="deleteItem = product.customersID" class="dropdown-item">
                                                            <i class="align-middle ltr:mr-2 rtl:ml-2 ri-delete-bin-line"></i> <span>Delete</span>
                                                        </a>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            </template>
                            </template>
                            <tr>
                                <template x-if="displayedProducts.length == 0">
                                    <td colspan="10" class="!p-8">
                                        <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" class="mx-auto size-12" viewBox="0 0 48 48">
                                            <linearGradient id="SVGID_1__h35ynqzIJzH4_gr1" x1="34.598" x2="15.982" y1="15.982" y2="34.598" gradientUnits="userSpaceOnUse">
                                                <stop offset="0" stop-color="#60e8fe"></stop>
                                                <stop offset=".033" stop-color="#6ae9fe"></stop>
                                                <stop offset=".197" stop-color="#97f0fe"></stop>
                                                <stop offset=".362" stop-color="#bdf5ff"></stop>
                                                <stop offset=".525" stop-color="#dafaff"></stop>
                                                <stop offset=".687" stop-color="#eefdff"></stop>
                                                <stop offset=".846" stop-color="#fbfeff"></stop>
                                                <stop offset="1" stop-color="#fff"></stop>
                                            </linearGradient>
                                            <path fill="url(#SVGID_1__h35ynqzIJzH4_gr1)" d="M40.036,33.826L31.68,25.6c0.847-1.739,1.335-3.684,1.335-5.748c0-7.27-5.894-13.164-13.164-13.164	S6.688,12.582,6.688,19.852c0,7.27,5.894,13.164,13.164,13.164c2.056,0,3.995-0.485,5.728-1.326l3.914,4.015l4.331,4.331	c1.715,1.715,4.496,1.715,6.211,0C41.751,38.321,41.751,35.541,40.036,33.826z"></path>
                                            <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M31.95,25.739l8.086,8.086c1.715,1.715,1.715,4.496,0,6.211l0,0c-1.715,1.715-4.496,1.715-6.211,0	l-4.331-4.331"></path>
                                            <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M7.525,24.511c-1.771-4.694-0.767-10.196,3.011-13.975c3.847-3.847,9.48-4.817,14.228-2.912"></path>
                                            <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M30.856,12.603c3.376,5.114,2.814,12.063-1.688,16.565c-4.858,4.858-12.565,5.129-17.741,0.814"></path>
                                        </svg>
                                        <p class="mt-2 text-center text-gray-500 dark:text-dark-500">No matching records found</p>
                                    </td>
                                </template>
                            </tr>      
                        </tbody>
                    </table>
                </div>
                <div class="grid grid-cols-12 gap-5 my-5 items-center"  x-show="displayedProducts.length !== 0">
                    <div class="col-span-12 md:col-span-6 justify-center md:justify-start flex flex-wrap">
                        <p class="text-gray-500 dark:text-dark-500 ltr:pr-1 rtl:pl-1">Showing <b x-text="showingStart"></b> - <b x-text="showingEnd"></b> of <b x-text="filterCustomer.length"></b> Results</p>
                        <span x-show="selectedItems.length > 0"><b x-text="selectedItems.length != 0 ? selectedItems.length : ''"> </b> Row Selected</span>
                    </div>
                    <div class="col-span-12 md:col-span-6">
                        <div class="flex justify-center md:justify-end pagination pagination-primary">
                            <button @click="prevPage()" :disabled="currentPage === 1" class="pagination-pre">
                                <i data-lucide="chevron-left" class="mr-1 ltr:inline-block rtl:hidden size-4"></i>
                                <i data-lucide="chevron-right" class="ml-1 ltr:hidden rtl:inline-block size-4"></i>
                                Prev
                            </button>
                            <template x-for="page in totalPages" :key="page">
                                <button @click="gotoPage(page)" :class="{ 'active': currentPage === page }" class="pagination-item">
                                    <span x-text="page"></span>
                                </button>
                            </template>
                            <button @click="nextPage()" :disabled="currentPage === totalPages" class="pagination-next">
                                Next
                                <i data-lucide="chevron-right" class="ml-1 ltr:inline-block rtl:hidden size-4"></i>
                                <i data-lucide="chevron-left" class="mr-1 ltr:hidden rtl:inline-block size-4"></i>
                            </button>
                        </div>
                    </div>
                </div>
               
              
            </div>

            <!--Add customers modals-->
            <div id="addCustomerModals" class="!hidden modal show" :class="{'show d-block': showAddCustomerForm || showEditCustomerForm}" x-show="showAddCustomerForm || showEditCustomerForm">
                <div class="modal-wrap modal-center">
                    <div class="p-0 modal-content">
                        <div class="h-20 bg-gray-100 rounded-t-md dark:bg-dark-850">
                        </div>
                        <div class="modal-content">
                            <div class="-mt-16">
                                <label for="logo">
                                    <div class="inline-flex items-center justify-center overflow-hidden bg-gray-100 border border-gray-200 rounded-full cursor-pointer dark:bg-dark-850 dark:border-dark-800 size-24">
                                        <img x-show="customerForm.image" :src="customerForm.image" class="object-cover w-full h-full rounded-full">
                                        <div x-show="!customerForm.image" class="flex flex-col items-center text-gray-500 dark:text-dark-500">
                                            <i data-lucide="upload"></i>
                                        </div>
                                    </div>
                                </label>
                                <div class="hidden mt-4">
                                    <label class="block">
                                        <span class="sr-only">Choose profile photo</span>
                                        <input type="file" name="logo" id="logo" @change="fileChosen" class="block w-full text-sm file:rounded-md focus:outline-0 text-slate-500 file:mr-4 file:py-2 file:px-4 file:border-0 file:text-sm file:font-semibold file:bg-violet-50 file:text-violet-700 hover:file:bg-violet-100" />
                                    </label>
                                </div>
                                <span x-show="errors.image" class="text-red-500" x-text="errors.image"></span>
                            </div>

                            <div class="mt-5">
                                <div class="grid grid-cols-12 gap-5">
                                    <div class="col-span-6">
                                        <label for="firstNameInput" class="form-label">First Name <span class="text-red-500">*</span></label>
                                        <input type="text" id="firstNameInput" class="form-input" placeholder="Enter your first name" x-model="customerForm.firstName" @input="validateField('firstName', customerForm.firstName, 'First name is required.')">
                                        <span x-show="errors.firstName" class="text-red-500" x-text="errors.firstName"></span>
                                    </div>
                                    <div class="col-span-6">
                                        <label for="lastNameInput" class="form-label">Last Name <span class="text-red-500">*</span></label>
                                        <input type="text" id="lastNameInput" class="form-input" placeholder="Enter your last name" x-model="customerForm.lastName" @input="validateField('lastName', customerForm.lastName, 'Last name is required.')">
                                        <span x-show="errors.lastName" class="text-red-500" x-text="errors.lastName"></span>
                                    </div>
                                    <div class="col-span-12">
                                        <label for="emailInput" class="form-label">Email <span class="text-red-500">*</span></label>
                                        <input type="email" id="emailInput" class="form-input" placeholder="Enter your email" x-model="customerForm.email" @input="validateEmailField()">
                                        <span x-show="errors.email" class="text-red-500" x-text="errors.email"></span>
                                    </div>
                                    <div class="col-span-12">
                                        <label for="phoneNumberInput" class="form-label">Phone Number <span class="text-red-500">*</span></label>
                                        <input type="text" id="phoneNumberInput" class="form-input" placeholder="************" x-model="customerForm.phoneNumber" @input="validatePhone()">
                                        <span x-show="errors.phone" class="text-red-500" x-text="errors.phone"></span>
                                    </div>
                                    <div class="col-span-6">
                                        <label for="genderInput" class="form-label">Gender <span class="text-red-500">*</span></label>
                                        <div class="flex items-center gap-3">
                                            <div class="input-radio-group">
                                                <input id="maleRadio" name="gender" class="input-radio input-radio-primary" type="radio" value="Male" x-model="customerForm.gender" :checked="customerForm.gender === 'male'" @change="validateField('gender', customerForm.gender, 'Gender is required.')"/>
                                                <label for="maleRadio" name="gender" class="input-radio-label">Male</label>
                                            </div>
                                            <div class="input-radio-group">
                                                <input id="femaleRadio" name="gender" class="input-radio input-radio-primary" type="radio" value="Female" x-model="customerForm.gender" :checked="customerForm.gender === 'female'" @change="validateField('gender', customerForm.gender, 'Gender is required.')" />
                                                <label for="femaleRadio" name="gender" class="input-radio-label">Female</label>
                                            </div>
                                        </div>
                                        <span x-show="errors.gender" class="text-red-500" x-text="errors.gender"></span>
                                    </div>
                                    <div class="col-span-6">
                                        <label for="subscriberSelect" class="form-label">Subscriber <span class="text-red-500">*</span></label>
                                        <div id="subscriberSelect" x-model="customerForm.subscriber" @change="validateField('subscriber', document.querySelector('#subscriberSelect') , 'Subscriber is required.')" ></div>
                                        <span x-show="errors.subscriber" class="text-red-500" x-text="errors.subscriber"></span>
                                    </div>
                                    <div class="col-span-6">
                                        <label for="locationInput" class="form-label">Location <span class="text-red-500">*</span></label>
                                        <input type="text" id="locationInput" class="form-input" placeholder="Location" x-model="customerForm.location" @input="validateField('location', customerForm.location, 'Location is required.')">
                                        <span x-show="errors.location" class="text-red-500" x-text="errors.location"></span>
                                    </div>
                                    <div class="col-span-6">
                                        <label for="statusSelect" class="form-label">Status <span class="text-red-500">*</span></label>
                                        <div id="statusSelect" x-model="customerForm.status" @change="validateField('status', document.querySelector('#statusSelect') , 'Status is required.')" ></div>
                                        <span x-show="errors.status" class="text-red-500" x-text="errors.status"></span>
                                    </div>
                                    <div class="col-span-12">
                                        <div class="flex justify-end gap-2">
                                            <button type="button" data-modal-close="addCustomerModals" class="btn btn-sub-gray">Cancel</button>
                                            <button class="btn btn-primary" @click="submitForm()" x-text="showAddCustomerForm ? 'Add Customer' : 'Update Customer'"></button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div><!--end-->

            <!--Overview customer modal-->
            <div id="overviewCustomerModals" class="!hidden modal show" x-init="initCustomerData">
                <div class="modal-wrap modal-center">
                    <div class="p-0 modal-content">
                        <div class="h-20 bg-gray-100 dark:bg-dark-850 rounded-t-md">
                        </div>
                        <div class="modal-content">
                            <div class="relative inline-block -mt-16 rounded-full">
                                <img :src="selectedProduct.image" alt="" class="rounded-full size-24">
                                <div class="absolute bottom-1.5 bg-green-500 border-2 border-white dark:border-dark-900 rounded-full size-4 ltr:right-2 rtl:left-2"></div>
                            </div>
                            <div class="mt-5">
                                <div class="overflow-x-auto">
                                    <table class="table flush">
                                        <tbody>
                                            <template x-for="(item, index) in customerData" :key="index">
                                                <tr class="*:!py-1.5">
                                                    <th x-text="item.title" class="!border-0 w-40"></th>
                                                    <td x-text="item.subTitle"></td>
                                                </tr>
                                            </template>
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <div class="flex justify-end gap-2 mt-5">
                                <button type="button" data-modal-close="overviewCustomerModals" class="btn btn-sub-gray">Cancel</button>
                                <button type="submit" class="btn btn-primary" data-modal-close="overviewCustomerModals" data-modal-target="editCustomerModals" @click="editCustomer(selectedProduct.customerID)">Edit Customer</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div><!--end-->

            <!--delete modals-->
            <div id="deleteModal" class="!hidden modal show">
                <div class="modal-wrap modal-xs modal-center">
                    <div class="text-center modal-content p-7">
                        <div class="flex items-center justify-center mx-auto mb-4 text-red-500 rounded-full bg-red-500/10 size-14 backdrop-blur-xl">
                            <i data-lucide="trash-2" class="size-6"></i>
                        </div>
                        <h5 class="mb-4">Are you sure you want to delete this Customer ?</h5>
                        <div class="flex items-center justify-center gap-2">
                            <button class="btn btn-red" @click="deleteCustomer()" data-modal-close="deleteModal">Delete</button>
                            <button data-modal-close="deleteModal" class="btn link link-primary">Cancel</button>
                        </div>
                    </div>
                </div>
            </div><!--end-->
        </div>
    </div>
    {{> partials/footer }}
</div>

{{> partials/vendor-scripts }}

<script type="module" src="assets/js/apps/ecommerce/customer-list.init.js"></script>

<script type="module" src="assets/js/main.js"></script>

</body>
</html>