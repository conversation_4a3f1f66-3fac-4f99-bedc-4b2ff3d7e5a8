{{> partials/main }}

<head>

    {{> partials/title-meta title="School" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}
        {{> partials/page-heading title="School" sub-title="Dashboards" }}

        <div class="grid grid-cols-12 gap-x-space">
            <div class="relative order-1 col-span-12 md:col-span-4 2xl:col-span-2 card">
                <div class="card-body">
                    <div class="flex items-center gap-2 mb-7">
                        <div class="flex items-center justify-center rounded-md bg-primary-500 size-7 text-primary-50">
                            <i data-lucide="graduation-cap" class="size-5"></i>
                        </div>
                        <p class="text-gray-500 dark:text-dark-500">Total Students</p>
                    </div>
                    <h5 class="relative inline-block mb-2 before:absolute before:border-b-2 before:border-primary-500 before:inset-x-0 before:-bottom-1"><span x-data="animatedCounter(1478, 500, 0)" x-init="updateCounter" x-text="Math.round(current)"></span></h5>
                </div>
            </div><!--end col-->
            <div class="relative order-2 col-span-12 md:col-span-4 2xl:col-span-2 card">
                <div class="card-body">
                    <div class="flex items-center gap-2 mb-7">
                        <div class="flex items-center justify-center bg-orange-500 rounded-md size-7 text-orange-50">
                            <i data-lucide="user-round" class="size-5"></i>
                        </div>
                        <p class="text-gray-500 dark:text-dark-500">Total Teachers</p>
                    </div>
                    <h5 class="relative inline-block mb-2 before:absolute before:border-b-2 before:border-orange-500 before:inset-x-0 before:-bottom-1"><span x-data="animatedCounter(120, 500, 0)" x-init="updateCounter" x-text="Math.round(current)"></span></h5>
                </div>
            </div><!--end col-->
            <div class="relative order-3 col-span-12 md:col-span-4 2xl:col-span-2 card">
                <div class="card-body">
                    <div class="flex items-center gap-2 mb-7">
                        <div class="flex items-center justify-center rounded-md bg-sky-500 size-7 text-sky-50">
                            <i data-lucide="book-open" class="size-5"></i>
                        </div>
                        <p class="text-gray-500 dark:text-dark-500">Total Courses</p>
                    </div>
                    <h5 class="relative inline-block mb-2 before:absolute before:border-b-2 before:border-sky-500 before:inset-x-0 before:-bottom-1"><span x-data="animatedCounter(120, 500, 0)" x-init="updateCounter" x-text="Math.round(current)"></span></h5>
                </div>
            </div><!--end col-->
            <div class="relative order-7 col-span-12 2xl:order-4 2xl:col-span-3 2xl:row-span-2 card">
                <div class="flex items-center gap-3 card-header">
                    <h6 class="card-title grow">Course Activities</h6>
                    <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown shrink-0">
                        <button x-ref="button" x-on:click="toggle()" title="dropdown-button" :aria-expanded="open.toString()" type="button" class="flex items-center text-gray-500 dark:text-dark-500">
                            <i data-lucide="ellipsis" class="size-5"></i>
                        </button>
                        <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden" dropdown-position="right">
                            <ul>
                                <li>
                                    <a href="#!" class="dropdown-item">
                                        <span>Weekly</span>
                                    </a>
                                </li>
                                <li>
                                    <a href="#!" class="dropdown-item">
                                        <span>Monthly</span>
                                    </a>
                                </li>
                                <li>
                                    <a href="#!" class="dropdown-item">
                                        <span>Yearly</span>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div x-data="gradientDonutApp" dir="ltr">
                        <div class="!min-h-full" data-chart-colors="[bg-gray-200, bg-primary-500]" data-chart-dark-colors="[bg-dark-850, bg-primary-500]" x-ref="gradientDonutChart"></div>
                    </div>
                </div>
            </div><!--end col-->

            <div class="order-8 col-span-12 2xl:order-5 2xl:col-span-3 2xl:row-span-4 card">
                <div class="card-body">
                    <div class="mb-5">
                        <div x-data="calendar()">
                            <h6 class="mb-3" x-text="monthName + ', ' + year"></h6>
                            <div data-simplebar id="horizontalScroll">
                                <div class="flex items-center gap-2">
                                    <template x-for="day in daysInMonth" :key="day">
                                        <a href="#!" :class="{
                                               'active': day === today,
                                               'flex items-center justify-center font-medium text-lg bg-gray-100 dark:bg-dark-850 rounded-md size-12 shrink-0 [&.active]:bg-primary-500 [&.active]:text-primary-50 [&.active]:border-primary-500': true
                                           }" x-text="day"></a>
                                    </template>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center gap-3 mb-4">
                        <h6 class="grow">Holiday Lists</h6>
                        <a href="#!" class="shrink-0 text-13 link link-primary text-primary-500">View More <i data-lucide="move-right" class="inline-block size-4"></i></a>
                    </div>
                    <!-- Swiper -->
                    <div class="swiper mySwiper" dir="ltr">
                        <div class="swiper-wrapper">
                            <div class="swiper-slide">
                                <div class="relative card">
                                    <div class="flex items-center gap-3 card-body">
                                        <div class="grow">
                                            <h6 class="mb-1"><i data-lucide="circle-dot" class="inline-block text-green-500 ltr:mr-1 rtl:ml-1 size-4 fill-green-500/15"></i> World Braille Day</h6>
                                            <p class="text-gray-500 dark:text-dark-500 text-13">04 Jan, 2024</p>
                                        </div>
                                        <img src="assets/images/school/holiday.png" alt="" class="shrink-0 size-10">
                                    </div>
                                </div>
                            </div>
                            <div class="swiper-slide">
                                <div class="relative card">
                                    <div class="flex items-center gap-3 card-body">
                                        <div class="grow">
                                            <h6 class="mb-1"><i data-lucide="circle-dot" class="inline-block text-green-500 ltr:mr-1 rtl:ml-1 size-4 fill-green-500/15"></i> Earth Hour</h6>
                                            <p class="text-gray-500 dark:text-dark-500 text-13">23 March 2024</p>
                                        </div>
                                        <img src="assets/images/school/holiday.png" alt="" class="shrink-0 size-10">
                                    </div>
                                </div>
                            </div>
                            <div class="swiper-slide">
                                <div class="relative card">
                                    <div class="flex items-center gap-3 card-body">
                                        <div class="grow">
                                            <h6 class="mb-1"><i data-lucide="circle-dot" class="inline-block text-green-500 ltr:mr-1 rtl:ml-1 size-4 fill-green-500/15"></i> Software Freedom Day</h6>
                                            <p class="text-gray-500 dark:text-dark-500 text-13">15 Sep 2024</p>
                                        </div>
                                        <img src="assets/images/school/holiday.png" alt="" class="shrink-0 size-10">
                                    </div>
                                </div>
                            </div>
                            <div class="swiper-slide">
                                <div class="relative card">
                                    <div class="flex items-center gap-3 card-body">
                                        <div class="grow">
                                            <h6 class="mb-1"><i data-lucide="circle-dot" class="inline-block text-green-500 ltr:mr-1 rtl:ml-1 size-4 fill-green-500/15"></i> Inventors Day</h6>
                                            <p class="text-gray-500 dark:text-dark-500 text-13">29 Sep 2024</p>
                                        </div>
                                        <img src="assets/images/school/holiday.png" alt="" class="shrink-0 size-10">
                                    </div>
                                </div>
                            </div>
                            <div class="swiper-slide">
                                <div class="relative card">
                                    <div class="flex items-center gap-3 card-body">
                                        <div class="grow">
                                            <h6 class="mb-1"><i data-lucide="circle-dot" class="inline-block text-green-500 ltr:mr-1 rtl:ml-1 size-4 fill-green-500/15"></i> World Teacher's Day</h6>
                                            <p class="text-gray-500 dark:text-dark-500 text-13">05 Oct 2024</p>
                                        </div>
                                        <img src="assets/images/school/holiday.png" alt="" class="shrink-0 size-10">
                                    </div>
                                </div>
                            </div>
                            <div class="swiper-slide">
                                <div class="relative card">
                                    <div class="flex items-center gap-3 card-body">
                                        <div class="grow">
                                            <h6 class="mb-1"><i data-lucide="circle-dot" class="inline-block text-green-500 ltr:mr-1 rtl:ml-1 size-4 fill-green-500/15"></i> Human Rights Day</h6>
                                            <p class="text-gray-500 dark:text-dark-500 text-13">10 Dec 2024</p>
                                        </div>
                                        <img src="assets/images/school/holiday.png" alt="" class="shrink-0 size-10">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div><!-- end Swiper -->
                    <!--start notice-->
                    <div class="relative">
                        <h6 class="mb-4">Notice Board</h6>
                        <div class="flex flex-col gap-5">
                            <div class="flex items-center gap-3">
                                <img src="assets/images/school/blog/img-05.jpg" alt="" class="object-cover w-16 h-20 rounded-md">
                                <div>
                                    <h6 class="mb-2 line-clamp-2"><a href="#!" class="text-current dark:text-current dark:hover:text-primary-500 link link-primary">Beginning or end of the school year</a></h6>
                                    <p class="text-gray-500 dark:text-dark-500">By Erwin Legros</p>
                                </div>
                            </div>
                            <div class="flex items-center gap-3">
                                <img src="assets/images/school/blog/img-03.jpg" alt="" class="object-cover w-16 h-20 rounded-md">
                                <div>
                                    <h6 class="mb-2 line-clamp-2"><a href="#!" class="text-current dark:text-current dark:hover:text-primary-500 link link-primary">Motivational or inspirational quotes</a></h6>
                                    <p class="text-gray-500 dark:text-dark-500">By Ardith Bode</p>
                                </div>
                            </div>
                            <div class="flex items-center gap-3">
                                <img src="assets/images/school/blog/img-04.jpg" alt="" class="object-cover w-16 h-20 rounded-md">
                                <div>
                                    <h6 class="mb-2 line-clamp-2"><a href="#!" class="text-current dark:text-current dark:hover:text-primary-500 link link-primary">Examination & Parent - Teacher Meeting</a></h6>
                                    <p class="text-gray-500 dark:text-dark-500">By Gerhard Boyle</p>
                                </div>
                            </div>
                            <div class="flex items-center gap-3">
                                <img src="assets/images/school/blog/img-02.jpg" alt="" class="object-cover w-16 h-20 rounded-md">
                                <div>
                                    <h6 class="mb-2 line-clamp-2"><a href="#!" class="text-current dark:text-current dark:hover:text-primary-500 link link-primary">Elementary school student activities</a></h6>
                                    <p class="text-gray-500 dark:text-dark-500">By Maci Crist</p>
                                </div>
                            </div>
                            <div class="flex items-center gap-3">
                                <img src="assets/images/school/blog/img-01.jpg" alt="" class="object-cover w-16 h-20 rounded-md">
                                <div>
                                    <h6 class="mb-2 line-clamp-2"><a href="#!" class="text-current dark:text-current dark:hover:text-primary-500 link link-primary">Random acts of kindness board compare and contrast</a></h6>
                                    <p class="text-gray-500 dark:text-dark-500">By Ruthie Blanda</p>
                                </div>
                            </div>
                            <div class="flex items-center gap-3">
                                <img src="assets/images/school/blog/img-06.jpg" alt="" class="object-cover w-16 h-20 rounded-md">
                                <div>
                                    <h6 class="mb-2 line-clamp-2"><a href="#!" class="text-current dark:text-current dark:hover:text-primary-500 link link-primary">A Book a Day Keeps the Monsters Away</a></h6>
                                    <p class="text-gray-500 dark:text-dark-500">By Magnus Miller</p>
                                </div>
                            </div>
                            <div class="flex items-center gap-3">
                                <img src="assets/images/school/blog/img-07.jpg" alt="" class="object-cover w-16 h-20 rounded-md">
                                <div>
                                    <h6 class="mb-2 line-clamp-2"><a href="#!" class="text-current dark:text-current dark:hover:text-primary-500 link link-primary">Student Work Coming Bulletin Board</a></h6>
                                    <p class="text-gray-500 dark:text-dark-500">By Jeremie Thiel</p>
                                </div>
                            </div>
                            <div class="flex items-center gap-3">
                                <img src="assets/images/school/blog/img-08.jpg" alt="" class="object-cover w-16 h-20 rounded-md">
                                <div>
                                    <h6 class="mb-2 line-clamp-2"><a href="#!" class="text-current dark:text-current dark:hover:text-primary-500 link link-primary">Add a Little Sunshine to Someone’s Day</a></h6>
                                    <p class="text-gray-500 dark:text-dark-500">By Treva Trantow</p>
                                </div>
                            </div>
                        </div>
                    </div><!--end notice-->
                </div>
            </div><!--end col-->
            <div class="relative order-4 col-span-12 2xl:order-6 md:col-span-4 2xl:col-span-2 card">
                <div class="card-body">
                    <div class="flex items-center gap-2 mb-7">
                        <div class="flex items-center justify-center bg-yellow-500 rounded-md size-7 text-yellow-50">
                            <i data-lucide="computer" class="size-5"></i>
                        </div>
                        <p class="text-gray-500 dark:text-dark-500">Class Rooms</p>
                    </div>
                    <h5 class="relative inline-block mb-2 before:absolute before:border-b-2 before:border-yellow-500 before:inset-x-0 before:-bottom-1"><span x-data="animatedCounter(65, 500, 0)" x-init="updateCounter" x-text="Math.round(current)"></span></h5>
                </div>
            </div><!--end col-->
            <div class="relative order-5 col-span-12 2xl:order-7 md:col-span-4 2xl:col-span-2 card">
                <div class="card-body">
                    <div class="flex items-center gap-2 mb-7">
                        <div class="flex items-center justify-center bg-pink-500 rounded-md size-7 text-pink-50">
                            <i data-lucide="hand-coins" class="size-5"></i>
                        </div>
                        <p class="text-gray-500 dark:text-dark-500">Total Earnings</p>
                    </div>
                    <h5 class="relative inline-block mb-2 before:absolute before:border-b-2 before:border-pink-500 before:inset-x-0 before:-bottom-1">$<span x-data="animatedCounter(487, 500, 0)" x-init="updateCounter" x-text="Math.round(current)"></span>M</h5>
                </div>
            </div><!--end col-->
            <div class="relative order-6 col-span-12 2xl:order-8 md:col-span-4 2xl:col-span-2 card">
                <div class="card-body">
                    <div class="flex items-center gap-2 mb-7">
                        <div class="flex items-center justify-center bg-purple-500 rounded-md size-7 text-purple-50">
                            <i data-lucide="medal" class="size-5"></i>
                        </div>
                        <p class="text-gray-500 dark:text-dark-500">Awards</p>
                    </div>
                    <h5 class="relative inline-block mb-2 before:absolute before:border-b-2 before:border-purple-500 before:inset-x-0 before:-bottom-1"><span x-data="animatedCounter(30, 500, 0)" x-init="updateCounter" x-text="Math.round(current)"></span>+</h5>
                </div>
            </div><!--end col-->
            <div class="order-9 col-span-12 md:col-span-6 2xl:col-span-6 card">
                <div class="flex items-center gap-3 card-header">
                    <h6 class="card-title grow">Total Students</h6>
                    <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown shrink-0">
                        <button x-ref="button" x-on:click="toggle()" title="dropdown-button" :aria-expanded="open.toString()" type="button" class="flex items-center text-gray-500 dark:text-dark-500">
                            <i data-lucide="ellipsis" class="size-5"></i>
                        </button>
                        <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden" dropdown-position="right">
                            <ul>
                                <li>
                                    <a href="#!" class="dropdown-item">
                                        <span>Weekly</span>
                                    </a>
                                </li>
                                <li>
                                    <a href="#!" class="dropdown-item">
                                        <span>Monthly</span>
                                    </a>
                                </li>
                                <li>
                                    <a href="#!" class="dropdown-item">
                                        <span>Yearly</span>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div x-data="dumbbellColumnApp" dir="ltr">
                        <div class="!min-h-full" data-chart-colors="[bg-primary-500, bg-red-500]" x-ref="dumbbellColumnChart"></div>
                    </div>
                </div>
            </div>
            <div class="order-10 col-span-12 md:col-span-6 2xl:col-span-3 card">
                <div class="card-header">
                    <h6 class="card-title">Upcoming Test</h6>
                </div>
                <div class="space-y-3 card-body">
                    <div class="flex items-center gap-3">
                        <div class="flex items-center justify-center rounded-md bg-orange-500/15 size-12">
                            <img src="assets/images/school/computer.png" loading="lazy" alt="" class="h-6">
                        </div>
                        <div class="grow">
                            <h6 class="mb-1"><a href="#!">Basic Computer</a></h6>
                            <p class="text-gray-500 dark:text-dark-500">Class 9</p>
                        </div>
                        <p class="text-red-500 shrink-0">26 Dec</p>
                    </div>
                    <div class="flex items-center gap-3">
                        <div class="flex items-center justify-center rounded-md bg-red-500/15 size-12">
                            <img src="assets/images/school/chemical.png" loading="lazy" alt="" class="h-6">
                        </div>
                        <div class="grow">
                            <h6 class="mb-1"><a href="#!">Science Part 2 Test</a></h6>
                            <p class="text-gray-500 dark:text-dark-500">Class 11</p>
                        </div>
                        <p class="text-red-500 shrink-0">21 Dec</p>
                    </div>
                    <div class="flex items-center gap-3">
                        <div class="flex items-center justify-center rounded-md bg-red-500/15 size-12">
                            <img src="assets/images/school/dictionary.png" loading="lazy" alt="" class="h-6">
                        </div>
                        <div class="grow">
                            <h6 class="mb-1"><a href="#!">English Grammar</a></h6>
                            <p class="text-gray-500 dark:text-dark-500">Class 12</p>
                        </div>
                        <p class="text-red-500 shrink-0">15 Dec</p>
                    </div>
                    <div class="flex items-center gap-3">
                        <div class="flex items-center justify-center rounded-md bg-primary-500/15 size-12">
                            <img src="assets/images/school/chemical.png" loading="lazy" alt="" class="h-6">
                        </div>
                        <div class="grow">
                            <h6 class="mb-1"><a href="#!">Science Part 1 Test</a></h6>
                            <p class="text-gray-500 dark:text-dark-500">Class 11</p>
                        </div>
                        <p class="text-red-500 shrink-0">03 Dec</p>
                    </div>
                    <div class="flex items-center gap-3">
                        <div class="flex items-center justify-center rounded-md bg-purple-500/15 size-12">
                            <img src="assets/images/school/change-management.png" loading="lazy"  alt="" class="h-6">
                        </div>
                        <div class="grow">
                            <h6 class="mb-1"><a href="#!">Management</a></h6>
                            <p class="text-gray-500 dark:text-dark-500">Class 12</p>
                        </div>
                        <p class="text-red-500 shrink-0">24 Nov</p>
                    </div>
                </div>
            </div>
            <div class="order-11 col-span-12 2xl:col-span-9 card" x-data="emailsTable()">
                <div class="flex items-center gap-3 card-header">
                    <h6 class="card-title grow">Top Score</h6>
                    <div class="shrink-0">
                        <a href="#!" class="py-1 px-2.5 btn btn-primary">
                            View All
                            <i data-lucide="move-right" class="ml-1 ltr:inline-block rtl:hidden size-4"></i>
                            <i data-lucide="move-left" class="mr-1 rtl:inline-block ltr:hidden size-4"></i>
                        </a>
                    </div>
                </div>
                <div class="pt-0 card-body">
                    <div class="overflow-x-auto table-box">
                        <table class="table whitespace-nowrap flush">
                            <tbody>
                                <template x-for="(email, index) in displayedEmails" :key="index">
                                    <tr>
                                        <td x-text="email.studentsName"></td>
                                        <td>
                                            <span x-text="email.marks"></span>/600
                                        </td>
                                        <td x-text="email.resultDate"></td>
                                        <td x-text="email.grade"></td>
                                        <td>
                                            <span x-text="email.passFail" :class="{
                                            'badge badge-green': email.passFail === 'Pass',
                                            'badge badge-orange': email.passFail === 'Fail',
                                        }"></span>
                                        </td>
                                    </tr>
                                </template>
                            </tbody>
                        </table>
                    </div>
                    <div class="grid items-center grid-cols-12 gap-space mt-space">
                        <div class="col-span-12 text-center md:col-span-6 ltr:md:text-left rtl:md:text-right">
                            <p class="text-gray-500 dark:text-dark-500">Showing <b x-text="showingStart"></b> - <b x-text="showingEnd"></b> of <b x-text="emails.length"></b> Results</p>
                        </div>
                        <div class="col-span-12 md:col-span-6">
                            <div class="flex justify-center md:justify-end pagination pagination-primary">
                                <button @click="prevPage()" :disabled="currentPage === 1" class="pagination-pre">
                                    <i data-lucide="chevron-left" class="mr-1 ltr:inline-block rtl:hidden size-4"></i>
                                    <i data-lucide="chevron-right" class="ml-1 rtl:inline-block ltr:hidden size-4"></i>
                                    Prev
                                </button>
                                <template x-for="page in totalPages" :key="page">
                                    <button @click="gotoPage(page)" :class="{ 'active': currentPage === page }" class="pagination-item">
                                        <span x-text="page"></span>
                                    </button>
                                </template>
                                <button @click="nextPage()" :disabled="currentPage === totalPages" class="pagination-next">
                                    Next
                                    <i data-lucide="chevron-right" class="ml-1 ltr:inline-block rtl:hidden size-4"></i>
                                    <i data-lucide="chevron-left" class="mr-1 rtl:inline-block ltr:hidden size-4"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div><!--end col-->
            <div class="order-12 col-span-12 xl:col-span-8 2xl:col-span-9 card">
                <div class="card-header">
                    <h6 class="card-title">
                        Continue Watching
                        <i data-lucide="move-right" class="ml-1 ltr:inline-block rtl:hidden size-4"></i>
                        <i data-lucide="move-left" class="mr-1 rtl:inline-block ltr:hidden size-4"></i>
                    </h6>
                </div>
                <div class="card-body">
                    <div class="grid grid-cols-12 gap-space">
                        <div class="col-span-12 lg:col-span-4">
                            <div class="aspect-video">
                                <iframe class="w-full rounded-md aspect-video" src="https://www.youtube.com/embed/_x9lsSPW4rA?si=dldJWZYL7u6URBi5" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>
                            </div>
                            <div class="mt-3">
                                <h6 class="mb-1 truncate"><a href="#!">Domiex - Admin & Dashboard Template Introduction</a></h6>
                                <p class="text-xs text-gray-500 dark:text-dark-500">Domiex Intro</p>
                            </div>
                        </div>
                        <div class="col-span-12 lg:col-span-4">
                            <div class="aspect-video">
                                <iframe class="w-full rounded-md aspect-video" src="https://www.youtube.com/embed/mSC6GwizOag?si=Dqcl3RgGrfRyqmHo" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>
                            </div>
                            <div class="mt-3">
                                <h6 class="mb-1 truncate"><a href="#!">What's new in Tailwind CSS v3.0?</a></h6>
                                <p class="text-xs text-gray-500 dark:text-dark-500">TailwindCSS</p>
                            </div>
                        </div>
                        <div class="col-span-12 lg:col-span-4">
                            <div class="aspect-video">
                                <iframe class="w-full rounded-md aspect-video" src="https://www.youtube.com/embed/RZ9cWr3tY9w?si=J6KavpQC6n9gaC64" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>
                            </div>
                            <div class="mt-3">
                                <h6 class="mb-1 truncate"><a href="#!">Controlling Stacking Contexts with Isolation Utilities</a></h6>
                                <p class="text-xs text-gray-500 dark:text-dark-500">TailwindCSS</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div><!--end col-->
            <div class="col-span-12 text-center bg-gray-100 order-13 dark:bg-dark-850 xl:col-span-4 2xl:col-span-3 card">
                <div class="card-body">
                    <h5 class="mb-2">Join the community and find out more information</h5>
                    <button type="button" class="btn btn-green">Explore Now</button>
                    <div class="mt-5">
                        <img src="assets/images/dashboards/school.png" loading="lazy" alt="" class="mx-auto h-44">
                    </div>
                </div>
            </div>
        </div>

    </div>
    {{> partials/footer }}
</div>


{{> partials/vendor-scripts }}

<script src="assets/libs/swiper/swiper-bundle.min.js"></script>

<script type="module" src="assets/js/dashboards/school.init.js"></script>

<script type="module" src="assets/js/main.js"></script>

</body>
</html>