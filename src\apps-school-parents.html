{{> partials/main }}

<head>

    {{> partials/title-meta title="List View" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}
{{> partials/page-heading title="List View" sub-title="Parents" }}

<div class="grid grid-cols-12 gap-x-space" x-data="parentsTable()">
    <div class="col-span-12 card">
        <div class="card-header">
            <div class="grid items-center gap-3 grid-cols-12">
                <div class="col-span-12 md:col-span-9 lg:col-span-5 xxl:col-span-3">
                    <div class="relative group/form grow">
                        <input type="email" class="ltr:pl-9 rtl:pr-9 form-input ltr:group-[&.right]/form:pr-9 rtl:group-[&.right]/form:pl-9 ltr:group-[&.right]/form:pl-4 rtl:group-[&.right]/form:pr-4" placeholder="Search student, class etc. ..." x-model="searchTerm" @input="filteredParents()">
                        <button class="absolute inset-y-0 flex items-center ltr:left-3 rtl:right-3 ltr:group-[&.right]/form:right-3 rtl:group-[&.right]/form:left-3 ltr:group-[&.right]/form:left-auto rtl:group-[&.right]/form:right-auto focus:outline-hidden">
                            <i data-lucide="search" class="text-gray-500 dark:text-dark-500 size-4 fill-gray-100 dark:fill-dark-850"></i>
                        </button>
                    </div>
                </div><!--end col-->
                <div class="col-span-12 md:col-span-3 lg:col-span-3 lg:col-start-10 xxl:col-span-2 xxl:col-start-11 ltr:md:text-right rtl:md:text-left">
                    <button class="btn btn-primary shrink-0" @click="handleModal('showAddParentsForm')" data-modal-target="parentsCreateModal"><i data-lucide="circle-plus" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> Add Parents</button>
                </div><!--end col-->
            </div><!--end grid-->
        </div>
        <div class="pt-0 card-body">
            <div>
                <div class="overflow-x-auto table-box whitespace-nowrap">
                    <table class="table flush">
                        <tbody>
                            <tr class="text-gray-500 bg-gray-100 dark:bg-dark-850 dark:text-dark-500">
                                <th x-on:click="sort('parentsName')" class="!font-medium cursor-pointer">Parents Name <span x-show="sortBy === 'parentsName'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th x-on:click="sort('studentName')" class="!font-medium cursor-pointer">Student Name <span x-show="sortBy === 'studentName'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th x-on:click="sort('relation')" class="!font-medium cursor-pointer">Relation <span x-show="sortBy === 'relation'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th x-on:click="sort('occupation')" class="!font-medium cursor-pointer">Occupation <span x-show="sortBy === 'occupation'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th x-on:click="sort('gender')" class="!font-medium cursor-pointer">Gender <span x-show="sortBy === 'gender'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th x-on:click="sort('email')" class="!font-medium cursor-pointer">Email <span x-show="sortBy === 'email'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th x-on:click="sort('phone')" class="!font-medium cursor-pointer">Phone <span x-show="sortBy === 'phone'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th x-on:click="sort('address')" class="!font-medium cursor-pointer">Address <span x-show="sortBy === 'address'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th class="!font-medium">Action</th>
                            </tr>
                            <template x-for="(student, index) in displayedParents" :key="index">
                                <tr class="*:px-3 *:py-2.5">
                                    <td x-text="student.parentsName"></td>
                                    <td>
                                        <div class="flex items-center gap-3">
                                            <div class="relative text-gray-500 bg-gray-100 rounded-full size-8 dark:bg-dark-850 dark:text-dark-500">
                                                <img x-show="student.image" :src="student.image" alt="" class="rounded-full" @error="removeImage($event)">
                                                <span x-show="!student.image" x-text="student.avatarText" class="absolute inset-0 flex items-center justify-center text-xs font-semibold text-gray-500 bg-gray-100 rounded-full dark:bg-dark-850 dark:text-dark-500"></span>
                                            </div>
                                            <div>
                                                <h6><a href="#!" x-text="student.studentName"></a></h6>
                                            </div>
                                        </div>
                                    </td>
                                    <td x-text="student.relation"></td>
                                    <td x-text="student.occupation"></td>
                                    <td x-text="student.gender"></td>
                                    <td x-text="student.email"></td>
                                    <td x-text="`+ ${student.phone}`"></td>
                                    <td x-text="student.address"></td>
                                    <td>
                                        <div class="flex items-center gap-2">
                                            <button class="btn btn-sub-gray btn-icon !size-8 rounded-md" @click="editParents(student.studentID)" data-modal-target="parentsCreateModal"><i class="ri-pencil-line"></i></button>
                                            <button class="btn btn-sub-red btn-icon !size-8 rounded-md" data-modal-target="deleteModal" @click="deleteParent = student.parentsName"><i class="ri-delete-bin-line"></i></button>
                                        </div>
                                    </td>
                                </tr>
                            </template>
                            <tr>
                                <template x-if="displayedParents.length == 0">
                                    <td colspan="10" class="!p-8">
                                        <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" class="mx-auto size-12" viewBox="0 0 48 48">
                                            <linearGradient id="SVGID_1__h35ynqzIJzH4_gr1" x1="34.598" x2="15.982" y1="15.982" y2="34.598" gradientUnits="userSpaceOnUse">
                                                <stop offset="0" stop-color="#60e8fe"></stop>
                                                <stop offset=".033" stop-color="#6ae9fe"></stop>
                                                <stop offset=".197" stop-color="#97f0fe"></stop>
                                                <stop offset=".362" stop-color="#bdf5ff"></stop>
                                                <stop offset=".525" stop-color="#dafaff"></stop>
                                                <stop offset=".687" stop-color="#eefdff"></stop>
                                                <stop offset=".846" stop-color="#fbfeff"></stop>
                                                <stop offset="1" stop-color="#fff"></stop>
                                            </linearGradient>
                                            <path fill="url(#SVGID_1__h35ynqzIJzH4_gr1)" d="M40.036,33.826L31.68,25.6c0.847-1.739,1.335-3.684,1.335-5.748c0-7.27-5.894-13.164-13.164-13.164	S6.688,12.582,6.688,19.852c0,7.27,5.894,13.164,13.164,13.164c2.056,0,3.995-0.485,5.728-1.326l3.914,4.015l4.331,4.331	c1.715,1.715,4.496,1.715,6.211,0C41.751,38.321,41.751,35.541,40.036,33.826z"></path>
                                            <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M31.95,25.739l8.086,8.086c1.715,1.715,1.715,4.496,0,6.211l0,0c-1.715,1.715-4.496,1.715-6.211,0	l-4.331-4.331"></path>
                                            <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M7.525,24.511c-1.771-4.694-0.767-10.196,3.011-13.975c3.847-3.847,9.48-4.817,14.228-2.912"></path>
                                            <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M30.856,12.603c3.376,5.114,2.814,12.063-1.688,16.565c-4.858,4.858-12.565,5.129-17.741,0.814"></path>
                                        </svg>
                                        <p class="mt-2 text-center text-gray-500 dark:text-dark-500">No matching records found</p>
                                    </td>
                                </template>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="grid grid-cols-12 gap-5 mt-5 items-center" x-show="displayedParents.length > 0">
                    <div class="col-span-12 text-center md:col-span-6 ltr:md:text-left rtl:md:text-right">
                        <p class="text-gray-500 dark:text-dark-500">Showing <b x-text="showingStart"></b> - <b x-text="showingEnd"></b> of
                            <b x-text="filterParents.length"></b> Results
                        </p>
                    </div>
                    <div class="col-span-12 md:col-span-6">
                        <div class="flex justify-center md:justify-end pagination pagination-primary">
                            <button @click="prevPage()" :disabled="currentPage === 1" class="pagination-pre">
                                <i data-lucide="chevron-left" class="mr-1 ltr:inline-block rtl:hidden size-4"></i>
                                <i data-lucide="chevron-right" class="ml-1 ltr:hidden rtl:inline-block size-4"></i>
                                Prev
                            </button>
                            <template x-for="page in totalPages" :key="page">
                                <button @click="gotoPage(page)" :class="{ 'active': currentPage === page }" class="pagination-item">
                                    <span x-text="page"></span>
                                </button>
                            </template>
                            <button @click="nextPage()" :disabled="currentPage === totalPages" class="pagination-next">
                                Next
                                <i data-lucide="chevron-right" class="ml-1 ltr:inline-block rtl:hidden size-4"></i>
                                <i data-lucide="chevron-left" class="mr-1 ltr:hidden rtl:inline-block size-4"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div><!--end col-->
    <!--create parents modals-->
    <div id="parentsCreateModal" class="!hidden modal show" :class="{ 'show block': showAddParentsForm || showEditParentsForm }" x-show="showAddParentsForm || showEditParentsForm">
        <div class="modal-wrap modal-center">
            <div class="p-2 modal-content">
                <div class="h-24 p-5 rounded-t-sm ltr:bg-gradient-to-r rtl:bg-gradient-to-l from-primary-200 via-pink-500/20 to-green-500/20">
                </div>
                <div class="p-4">
                    <div class="-mt-16">
                        <label for="logo">
                            <div class="inline-flex items-center justify-center overflow-hidden bg-gray-100 border-2 border-white rounded-full cursor-pointer dark:border-dark-900 dark:bg-dark-850 size-24">
                                <img x-show="parentsForm.image" :src="parentsForm.image" class="object-cover w-full h-full rounded-full">
                                <div x-show="!parentsForm.image" class="flex flex-col items-center text-gray-500 dark:text-dark-500">
                                    <i data-lucide="upload"></i>
                                </div>
                            </div>
                        </label>
                        <div class="hidden mt-4">
                            <label class="block">
                                <span class="sr-only">Choose profile photo</span>
                                <input type="file" name="logo" id="logo" @change="fileChosen" class="block w-full text-sm file:rounded-md focus:outline-0 text-slate-500 file:mr-4 file:py-2 file:px-4 file:border-0 file:text-sm file:font-semibold file:bg-violet-50 file:text-violet-700 hover:file:bg-violet-100" />
                            </label>
                        </div>
                    </div>
                    <div class="grid grid-cols-12 gap-4 mt-space">
                        <div class="col-span-12">
                            <label for="parentsInput" class="form-label">Parents Name</label>
                            <input type="text" id="parentsInput" class="form-input" placeholder="Parents Name" x-model="parentsForm.parentsName" @input="validateField('parentsName' , parentsForm.parentsName , 'Parents Name is required.')">
                            <span x-show="errors.parentsName" class="text-red-500" x-text="errors.parentsName"></span>
                        </div>
                        <div class="col-span-12">
                            <label for="studentNameInput" class="form-label">Student Name</label>
                            <input type="text" id="studentNameInput" class="form-input" placeholder="Student name" x-model="parentsForm.studentName" @input="validateField('studentName' , parentsForm.studentName , 'Student Name is required.')">
                            <span x-show="errors.studentName" class="text-red-500" x-text="errors.studentName"></span>
                        </div>
                        <div class="col-span-6">
                            <label for="relationInput" class="form-label">Relation</label>
                            <input type="text" id="relationInput" class="form-input" placeholder="Relation" x-model="parentsForm.relation" @input="validateField('relation' , parentsForm.relation , 'Relation is required.')">
                            <span x-show="errors.relation" class="text-red-500" x-text="errors.relation"></span>
                        </div>
                        <div class="col-span-6">
                            <label for="genderSelect" class="form-label">Gender</label>
                            <div id="genderSelect" x-model="parentsForm.gender" @change="validateField('gender' , document.querySelector('#genderSelect') , 'Gender is required.')" ></div>
                            <span x-show="errors.gender" class="text-red-500" x-text="errors.gender"></span>
                        </div>
                        <div class="col-span-12">
                            <label for="occupationInput" class="form-label">Occupation</label>
                            <input type="text" id="occupationInput" class="form-input" placeholder="Occupation" x-model="parentsForm.occupation" @input="validateField('occupation' , parentsForm.occupation , 'Occupation is required.')">
                            <span x-show="errors.occupation" class="text-red-500" x-text="errors.occupation"></span>
                        </div>
                        <div class="col-span-6">
                            <label for="emailInput" class="form-label">Email</label>
                            <input type="email" id="emailInput" class="form-input" placeholder="Enter your email" x-model="parentsForm.email" @input="validateEmailField()">
                            <span x-show="errors.email" class="text-red-500" x-text="errors.email"></span>
                        </div>
                        <div class="col-span-6">
                            <label for="phonenumberInput" class="form-label">Phone Number</label>
                            <input type="tel" id="phonenumberInput" class="form-input" placeholder="Enter your phone number" x-model="parentsForm.phone" @input="validatePhone()">
                            <span x-show="errors.phone" class="text-red-500" x-text="errors.phone"></span>
                        </div>
                        <div class="flex items-center justify-end col-span-12 gap-2">
                            <button type="button" class="btn btn-active-red" @click="resetForm()" data-modal-close="parentsCreateModal">
                                <i data-lucide="x" class="inline-block size-4"></i> <span class="align-baseline">Close</span>
                            </button>
                            <button type="button" class="btn btn-primary" x-text=" showAddParentsForm ? 'Add Parents' : 'Update Parents'" @click="submitForm()">
                                Add Parents
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!--delete modal-->
    <div id="deleteModal" class="!hidden modal show">
        <div class="modal-wrap modal-xs modal-center">
            <div class="text-center modal-content p-7">
                <div class="flex items-center justify-center mx-auto mb-4 text-red-500 rounded-full bg-red-500/10 size-14 backdrop-blur-xl">
                    <i data-lucide="trash-2" class="size-6"></i>
                </div>
                <h5 class="mb-4">Are you sure you want to delete this student ?</h5>
                <div class="flex items-center justify-center gap-2">
                    <button class="btn btn-red" @click="deleteParents()" data-modal-close="deleteModal">Delete</button>
                    <button data-modal-close="deleteModal" class="btn link link-primary">Cancel</button>
                </div>
            </div>
        </div>
    </div><!--end-->

</div><!--end grid-->

</div>
{{> partials/footer }}
</div>
</div>

{{> partials/vendor-scripts }}

<script type="module" src="assets/js/apps/school/parents/list-view.init.js"></script>

<script type="module" src="assets/js/main.js"></script>

</body>
</html>