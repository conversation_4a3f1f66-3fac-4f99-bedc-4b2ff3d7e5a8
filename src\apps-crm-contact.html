{{> partials/main }}

<head>

    {{> partials/title-meta title="Contact" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}
{{> partials/page-heading title="Contact" sub-title="CRM" }}
<div x-data="contactTable()">
    <div class="card">
        <div class="card-header">
            <div class="flex flex-wrap justify-between gap-5">
                <div>
                    <div class="relative group/form">
                        <input type="text" class="ltr:pl-9 rtl:pr-9 form-input ltr:group-[&.right]/form:pr-9 rtl:group-[&.right]/form:pl-9 ltr:group-[&.right]/form:pl-4 rtl:group-[&.right]/form:pr-4" placeholder="Search for ..." @input="filterContact" x-model="searchTerm">
                        <div class="absolute inset-y-0 flex items-center text-gray-500 dark:text-dark-500 ltr:left-3 rtl:right-3 ltr:group-[&.right]/form:right-3 rtl:group-[&.right]/form:left-3 ltr:group-[&.right]/form:left-auto rtl:group-[&.right]/form:right-auto focus:outline-hidden">
                            <i data-lucide="search" class="size-4"></i>
                        </div>
                    </div>
                </div>
                <div>
                    <div class="flex flex-wrap gap-2">
                        <button class="btn btn-red btn-icon " x-show="selectedItems.length > 0" @click="deleteSelectedItems()">
                            <i data-lucide="trash" class="inline-block size-4"></i>
                        </button>
                        <button type="button" class="btn btn-sub-gray">
                            <i data-lucide="download" class="inline-block size-4"></i>
                            <span class="align-baseline">Export</span>
                        </button>
                        <button type="button" class="btn btn-primary" @click="handleModal('showAddContactForm')" data-modal-target="contactCreateModal">
                            <i data-lucide="plus" class="inline-block size-4"></i>
                            <span class="align-baseline">Add Contact</span>
                        </button>
                        <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown shrink-0">
                            <button x-ref="button" x-on:click="toggle()" :aria-expanded="open.toString()" type="button" title="dropdown-button" class="btn btn-sub-gray">
                                <i data-lucide="filter" class="inline-block ltr:mr-1 rtl:ml-1 align-center size-4"></i> Sort By
                            </button>

                            <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu !w-64" dropdown-position="right">
                                <ul>
                                    <li>
                                        <a href="#!" x-on:click="init , sort(null) " class="dropdown-item">
                                            No Sorting
                                        </a>
                                    </li>
                                    <li>
                                        <a href="#!" x-on:click="sort('contactName')" class="dropdown-item">
                                            Alphabetical (A -> Z)
                                        </a>
                                    </li>
                                    <li>
                                        <a href="#!" x-on:click="sort('contactName')" class="dropdown-item">
                                            Reverse Alphabetical (Z -> A)
                                        </a>
                                    </li>
                                    <li>
                                        <a href="#!" x-on:click="sort('status')" class="dropdown-item">
                                            Status
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div>
                <div class="overflow-x-auto">
                    <table class="table whitespace-nowrap">
                        <tbody>
                            <tr class="text-gray-500 bg-gray-100 dark:bg-dark-850 dark:text-dark-500">
                                <th class="!font-medium">
                                    <label for="checkboxAll" class="hidden input-check-label"></label><input id="checkboxAll" class="input-check input-check-primary" type="checkbox" x-model="selectAll" x-on:click="toggleAll" /></th>
                                <th x-on:click="sort('contactID')" class="!font-medium cursor-pointer">ID <span x-show="sortBy === 'contactID'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th x-on:click="sort('contactName')" class="!font-medium cursor-pointer">Name <span x-show="sortBy === 'contactName'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th x-on:click="sort('company')" class="!font-medium cursor-pointer">Company <span x-show="sortBy === 'company'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th x-on:click="sort('role')" class="!font-medium cursor-pointer">Role <span x-show="sortBy === 'role'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th x-on:click="sort('email')" class="!font-medium cursor-pointer">Email <span x-show="sortBy === 'email'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th x-on:click="sort('website')" class="!font-medium cursor-pointer">Website <span x-show="sortBy === 'website'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th x-on:click="sort('status')" class="!font-medium cursor-pointer">Status <span x-show="sortBy === 'status'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th class="!font-medium">Action</th>
                            </tr>
                            <template x-if="displayedContacts.length > 0">
                                <template x-for="(contact, index) in displayedContacts" :key="index">
                                    <tr>
                                        <td>
                                            <div class="flex items-center">
                                                <label :for="`contact${index}`" class="hidden input-check-label"></label>
                                                <input :id="`contact${index}`" class="input-check input-check-primary" type="checkbox" @click="toggleItem(contact)" :checked="selectedItems.includes(contact)" />
                                            </div>
                                        </td>
                                        <td x-text="contact.contactID"></td>
                                        <td>
                                            <div class="flex items-center gap-2">
                                                <img :src="contact.image" alt="" class="rounded-full shrink-0 size-9">
                                                <div class="overflow-hidden grow">
                                                    <h6 class="truncate"><a href="#!" data-modal-target="contactOverviewModal" class="text-current link link-primary" x-text="contact.contactName" ></a></h6>
                                                    <p x-text="contact.phoneNumber" class="text-sm text-gray-500 truncate dark:text-dark-500"></p>
                                                </div>
                                            </div>
                                        </td>
                                        <td x-text="contact.company"></td>
                                        <td x-text="contact.role"></td>
                                        <td x-text="contact.email"></td>
                                        <td><a href="#!" class="badge badge-gray" x-text="contact.website"></a></td>
                                        <td>
                                            <span x-text="contact.status" :class="{
                                                'badge badge-pink': contact.status === 'Customer',
                                                'badge badge-yellow': contact.status === 'Personal',
                                                'badge badge-sky': contact.status === 'Employee',
                                                'badge badge-purple': contact.status === 'Marketing'
                                            }"></span>
                                        </td>
                                        <td>
                                            <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown">
                                                <button x-ref="button" x-on:click="toggle()" :aria-expanded="open.toString()" type="button" title="dropdown-button" class="flex items-center text-gray-500 dark:text-dark-500">
                                                    <i class="ri-more-2-fill"></i>
                                                </button>
                                                <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden" dropdown-position="right">
                                                    <ul>
                                                        <li>
                                                            <a href="#!" data-modal-target="contactOverviewModal" @click="reviewContact(contact.contactID)" class="dropdown-item">
                                                                <i class="align-middle ltr:mr-2 rtl:ml-2 ri-eye-line"></i> <span>Overview</span>
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a href="#!" data-modal-target="contactCreateModal" @click="editContact(contact.contactID)" class="dropdown-item">
                                                                <i class="align-middle ltr:mr-2 rtl:ml-2 ri-pencil-line"></i> <span>Edit</span>
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a href="#!" data-modal-target="deleteModal" @click="deleteItem = contact.contactID" class="dropdown-item">
                                                                <i class="align-middle ltr:mr-2 rtl:ml-2 ri-delete-bin-line"></i> <span>Delete</span>
                                                            </a>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                </template>
                            </template>
                            <template x-if="displayedContacts.length == 0">
                                <td colspan="10" class="!p-8">
                                    <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" class="mx-auto size-12" viewBox="0 0 48 48">
                                        <linearGradient id="SVGID_1__h35ynqzIJzH4_gr1" x1="34.598" x2="15.982" y1="15.982" y2="34.598" gradientUnits="userSpaceOnUse">
                                            <stop offset="0" stop-color="#60e8fe"></stop>
                                            <stop offset=".033" stop-color="#6ae9fe"></stop>
                                            <stop offset=".197" stop-color="#97f0fe"></stop>
                                            <stop offset=".362" stop-color="#bdf5ff"></stop>
                                            <stop offset=".525" stop-color="#dafaff"></stop>
                                            <stop offset=".687" stop-color="#eefdff"></stop>
                                            <stop offset=".846" stop-color="#fbfeff"></stop>
                                            <stop offset="1" stop-color="#fff"></stop>
                                        </linearGradient>
                                        <path fill="url(#SVGID_1__h35ynqzIJzH4_gr1)" d="M40.036,33.826L31.68,25.6c0.847-1.739,1.335-3.684,1.335-5.748c0-7.27-5.894-13.164-13.164-13.164	S6.688,12.582,6.688,19.852c0,7.27,5.894,13.164,13.164,13.164c2.056,0,3.995-0.485,5.728-1.326l3.914,4.015l4.331,4.331	c1.715,1.715,4.496,1.715,6.211,0C41.751,38.321,41.751,35.541,40.036,33.826z"></path>
                                        <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M31.95,25.739l8.086,8.086c1.715,1.715,1.715,4.496,0,6.211l0,0c-1.715,1.715-4.496,1.715-6.211,0	l-4.331-4.331"></path>
                                        <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M7.525,24.511c-1.771-4.694-0.767-10.196,3.011-13.975c3.847-3.847,9.48-4.817,14.228-2.912"></path>
                                        <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M30.856,12.603c3.376,5.114,2.814,12.063-1.688,16.565c-4.858,4.858-12.565,5.129-17.741,0.814"></path>
                                    </svg>
                                    <p class="mt-2 text-center text-gray-500 dark:text-dark-500">No matching records found</p>
                                </td>
                            </template>
                        </tbody>
                    </table>
                </div>
                <div class="grid grid-cols-12 gap-space mt-space items-center" x-show="displayedContacts.length !== 0">
                    <div class="col-span-12 md:col-span-6 justify-center md:justify-start flex flex-wrap">
                        <p class="text-gray-500 dark:text-dark-500 ltr:pr-1 rtl:pl-1">Showing <b x-text="showingStart"></b> - <b x-text="showingEnd"></b> of <b x-text="contacts.length"></b> Results</p>
                        <span x-show="selectedItems.length > 0"><b x-text="selectedItems.length != 0 ? selectedItems.length : ''"> </b> Row Selected</span>
                    </div>
                    <div class="col-span-12 md:col-span-6">
                        <div class="flex justify-center md:justify-end pagination pagination-primary">
                            <button @click="prevPage()" :disabled="currentPage === 1" class="pagination-pre">
                                <i data-lucide="chevron-left" class="mr-1 ltr:inline-block rtl:hidden size-4"></i>
                                <i data-lucide="chevron-right" class="ml-1 ltr:hidden rtl:inline-block size-4"></i>
                                Prev
                            </button>
                            <template x-for="page in totalPages" :key="page">
                                <button @click="gotoPage(page)" :class="{ 'active': currentPage === page }" class="pagination-item">
                                    <span x-text="page"></span>
                                </button>
                            </template>
                            <button @click="nextPage()" :disabled="currentPage === totalPages" class="pagination-next">
                                Next
                                <i data-lucide="chevron-right" class="ml-1 ltr:inline-block rtl:hidden size-4"></i>
                                <i data-lucide="chevron-left" class="mr-1 ltr:hidden rtl:inline-block size-4"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!--contact create modals-->
    <div id="contactCreateModal" class="!hidden modal show" :class="{'show d-block': showAddContactForm || showEditContactForm}" x-show="showAddContactForm || showEditContactForm">
        <div class="modal-wrap modal-center">
            <div class="p-2 modal-content">
                <div class="h-24 p-5 rounded-t-sm bg-gradient-to-r from-primary-500/20 via-pink-500/20 to-green-500/20">
                </div>
                <div class="p-4">
                    <div class="-mt-16">
                        <label for="logo">
                            <div class="inline-flex items-center justify-center overflow-hidden bg-gray-100 border-2 border-white border-solid rounded-full cursor-pointer dark:border-dark-900 dark:bg-dark-850 size-24">
                                <img x-show="contactForm.image" :src="contactForm.image" class="object-cover w-full h-full rounded-full">
                                <div x-show="!contactForm.image" class="flex flex-col items-center text-gray-500 dark:text-dark-500">
                                    <i data-lucide="upload"></i>
                                </div>
                            </div>
                        </label>
                        <div class="hidden mt-4">
                            <label class="block">
                                <span class="sr-only">Choose profile photo</span>
                                <input type="file" name="logo" id="logo" @change="fileChosen" class="block w-full text-sm file:rounded-md focus:outline-0 text-slate-500 file:mr-4 file:py-2 file:px-4 file:border-0 file:text-sm file:font-semibold file:bg-violet-50 file:text-violet-700 hover:file:bg-violet-100" />
                            </label>
                        </div>
                    </div>
                    <span x-show="errors.image" class="text-red-500" x-text="errors.image"></span>
                    <div class="grid grid-cols-12 gap-4 mt-5">
                        <div class="col-span-12">
                            <label for="fullNameInput" class="form-label">Full Name</label>
                            <input type="text" id="fullNameInput" class="form-input" placeholder="Full name" x-model="contactForm.contactName" @input="validateField('contactName', contactForm.contactName, 'Full name is required.')">
                            <span x-show="errors.contactName" class="text-red-500" x-text="errors.contactName"></span>
                        </div>
                        <div class="col-span-12">
                            <label for="companyNameInput" class="form-label">Company Name</label>
                            <input type="text" id="companyNameInput" class="form-input" placeholder="Company name" x-model="contactForm.company" @input="validateField('company', contactForm.company, 'Company name is required.')">
                            <span x-show="errors.company" class="text-red-500" x-text="errors.company"></span>
                        </div>
                        <div class="col-span-12">
                            <label for="roleInput" class="form-label">Role</label>
                            <input type="text" id="roleInput" class="form-input" placeholder="Role" x-model="contactForm.role" @input="validateField('role', contactForm.role, 'Role is required.')">
                            <span x-show="errors.role" class="text-red-500" x-text="errors.role"></span>
                        </div>
                        <div class="col-span-12">
                            <label for="emailInput" class="form-label">Email</label>
                            <input type="email" id="emailInput" class="form-input" placeholder="<EMAIL>" x-model="contactForm.email" @input="validateEmailField()">
                            <span x-show="errors.email" class="text-red-500" x-text="errors.email"></span>
                        </div>
                        <div class="col-span-12">
                            <label for="phoneNumberInput" class="form-label">Phone Number</label>
                            <input type="tel" id="phoneNumberInput" class="form-input" placeholder="+(00) 0000 000" x-model="contactForm.phoneNumber" @input="formatPhoneNumber()">
                            <span x-show="errors.phoneNumber" class="text-red-500" x-text="errors.phoneNumber"></span>
                        </div>
                        <div class="col-span-6">
                            <label for="websiteInput" class="form-label">WebSite</label>
                            <input type="url" id="websiteInput" class="form-input" placeholder="www.domiex.com" x-model="contactForm.website" @input="validateURLField()">
                            <span x-show="errors.website" class="text-red-500" x-text="errors.website"></span>
                        </div>
                        <div class="col-span-6">
                            <label for="statusSelect" class="form-label">Status</label>
                            <div id="statusSelect" x-model="contactForm.status" @change="validateField('status', document.getElementById('statusSelect') , 'Status is required.')"></div>
                            <span x-show="errors.status" class="text-red-500" x-text="errors.status"></span>
                        </div>
                        <div class="flex items-center justify-end col-span-12 gap-2 mt-5">
                            <button type="button" class="btn btn-active-red" @click="resetForm()" data-modal-close="contactCreateModal">
                                <i data-lucide="x" class="inline-block size-4"></i> <span class="align-baseline">Close</span>
                            </button>
                            <button type="button" class="btn btn-primary" x-text=" showAddContactForm ? 'Add Contact' : 'Update Contact'" @click="submitForm()">
                                <i data-lucide="plus" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!--contact overview-->
    <div id="contactOverviewModal" class="!hidden modal show">
        <div class="modal-wrap modal-center">
            <div class="p-2 modal-content">
                <div class="h-24 p-5 rounded-t-sm bg-gradient-to-r from-primary-500/20 via-pink-500/20 to-green-500/20">
                </div>
                <div class="p-4">
                    <div class="flex">
                        <div class="relative inline-block -mt-16 rounded-full ltr:mr-auto rtl:ml-auto shrink-0">
                            <img :src="selectedContact.image" alt="" class="rounded-full size-24">
                            <div class="absolute bg-green-500 border-2 border-white dark:border-dark-900 rounded-full bottom-2.5 size-4 ltr:right-2.5 rtl:left-2.5"></div>
                        </div>
                        <div x-data="{ loadingButton: false, isActive: false }" class="shrink-0">
                            <button @click="loadingButton = true; setTimeout(() => { loadingButton = false; isActive = !isActive; }, 2000)" class="btn btn-pink btn-icon-text">
                                <span class="flex items-center gap-2" x-show="!isActive"><i class="ri-user-add-line"></i> Follow</span>
                                <span class="flex items-center gap-2" x-show="isActive"><i class="ri-user-unfollow-line"></i> UnFollow</span>
                                <svg x-show="loadingButton" class="text-white size-4 animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-0" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                    <h6 class="mt-3" x-text="selectedContact.contactName"></h6>
                    <span class="mb-3 text-gray-500 dark:text-dark-500" x-text="selectedContact.role"> </span> at <span href="#!" class="text-primary-500" x-text="selectedContact.company"></span>

                    <p class="mb-3 text-gray-500 dark:text-dark-500">Overview</p>

                    <div class="flex flex-col gap-3">
                        <div class="flex items-center gap-2">
                            <div class="w-48 font-medium shrink-0">
                                <i data-lucide="briefcase" class="inline-block text-gray-500 dark:text-dark-500 size-4"></i> <span class="align-baseline">Company Name</span>
                            </div>
                            <p x-text="selectedContact.company"></p>
                        </div>
                        <div class="flex items-center gap-2">
                            <div class="w-48 font-medium shrink-0">
                                <i data-lucide="building-2" class="inline-block text-gray-500 dark:text-dark-500 size-4"></i> <span class="align-baseline">Role</span>
                            </div>
                            <p x-text="selectedContact.role"></p>
                        </div>
                        <div class="flex items-center gap-2">
                            <div class="w-48 font-medium shrink-0">
                                <i data-lucide="mail" class="inline-block text-gray-500 dark:text-dark-500 size-4"></i> <span class="align-baseline">Email</span>
                            </div>
                            <p><a href="mailto:<EMAIL>" x-text="selectedContact.email"></a></p>
                        </div>
                        <div class="flex items-center gap-2">
                            <div class="w-48 font-medium shrink-0">
                                <i data-lucide="Phone" class="inline-block text-gray-500 dark:text-dark-500 size-4"></i> <span class="align-baseline">Phone Number</span>
                            </div>
                            <p x-text="selectedContact.phoneNumber"></p>
                        </div>
                        <div class="flex items-center gap-2">
                            <div class="w-48 font-medium shrink-0">
                                <i data-lucide="globe" class="inline-block text-gray-500 dark:text-dark-500 size-4"></i> <span class="align-baseline">Website</span>
                            </div>
                            <p><a href="#!" x-text="selectedContact.website"></a></p>
                        </div>
                        <div class="flex items-center gap-2">
                            <div class="w-48 font-medium shrink-0">
                                <i data-lucide="gem" class="inline-block text-gray-500 dark:text-dark-500 size-4"></i> <span class="align-baseline">Status</span>
                            </div>
                            <p class="badge badge-pink" x-text="selectedContact.status"></p>
                        </div>
                    </div>
                    <div class="flex items-center justify-end gap-2 mt-5">
                        <button type="button" class="btn btn-active-red" data-modal-close="contactOverviewModal"><i data-lucide="x" class="inline-block size-4"></i> <span class="align-baseline">Close</span></button>
                        <button type="button" class="btn btn-primary" data-modal-target="contactCreateModal" data-modal-close="contactOverviewModal"><i data-lucide="pencil" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> Edit Contact</button>
                    </div>
                </div>
            </div>
        </div>
    </div><!--end-->

    <!--delete modal-->
    <div id="deleteModal" class="!hidden modal show">
        <div class="modal-wrap modal-xs modal-center">
            <div class="text-center modal-content p-7">
                <div class="flex items-center justify-center mx-auto mb-4 text-red-500 rounded-full bg-red-500/10 size-14 backdrop-blur-xl">
                    <i data-lucide="trash-2" class="size-6"></i>
                </div>
                <h5 class="mb-4">Are you sure you want to delete this Contact ?</h5>
                <div class="flex items-center justify-center gap-2">
                    <button class="btn btn-red" @click="deleteProduct()" data-modal-close="deleteModal">Delete</button>
                    <button data-modal-close="deleteModal" class="btn link link-primary">Cancel</button>
                </div>
            </div>
        </div>
    </div><!--end-->
</div>
</div>
{{> partials/footer }}
</div>
{{> partials/vendor-scripts }}

<script type="module" src="assets/js/apps/crm/contact-list.init.js"></script>
<script type="module" src="assets/js/main.js"></script>

</body>
</html>