import promotionsListData from "../../../json/apps/pos/promotions-marketing";

function promotionTable() {
    return {
        promotions: [],
        filterPromotion: [],
        sortBy: 'name',
        searchTerm: '',
        sortDirection: 'asc',
        sortClasses: {
            'asc': '↑',
            'desc': '↓'
        },
        selectAll: false,
        selectedItems: [],
        currentPage: 1,
        itemsPerPage: 6,
        editingPromotion: null,
        newPromotion: {
            promotionName: {
                title: '',
                description: ''
            },
            type: 'Percentage',
            discount: '',
            startDate: '',
            endDate: '',
            status: 'Active',
            usage: '0/500'
        },
        recentActivities: [],

        addActivity(action, promotion) {
            const now = new Date();
            const activity = {
                id: Date.now(),
                action: action,
                promotionName: promotion.promotionName.title,
                timestamp: now,
                status: promotion.status,
                type: action === 'created' ? 'green' : 
                      action === 'updated' ? 'blue' : 
                      action === 'deleted' ? 'orange' : 
                      action === 'expired' ? 'red' : 'yellow'
            };
            this.recentActivities.unshift(activity);
            // Keep only last 10 activities
            if (this.recentActivities.length > 10) {
                this.recentActivities.pop();
            }
        },

        getTimeAgo(timestamp) {
            const seconds = Math.floor((new Date() - timestamp) / 1000);
            
            let interval = seconds / 31536000;
            if (interval > 1) return Math.floor(interval) + ' years ago';
            
            interval = seconds / 2592000;
            if (interval > 1) return Math.floor(interval) + ' months ago';
            
            interval = seconds / 86400;
            if (interval > 1) return Math.floor(interval) + ' days ago';
            
            interval = seconds / 3600;
            if (interval > 1) return Math.floor(interval) + ' hours ago';
            
            interval = seconds / 60;
            if (interval > 1) return Math.floor(interval) + ' minutes ago';
            
            return Math.floor(seconds) + ' seconds ago';
        },

        generateAvatarText(promotion) {
            const words = promotion.promotionName.title.split(' ');
            if (words.length === 1) {
                // For single word titles, take first 2 letters
                return words[0].substring(0, 2).toUpperCase();
            } else {
                // For multiple words, take first letter of first two words
                return words.slice(0, 2).map(word => word[0]).join('').toUpperCase();
            }
        },

        generatePromoCode(promotion) {
            // Get the first word of the title
            const firstWord = promotion.promotionName.title.split(' ')[0].toUpperCase();
            
            // Get the first letter of the second word if it exists
            const secondWord = promotion.promotionName.title.split(' ')[1];
            const secondLetter = secondWord ? secondWord[0].toUpperCase() : '';
            
            // Get discount value without special characters
            const discountValue = promotion.discount.replace(/[^0-9]/g, '');
            
            // Combine them together
            return `${firstWord}${secondLetter}${discountValue}`;
        },

        resetForm() {
            this.editingPromotion = null;
            this.newPromotion = {
                promotionName: {
                    title: '',
                    description: ''
                },
                type: 'Percentage',
                discount: '',
                startDate: '',
                endDate: '',
                status: 'Active',
                usage: '0/100'
            };
        },

        editPromotion(promotion) {
            this.editingPromotion = { ...promotion };
            // Split duration into start and end dates
            const [start, end] = promotion.duration.split(' - ');
            this.newPromotion = {
                ...promotion,
                startDate: this.parseDate(start),
                endDate: this.parseDate(end)
            };
        },

        parseDate(dateStr) {
            const [month, day] = dateStr.split(' ');
            const date = new Date(`${month} ${day}, ${new Date().getFullYear()}`);
            return date.toISOString().split('T')[0];
        },

        formatDate(date) {
            return new Date(date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
        },

        savePromotion(event) {
            event.preventDefault();
            
            const formattedDuration = `${this.formatDate(this.newPromotion.startDate)} - ${this.formatDate(this.newPromotion.endDate)}`;
            
            const promotionToSave = {
                promotionName: {
                    title: this.newPromotion.promotionName.title,
                    description: this.newPromotion.promotionName.description
                },
                type: this.newPromotion.type,
                discount: this.newPromotion.discount,
                duration: formattedDuration,
                status: this.newPromotion.status,
                usage: this.newPromotion.usage
            };
            
            if (this.editingPromotion) {
                const index = this.promotions.findIndex(p => p.promoCode === this.editingPromotion.promoCode);
                if (index !== -1) {
                    const newPromoCode = this.generatePromoCode(promotionToSave);
                    this.promotions[index] = {
                        ...promotionToSave,
                        promoCode: newPromoCode
                    };
                    this.addActivity('updated', this.promotions[index]);
                }
            } else {
                const newPromo = {
                    ...promotionToSave,
                    promoCode: this.generatePromoCode(promotionToSave)
                };
                this.promotions.unshift(newPromo);
                this.addActivity('created', newPromo);
            }
            
            this.filteredEmployee();
            this.resetForm();
        },

        cancelEdit() {
            this.resetForm();
        },

        deletePromotion(promotion) {
            if (confirm('Are you sure you want to delete this promotion?')) {
                this.promotions = this.promotions.filter(p => p.promoCode !== promotion.promoCode);
                this.addActivity('deleted', promotion);
                this.filteredEmployee();
                this.updateSelectAllState();
            }
        },

        // Fixed toggleAll function
        toggleAll() {
            if (this.selectAll) {
                // Select all displayed promotions
                this.selectedItems = [...this.displayedPromotion];
            } else {
                // Deselect all
                this.selectedItems = [];
            }
        },

        // New function to handle individual checkbox changes
        toggleItem(promotion) {
            const index = this.selectedItems.findIndex(item => item.promoCode === promotion.promoCode);
            if (index > -1) {
                this.selectedItems.splice(index, 1);
            } else {
                this.selectedItems.push(promotion);
            }
            this.updateSelectAllState();
        },

        // New function to update select all state based on individual selections
        updateSelectAllState() {
            const displayedPromotions = this.displayedPromotion;
            const selectedDisplayedPromotions = this.selectedItems.filter(selected => 
                displayedPromotions.some(displayed => displayed.promoCode === selected.promoCode)
            );
            
            if (selectedDisplayedPromotions.length === 0) {
                this.selectAll = false;
            } else if (selectedDisplayedPromotions.length === displayedPromotions.length) {
                this.selectAll = true;
            } else {
                this.selectAll = false;
            }
        },

        // Helper function to check if an item is selected
        isItemSelected(promotion) {
            return this.selectedItems.some(item => item.promoCode === promotion.promoCode);
        },

        get totalPages() {
            return Math.ceil(this.filterPromotion.length / this.itemsPerPage);
        },

        get displayedPromotion() {
            const start = (this.currentPage - 1) * this.itemsPerPage;
            const end = start + this.itemsPerPage;
            return this.filterPromotion.slice(start, end);
        },

        get showingStart() {
            return Math.min((this.currentPage - 1) * this.itemsPerPage + 1, this.filterPromotion.length);
        },

        get showingEnd() {
            return Math.min(this.currentPage * this.itemsPerPage, this.filterPromotion.length);
        },

        init() {
            this.promotions = promotionsListData.map(promotion => ({
                ...promotion,
                promoCode: this.generatePromoCode(promotion)
            }));
            
            // Initialize with some default activities
            this.recentActivities = [
                {
                    id: 1,
                    action: 'created',
                    promotionName: 'Black Friday Sale',
                    timestamp: new Date(Date.now() - 7200000), // 2 hours ago
                    status: 'Active',
                    type: 'green'
                },
                {
                    id: 5,
                    action: 'updated',
                    promotionName: 'Weekend Special',
                    timestamp: new Date(Date.now() - 14400000), // 4 hours ago
                    status: 'Active',
                    type: 'blue'
                },
                {
                    id: 2,
                    action: 'updated',
                    promotionName: 'Holiday Special',
                    timestamp: new Date(Date.now() - 18000000), // 5 hours ago
                    status: 'Scheduled',
                    type: 'blue'
                },
                {
                    id: 7,
                    action: 'created',
                    promotionName: 'Early Bird Special',
                    timestamp: new Date(Date.now() - 21600000), // 6 hours ago
                    status: 'Active',
                    type: 'green'
                },
                {
                    id: 6,
                    action: 'deleted',
                    promotionName: 'Clearance Event',
                    timestamp: new Date(Date.now() - 28800000), // 8 hours ago
                    status: 'Inactive',
                    type: 'orange'
                },
                {
                    id: 8,
                    action: 'expired',
                    promotionName: 'Bundle Deal',
                    timestamp: new Date(Date.now() - 36000000), // 10 hours ago
                    status: 'Expired',
                    type: 'red'
                },
                {
                    id: 4,
                    action: 'created',
                    promotionName: 'Back to School',
                    timestamp: new Date(Date.now() - 43200000), // 12 hours ago
                    status: 'Scheduled',
                    type: 'green'
                },
                {
                    id: 3,
                    action: 'expired',
                    promotionName: 'Summer Sale',
                    timestamp: new Date(Date.now() - 86400000), // 1 day ago
                    status: 'Expired',
                    type: 'red'
                },
                {
                    id: 9,
                    action: 'updated',
                    promotionName: 'Bundle Deal',
                    timestamp: new Date(Date.now() - 227000000), // 2 days ago
                    status: 'Updated',
                    type: 'blue'
                }
            ];
            
            this.filteredEmployee();
            this.sort('name');
        },

        filteredEmployee() {
            // Create a new array from promotions
            this.filterPromotion = [...this.promotions];
            
            // Apply search filter if search term exists
            const searchTerm = this.searchTerm.trim().toLowerCase();
            if (searchTerm) {
                this.filterPromotion = this.filterPromotion.filter((promotion) => {
                    return Object.values(promotion).some(value => {
                        if (typeof value === 'object' && value !== null) {
                            return Object.values(value).some(v => 
                                v && v.toString().toLowerCase().includes(searchTerm)
                            );
                        }
                        return value && value.toString().toLowerCase().includes(searchTerm);
                    });
                });
            }
            
            // Update select all state when filter changes
            this.updateSelectAllState();
        },

        sort(column) {
            if (column === this.sortBy) {
                this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
            } else {
                this.sortDirection = 'asc';
                this.sortBy = column;
            }

            this.filterPromotion.sort((a, b) => {
                let valueA, valueB;
                
                if (column === 'promotionName') {
                    valueA = a.promotionName.title;
                    valueB = b.promotionName.title;
                } else {
                    valueA = a[column];
                    valueB = b[column];
                }

                if (valueA > valueB) {
                    return this.sortDirection === 'asc' ? 1 : -1;
                }
                if (valueA < valueB) {
                    return this.sortDirection === 'asc' ? -1 : 1;
                }
                return 0;
            });
            
            // Update select all state when sort changes
            this.updateSelectAllState();
        },

        prevPage() {
            if (this.currentPage > 1) {
                this.currentPage--;
                this.updateSelectAllState();
            }
        },

        nextPage() {
            if (this.currentPage < this.totalPages) {
                this.currentPage++;
                this.updateSelectAllState();
            }
        },

        gotoPage(page) {
            this.currentPage = page;
            this.updateSelectAllState();
        },
    };
}

// Initialize Alpine.js
document.addEventListener('alpine:init', () => {
    Alpine.data('promotionTable', promotionTable);
});

// Initialize Lucide icons
document.addEventListener('DOMContentLoaded', function () {
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
});