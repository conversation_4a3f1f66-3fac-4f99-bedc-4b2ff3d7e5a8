{{> partials/main }}

<head>

    {{> partials/title-meta title="Line Awesome" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}

{{> partials/page-heading title="Line Awesome" sub-title="Icons" }}

<div class="grid grid-cols-12 gap-x-space">
    <div class="col-span-12 card">
        <div class="flex flex-col gap-2 md:items-center md:flex-row card-header">
            <h6 class="text-15 grow">Line Awesome Icons</h6>
            <a href="https://icons8.com/line-awesome" target="_blank" class="font-medium text-red-500 underline transition duration-200 ease-linear hover:text-red-600 shrink-0">View All Icons <i data-lucide="move-right" class="inline-block ml-1 size-4"></i></a>
        </div>
        <div class="card-body">
            <p class="mb-3 text-gray-500 dark:text-dark-500">Line Awesome can be loaded via CDN or downloaded as zip archive. You can read more about this options on Line Awesome how-to page.</p>

            <h6 class="mb-2 text-16">Installation</h6>
            <p class="text-gray-500 dark:text-dark-500 mb-2">Alternatively, Line Awesome can be installed as npm package:</p>

            <pre><deckgo-highlight-code lang="js">
                        <code slot="code">npm install line-awesome</code>
                    </deckgo-highlight-code></pre>

            <p class="text-gray-500 dark:text-dark-500 my-2">import CSS to your <code class="text-pink-500">icons.scss</code></p>

            <pre><deckgo-highlight-code lang="js">
                        <code slot="code">@import 'line-awesome/dist/line-awesome/css/line-awesome.css';</code>
                    </deckgo-highlight-code></pre>

            <h6 class="mb-1 mt-2">CDN</h6>
            <p class="text-gray-500 dark:text-dark-500 mb-2">If you already have Font Awesome installed, simply switch out the link to start using Line Awesome:</p>

            <pre><deckgo-highlight-code lang="js">
                        <code slot="code">&lt;link href=&quot;https://maxst.icons8.com/vue-static/landings/line-awesome/font-awesome-line-awesome/css/all.min.css&quot; rel=&quot;stylesheet&quot; /&gt;</code>
                    </deckgo-highlight-code></pre>

            <h6 class="mb-1 mt-2">Usage</h6>
            <p class="text-gray-500 dark:text-dark-500 mb-2">Use the preview page to quickly find the name of the icon you want to use.</p>

            <pre><deckgo-highlight-code lang="js"><code slot="code">&lt;i class=&quot;las la-battery-three-quarters&quot;&gt;&lt;/i&gt;</code>
                    </deckgo-highlight-code></pre>

            <p class="mb-0 text-gray-500 dark:text-dark-500 mt-2">For more details, see the <a href="https://icons8.com/line-awesome/howto" target="_blank" class="transition duration-200 ease-linear hover:text-primary-600 text-primary-500">documentation</a>.</p>
        </div>
    </div><!--end col-->
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Color Icons</h6>
        </div>
        <div class="card-body">
            <div class="*:size-10 *:flex *:items-center *:justify-center flex items-center *:border *:border-gray-200 dark:*:border-dark-800 gap-2 *:rounded-md text-xl flex-wrap">
                <div><i class="las la-battery-three-quarters text-primary-500"></i></div>
                <div><i class="text-gray-500 dark:text-dark-500 las la-radiation"></i></div>
                <div><i class="text-green-500 las la-angle-double-right"></i></div>
                <div><i class="text-purple-500 las la-photo-video"></i></div>
                <div><i class="text-yellow-500 las la-campground"></i></div>
                <div><i class="lab la-modx text-sky-500"></i></div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Sizes Icons</h6>
        </div>
        <div class="card-body">
            <div class="flex flex-wrap items-center gap-3">
                <div><i class="text-xs las la-battery-three-quarters text-primary-500"></i></div>
                <div><i class="las la-battery-three-quarters text-primary-500 text-14"></i></div>
                <div><i class="las la-battery-three-quarters text-primary-500"></i></div>
                <div><i class="las la-battery-three-quarters text-primary-500 text-16"></i></div>
                <div><i class="text-lg las la-battery-three-quarters text-primary-500"></i></div>
                <div><i class="text-xl las la-battery-three-quarters text-primary-500"></i></div>
                <div><i class="text-2xl las la-battery-three-quarters text-primary-500"></i></div>
                <div><i class="text-3xl las la-battery-three-quarters text-primary-500"></i></div>
                <div><i class="text-4xl las la-battery-three-quarters text-primary-500"></i></div>
                <div><i class="text-5xl las la-battery-three-quarters text-primary-500"></i></div>
                <div><i class="text-6xl las la-battery-three-quarters text-primary-500"></i></div>
                <div><i class="text-8xl las la-battery-three-quarters text-primary-500"></i></div>
            </div>
        </div>
    </div><!--end col-->
</div><!--end grid-->



</div>
{{> partials/footer }}
</div>

{{> partials/vendor-scripts }}

<script type="module" src="assets/js/ui/advanced-highlight-code.init.js"></script>
<script type="module" src="assets/js/main.js"></script>

</body>
</html>