{{> partials/main }}

<head>

    {{> partials/title-meta title="Book" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}
        {{> partials/page-heading title="Book" sub-title="Appointments" }}

        <div class="grid grid-cols-12 gap-x-space" x-data="bookForm">
            <div class="col-span-12 xl:col-span-8 2xl:col-span-9 card">
                <div class="card-header">
                    <h6 class="card-title">Appointment Book</h6>
                </div>
                <div class="card-body">
                    <form action="#!">
                        <div class="grid grid-cols-12 gap-space">
                            <div class="col-span-12">
                                <label for="patientNameInput" class="form-label">Patient Name</label>
                                <input type="text" id="patientNameInput" class="form-input" placeholder="Enter your patient name" x-model="form.name" @input="validateField('name', form.name, 'Patient name is required.')">
                                <span x-show="errors.name" class="text-red-500" x-text="errors.name"></span>
                            </div>
                            <div class="col-span-12 sm:col-span-6 xl:col-span-4">
                                <label for="emailInput" class="form-label">Email</label>
                                <input type="email" id="emailInput" class="form-input" placeholder="Enter your email"  x-model="form.email" @input="validateField('email', form.email, 'Email is required.')">
                                <span x-show="errors.email" class="text-red-500" x-text="errors.email"></span>
                            </div>
                            <div class="col-span-12 sm:col-span-6 xl:col-span-4">
                                <label for="phoneNumberInput" class="form-label">Phone Number</label>
                                <input type="number" id="phoneNumberInput" class="form-input" placeholder="(00) 000 00 0000" x-model="form.phone" @input="validateField('phone', form.phone, 'Phone number is required.')">
                                <span x-show="errors.phone" class="text-red-500" x-text="errors.phone"></span>
                            </div>
                            <div class="col-span-12 sm:col-span-6 xl:col-span-4">
                                <label for="selectDateInput" class="form-label">Select Date</label>
                                <input type="text" id="selectDateInput" class="form-input" placeholder="Select date" data-provider="flatpickr" data-date-format="d M, Y"  placeholder="YYYY-MM-DD"  x-model="form.date" @input="validateField('date', form.date, 'Date is required.')">
                                <span x-show="errors.date" class="text-red-500" x-text="errors.date"></span>
                            </div>
                            <div class="col-span-12">
                                <label class="form-label">Start Time</label>
                                <div class="flex flex-wrap gap-2 md:gap-5">
                                    <div class="input-radio-group">
                                        <input id="timeRadio1" class="hidden input-radio peer" name="startTime" type="radio" >
                                        <label for="timeRadio1" class="px-3 py-1.5 border border-gray-200 rounded-md inline-block input-radio-label dark:border-dark-800 peer-checked:border-primary-500 dark:peer-checked:border-primary-500 peer-disabled:bg-gray-100 dark:peer-disabled:bg-dark-850 peer-disabled:text-gray-500 dark:peer-disabled:text-dark-500">
                                            09:00 AM
                                        </label>
                                    </div>
                                    <div class="input-radio-group">
                                        <input id="timeRadio2" class="hidden input-radio peer" name="startTime" type="radio" >
                                        <label for="timeRadio2" class="px-3 py-1.5 border border-gray-200 rounded-md inline-block input-radio-label dark:border-dark-800 peer-checked:border-primary-500 dark:peer-checked:border-primary-500 peer-disabled:bg-gray-100 dark:peer-disabled:bg-dark-850 peer-disabled:text-gray-500 dark:peer-disabled:text-dark-500">
                                            09:30 AM
                                        </label>
                                    </div>
                                    <div class="input-radio-group">
                                        <input id="timeRadio3" class="hidden input-radio peer" name="startTime" type="radio" disabled>
                                        <label for="timeRadio3" class="px-3 py-1.5 border border-gray-200 rounded-md inline-block input-radio-label dark:border-dark-800 peer-checked:border-primary-500 dark:peer-checked:border-primary-500 peer-disabled:bg-gray-100 dark:peer-disabled:bg-dark-850 peer-disabled:text-gray-500 dark:peer-disabled:text-dark-500">
                                            10:00 AM
                                        </label>
                                    </div>
                                    <div class="input-radio-group">
                                        <input id="timeRadio4" class="hidden input-radio peer" name="startTime" type="radio">
                                        <label for="timeRadio4" class="px-3 py-1.5 border border-gray-200 rounded-md inline-block input-radio-label dark:border-dark-800 peer-checked:border-primary-500 dark:peer-checked:border-primary-500 peer-disabled:bg-gray-100 dark:peer-disabled:bg-dark-850 peer-disabled:text-gray-500 dark:peer-disabled:text-dark-500">
                                            10:30 AM
                                        </label>
                                    </div>
                                    <div class="input-radio-group">
                                        <input id="timeRadio5" class="hidden input-radio peer" name="startTime" type="radio">
                                        <label for="timeRadio5" class="px-3 py-1.5 border border-gray-200 rounded-md inline-block input-radio-label dark:border-dark-800 peer-checked:border-primary-500 dark:peer-checked:border-primary-500 peer-disabled:bg-gray-100 dark:peer-disabled:bg-dark-850 peer-disabled:text-gray-500 dark:peer-disabled:text-dark-500">
                                            11:00 AM
                                        </label>
                                    </div>
                                    <div class="input-radio-group">
                                        <input id="timeRadio6" class="hidden input-radio peer" name="startTime" type="radio">
                                        <label for="timeRadio6" class="px-3 py-1.5 border border-gray-200 rounded-md inline-block input-radio-label dark:border-dark-800 peer-checked:border-primary-500 dark:peer-checked:border-primary-500 peer-disabled:bg-gray-100 dark:peer-disabled:bg-dark-850 peer-disabled:text-gray-500 dark:peer-disabled:text-dark-500">
                                            11:30 AM
                                        </label>
                                    </div>
                                    <div class="input-radio-group">
                                        <input id="timeRadio7" class="hidden input-radio peer" name="startTime" type="radio">
                                        <label for="timeRadio7" class="px-3 py-1.5 border border-gray-200 rounded-md inline-block input-radio-label dark:border-dark-800 peer-checked:border-primary-500 dark:peer-checked:border-primary-500 peer-disabled:bg-gray-100 dark:peer-disabled:bg-dark-850 peer-disabled:text-gray-500 dark:peer-disabled:text-dark-500">
                                            12:00 PM
                                        </label>
                                    </div>
                                    <div class="input-radio-group">
                                        <input id="timeRadio8" class="hidden input-radio peer" name="startTime" type="radio">
                                        <label for="timeRadio8" class="px-3 py-1.5 border border-gray-200 rounded-md inline-block input-radio-label dark:border-dark-800 peer-checked:border-primary-500 dark:peer-checked:border-primary-500 peer-disabled:bg-gray-100 dark:peer-disabled:bg-dark-850 peer-disabled:text-gray-500 dark:peer-disabled:text-dark-500">
                                            12:30 PM
                                        </label>
                                    </div>
                                    <div class="input-radio-group">
                                        <input id="timeRadio9" class="hidden input-radio peer" name="startTime" type="radio">
                                        <label for="timeRadio9" class="px-3 py-1.5 border border-gray-200 rounded-md inline-block input-radio-label dark:border-dark-800 peer-checked:border-primary-500 dark:peer-checked:border-primary-500 peer-disabled:bg-gray-100 dark:peer-disabled:bg-dark-850 peer-disabled:text-gray-500 dark:peer-disabled:text-dark-500">
                                            01:00 PM
                                        </label>
                                    </div>
                                    <div class="input-radio-group">
                                        <input id="timeRadio10" class="hidden input-radio peer" name="startTime" type="radio">
                                        <label for="timeRadio10" class="px-3 py-1.5 border border-gray-200 rounded-md inline-block input-radio-label dark:border-dark-800 peer-checked:border-primary-500 dark:peer-checked:border-primary-500 peer-disabled:bg-gray-100 dark:peer-disabled:bg-dark-850 peer-disabled:text-gray-500 dark:peer-disabled:text-dark-500">
                                            01:30 PM
                                        </label>
                                    </div>
                                    <div class="input-radio-group">
                                        <input id="timeRadio11" class="hidden input-radio peer" name="startTime" type="radio">
                                        <label for="timeRadio11" class="px-3 py-1.5 border border-gray-200 rounded-md inline-block input-radio-label dark:border-dark-800 peer-checked:border-primary-500 dark:peer-checked:border-primary-500 peer-disabled:bg-gray-100 dark:peer-disabled:bg-dark-850 peer-disabled:text-gray-500 dark:peer-disabled:text-dark-500">
                                            02:00 PM
                                        </label>
                                    </div>
                                    <div class="input-radio-group">
                                        <input id="timeRadio12" class="hidden input-radio peer" name="startTime" type="radio">
                                        <label for="timeRadio12" class="px-3 py-1.5 border border-gray-200 rounded-md inline-block input-radio-label dark:border-dark-800 peer-checked:border-primary-500 dark:peer-checked:border-primary-500 peer-disabled:bg-gray-100 dark:peer-disabled:bg-dark-850 peer-disabled:text-gray-500 dark:peer-disabled:text-dark-500">
                                            02:30 PM
                                        </label>
                                    </div>
                                    <div class="input-radio-group">
                                        <input id="timeRadio13" class="hidden input-radio peer" name="startTime" type="radio" disabled>
                                        <label for="timeRadio13" class="px-3 py-1.5 border border-gray-200 rounded-md inline-block input-radio-label dark:border-dark-800 peer-checked:border-primary-500 dark:peer-checked:border-primary-500 peer-disabled:bg-gray-100 dark:peer-disabled:bg-dark-850 peer-disabled:text-gray-500 dark:peer-disabled:text-dark-500">
                                            03:00 PM
                                        </label>
                                    </div>
                                    <div class="input-radio-group">
                                        <input id="timeRadio14" class="hidden input-radio peer" name="startTime" type="radio">
                                        <label for="timeRadio14" class="px-3 py-1.5 border border-gray-200 rounded-md inline-block input-radio-label dark:border-dark-800 peer-checked:border-primary-500 dark:peer-checked:border-primary-500 peer-disabled:bg-gray-100 dark:peer-disabled:bg-dark-850 peer-disabled:text-gray-500 dark:peer-disabled:text-dark-500">
                                            03:30 PM
                                        </label>
                                    </div>
                                    <div class="input-radio-group">
                                        <input id="timeRadio15" class="hidden input-radio peer" name="startTime" type="radio">
                                        <label for="timeRadio15" class="px-3 py-1.5 border border-gray-200 rounded-md inline-block input-radio-label dark:border-dark-800 peer-checked:border-primary-500 dark:peer-checked:border-primary-500 peer-disabled:bg-gray-100 dark:peer-disabled:bg-dark-850 peer-disabled:text-gray-500 dark:peer-disabled:text-dark-500">
                                            04:00 PM
                                        </label>
                                    </div>
                                    <div class="input-radio-group">
                                        <input id="timeRadio16" class="hidden input-radio peer" name="startTime" type="radio">
                                        <label for="timeRadio16" class="px-3 py-1.5 border border-gray-200 rounded-md inline-block input-radio-label dark:border-dark-800 peer-checked:border-primary-500 dark:peer-checked:border-primary-500 peer-disabled:bg-gray-100 dark:peer-disabled:bg-dark-850 peer-disabled:text-gray-500 dark:peer-disabled:text-dark-500">
                                            04:30 PM
                                        </label>
                                    </div>
                                    <div class="input-radio-group">
                                        <input id="timeRadio17" class="hidden input-radio peer" name="startTime" type="radio">
                                        <label for="timeRadio17" class="px-3 py-1.5 border border-gray-200 rounded-md inline-block input-radio-label dark:border-dark-800 peer-checked:border-primary-500 dark:peer-checked:border-primary-500 peer-disabled:bg-gray-100 dark:peer-disabled:bg-dark-850 peer-disabled:text-gray-500 dark:peer-disabled:text-dark-500">
                                            05:00 PM
                                        </label>
                                    </div>
                                    <div class="input-radio-group">
                                        <input id="timeRadio18" class="hidden input-radio peer" name="startTime" type="radio">
                                        <label for="timeRadio18" class="px-3 py-1.5 border border-gray-200 rounded-md inline-block input-radio-label dark:border-dark-800 peer-checked:border-primary-500 dark:peer-checked:border-primary-500 peer-disabled:bg-gray-100 dark:peer-disabled:bg-dark-850 peer-disabled:text-gray-500 dark:peer-disabled:text-dark-500">
                                            05:30 PM
                                        </label>
                                    </div>
                                    <div class="input-radio-group">
                                        <input id="timeRadio19" class="hidden input-radio peer" name="startTime" type="radio">
                                        <label for="timeRadio19" class="px-3 py-1.5 border border-gray-200 rounded-md inline-block input-radio-label dark:border-dark-800 peer-checked:border-primary-500 dark:peer-checked:border-primary-500 peer-disabled:bg-gray-100 dark:peer-disabled:bg-dark-850 peer-disabled:text-gray-500 dark:peer-disabled:text-dark-500">
                                            06:00 PM
                                        </label>
                                    </div>
                                </div>
                                <span x-show="errors.startTime" class="text-red-500" x-text="errors.startTime"></span>
                            </div>
                            <div class="col-span-12">
                                <label class="form-label">End Time</label>
                                <div class="flex flex-wrap gap-2 md:gap-5">
                                    <div class="input-radio-group">
                                        <input id="dateRadio1" class="hidden input-radio peer" name="endTime" type="radio">
                                        <label for="dateRadio1" class="px-3 py-1.5 border border-gray-200 rounded-md inline-block input-radio-label dark:border-dark-800 peer-checked:border-primary-500 dark:peer-checked:border-primary-500 peer-disabled:bg-gray-100 dark:peer-disabled:bg-dark-850 peer-disabled:text-gray-500 dark:peer-disabled:text-dark-500">
                                            09:00 AM
                                        </label>
                                    </div>
                                    <div class="input-radio-group">
                                        <input id="dateRadio2" class="hidden input-radio peer" name="endTime" type="radio">
                                        <label for="dateRadio2" class="px-3 py-1.5 border border-gray-200 rounded-md inline-block input-radio-label dark:border-dark-800 peer-checked:border-primary-500 dark:peer-checked:border-primary-500 peer-disabled:bg-gray-100 dark:peer-disabled:bg-dark-850 peer-disabled:text-gray-500 dark:peer-disabled:text-dark-500">
                                            09:30 AM
                                        </label>
                                    </div>
                                    <div class="input-radio-group">
                                        <input id="dateRadio3" class="hidden input-radio peer" name="endTime" type="radio">
                                        <label for="dateRadio3" class="px-3 py-1.5 border border-gray-200 rounded-md inline-block input-radio-label dark:border-dark-800 peer-checked:border-primary-500 dark:peer-checked:border-primary-500 peer-disabled:bg-gray-100 dark:peer-disabled:bg-dark-850 peer-disabled:text-gray-500 dark:peer-disabled:text-dark-500">
                                            10:00 AM
                                        </label>
                                    </div>
                                    <div class="input-radio-group">
                                        <input id="dateRadio4" class="hidden input-radio peer" name="endTime" type="radio" disabled>
                                        <label for="dateRadio4" class="px-3 py-1.5 border border-gray-200 rounded-md inline-block input-radio-label dark:border-dark-800 peer-checked:border-primary-500 dark:peer-checked:border-primary-500 peer-disabled:bg-gray-100 dark:peer-disabled:bg-dark-850 peer-disabled:text-gray-500 dark:peer-disabled:text-dark-500">
                                            10:30 AM
                                        </label>
                                    </div>
                                    <div class="input-radio-group">
                                        <input id="dateRadio5" class="hidden input-radio peer" name="endTime" type="radio">
                                        <label for="dateRadio5" class="px-3 py-1.5 border border-gray-200 rounded-md inline-block input-radio-label dark:border-dark-800 peer-checked:border-primary-500 dark:peer-checked:border-primary-500 peer-disabled:bg-gray-100 dark:peer-disabled:bg-dark-850 peer-disabled:text-gray-500 dark:peer-disabled:text-dark-500">
                                            11:00 AM
                                        </label>
                                    </div>
                                    <div class="input-radio-group">
                                        <input id="dateRadio6" class="hidden input-radio peer" name="endTime" type="radio">
                                        <label for="dateRadio6" class="px-3 py-1.5 border border-gray-200 rounded-md inline-block input-radio-label dark:border-dark-800 peer-checked:border-primary-500 dark:peer-checked:border-primary-500 peer-disabled:bg-gray-100 dark:peer-disabled:bg-dark-850 peer-disabled:text-gray-500 dark:peer-disabled:text-dark-500">
                                            11:30 AM
                                        </label>
                                    </div>
                                    <div class="input-radio-group">
                                        <input id="dateRadio7" class="hidden input-radio peer" name="endTime" type="radio">
                                        <label for="dateRadio7" class="px-3 py-1.5 border border-gray-200 rounded-md inline-block input-radio-label dark:border-dark-800 peer-checked:border-primary-500 dark:peer-checked:border-primary-500 peer-disabled:bg-gray-100 dark:peer-disabled:bg-dark-850 peer-disabled:text-gray-500 dark:peer-disabled:text-dark-500">
                                            12:00 PM
                                        </label>
                                    </div>
                                    <div class="input-radio-group">
                                        <input id="dateRadio8" class="hidden input-radio peer" name="endTime" type="radio">
                                        <label for="dateRadio8" class="px-3 py-1.5 border border-gray-200 rounded-md inline-block input-radio-label dark:border-dark-800 peer-checked:border-primary-500 dark:peer-checked:border-primary-500 peer-disabled:bg-gray-100 dark:peer-disabled:bg-dark-850 peer-disabled:text-gray-500 dark:peer-disabled:text-dark-500">
                                            12:30 PM
                                        </label>
                                    </div>
                                    <div class="input-radio-group">
                                        <input id="dateRadio9" class="hidden input-radio peer" name="endTime" type="radio">
                                        <label for="dateRadio9" class="px-3 py-1.5 border border-gray-200 rounded-md inline-block input-radio-label dark:border-dark-800 peer-checked:border-primary-500 dark:peer-checked:border-primary-500 peer-disabled:bg-gray-100 dark:peer-disabled:bg-dark-850 peer-disabled:text-gray-500 dark:peer-disabled:text-dark-500">
                                            01:00 PM
                                        </label>
                                    </div>
                                    <div class="input-radio-group">
                                        <input id="dateRadio10" class="hidden input-radio peer" name="endTime" type="radio">
                                        <label for="dateRadio10" class="px-3 py-1.5 border border-gray-200 rounded-md inline-block input-radio-label dark:border-dark-800 peer-checked:border-primary-500 dark:peer-checked:border-primary-500 peer-disabled:bg-gray-100 dark:peer-disabled:bg-dark-850 peer-disabled:text-gray-500 dark:peer-disabled:text-dark-500">
                                            01:30 PM
                                        </label>
                                    </div>
                                    <div class="input-radio-group">
                                        <input id="dateRadio11" class="hidden input-radio peer" name="endTime" type="radio">
                                        <label for="dateRadio11" class="px-3 py-1.5 border border-gray-200 rounded-md inline-block input-radio-label dark:border-dark-800 peer-checked:border-primary-500 dark:peer-checked:border-primary-500 peer-disabled:bg-gray-100 dark:peer-disabled:bg-dark-850 peer-disabled:text-gray-500 dark:peer-disabled:text-dark-500">
                                            02:00 PM
                                        </label>
                                    </div>
                                    <div class="input-radio-group">
                                        <input id="dateRadio12" class="hidden input-radio peer" name="endTime" type="radio">
                                        <label for="dateRadio12" class="px-3 py-1.5 border border-gray-200 rounded-md inline-block input-radio-label dark:border-dark-800 peer-checked:border-primary-500 dark:peer-checked:border-primary-500 peer-disabled:bg-gray-100 dark:peer-disabled:bg-dark-850 peer-disabled:text-gray-500 dark:peer-disabled:text-dark-500">
                                            02:30 PM
                                        </label>
                                    </div>
                                    <div class="input-radio-group">
                                        <input id="dateRadio13" class="hidden input-radio peer" name="endTime" type="radio" disabled>
                                        <label for="dateRadio13" class="px-3 py-1.5 border border-gray-200 rounded-md inline-block input-radio-label dark:border-dark-800 peer-checked:border-primary-500 dark:peer-checked:border-primary-500 peer-disabled:bg-gray-100 dark:peer-disabled:bg-dark-850 peer-disabled:text-gray-500 dark:peer-disabled:text-dark-500">
                                            03:00 PM
                                        </label>
                                    </div>
                                    <div class="input-radio-group">
                                        <input id="dateRadio14" class="hidden input-radio peer" name="endTime" type="radio">
                                        <label for="dateRadio14" class="px-3 py-1.5 border border-gray-200 rounded-md inline-block input-radio-label dark:border-dark-800 peer-checked:border-primary-500 dark:peer-checked:border-primary-500 peer-disabled:bg-gray-100 dark:peer-disabled:bg-dark-850 peer-disabled:text-gray-500 dark:peer-disabled:text-dark-500">
                                            03:30 PM
                                        </label>
                                    </div>
                                    <div class="input-radio-group">
                                        <input id="dateRadio15" class="hidden input-radio peer" name="endTime" type="radio">
                                        <label for="dateRadio15" class="px-3 py-1.5 border border-gray-200 rounded-md inline-block input-radio-label dark:border-dark-800 peer-checked:border-primary-500 dark:peer-checked:border-primary-500 peer-disabled:bg-gray-100 dark:peer-disabled:bg-dark-850 peer-disabled:text-gray-500 dark:peer-disabled:text-dark-500">
                                            04:00 PM
                                        </label>
                                    </div>
                                    <div class="input-radio-group">
                                        <input id="dateRadio16" class="hidden input-radio peer" name="endTime" type="radio">
                                        <label for="dateRadio16" class="px-3 py-1.5 border border-gray-200 rounded-md inline-block input-radio-label dark:border-dark-800 peer-checked:border-primary-500 dark:peer-checked:border-primary-500 peer-disabled:bg-gray-100 dark:peer-disabled:bg-dark-850 peer-disabled:text-gray-500 dark:peer-disabled:text-dark-500">
                                            04:30 PM
                                        </label>
                                    </div>
                                    <div class="input-radio-group">
                                        <input id="dateRadio17" class="hidden input-radio peer" name="endTime" type="radio">
                                        <label for="dateRadio17" class="px-3 py-1.5 border border-gray-200 rounded-md inline-block input-radio-label dark:border-dark-800 peer-checked:border-primary-500 dark:peer-checked:border-primary-500 peer-disabled:bg-gray-100 dark:peer-disabled:bg-dark-850 peer-disabled:text-gray-500 dark:peer-disabled:text-dark-500">
                                            05:00 PM
                                        </label>
                                    </div>
                                    <div class="input-radio-group">
                                        <input id="dateRadio18" class="hidden input-radio peer" name="endTime" type="radio">
                                        <label for="dateRadio18" class="px-3 py-1.5 border border-gray-200 rounded-md inline-block input-radio-label dark:border-dark-800 peer-checked:border-primary-500 dark:peer-checked:border-primary-500 peer-disabled:bg-gray-100 dark:peer-disabled:bg-dark-850 peer-disabled:text-gray-500 dark:peer-disabled:text-dark-500">
                                            05:30 PM
                                        </label>
                                    </div>
                                    <div class="input-radio-group">
                                        <input id="dateRadio19" class="hidden input-radio peer" name="endTime" type="radio">
                                        <label for="dateRadio19" class="px-3 py-1.5 border border-gray-200 rounded-md inline-block input-radio-label dark:border-dark-800 peer-checked:border-primary-500 dark:peer-checked:border-primary-500 peer-disabled:bg-gray-100 dark:peer-disabled:bg-dark-850 peer-disabled:text-gray-500 dark:peer-disabled:text-dark-500">
                                            06:00 PM
                                        </label>
                                    </div>
                                </div>
                                <span x-show="errors.endTime" class="text-red-500" x-text="errors.endTime"></span>
                            </div>
                            <div class="col-span-12">
                                <label for="doctorSelect" class="form-label">Doctor Name</label>
                                <div id="doctorSelect" placeholder="Select Doctor" x-model="form.doctorName" @change="validateField('doctorName' , document.querySelector('#doctorSelect') , 'Doctor name is required')"></div>
                                <span x-show="errors.doctorName" x-text="errors.doctorName" class="text-red-500"></span>
                            </div>
                            <div class="col-span-12">
                                <label for="treatmentInput" class="form-label">Treatment</label>
                                <textarea name="treatmentInput" id="treatmentInput" class="h-auto form-input" rows="3" x-model="form.treatment" @input="validateField('treatment', form.treatment, 'Treatment is required.')"></textarea>
                                <span x-show="errors.treatment" x-text="errors.treatment" class="text-red-500"></span>
                            </div>
                            <div class="col-span-12">
                                <div class="flex flex-wrap items-center justify-end gap-2">
                                    <button type="reset" @click="reset($event)" class="btn btn-sub-gray">Reset</button>
                                    <button @click="onSubmit($event)" class="btn btn-primary">Book Appointment</button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <div class="col-span-12 xl:col-span-4 2xl:col-span-3">
                <div class="bg-green-100 !border-green-500/20 dark:bg-green-500/15 card">
                    <div class="card-body">
                        <h6 class="mb-3">Hospital Hours</h6>

                        <p class="mb-2 text-gray-500 dark:text-dark-400">Monday - Friday 09:00AM - 06:00PM</p>
                        <p class="mb-4 text-gray-500 dark:text-dark-400">Saturday 09:00AM - 03:00PM</p>

                        <h6 class="mb-3">Sunday Closed</h6>
                        <p class="text-red-500">* Every 2nd, 4th Saturday and all govt holidays are closed.</p>
                    </div>
                </div>
            </div>
        </div>

    </div>
    {{> partials/footer }}
</div>

{{> partials/vendor-scripts }}

<script type="module" src="assets/js/apps/hospital/appointments/list.init.js"></script>

<script type="module" src="assets/js/main.js"></script>

</body>
</html>