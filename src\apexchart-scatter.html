{{> partials/main }}

<head>

    {{> partials/title-meta title="Scatter Charts" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}

{{> partials/page-heading title="Scatter Charts" sub-title="Apexcharts" }}

<div class="grid grid-cols-12 gap-x-space">
    <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Basic</h6>
        </div>
        <div class="card-body">
            <div x-data="basicScatterApp" dir="ltr">
                <div class="!min-h-full" data-chart-colors="[bg-primary-500, bg-yellow-500, bg-red-500]" x-ref="basicScatterChart"></div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Scatter – Datetime</h6>
        </div>
        <div class="card-body">
            <div x-data="datetimeScatterApp" dir="ltr">
                <div class="!min-h-full" data-chart-colors="[bg-primary-500, bg-green-500, bg-purple-500, bg-orange-500, bg-red-500]" x-ref="datetimeScatterChart"></div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 md:col-span-6 card">
        <div class="card-header">
            <h6 class="card-title">Scatter – Images</h6>
        </div>
        <div class="card-body">
            <div x-data="imagesScatterApp" dir="ltr">
                <div class="!min-h-full" x-ref="imagesScatterChart"></div>
            </div>
        </div>
    </div><!--end col-->
</div><!--end grid-->

</div>
{{> partials/footer }}
</div>

{{> partials/vendor-scripts }}

<script type="module" src="assets/js/charts/scatter-chart.init.js"></script>

<script type="module" src="assets/js/main.js"></script>

</body>
</html>