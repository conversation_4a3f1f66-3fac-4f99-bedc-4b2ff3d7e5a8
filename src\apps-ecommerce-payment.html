{{> partials/main }}

<head>

    {{> partials/title-meta title="Payment" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}
{{> partials/page-heading title="Payment" sub-title="Ecommerce" }}

<div class="grid grid-cols-12 gap-x-5">
    <div class="col-span-12 xl:col-span-8">
        <div class="mb-5 alert alert-green">
            <div class="flex flex-wrap items-center gap-3">
                <div class="flex items-center justify-center bg-white rounded-full dark:bg-dark-900 shrink-0 size-14">
                    <i data-lucide="user-round-plus" class="text-green-500 fill-green-500/10"></i>
                </div>
                <div class="grow">
                    <h6 class="mb-2 text-gray-800 dark:text-dark-50">Invite your friend now by referral code!!</h6>
                    <p class="text-gray-500 dark:text-dark-500">Maximize your rewards by sharing your unique referral code for exclusive benefits!</p>
                </div>
                <div class="shrink-0">
                    <button class="btn btn-green"><i data-lucide="user-round-plus" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-center">Invite Now</span></button>
                </div>
            </div>
        </div>
        <div class="card">
            <div class="card-header">
                <div x-data="{ activeTab: 'card' }">
                    <div class="grid grid-cols-12 gap-5 mb-5">
                        <div class="col-span-12 md:col-span-6 xl:col-span-4">
                            <div @click="activeTab = 'card'" :class="{ 'bg-gray-100 dark:bg-dark-850': activeTab === 'card' }" class="flex items-center gap-3 p-4 border border-gray-200 rounded-md cursor-pointer dark:border-dark-800">
                                <img src="assets/images/payment/visa.png" alt="" class="h-6">
                                <h6>Debit / Credit Card</h6>
                            </div>
                        </div>
                        <div class="col-span-12 md:col-span-6 xl:col-span-4">
                            <div @click="activeTab = 'bank'" :class="{ 'bg-gray-100 dark:bg-dark-850': activeTab === 'bank' }" class="flex items-center gap-3 p-4 border border-gray-200 rounded-md cursor-pointer dark:border-dark-800">
                                <img src="assets/images/payment/bank.png" alt="" class="h-6">
                                <h6>Bank Transfer</h6>
                            </div>
                        </div>
                    </div>

                    <div x-show="activeTab === 'card'">
                        <h6 class="mb-3">Debit / Credit Card</h6>
                        <div x-data="paymentForm">
                            <form @submit.prevent="validateForm">
                                <div class="grid grid-cols-12 gap-5">
                                    <div class="col-span-12">
                                        <label for="cardName" class="form-label">Card Holder Name</label>
                                        <input type="text" id="cardName" x-model="cardName" @input="validateInput('cardName')" class="form-input" placeholder="Enter full name">
                                        <span x-show="errors.cardName" class="text-sm text-red-500" x-text="errors.cardName"></span>
                                    </div>
                                    <div class="col-span-12">
                                        <label for="debitCreditNumber" class="form-label">Debit / Credit Card Number</label>
                                        <input type="text" id="debitCreditNumber" x-model="cardNumber" @input="validateInput('cardNumber')" class="form-input" placeholder="0000 0000 0000 0000" maxlength="19">
                                        <span x-show="errors.cardNumber" class="text-sm text-red-500" x-text="errors.cardNumber"></span>
                                    </div>
                                    <div class="col-span-12 md:col-span-6">
                                        <label for="expiryDate" class="form-label">Expiry Date</label>
                                        <input type="text" id="expiryDate" x-model="expiryDate" @input="validateInput('expiryDate')" class="form-input" placeholder="MM-YY" maxlength="5">
                                        <span x-show="errors.expiryDate" class="text-sm text-red-500" x-text="errors.expiryDate"></span>
                                    </div>
                                    <div class="col-span-12 md:col-span-6">
                                        <label for="cvv" class="form-label">CVV</label>
                                        <input type="text" id="cvv" x-model="cvv" @input="validateInput('cvv')" class="form-input" placeholder="000" maxlength="3">
                                        <span x-show="errors.cvv" class="text-sm text-red-500" x-text="errors.cvv"></span>
                                    </div>
                                    <div class="col-span-12">
                                        <div class="input-check-group">
                                            <input id="checkboxBasic1" class="input-check input-check-primary" type="checkbox" />
                                            <label for="checkboxBasic1" class="input-check-label">Save my card for future</label>
                                        </div>
                                    </div>
                                    <div class="col-span-12 ltr:text-right rtl:text-left">
                                        <button type="submit" class="btn btn-primary" data-modal-target="defaultModal">
                                            Pay Now
                                            <i data-lucide="move-right" class="ml-1 ltr:inline-block rtl:hidden size-4"></i>
                                            <i data-lucide="move-left" class="mr-1 rtl:inline-block ltr:hidden size-4"></i>
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                    <div x-show="activeTab === 'bank'">
                        <h6 class="mb-3">Bank Transfer</h6>
                        <div x-data="bankForm()">
                            <form @submit.prevent="validateForm">
                                <div class="grid grid-cols-12 gap-5">
                                    <div class="col-span-12">
                                        <label for="bankHolderName" class="form-label">Bank Holder Name</label>
                                        <input type="text" id="bankHolderName" x-model="bankHolderName" @input="validateInput('bankHolderName')" class="w-full p-2 border rounded-sm form-input" placeholder="Enter full name">
                                        <span x-show="errors.bankHolderName" class="text-sm text-red-500" x-text="errors.bankHolderName"></span>
                                    </div>
                                    <div class="col-span-6">
                                        <label for="accountNumber" class="form-label">Account Number</label>
                                        <input type="number" id="accountNumber" x-model="accountNumber" @input="validateInput('accountNumber')" class="w-full p-2 border rounded-sm form-input" placeholder="Enter account number" maxlength="12">
                                        <span x-show="errors.accountNumber" class="text-sm text-red-500" x-text="errors.accountNumber"></span>
                                    </div>
                                    <div class="col-span-6">
                                        <label for="confirmAccountNumber" class="form-label">Confirm Account Number</label>
                                        <input type="number" id="confirmAccountNumber" x-model="confirmAccountNumber" @input="validateInput('confirmAccountNumber')" class="w-full p-2 border rounded-sm form-input" placeholder="Enter confirm account number" maxlength="12">
                                        <span x-show="errors.confirmAccountNumber" class="text-sm text-red-500" x-text="errors.confirmAccountNumber"></span>
                                    </div>
                                    <div class="col-span-6">
                                        <label for="ifscCode" class="form-label">IFSC Code</label>
                                        <input type="text" id="ifscCode" x-model="ifscCode" @input="validateInput('ifscCode')" class="w-full p-2 border rounded-sm form-input" placeholder="IFSC Code" maxlength="11">
                                        <span x-show="errors.ifscCode" class="text-sm text-red-500" x-text="errors.ifscCode"></span>
                                    </div>
                                    <div class="col-span-6">
                                        <label for="bankName" class="form-label">Bank Name</label>
                                        <input type="text" id="bankName" x-model="bankName" @input="validateInput('bankName')" class="w-full p-2 border rounded-sm form-input" placeholder="Bank name">
                                        <span x-show="errors.bankName" class="text-sm text-red-500" x-text="errors.bankName"></span>
                                    </div>
                                    <div class="col-span-12 ltr:text-right rtl:text-left">
                                        <button type="submit" class="btn btn-primary" data-modal-target="defaultModal">
                                            Pay Now
                                            <i data-lucide="move-right" class="ml-1 ltr:inline-block rtl:hidden size-4"></i>
                                            <i data-lucide="move-left" class="mr-1 rtl:inline-block ltr:hidden size-4"></i>
                                        </button>
                                    </div>
                                </div>
                            </form>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-span-12 xl:col-span-4">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title">Order Summary</h6>
            </div>
            <div class="card-body">
                <table class="table table-sm flush">
                    <tr>
                        <td class="font-semibold">Sub Amount</td>
                        <td>$316.89</td>
                    </tr>
                    <tr>
                        <td class="font-semibold">Vat Amount (6%)</td>
                        <td>$19.19</td>
                    </tr>
                    <tr>
                        <td class="font-semibold">Discount (10%)</td>
                        <td>-$31.98</td>
                    </tr>
                    <tr>
                        <td class="font-semibold">Shipping Charge</td>
                        <td>$35.00</td>
                    </tr>
                    <tr class="border-t border-gray-200 dark:border-dark-800">
                        <td class="font-semibold"><div class="py-1.5">Total Amount</div></td>
                        <td><div class="py-1.5">$339.10</div></td>
                    </tr>
                </table>
                <div class="my-4">
                    <button class="w-full btn btn-primary">Pay $339.10</button>
                </div>
                <p class="text-center text-gray-500 dark:text-dark-500">100% Money back guarantee</p>
            </div>
        </div>
    </div>
</div>

</div>
{{> partials/footer }}
</div>

<div id="defaultModal" class="!hidden modal show">
    <div class="modal-wrap modal-sm modal-center">
        <div class="text-center modal-content p-8 bg-[url('../images/others/payment-modal.png')] bg-cover bg-center">
            <div class="flex items-center justify-center mx-auto mt-20 mb-4 text-green-500 rounded-full bg-green-500/10 size-14 backdrop-blur-xl">
                <i data-lucide="party-popper" class="size-6"></i>
            </div>
            <h5 class="mb-1">Thank you for your purchase.</h5>
            <p class="mb-4 text-gray-500 dark:text-dark-500">Your payment has been processed successfully!</p>
            <div class="flex items-center justify-center gap-2">
                <a href="apps-ecommerce-products-list.html" class="btn btn-green">Continue Shopping!</a>
            </div>
        </div>
    </div>
</div>

{{> partials/vendor-scripts }}

<script type="module" src="assets/js/apps/ecommerce/payment.init.js"></script>

<script type="module" src="assets/js/main.js"></script>

</body>
</html>