{{> partials/main }}

<head>

    {{> partials/title-meta title="Doctor Schedule" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}
{{> partials/page-heading title="Doctor Schedule" sub-title="Hospital" }}


<div x-data="datePicker()" id="scrollTodayDate">
    <div x-data="{
                    open: false,
                    toggle() {
                        if (this.open) {
                            return this.close();
                        }
                        this.$refs.button.focus();
                        this.open = true;
                    },
                    close(focusAfter) {
                        if (!this.open) return;
                        this.open = false;
                        focusAfter && focusAfter.focus();
                    }
                }" x-on:keydown.escape.prevent.stop="close($refs.button)" x-on:focusin.window="!$refs.panel.contains($event.target) && close()" x-id="['dropdown-button']" class="dropdown">
        <button x-ref="button" x-on:click="toggle()" :aria-expanded="open" :aria-controls="$id('dropdown-button')" type="button" class="flex items-center gap-2 btn-primary btn">
            <span x-text="months[selectedMonth] + ' ' + selectedYear"></span>
        </button>

        <div x-ref="panel" x-show="open" x-transition.origin.top.left x-on:click.outside="close($refs.button)" :id="$id('dropdown-button')" style="display: none;" class="p-2 dropdown-menu">
            <template x-for="(month, index) in months" :key="index">
                <a href="#" @click.prevent="selectMonth(index)" class="dropdown-item">
                    <span x-text="month"></span>
                </a>
            </template>
        </div>
    </div>

    <div data-simplebar class="mt-5">
        <div class="flex gap-5">
            <template x-for="(date, index) in dates" :key="index">
                <a href="#!" :class="{'active-date': date.isToday}" class="flex items-center justify-center text-center border border-gray-200 rounded-md dark:border-dark-800 size-16 shrink-0" x-on:click="selectDate(date)">
                    <div>
                        <h5 x-text="date.day"></h5>
                        <p x-text="date.name"></p>
                    </div>
                </a>
            </template>
        </div>
    </div>
    <div x-show="selectedAppointments.length > 0" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-x-space mt-space">
        <template x-for="(appointment, index) in selectedAppointments" :key="index">
            <div class="relative card before:absolute ltr:before:-left-0.5 rtl:before:-right-0.5 before:rounded-full before:top-5 before:h-12 before:w-[2px]" :class="`before:bg-${appointment.color}`">
                <div class="card-body">
                    <div class="flex items-center gap-2 mb-5">
                        <h3 class="flex items-center justify-center border border-gray-200 rounded-md dark:border-dark-800 size-12" x-text="new Date(appointment.date).getDate()"></h3>
                        <div>
                            <h6 class="mb-0.5" x-text="formatDate(new Date(appointment.date))"></h6>
                            <p class="text-xs text-gray-500 dark:text-dark-500" x-text="appointment.monthYear"></p>
                        </div>
                    </div>
                    <h6 x-text="appointment.name"></h6>
                    <p class="mb-2 text-gray-500 dark:text-dark-500" x-text="appointment.specialty"></p>
                    <small class="text-gray-500 dark:text-dark-500">Notes:</small>
                    <p class="mb-2" x-text="appointment.notes"></p>
                    <div class="flex items-center gap-1 font-semibold"><i class="text-lg font-normal text-gray-500 dark:text-dark-500 ri-time-line"></i><span x-text="appointment.time"></span></div>
                </div>
            </div>
        </template>
    </div>
</div>

</div>
{{> partials/footer }}
</div>


{{> partials/vendor-scripts }}

<script type="module" src="assets/js/apps/hospital/schedule/schedule.init.js"></script>

<script type="module" src="assets/js/main.js"></script>

</body>
</html>