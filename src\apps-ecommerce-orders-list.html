{{> partials/main }}

<head>

    {{> partials/title-meta title="Orders List" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}
{{> partials/page-wrapper }}
<div>
    {{> partials/page-heading title="Orders List" sub-title="Orders" }}

    <div x-data="{ orders: [
            { title: 'New Orders', count: 345, percentage: '19%', class: 'bg-primary-100 dark:bg-primary-500/10 !border-primary-200 dark:!border-primary-500/20', icon: 'ri-arrow-right-up-line' },
            { title: 'Pending Orders', count: 120, percentage: '2.98%', class: 'bg-yellow-100 dark:bg-yellow-500/10 !border-yellow-200 dark:!border-yellow-500/20', icon: 'ri-arrow-right-down-line' },
            { title: 'Delivered Orders', count: 225, percentage: '8.56%', class: 'bg-green-100 dark:bg-green-500/10 !border-green-200 dark:!border-green-500/20', icon: 'ri-arrow-right-up-line' },
            { title: 'Total Orders', count: 9451, percentage: '24.08%', class: 'bg-purple-100 dark:bg-purple-500/10 !border-purple-200 dark:!border-purple-500/20', icon: 'ri-arrow-right-up-line' }
        ] }">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-x-5">
            <template x-for="(order, index) in orders" :key="index">
                <div :class="[order.class, 'card']">
                    <div class="card-body">
                        <h6 class="mb-3" x-text="order.title"></h6>
                        <div class="flex items-center divide-x *:px-3 divide-gray-300 dark:divide-dark-800">
                            <h4 class="ltr:pl-0 rtl:pr-0" x-text="order.count"></h4>
                            <p class="text-gray-500"><span class="font-semibold"><i :class="order.icon"></i><span x-text="order.percentage"></span></span> this months</p>
                        </div>
                    </div>
                </div>
            </template>
        </div><!--end grid-->
    </div>

    <div x-data="productTable()">
        <div class="card">
            <div class="card-body">
                <div class="grid grid-cols-12">
                    <div class="col-span-12 xl:col-span-8">
                        <ul class="flex items-center gap-2 overflow-x-auto">
                            <li>
                                <a href="#!" @click="orderStatus('All'); activeTab = 'All'" :class="{ 'active': activeTab === 'All' }" class="whitespace-nowrap relative block px-4 py-2 font-medium text-center transition duration-200 ease-linear rounded-md text-gray-500 [&.active]:bg-gray-100 dark:[&.active]:bg-dark-850 dark:text-dark-500 hover:text-gray-900 dark:hover:text-dark-50 [&.active]:text-gray-900 dark:[&.active]:text-dark-50 active">
                                    <span class="align-middle">All Orders</span>
                                </a>
                            </li>
                            <li>
                                <a href="#!" @click.prevent="orderStatus('New'); activeTab = 'New'" :class="{ 'active': activeTab === 'New' }" class="whitespace-nowrap relative block px-4 py-2 font-medium text-center transition duration-200 ease-linear rounded-md text-gray-500 [&.active]:bg-gray-100 dark:[&.active]:bg-dark-850 dark:text-dark-500 hover:text-gray-900 dark:hover:text-dark-50 [&.active]:text-gray-900 dark:[&.active]:text-dark-50">
                                    <span class="align-middle">New</span>
                                </a>
                            </li>
                            <li>
                                <a href="#!" @click.prevent="orderStatus('Pending'); activeTab = 'Pending'" :class="{ 'active': activeTab === 'Pending' }" class="whitespace-nowrap relative block px-4 py-2 font-medium text-center transition duration-200 ease-linear rounded-md text-gray-500 [&.active]:bg-gray-100 dark:[&.active]:bg-dark-850 dark:text-dark-500 hover:text-gray-900 dark:hover:text-dark-50 [&.active]:text-gray-900 dark:[&.active]:text-dark-50">
                                    <span class="align-middle">Pending</span>
                                </a>
                            </li>
                            <li>
                                <a href="#!" @click.prevent="orderStatus('Delivered'); activeTab = 'Delivered'" :class="{ 'active': activeTab === 'Delivered' }" class="whitespace-nowrap relative block px-4 py-2 font-medium text-center transition duration-200 ease-linear rounded-md text-gray-500 [&.active]:bg-gray-100 dark:[&.active]:bg-dark-850 dark:text-dark-500 hover:text-gray-900 dark:hover:text-dark-50 [&.active]:text-gray-900 dark:[&.active]:text-dark-50">
                                    <span class="align-middle">Delivered</span>
                                </a>
                            </li>
                            <li>
                                <a href="#!" @click.prevent="orderStatus('Shipping'); activeTab = 'Shipping'" :class="{ 'active': activeTab === 'Shipping' }" class="relative block px-4 py-2 font-medium text-center transition duration-200 ease-linear rounded-md text-gray-500 [&.active]:bg-gray-100 dark:[&.active]:bg-dark-850 dark:text-dark-500 hover:text-gray-900 dark:hover:text-dark-50 [&.active]:text-gray-900 dark:[&.active]:text-dark-50">
                                    <span class="align-middle">Shipping</span>
                                </a>
                            </li>
                            <li>
                                <a href="#!" @click.prevent="orderStatus('Cancelled'); activeTab = 'Cancelled'" :class="{ 'active': activeTab === 'Cancelled' }" class="relative block px-4 py-2 font-medium text-center transition duration-200 ease-linear rounded-md text-gray-500 [&.active]:bg-gray-100 dark:[&.active]:bg-dark-850 dark:text-dark-500 hover:text-gray-900 dark:hover:text-dark-50 [&.active]:text-gray-900 dark:[&.active]:text-dark-50">
                                    <span class="align-middle">Cancelled</span>
                                </a>
                            </li>
                        </ul>

                    </div>
                    <div class="flex col-span-12 gap-3 mt-4 xl:mt-0 xl:justify-end xl:col-span-4">
                        <button class="btn btn-red btn-icon " x-show="selectedItems.length > 0" @click="deleteSelectedItems()"><i data-lucide="trash" class="size-4"></i></button>
                        <button class="btn btn-primary" data-modal-target="addOrderModal" @click="handleModal('showAddOrderForm')"><i data-lucide="plus" class="inline-block mr-1 size-4"></i> New Order</button>
                        <button class="btn btn-sub-gray" @click="sort('productName')"><i data-lucide="filter" class="inline-block mr-1 size-4"></i> Filters</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="pt-0 card-body">
                <div>
                    <div class="overflow-x-auto table-box">
                        <table class="table hovered ">
                            <tbody>
                                <tr class="text-gray-500 bg-gray-100 dark:bg-dark-850 dark:text-dark-500">
                                    <th class="!font-medium whitespace-nowrap">
                                        <div class="input-check-group">
                                            <label for="checkboxAll" class="hidden input-check-label"></label>
                                            <input id="checkboxAll" class="input-check input-check-primary" type="checkbox" x-model="selectAll" @click="toggleAll" />
                                        </div>
                                    </th>
                                    <th x-on:click="sort('ordersID')" class="!font-medium whitespace-nowrap cursor-pointer">Order ID <span x-show="sortBy === 'ordersID'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                    <th x-on:click="sort('ordersDate')" class="!font-medium whitespace-nowrap cursor-pointer">Order Date <span x-show="sortBy === 'ordersDate'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                    <th x-on:click="sort('deliveredDate')" class="!font-medium whitespace-nowrap cursor-pointer">Delivered Date <span x-show="sortBy === 'deliveredDate'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                    <th x-on:click="sort('customersName')" class="!font-medium whitespace-nowrap cursor-pointer">Customers <span x-show="sortBy === 'customersName'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                    <th x-on:click="sort('productName')" class="!font-medium whitespace-nowrap cursor-pointer">Product <span x-show="sortBy === 'productName'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                    <th x-on:click="sort('payment')" class="!font-medium whitespace-nowrap cursor-pointer">Payment <span x-show="sortBy === 'payment'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                    <th x-on:click="sort('total')" class="!font-medium whitespace-nowrap cursor-pointer">Total <span x-show="sortBy === 'total'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                    <th x-on:click="sort('qty')" class="!font-medium whitespace-nowrap cursor-pointer">QTY <span x-show="sortBy === 'qty'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                    <th x-on:click="sort('status')" class="!font-medium whitespace-nowrap cursor-pointer">status <span x-show="sortBy === 'status'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                    <th class="!font-medium whitespace-nowrap">Action</th>
                                </tr>
                                <template x-for="(product, index) in displayedProducts" :key="product.ordersID">
                                    <tr>
                                        <td class="whitespace-nowrap">
                                            <div class="input-check-group">
                                                <label :for="`orders${index}`" class="hidden input-check-label"></label>
                                                <input :id="`orders${index}`" class="input-check input-check-primary" type="checkbox" @click="toggleItem(product)" :checked="selectedItems.includes(product)" />
                                            </div>
                                        </td>
                                        <td class="whitespace-nowrap"><a href="#!" class="link link-primary" x-text="product.ordersID"></a></td>
                                        <td class="whitespace-nowrap" x-text="product.ordersDate"></td>
                                        <td class="whitespace-nowrap" x-text="product.deliveredDate"></td>
                                        <td class="whitespace-nowrap" x-text="product.customersName"></td>
                                        <td class="whitespace-nowrap" x-text="product.productName"></td>
                                        <td class="whitespace-nowrap">
                                            <span x-text="product.payment" :class="{
                                                    'badge badge-green': product.payment === 'Paid',
                                                    'badge badge-gray': product.payment === 'COD',
                                                    'badge badge-red': product.payment === 'Unpaid'
                                                }"></span>
                                        </td>
                                        <td class="whitespace-nowrap" x-text="product.total"></td>
                                        <td class="whitespace-nowrap" x-text="product.qty"></td>
                                        <td class="whitespace-nowrap">
                                            <span x-text="product.status" :class="{
                                                    'badge badge-green': product.status === 'Delivered',
                                                    'badge badge-primary': product.status === 'New',
                                                    'badge badge-red': product.status === 'Cancelled',
                                                    'badge badge-purple': product.status === 'Shipping',
                                                    'badge badge-yellow': product.status === 'Pending'
                                                }"></span>
                                        </td>
                                        <td class="whitespace-nowrap">
                                            <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown">
                                                <button x-ref="button" x-on:click="toggle()" :aria-expanded="open.toString()" type="button" class="flex items-center text-gray-500 dark:text-dark-500">
                                                    <i class="ri-more-2-fill"></i>
                                                </button>
                                                <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden">
                                                    <ul>
                                                        <li>
                                                            <a href="#!" data-modal-target="overviewOrderModal" @click="reviewOrder(product.ordersID)" class="dropdown-item">
                                                                <i class="align-middle ltr:mr-2 rtl:ml-2 ri-eye-line"></i> <span>Overview</span>
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a href="#!" data-modal-target="addOrderModal" @click="editOrder(product.ordersID)" class="dropdown-item">
                                                                <i class="align-middle ltr:mr-2 rtl:ml-2 ri-pencil-line"></i> <span>Edit</span>
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a href="#!" data-modal-target="deleteModal" @click="deleteOrder(product.ordersID)" x-on:click="close()" class="dropdown-item">
                                                                <i class="align-middle ltr:mr-2 rtl:ml-2 ri-delete-bin-line"></i> <span>Delete</span>
                                                            </a>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                </template>
                                <tr>
                                    <template x-if="displayedProducts.length == 0">
                                        <td colspan="10" class="!p-8">
                                            <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" class="mx-auto size-12" viewBox="0 0 48 48">
                                                <linearGradient id="SVGID_1__h35ynqzIJzH4_gr1" x1="34.598" x2="15.982" y1="15.982" y2="34.598" gradientUnits="userSpaceOnUse">
                                                    <stop offset="0" stop-color="#60e8fe"></stop>
                                                    <stop offset=".033" stop-color="#6ae9fe"></stop>
                                                    <stop offset=".197" stop-color="#97f0fe"></stop>
                                                    <stop offset=".362" stop-color="#bdf5ff"></stop>
                                                    <stop offset=".525" stop-color="#dafaff"></stop>
                                                    <stop offset=".687" stop-color="#eefdff"></stop>
                                                    <stop offset=".846" stop-color="#fbfeff"></stop>
                                                    <stop offset="1" stop-color="#fff"></stop>
                                                </linearGradient>
                                                <path fill="url(#SVGID_1__h35ynqzIJzH4_gr1)" d="M40.036,33.826L31.68,25.6c0.847-1.739,1.335-3.684,1.335-5.748c0-7.27-5.894-13.164-13.164-13.164	S6.688,12.582,6.688,19.852c0,7.27,5.894,13.164,13.164,13.164c2.056,0,3.995-0.485,5.728-1.326l3.914,4.015l4.331,4.331	c1.715,1.715,4.496,1.715,6.211,0C41.751,38.321,41.751,35.541,40.036,33.826z"></path>
                                                <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M31.95,25.739l8.086,8.086c1.715,1.715,1.715,4.496,0,6.211l0,0c-1.715,1.715-4.496,1.715-6.211,0	l-4.331-4.331"></path>
                                                <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M7.525,24.511c-1.771-4.694-0.767-10.196,3.011-13.975c3.847-3.847,9.48-4.817,14.228-2.912"></path>
                                                <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M30.856,12.603c3.376,5.114,2.814,12.063-1.688,16.565c-4.858,4.858-12.565,5.129-17.741,0.814"></path>
                                            </svg>
                                            <p class="mt-2 text-center text-gray-500 dark:text-dark-500">No matching records found</p>
                                        </td>
                                    </template>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="grid grid-cols-12 gap-5 mt-5 items-center" x-show="displayedProducts.length !== 0">
                        <div class="col-span-12 lg:col-span-6 justify-center lg:justify-start flex flex-wrap">
                            <p class="text-gray-500 dark:text-dark-500 ltr:pr-1 rtl:pl-1">Showing <b x-text="showingStart"></b> - <b x-text="showingEnd"></b> of <b x-text="filteredProducts.length"></b> Results</p>
                            <span x-show="selectedItems.length > 0"><b x-text="selectedItems.length != 0 ? selectedItems.length : ''"></b> Row Selected</span>
                        </div>
                        <div class="col-span-12 lg:col-span-6">
                            <div class="flex justify-center lg:justify-end pagination pagination-primary">
                                <button @click="prevPage()" :disabled="currentPage === 1" class="pagination-pre">
                                    <i data-lucide="chevron-left" class="mr-1 ltr:inline-block rtl:hidden size-4"></i>
                                    <i data-lucide="chevron-right" class="ml-1 ltr:hidden rtl:inline-block size-4"></i>
                                    Prev
                                </button>
                                <template x-for="page in totalPages" :key="page">
                                    <button @click="gotoPage(page)" :class="{ 'active': currentPage === page }" class="pagination-item">
                                        <span x-text="page"></span>
                                    </button>
                                </template>
                                <button @click="nextPage()" :disabled="currentPage === totalPages" class="pagination-next">
                                    Next
                                    <i data-lucide="chevron-right" class="ml-1 ltr:inline-block rtl:hidden size-4"></i>
                                    <i data-lucide="chevron-left" class="mr-1 ltr:hidden rtl:inline-block size-4"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div><!--end card-->

        <div id="addOrderModal" class="!hidden modal show" :class="{'show d-block': showAddOrderForm || showEditOrderForm}" x-show="showAddOrderForm || showEditOrderForm">
            <div class="modal-wrap modal-center">
                <div class="modal-header">
                    <h6 x-text="showAddOrderForm ? 'Add Order' : 'Edit Order'">Add New Order</h6>
                    <button data-modal-close="addOrderModal" class="link link-red float-end"><i data-lucide="x" class="size-5"></i></button>
                </div>
                <div class="modal-content">
                    <form>
                        <div class="grid grid-cols-12 gap-5">
                            <div class="col-span-12">
                                <label for="orderIDInput" class="form-label">Order ID</label>
                                <input type="text" id="orderIDInput" class="form-input" placeholder="Order ID" value="PEO-14565" x-model="form.ordersID" disabled>
                            </div>
                            <div class="col-span-6">
                                <label for="orderDateInput" class="form-label">Order Date</label>
                                <input type="text" placeholder="YYYY-MM-DD" id="orderDateInput" class="form-input" data-provider="flatpickr" data-date-format="d M, Y" placeholder="Order ID" x-model="form.ordersDate" @input="validateField('ordersDate', form.ordersDate, 'Order date is required.')">
                                <span x-show="errors.ordersDate" class="text-red-500" x-text="errors.ordersDate"></span>
                            </div>
                            <div class="col-span-6">
                                <label for="deliveredDateInput" class="form-label">Delivered Date</label>
                                <input type="text" placeholder="YYYY-MM-DD" id="deliveredDateInput" class="form-input" data-provider="flatpickr" data-date-format="d M, Y" placeholder="Order ID" x-model="form.deliveredDate" @input="validateField('deliveredDate', form.deliveredDate, 'Delivered date is required.')">
                                <span x-show="errors.deliveredDate" class="text-red-500" x-text="errors.deliveredDate"></span>
                            </div>
                            <div class="col-span-12">
                                <label for="customerNameInput" class="form-label">Customer Name</label>
                                <input type="text" id="customerNameInput" class="form-input" placeholder="Customer name" x-model="form.customersName" @input="validateField('customersName', form.customersName, 'Full name is required.')">
                                <span x-show="errors.customersName" class="text-red-500" x-text="errors.customersName"></span>
                            </div>
                            <div class="col-span-6">
                                <label for="quantityInput" class="form-label">Quantity</label>
                                <div>
                                    <div class="input-spin-group">
                                        <div data-modal-target="addOrderModal" @click="decreaseQty" class="input-spin-minus"><i class="size-4" data-lucide="minus"></i></div>
                                        <input type="text" x-model="form.qty" class="input-spin form-input" id="quantityInput" readonly>
                                        <div data-modal-target="addOrderModal" @click="increaseQty" class="input-spin-plus"><i class="size-4" data-lucide="plus"></i></div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-span-6">
                                <label for="totalAmountInput" class="form-label">Product Amount</label>
                                <input type="number" class="form-input" placeholder="Amount" x-model="form.price" @input="validateField('price', form.price, 'Price is required.')">
                                <span x-show="errors.price" class="text-red-500" x-text="errors.price"></span>
                            </div>
                            <div class="col-span-12">
                                <label for="productNameSelect" class="form-label">Total Price</label>
                                <input type="number" class="form-input" placeholder="Total Amount" x-model.number="form.total" readonly>
                            </div>
                            <div class="col-span-12">
                                <label for="productNameSelect" class="form-label">Products Name</label>
                                <div id="productNameSelect" x-model="form.productName" @change="validateField('productName', document.querySelector('#productNameSelect')  , 'Product name is required.')"></div>
                                <span x-show="errors.productName" class="text-red-500" x-text="errors.productName"></span>
                            </div>
                            <div class="col-span-6">
                                <label for="paymentStatusSelect" class="form-label">Payment Status</label>
                                <div id="paymentStatusSelect" x-model="form.payment" @change="validateField('payment', document.querySelector('#paymentStatusSelect') , 'Payment is required.')"></div>
                                <span x-show="errors.payment" class="text-red-500" x-text="errors.payment"></span>
                            </div>
                            <div class="col-span-6">
                                <label for="orderStatusSelect" class="form-label">Order Status</label>
                                <div id="orderStatusSelect" x-model="form.status" @change="validateField('status', document.querySelector('#orderStatusSelect')  , 'Status is required.')"></div>
                                <span x-show="errors.status" class="text-red-500" x-text="errors.status"></span>
                            </div>
                        </div>
                    </form>
                    <div class="flex justify-end gap-2 mt-5">
                        <button data-modal-close="addOrderModal" type="button" class="btn btn-active-red">Cancel</button>
                        <button class="btn btn-primary" x-text="showAddOrderForm ? 'Add Order' : 'Update Order'" @click="submitForm()"></button>
                    </div>
                </div>
            </div>
        </div>

        <div id="overviewOrderModal" class="!hidden modal show">
            <div class="modal-wrap modal-center">
                <div class="modal-content">
                    <button data-modal-close="overviewOrderModal" class="link link-red float-end"><i data-lucide="x" class="size-5"></i></button>
                    <div class="p-2 border border-gray-200 border-dashed rounded-md dark:border-dark-800 size-24">
                        <img :src="selectedOrder.image" alt="">
                    </div>
                    <h6 class="mt-4 mb-2">Order<a href="#!" x-text="selectedOrder.ordersID"></a></h6>
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <p class="mb-1 text-gray-500 dark:text-dark-500">Customers</p>
                            <h6 x-text="selectedOrder.customersName"></h6>
                        </div>
                        <div>
                            <p class="mb-1 text-gray-500 dark:text-dark-500">Items</p>
                            <h6><a href="#!" class="text-gray-800 link link-primary" x-text="selectedOrder.productName"></a></h6>
                        </div>
                        <div>
                            <p class="mb-1 text-gray-500 dark:text-dark-500">Order Date</p>
                            <h6 x-text="selectedOrder.ordersDate"></h6>
                        </div>
                        <div>
                            <p class="mb-1 text-gray-500 dark:text-dark-500">Delivered Date</p>
                            <h6 x-text="selectedOrder.deliveredDate"></h6>
                        </div>
                        <div>
                            <p class="mb-1 text-gray-500 dark:text-dark-500">Payment</p>
                            <span :class="{ 
                                    'badge badge-green': selectedOrder.payment === 'Paid',
                                    'badge badge-gray': selectedOrder.payment === 'COD',
                                    'badge badge-red': selectedOrder.payment === 'Unpaid'
                                }" x-text="selectedOrder.payment"></span>
                        </div>
                        <div>
                            <p class="mb-1 text-gray-500 dark:text-dark-500">Status</p>
                            <span :class="{
                                    'badge badge-green': selectedOrder.status === 'Delivered',
                                    'badge badge-primary': selectedOrder.status === 'New',
                                    'badge badge-red': selectedOrder.status === 'Cancelled',
                                    'badge badge-purple': selectedOrder.status === 'Shipping',
                                    'badge badge-yellow': selectedOrder.status === 'Pending'
                                }" x-text="selectedOrder.status"></span>
                        </div>
                        <div>
                            <p class="mb-1 text-gray-500 dark:text-dark-500">Product Quantity</p>
                            <h6 x-text="selectedOrder.qty"></h6>
                        </div>
                        <div>
                            <p class="mb-1 text-gray-500 dark:text-dark-500">Total Amount</p>
                            <h6 x-text="selectedOrder.total"></h6>
                        </div>
                    </div>
                    <div class="flex justify-end gap-2 mt-5">
                        <button type="button" class="btn btn-active-red" data-modal-close="overviewOrderModal">Cancel</button>
                        <button type="submit" class="btn btn-primary" data-modal-close="overviewOrderModal" data-modal-target="addOrderModal">Edit Order</button>
                    </div>
                </div>
            </div>
        </div>

    </div>
</div>
{{> partials/footer }}
</div>

</div>
{{> partials/vendor-scripts }}

<script type="module" src="assets/js/apps/ecommerce/orders-list.init.js"></script>
<script type="module" src="assets/js/main.js"></script>

</body>
</html>