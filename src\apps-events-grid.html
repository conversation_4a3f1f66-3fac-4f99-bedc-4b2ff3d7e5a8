{{> partials/main }}

<head>

    {{> partials/title-meta title="Grid View" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}
{{> partials/page-heading title="Grid View" sub-title="Events" }}


<div x-data="eventsTable()">
    <div class="flex flex-wrap items-center gap-5">
        <h6 class="grow">Event Listings (<span x-text="totalEvents"></span>)</h6>
        <div class="flex items-center gap-2 shrink-0">
            <div x-data="{
                open: false,
                toggle() {
                    if (this.open) {
                        return this.close();
                    }
                    this.$refs.button.focus();
                    this.open = true;
                },
                close(focusAfter) {
                    if (!this.open) return;
                    this.open = false;
                    focusAfter && focusAfter.focus();
                }
            }" x-on:keydown.escape.prevent.stop="close($refs.button)" x-on:focusin.window="!$refs.panel.contains($event.target) && close()" x-id="['dropdown-button']" class="dropdown shrink-0">
                <button x-ref="button" x-on:click="toggle()" :aria-expanded="open" :aria-controls="$id('dropdown-button')" type="button" title="dropdown-button" class="btn btn-sub-gray btn-icon btn-icon-text">
                    <i data-lucide="sliders-horizontal" class="size-4"></i>
                </button>

                <div x-ref="panel" x-show="open" x-transition.origin.top.right x-on:click.outside="close($refs.button)" :id="$id('dropdown-button')" style="display: none;" class="p-2 !w-52 dropdown-menu dropdown-right">
                    <ul>
                        <li>
                            <a href="#!" x-on:click="init , sort(null) " class="dropdown-item">
                                No Sorting
                            </a>
                        </li>
                        <li>
                            <a href="#!" x-on:click="sort('name')" class="dropdown-item">
                                Alphabetical (A -> Z)
                            </a>
                        </li>
                        <li>
                            <a href="#!" x-on:click="sort('name')" class="dropdown-item">
                                Reverse Alphabetical (Z -> A)
                            </a>
                        </li>
                        <li>
                            <a href="#!" x-on:click="sort('status')" class="dropdown-item">
                                Status
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
            <button class="btn btn-primary shrink-0" data-modal-target="addEventModal" @click="handleModal('showAddEventForm')"><i data-lucide="plus" class="inline-block size-4"></i><span class="align-baseline">Add New Event</span></button>
        </div>
    </div>

    <div class="grid grid-cols-1 mt-5 md:grid-cols-2 xl:grid-cols-3 gap-x-space">
        <template x-for="(event, index) in displayedEvents" :key="index">
            <div class="card">
                <div class="card-body">
                    <div class="flex items-center gap-3">
                        <img :src="event.image" alt="" class="rounded-full size-12 shrink-0">
                        <div class="grow">
                            <h6 class="mb-1"><a href="#!" x-text="event.username"></a></h6>
                            <p class="text-sm text-gray-500 dark:text-dark-500" x-text="formatDateTime(event.date, event.time)"></p>
                        </div>
                        <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown shrink-0">
                            <button x-ref="button" x-on:click="toggle()" :aria-expanded="open.toString()" type="button" title="dropdown-button" class="flex items-center text-gray-500 dark:text-dark-500">
                                <i class="ri-more-fill"></i>
                            </button>
                            <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 dropdown-menu hidden">
                                <ul>
                                    <li>
                                        <a href="apps-event-overview.html" class="dropdown-item">
                                            <i class="align-middle ltr:mr-2 rtl:ml-2 ri-eye-line"></i> <span>Overview</span>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="#!" @click="editEvent(event.name)" data-modal-target="addEventModal" class="dropdown-item">
                                            <i class="align-middle ltr:mr-2 rtl:ml-2 ri-pencil-line"></i> <span>Edit</span>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="#!" @click="deleteEvent(index); close()" class="dropdown-item">
                                            <i class="align-middle ltr:mr-2 rtl:ml-2 ri-delete-bin-line"></i> <span>Delete</span>
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="mt-5">
                        <img :src="event.mainImage" alt="" class="object-cover w-full h-48 rounded-md">
                    </div>
                    <div class="flex gap-3 mt-5">
                        <div>
                            <div class="flex flex-col items-center justify-center mx-auto mb-2.5 border bg-red-500/20 rounded-md border-red-500/20 size-16 text-red-500">
                                <p class="mb-0.5" x-text="getWeekday(event.date)"></p>
                                <h3 class="leading-none" x-text="new Date(event.date).getDate()"></h3>
                            </div>
                            <button type="button" data-modal-target="bookEventModal" @click="bookEvent(event)" class="btn btn-primary">Book</button>
                        </div>
                        <div>
                            <h6 class="mb-1"><a href="#!" class="text-current link link-primary" x-text="event.name"></a></h6>
                            <p class="mb-2 text-gray-500 dark:text-dark-500"><span x-text="formatDate(event.date)"></span><span class="ltr:pl-2 rtl:pr-2 ltr:ml-1.5 rtl:mr-1.5 ltr:border-l rtl:border-r border-gray-200 dark:border-dark-800" x-text="event.location"></span></p>
                            <p class="mb-1 text-gray-500 dark:text-dark-500">Contributors</p>
                            <div class="flex mx-3 -space-x-3 grow rtl:space-x-reverse">
                                <template x-for="contributor in event.contributors" :key="index">
                                    <a href="#!" class="transition duration-300 ease-linear hover:z-10" title="avatar link"><img class="border-2 border-white rounded-full size-8 dark:border-dark-900" :src="contributor.image" alt=""></a>
                                </template>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </template>
    </div>
    <div class="grid grid-cols-12 gap-5 mb-5 items-center">
        <div class="col-span-12 md:col-span-6 justify-center md:justify-start flex flex-wrap">
            <p class="text-gray-500 dark:text-dark-500">Showing <b x-text="showingStart"></b> - <b x-text="showingEnd"></b> of <b x-text="filterEvents.length"></b> Results</p>
        </div>
        <div class="col-span-12 md:col-span-6">
            <div class="flex justify-center md:justify-end pagination pagination-primary">
                <button @click="prevPage()" :disabled="currentPage === 1" class="pagination-pre">
                    <i data-lucide="chevron-left" class="mr-1 ltr:inline-block rtl:hidden size-4"></i>
                    <i data-lucide="chevron-right" class="ml-1 ltr:hidden rtl:inline-block size-4"></i>
                    Prev
                </button>
                <template x-for="page in totalPages" :key="page">
                    <button @click="gotoPage(page)" :class="{ 'active': currentPage === page }" class="pagination-item">
                        <span x-text="page"></span>
                    </button>
                </template>
                <button @click="nextPage()" :disabled="currentPage === totalPages" class="pagination-next">
                    Next
                    <i data-lucide="chevron-right" class="ml-1 ltr:inline-block rtl:hidden size-4"></i>
                    <i data-lucide="chevron-left" class="mr-1 ltr:hidden rtl:inline-block size-4"></i>
                </button>
            </div>
        </div>
    </div>

    <div id="addEventModal" class="!hidden modal show" :class="{'show d-block': showAddEventForm || showEditEventForm}" x-show="showAddEventForm || showEditEventForm">
        <div class="modal-wrap modal-center">
            <div class="modal-header">
                <h6 x-text="showAddEventForm ? 'Add New Event' : 'Edit Event'">Add New Event</h6>
                <button data-modal-close="addEventModal" class="link link-red float-end"><i data-lucide="x" class="size-5"></i></button>
            </div>
            <div class="modal-content">
                <div class="grid grid-cols-12 gap-5">
                    <div class="col-span-12">
                        <h6 class="form-label">Event Logo</h6>
                        <div>
                            <label for="logo">
                                <span class="inline-flex items-center justify-center w-full h-32 overflow-hidden bg-gray-100 border border-gray-200 rounded-md cursor-pointer dark:bg-dark-850 dark:border-dark-800">
                                    <img x-show="eventForm.mainImage" :src="eventForm.mainImage" class="object-cover h-24">
                                    <span x-show="!eventForm.mainImage" class="flex flex-col items-center text-gray-500 dark:text-dark-500">
                                        <i data-lucide="upload"></i>
                                        <span class="block mt-3">Upload Your Event Logo</span>
                                    </span>
                                </span>
                            </label>
                            <div class="hidden">
                                <label class="block">
                                    <span class="sr-only">Choose profile photo</span>
                                    <input type="file" name="logo" id="logo" @change="fileChosen" class="block w-full text-sm file:rounded-md focus:outline-0 text-slate-500 file:mr-4 file:py-2 file:px-4 file:border-0 file:text-sm file:font-semibold file:bg-violet-50 file:text-violet-700 hover:file:bg-violet-100" />
                                </label>
                            </div>
                        </div>
                        <span x-show="errors.image" class="text-red-500" x-text="errors.image"></span>
                    </div>
                    <div class="col-span-12">
                        <label for="nameInput" class="form-label">Event Name</label>
                        <input type="text" id="nameInput" class="form-input" placeholder="Event name" x-model="eventForm.name" @input="validateField('name', eventForm.name, 'Event name is required.')">
                        <span x-show="errors.name" class="text-red-500" x-text="errors.name"></span>
                    </div>
                    <div class="col-span-6">
                        <label for="dateInput" class="form-label">Event Date</label>
                        <input type="text" placeholder="YYYY-MM-DD" id="dateInput" class="form-input" data-provider="flatpickr" data-date-format="Y-m-d" x-model="eventForm.date" @input="validateField('date', eventForm.date, 'Date is required.')">
                        <span x-show="errors.date" class="text-red-500" x-text="errors.date"></span>
                    </div>
                    <div class="col-span-6">
                        <label for="eventDurationInput" class="form-label">Event Duration</label>
                        <input type="text" placeholder="00:00" id="eventDurationInput" class="form-input" data-provider="timepickr" data-time-basic x-model="eventForm.time" @input="validateField('time', eventForm.time, 'Event duration is required.')">
                        <span x-show="errors.time" class="text-red-500" x-text="errors.time"></span>
                    </div>
                    <div class="col-span-6">
                        <label for="peopleInput" class="form-label">Total People</label>
                        <input type="number" placeholder="0" id="peopleInput" class="form-input" x-model="eventForm.peopleSize" @input="validateField('peopleSize', eventForm.peopleSize, 'People size is required.')">
                        <span x-show="errors.peopleSize" class="text-red-500" x-text="errors.peopleSize"></span>
                    </div>
                    <div class="col-span-6">
                        <label for="priceInput" class="form-label">Price</label>
                        <input type="number" placeholder="$00.00" id="priceInput" class="form-input" x-model="eventForm.price" @input="validateField('price', eventForm.price, 'Price is required.')">
                        <span x-show="errors.price" class="text-red-500" x-text="errors.price"></span>
                    </div>
                    <div class="col-span-12">
                        <label for="locationInput" class="form-label">Location</label>
                        <input type="text" placeholder="Enter event location" id="locationInput" class="form-input" x-model="eventForm.location" @input="validateField('location', eventForm.location, 'Location is required.')">
                        <span x-show="errors.location" class="text-red-500" x-text="errors.location"></span>
                    </div>
                    <div class="col-span-12">
                        <label for="contributorsSelect" class="form-label">contributors </label>
                        <div id="contributorsSelect" placeholder="Select contributor To" x-model="eventForm.contributor" @change="validateField('contributors', $event.target.value )"></div>
                        <span x-show="errors.contributors" class="text-red-500" x-text="errors.contributors"></span>
                    </div>
                    <div class="col-span-6">
                        <label for="eventTypeSelect" class="form-label">Event Type</label>
                        <div id="eventTypeSelect" placeholder="Select event type" x-model="eventForm.eventType" @change="validateField('eventType' , $event.target.value )"></div>
                        <span x-show="errors.eventType" class="text-red-500" x-text="errors.eventType"></span>
                    </div>
                    <div class="col-span-6">
                        <label for="StatusSelect" class="form-label">Status</label>
                        <div type="text" id="StatusSelect" placeholder="Select status" x-model="eventForm.status" @change="validateField('status', $event.target.value )"></div>
                        <span x-show="errors.status" class="text-red-500" x-text="errors.status"></span>
                    </div>
                </div>
                <div class="flex justify-end gap-2 mt-5">
                    <button type="button" data-modal-close="addEventModal" class="btn btn-active-red">Cancel</button>
                    <button class="btn btn-primary" @click="submitForm()" x-text="showAddEventForm ? 'Add Event' : 'Edit Event'">Add Event</button>
                </div>
            </div>
        </div>
    </div>

    <div id="bookEventModal" class="!hidden modal show" x-data="bookEventModal()">
        <div class="modal-wrap modal-center modal-lg">
            <div class="modal-header">
                <h6 x-text="selectedItems.name">Tech Innovations Summit</h6>
                <button data-modal-close="bookEventModal" class="link link-red ltr:float-end rtl:float-start"><i data-lucide="x" class="size-5"></i></button>
            </div>
            <div class="modal-content">
                <div class="flex items-center gap-3 mb-4">
                    <img :src="selectedItems.image" alt="" class="rounded-full size-12 shrink-0">
                    <div class="grow">
                        <h6 class="mb-1"><a href="#!" x-text="selectedItems.username">Declan Grieve</a></h6>
                        <p class="text-sm text-gray-500 dark:text-dark-500" x-text="formatDateTime(selectedItems.date, selectedItems.time)">Sun, 19 May 2024, 3:30 pm</p>
                    </div>
                </div>
                <div class="mt-5">
                    <img :src="selectedItems.mainImage" alt="" class="object-cover w-full h-48 rounded-md">
                </div>
                <div class="flex gap-3 mt-5">
                    <div>
                        <div class="flex flex-col items-center justify-center mx-auto text-red-500 border rounded-md bg-red-500/15 border-red-500/20 size-16">
                            <p class="mb-0.5" x-text="getWeekday(selectedItems.date)">Sun</p>
                            <h3 class="leading-none" x-text="new Date(selectedItems.date).getDate()">19</h3>
                        </div>
                    </div>
                    <div>
                        <h6 class="mb-1"><a href="#!" class="text-current link link-primary" x-text="selectedItems.name">Tech Innovations Summit</a></h6>
                        <p class="mb-2 text-gray-500 dark:text-dark-500"><span x-text="formatDate(selectedItems.date)"></span>19 May 2024<span class="ltr:pl-2 rtl:pr-2 ltr:ml-1.5 rtl:mr-1.5 ltr:border-l rtl:border-r border-gray-200 dark:border-dark-800" x-text="selectedItems.location">San Francisco, CA</span></p>
                        <p class="mb-1 text-gray-500 dark:text-dark-500">Contributors</p>
                        <div class="flex -space-x-3 grow rtl:space-x-reverse">
                            <template x-for="contributor in selectedItems.contributors" :key="index">
                                <a href="#!" class="transition duration-300 ease-linear hover:z-10"><img class="border-2 border-white rounded-full size-8 dark:border-dark-900" :src="contributor.image" alt=""></a>
                            </template>
                        </div>
                    </div>
                </div>
                <form action="#!">
                    <div class="grid grid-cols-12 gap-5">
                        <div class="col-span-12">
                            <label for="fullNameInput" class="form-label">Full Name</label>
                            <input type="text" id="fullNameInput" class="form-input" placeholder="Enter full name">
                        </div>
                        <div class="col-span-6">
                            <label for="totalTicketInput" class="form-label">Total Tickets</label>
                            <input type="number" id="totalTicketInput" class="form-input" placeholder="0" x-model="totalTickets" @input="calculatePrice">
                        </div>
                        <div class="col-span-6">
                            <label for="pricePerTicketInput" class="form-label">Price per Ticket</label>
                            <input type="number" id="pricePerTicketInput" class="form-input" placeholder="$0.00" value="499" x-model="pricePerTicket" @input="calculatePrice">
                        </div>
                        <div class="col-span-6">
                            <label for="totalAmountInput" class="form-label">Total Amount</label>
                            <input type="text" id="totalAmountInput" class="form-input" x-model="totalAmount" placeholder="$0.00" readonly>
                        </div>
                        <div class="col-span-12 text-right">
                            <button type="submit" data-modal-close="bookEventModal" class="btn btn-primary">Book Now</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

</div>

</div>
{{> partials/footer }}
</div>

{{> partials/vendor-scripts }}

<script type="module" src="assets/js/apps/events/grid.init.js"></script>

<script type="module" src="assets/js/main.js"></script>

</body>
</html>