{{> partials/main }}

<head>

    {{> partials/title-meta title="List View" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}
{{> partials/page-heading title="List View" sub-title="Teachers" }}

<div class="grid grid-cols-12 gap-x-space">
    <div class="col-span-12 md:col-span-3 card">
        <div class="card-body">
            <div class="flex items-center justify-center border rounded-full border-yellow-500/20 size-12 bg-yellow-500/10">
                <i data-lucide="archive" class="text-yellow-500 size-5 fill-yellow-500/20"></i>
            </div>
            <div class="mt-4">
                <h6 class="mb-2">English teacher of the month</h6>
                <div class="flex items-center gap-2">
                    <img src="assets/images/avatar/user-11.png" alt="" class="rounded-full size-6">
                    <p class="text-gray-500 dark:text-dark-500"><PERSON></p>
                </div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 md:col-span-3 card">
        <div class="card-body">
            <div class="flex items-center justify-center border rounded-full border-sky-500/20 size-12 bg-sky-500/10">
                <i data-lucide="blend" class="text-sky-500 size-5 fill-sky-500/20"></i>
            </div>
            <div class="mt-4">
                <h6 class="mb-2">Physics teacher of the month</h6>
                <div class="flex items-center gap-2">
                    <img src="assets/images/avatar/user-15.png" alt="" class="rounded-full size-6">
                    <p class="text-gray-500 dark:text-dark-500">April Lovell</p>
                </div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 md:col-span-3 card">
        <div class="card-body">
            <div class="flex items-center justify-center border rounded-full border-green-500/20 size-12 bg-green-500/10">
                <i data-lucide="container" class="text-green-500 size-5 fill-green-500/20"></i>
            </div>
            <div class="mt-4">
                <h6 class="mb-2">History teacher of the month</h6>
                <div class="flex items-center gap-2">
                    <img src="assets/images/avatar/user-16.png" alt="" class="rounded-full size-6">
                    <p class="text-gray-500 dark:text-dark-500">Elisa Harris</p>
                </div>
            </div>
        </div>
    </div><!--end col-->
    <div class="col-span-12 md:col-span-3 card">
        <div class="card-body">
            <div class="flex items-center justify-center border rounded-full border-red-500/20 size-12 bg-red-500/20">
                <i data-lucide="cross" class="text-red-500 size-5 fill-red-500/20"></i>
            </div>
            <div class="mt-4">
                <h6 class="mb-2">Biology teacher of the month</h6>
                <div class="flex items-center gap-2">
                    <img src="assets/images/avatar/user-17.png" alt="" class="rounded-full size-6">
                    <p class="text-gray-500 dark:text-dark-500">Jeanne Lane</p>
                </div>
            </div>
        </div>
    </div><!--end col-->
</div><!--end grid-->

<div class="grid grid-cols-12 gap-x-space" x-data="teachersTable" x-init="init()">
    <div class="col-span-12 card">
        <div class="card-header">
            <div class="grid items-center grid-cols-12 gap-space">
                <div class="col-span-12 md:col-span-4 2xl:col-span-3">
                    <div class="relative group/form grow">
                        <input type="email" class="ltr:pl-9 rtl:pr-9 form-input ltr:group-[&.right]/form:pr-9 rtl:group-[&.right]/form:pl-9 ltr:group-[&.right]/form:pl-4 rtl:group-[&.right]/form:pr-4" placeholder="Search for ..." x-model="searchTerm" @input="filterTeachers()">
                        <button class="absolute inset-y-0 flex items-center ltr:left-3 rtl:right-3 ltr:group-[&.right]/form:right-3 rtl:group-[&.right]/form:left-3 ltr:group-[&.right]/form:left-auto rtl:group-[&.right]/form:right-auto focus:outline-hidden">
                            <i data-lucide="search" class="text-gray-500 dark:text-dark-500 size-4 fill-gray-100 dark:fill-dark-850"></i>
                        </button>
                    </div>
                </div>
                <div class="col-span-12 md:col-start-9 md:col-span-4 2xl:col-span-2 2xl:col-start-11 md:ltr:text-right md:rtl:text-left">
                    <button class="btn btn-primary shrink-0" @click="handleModal('showAddTeacherForm')" data-modal-target="addTeacherModal"><i data-lucide="circle-plus" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> Add Teacher</button>
                </div>
            </div>
        </div>
        <div class="pt-0 card-body">
            <div>
                <div class="overflow-x-auto table-box whitespace-nowrap">
                    <table class="table flush">
                        <tbody>
                            <tr class="text-gray-500 bg-gray-100 dark:bg-dark-850 dark:text-dark-500">
                                <th x-on:click="sort('teacherID')" class="!font-medium cursor-pointer">ID <span x-show="sortBy === 'teacherID'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th x-on:click="sort('teacherName')" class="!font-medium cursor-pointer">Teacher Name <span x-show="sortBy === 'teacherName'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th x-on:click="sort('email')" class="!font-medium cursor-pointer">Email <span x-show="sortBy === 'email'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th x-on:click="sort('phone')" class="!font-medium cursor-pointer">Phone <span x-show="sortBy === 'phone'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th x-on:click="sort('salary')" class="!font-medium cursor-pointer">Salary <span x-show="sortBy === 'salary'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th x-on:click="sort('experience')" class="!font-medium cursor-pointer">Experience <span x-show="sortBy === 'experience'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th x-on:click="sort('title')" class="!font-medium cursor-pointer">Title <span x-show="sortBy === 'title'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th x-on:click="sort('date')" class="!font-medium cursor-pointer">Joining Date <span x-show="sortBy === 'date'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th class="!font-medium">Action</th>
                            </tr>
                            <template x-for="(teacher, index) in displayedTeachers" :key="index">
                                <tr class="*:px-3 *:py-2.5">
                                    <td x-text="teacher.teacherID"></td>
                                    <td>
                                        <div class="flex items-center gap-3">
                                            <div class="relative text-gray-500 bg-gray-100 rounded-full dark:text-dark-500 dark:bg-dark-850 size-8">
                                                <img :src="teacher.image" alt="" class="rounded-full" @error="removeImage($event)">
                                                <span x-show="!teacher.image" x-text="teacher.avatarText" class="absolute inset-0 flex items-center justify-center text-xs font-semibold text-gray-500 bg-gray-100 rounded-full dark:text-dark-500 dark:bg-dark-850"></span>
                                            </div>
                                            <div>
                                                <h6><a href="apps-school-teachers-overview.html" x-text="teacher.teacherName"></a></h6>
                                            </div>
                                        </div>
                                    </td>
                                    <td x-text="teacher.email"></td>
                                    <td x-text="`+${teacher.phone}`"></td>
                                    <td x-text="teacher.salary"></td>
                                    <td x-text="teacher.experience"></td>
                                    <td>
                                        <span x-text="teacher.title" :class="{
                                                    'badge badge-primary': teacher.title === 'Teacher',
                                                    'badge badge-purple': teacher.title === 'Professor',
                                                    'badge badge-green': teacher.title === 'Instructor',
                                                    'badge badge-red': teacher.title === 'Lecturer',
                                                    'badge badge-orange': teacher.title === 'Senior Lecturer',
                                                    'badge badge-gray': teacher.title === 'Associate Professor',
                                                    'badge badge-indigo': teacher.title === 'Assistant Professor',
                                                    'badge badge-yellow': teacher.title === 'Assistant'
                                                }"></span>
                                    </td>
                                    <td x-text="teacher.formattedDate"></td>
                                    <td>
                                        <div class="flex items-center gap-2">
                                            <button class="btn btn-sub-gray btn-icon !size-8 rounded-md" @click="editTeacher(teacher.teacherID)" data-modal-target="addTeacherModal"><i class="ri-pencil-line"></i></button>
                                            <button class="btn btn-sub-red btn-icon !size-8 rounded-md" @click="deleteTeacher = teacher.email" data-modal-target="deleteModal"><i class="ri-delete-bin-line"></i></button>
                                        </div>
                                    </td>
                                </tr>
                            </template>
                            <tr>
                                <template x-if="displayedTeachers.length == 0">
                                    <td colspan="10" class="!p-8">
                                        <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" class="mx-auto size-12" viewBox="0 0 48 48">
                                            <linearGradient id="SVGID_1__h35ynqzIJzH4_gr1" x1="34.598" x2="15.982" y1="15.982" y2="34.598" gradientUnits="userSpaceOnUse">
                                                <stop offset="0" stop-color="#60e8fe"></stop>
                                                <stop offset=".033" stop-color="#6ae9fe"></stop>
                                                <stop offset=".197" stop-color="#97f0fe"></stop>
                                                <stop offset=".362" stop-color="#bdf5ff"></stop>
                                                <stop offset=".525" stop-color="#dafaff"></stop>
                                                <stop offset=".687" stop-color="#eefdff"></stop>
                                                <stop offset=".846" stop-color="#fbfeff"></stop>
                                                <stop offset="1" stop-color="#fff"></stop>
                                            </linearGradient>
                                            <path fill="url(#SVGID_1__h35ynqzIJzH4_gr1)" d="M40.036,33.826L31.68,25.6c0.847-1.739,1.335-3.684,1.335-5.748c0-7.27-5.894-13.164-13.164-13.164	S6.688,12.582,6.688,19.852c0,7.27,5.894,13.164,13.164,13.164c2.056,0,3.995-0.485,5.728-1.326l3.914,4.015l4.331,4.331	c1.715,1.715,4.496,1.715,6.211,0C41.751,38.321,41.751,35.541,40.036,33.826z"></path>
                                            <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M31.95,25.739l8.086,8.086c1.715,1.715,1.715,4.496,0,6.211l0,0c-1.715,1.715-4.496,1.715-6.211,0	l-4.331-4.331"></path>
                                            <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M7.525,24.511c-1.771-4.694-0.767-10.196,3.011-13.975c3.847-3.847,9.48-4.817,14.228-2.912"></path>
                                            <path fill="none" stroke="#10cfe3" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="3" d="M30.856,12.603c3.376,5.114,2.814,12.063-1.688,16.565c-4.858,4.858-12.565,5.129-17.741,0.814"></path>
                                        </svg>
                                        <p class="mt-2 text-center text-gray-500 dark:text-dark-500">No matching records found</p>
                                    </td>
                                </template>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="grid grid-cols-12 mt-space gap-space items-center" x-show="displayedTeachers.length > 0">
                    <div class="col-span-12 text-center md:col-span-6 ltr:md:text-left rtl:md:text-right">
                        <p class="text-gray-500 dark:text-dark-500">Showing <b x-text="showingStart"></b> - <b x-text="showingEnd"></b> of <b x-text="filteredTeachers.length"></b> Results</p>
                    </div>
                    <div class="col-span-12 md:col-span-6">
                        <div class="flex justify-center md:justify-end pagination pagination-primary">
                            <button @click="prevPage()" :disabled="currentPage === 1" class="pagination-pre">
                                <i data-lucide="chevron-left" class="mr-1 ltr:inline-block rtl:hidden size-4"></i>
                                <i data-lucide="chevron-right" class="ml-1 ltr:hidden rtl:inline-block size-4"></i>
                                Prev
                            </button>
                            <template x-for="page in totalPages" :key="page">
                                <button @click="gotoPage(page)" :class="{ 'active': currentPage === page }" class="pagination-item">
                                    <span x-text="page"></span>
                                </button>
                            </template>
                            <button @click="nextPage()" :disabled="currentPage === totalPages" class="pagination-next">
                                Next
                                <i data-lucide="chevron-right" class="ml-1 ltr:inline-block rtl:hidden size-4"></i>
                                <i data-lucide="chevron-left" class="mr-1 ltr:hidden rtl:inline-block size-4"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div><!--end col-->
    <!--Create Teacher-->
    <div id="addTeacherModal" class="!hidden modal show" :class="{'show d-block': showAddTeacherForm || showEditTeacherForm}" x-show="showAddTeacherForm || showEditTeacherForm">
        <div class="modal-wrap modal-center">
            <div class="modal-header">
                <h6 class="modal-title" x-text="showAddTeacherForm ? 'Add Teacher' : 'Edit Teacher'">Add Teacher</h6>
                <button data-modal-close="addTeacherModal" class="link link-red"><i data-lucide="x" class="size-5"></i></button>
            </div>
            <div class="modal-content">
                <div class="grid grid-cols-12 gap-4">
                    <div class="col-span-12">
                        <label for="titleSelect" class="form-label">Title</label>
                        <div id="titleSelect" placeholder="Select Class" x-model="teacherForm.title" @change="validateField('title', document.getElementById('titleSelect') , 'Title is required.');"></div>
                        <span x-show="errors.title" class="text-red-500" x-text="errors.title"></span>
                    </div>
                    <div class="col-span-12">
                        <label for="teacherNameInput" class="form-label">Teacher Name</label>
                        <input type="text" id="teacherNameInput" class="form-input" placeholder="Teacher name" x-model="teacherForm.teacherName" @input="validateField('teacherName', teacherForm.teacherName, 'Teacher name is required.');">
                        <span x-show="errors.teacherName" class="text-red-500" x-text="errors.teacherName"></span>
                    </div>
                    <div class="col-span-6">
                        <label for="emailInput" class="form-label">Email</label>
                        <input type="email" id="emailInput" class="form-input" placeholder="<EMAIL>" x-model="teacherForm.email" @input="validateField('email', teacherForm.email, 'Email is required.');">
                        <span x-show="errors.email" class="text-red-500" x-text="errors.email"></span>
                    </div>
                    <div class="col-span-6">
                        <label for="phoneInput" class="form-label">Phone</label>
                        <input type="text" id="phoneInput" class="form-input" placeholder="+1012456789" x-model="teacherForm.phone" @input="validatePhone()">
                        <span x-show="errors.phone" class="text-red-500" x-text="errors.phone"></span>
                    </div>
                    <div class="col-span-6">
                        <label for="salaryInput" class="form-label">Salary ($)</label>
                        <input type="text" id="salaryInput" class="form-input" placeholder="$0" x-model="teacherForm.salary" @input="validateField('salary', teacherForm.salary, 'Salary is required.');">
                        <span x-show="errors.salary" class="text-red-500" x-text="errors.salary"></span>
                    </div>
                    <div class="col-span-6">
                        <label for="experienceInput" class="form-label">Experience (Years)</label>
                        <input type="text" id="experienceInput" class="form-input" placeholder="0 Years" x-model="teacherForm.experience" @input="validateField('experience', teacherForm.experience, 'Experience is required.');">
                        <span x-show="errors.experience" class="text-red-500" x-text="errors.experience"></span>
                    </div>
                    <!-- <template x-if="showAddTeacherForm"> -->
                    <div class="col-span-12">
                        <label for="lastSchoolNameInput" class="form-label">Last School Name</label>
                        <input type="text" id="lastSchoolNameInput" class="form-input" placeholder="School name" x-model="teacherForm.lastSchool" @input="validateField('lastSchool', teacherForm.lastSchool, 'Last school is required.');">
                        <span x-show="errors.lastSchool" class="text-red-500" x-text="errors.lastSchool"></span>
                    </div>
                    <!-- </template> -->
                    <div class="col-span-12">
                        <label for="joiningDateSelect" class="form-label">Joining Date</label>
                        <input id="joiningDateSelect" data-provider="flatpickr" data-date-format="d M, Y" type="text" placeholder="DD-MM-YYYY" class="form-input" x-model="teacherForm.date" @change="validateField('date', teacherForm.date, 'Date is required.');">
                        <span x-show="errors.date" class="text-red-500" x-text="errors.date"></span>
                    </div>
                </div>
                <div class="flex items-center justify-end gap-2 mt-5">
                    <button type="button" class="btn btn-active-red" data-modal-close="addTeacherModal">
                        <i data-lucide="x" class="inline-block size-4"></i>
                        <span class="align-baseline">Close</span>
                    </button>
                    <button type="button" class="btn btn-primary" x-text="showAddTeacherForm ? 'Add Teacher' : 'Update Teacher'" @click="submitForm()">Add Teacher</button>
                </div>
            </div>
        </div>
    </div>

    <!--delete modal-->
    <div id="deleteModal" class="!hidden modal show">
        <div class="modal-wrap modal-xs modal-center">
            <div class="text-center modal-content p-7">
                <div class="flex items-center justify-center mx-auto mb-4 text-red-500 rounded-full bg-red-500/10 size-14 backdrop-blur-xl">
                    <i data-lucide="trash-2" class="size-6"></i>
                </div>
                <h5 class="mb-4">Are you sure you want to delete this teacher ?</h5>
                <div class="flex items-center justify-center gap-2">
                    <button class="btn btn-red" @click="deleteTeacherData()" data-modal-close="deleteModal">Delete</button>
                    <button data-modal-close="deleteModal" class="btn link link-primary">Cancel</button>
                </div>
            </div>
        </div>
    </div><!--end-->
</div><!--end grid-->

</div>
{{> partials/footer }}
</div>



{{> partials/vendor-scripts }}

<script type="module" src="assets/js/apps/school/teachers/teachers-list.init.js"></script>

<script type="module" src="assets/js/main.js"></script>

</body>
</html>