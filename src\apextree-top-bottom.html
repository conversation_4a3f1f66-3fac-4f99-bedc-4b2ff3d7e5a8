{{> partials/main }}

<head>

    {{> partials/title-meta title="Top to Bottom" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}

{{> partials/page-heading title="Top to Bottom" sub-title="Apextree" }}

<div class="grid grid-cols-12 gap-x-space">
    <div class="col-span-12 card">
        <div class="card-header">
            <h6 class="card-title">Top to Bottom</h6>
        </div>
        <div class="card-body">
            <div x-data="apexTreeApp()" x-init="init" class="border border-gray-200 rounded-md dark:border-dark-800">
                <div id="apex-tree-container" x-ref="apexTreeContainer" data-chart-colors="[bg-gray-200, bg-white, bg-primary-500, bg-purple-500, bg-yellow-500, bg-gray-800, bg-orange-500, bg-green-500, bg-pink-500]" data-chart-dark-colors="[bg-dark-800, bg-white, bg-primary-500, bg-purple-500, bg-yellow-500, bg-dark-800, bg-orange-500, bg-green-500, bg-pink-500]"></div>
            </div>
            
        </div>
    </div><!--end col-->
</div><!--end grid-->

</div>
{{> partials/footer }}
</div>

{{> partials/vendor-scripts }}

<script type="module" src="assets/js/charts/apextree-top-bottom.init.js"></script>

<script type="module" src="assets/js/main.js"></script>

</body>
</html>