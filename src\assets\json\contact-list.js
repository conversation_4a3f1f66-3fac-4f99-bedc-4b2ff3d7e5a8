import user1 from "/assets/images/avatar/user-1.png";
import user2 from "/assets/images/avatar/user-2.png";
import user3 from "/assets/images/avatar/user-3.png";
import user4 from "/assets/images/avatar/user-4.png";
import user5 from "/assets/images/avatar/user-5.png";
import user6 from "/assets/images/avatar/user-6.png";
import user7 from "/assets/images/avatar/user-7.png";
import user8 from "/assets/images/avatar/user-8.png";
import user9 from "/assets/images/avatar/user-9.png";
import user10 from "/assets/images/avatar/user-10.png";
import user11 from "/assets/images/avatar/user-11.png";
import user12 from "/assets/images/avatar/user-12.png";
import user13 from "/assets/images/avatar/user-13.png";
import user14 from "/assets/images/avatar/user-14.png";
import user15 from "/assets/images/avatar/user-15.png";
import user16 from "/assets/images/avatar/user-16.png";
import user17 from "/assets/images/avatar/user-17.png";
import user18 from "/assets/images/avatar/user-18.png";

const data = [
    {
        "image": user1,
        "contactName": "Pat Martinez",
        "phoneNumber": "+890 1829 15781",
        "company": "StarTech Dynamics",
        "role": "Web Designer",
        "email": "<EMAIL>",
        "website": "patmartizen.com",
        "status": "Customer"
    },
    {
        "image": user2,
        "contactName": "Jane Brown",
        "phoneNumber": "+957 6326 78821",
        "company": "StarTech Dynamics",
        "role": "Web Designer",
        "email": "<EMAIL>",
        "website": "janebrown.com",
        "status": "Personal"
    },
    {
        "image": user3,
        "contactName": "John Davis",
        "phoneNumber": "+264 1427 33002",
        "company": "StarTech Dynamics",
        "role": "UI / UX Designer",
        "email": "<EMAIL>",
        "website": "johndavis.com",
        "status": "Customer"
    },
    {
        "image": user4,
        "contactName": "Jordan Davis",
        "phoneNumber": "+688 9444 65363",
        "company": "BrightFuture Tech",
        "role": "Graphic Designer",
        "email": "<EMAIL>",
        "website": "jordandavis.com",
        "status": "Customer"
    },
    {
        "image": user5,
        "contactName": "Alex Lee",
        "phoneNumber": "+300 8108 69119",
        "company": "Quantum Innovations",
        "role": "Co Founder",
        "email": "<EMAIL>",
        "website": "alexlee.com",
        "status": "Personal"
    },
    {
        "image": user6,
        "contactName": "Casey Martinez",
        "phoneNumber": "+646 9347 84543",
        "company": "BrightFuture Tech",
        "role": "Graphic Designer",
        "email": "<EMAIL>",
        "website": "casetmartinez.com",
        "status": "Personal"
    },
    {
        "image": user7,
        "contactName": "Taylor Wilson",
        "phoneNumber": "+749 6102 50325",
        "company": "BrightFuture Tech",
        "role": "ASP.Net Developer",
        "email": "<EMAIL>",
        "website": "taylor.com",
        "status": "Customer"
    },
    {
        "image": user8,
        "contactName": "Chris Smith",
        "phoneNumber": "+829 5728 93265",
        "company": "BlueSky Enterprises",
        "role": "Product Manager",
        "email": "<EMAIL>",
        "website": "chrissmith.com",
        "status": "Employee"
    },
    {
        "image": user9,
        "contactName": "Jane Brown",
        "phoneNumber": "+213 9689 10505",
        "company": "BrightFuture Tech",
        "role": "web Developer",
        "email": "<EMAIL>",
        "website": "jane.brigth.com",
        "status": "Personal"
    },
    {
        "image": user10,
        "contactName": "John Garcia",
        "phoneNumber": "+846 9274 23870",
        "company": "Quantum Innovations",
        "role": "Product Manager",
        "email": "<EMAIL>",
        "website": "johangarcia.com",
        "status": "Personal"
    },
    {
        "image": user11,
        "contactName": "Chris Wilson",
        "phoneNumber": "+285 1994 96029",
        "company": "BlueSky Enterprises",
        "role": "React Developer",
        "email": "<EMAIL>",
        "website": "chriswilson.com",
        "status": "Customer"
    },
    {
        "image": user12,
        "contactName": "Alex Lee",
        "phoneNumber": "+695 2025 51582",
        "company": "BrightFuture Tech",
        "role": "Software Engineer",
        "email": "<EMAIL>",
        "website": "alextech.com",
        "status": "Customer"
    },
    {
        "image": user13,
        "contactName": "Cameron Wilson",
        "phoneNumber": "+840 4447 94334",
        "company": "BlueSky Enterprises",
        "role": "Laravel Developer",
        "email": "<EMAIL>",
        "website": "cameron.com",
        "status": "Customer"
    },
    {
        "image": user14,
        "contactName": "Sam Brown",
        "phoneNumber": "+438 6305 33828",
        "company": "BlueSky Enterprises",
        "role": "Marketing Specialist",
        "email": "<EMAIL>",
        "website": "sambrown.com",
        "status": "Marketing"
    },
    {
        "image": user15,
        "contactName": "Pat Martinez",
        "phoneNumber": "+356 8229 92921",
        "company": "Synergy Solutions",
        "role": "Laravel Developer",
        "email": "<EMAIL>",
        "website": "patmartiz.com",
        "status": "Employee"
    },
    {
        "image": user16,
        "contactName": "Chris Smith",
        "phoneNumber": "+880 8152 56315",
        "company": "BrightFuture Tech",
        "role": "UI / UX Designer",
        "email": "<EMAIL>",
        "website": "chrissmith.com",
        "status": "Personal"
    },
    {
        "image": user17,
        "contactName": "Cameron Wilson",
        "phoneNumber": "+599 4447 23760",
        "company": "BlueSky Enterprises",
        "role": "Co Founder",
        "email": "<EMAIL>",
        "website": "cameronwilson.com",
        "status": "Employee"
    },
    {
        "image": user18,
        "contactName": "Casey Martinez",
        "phoneNumber": "+590 5863 84911",
        "company": "StarPath Dynamics",
        "role": "UI / UX Designer",
        "email": "<EMAIL>",
        "website": "caseystarpath.com",
        "status": "Customer"
    }
]

export default data;