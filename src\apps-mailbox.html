{{> partials/main }}

<head>

    {{> partials/title-meta title="Mailbox" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

<div class="relative min-h-screen">
    <div class="page-wrapper pt-[calc(theme('spacing.topbar')_*_1.2)] group-data-[layout=horizontal]:pt-[calc(theme('spacing.topbar')_*_1.9)]">
        {{> partials/page-heading title="Mailbox" sub-title="Apps" }}


        <div class="flex flex-col xl:flex-row" x-data="emailApp">
            <div class="mb-space xl:hidden">
                <button class="btn btn-primary" @click="isMenuOpen = !isMenuOpen"><i data-lucide="menu" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> Menu</button>
            </div>
            <div class="fixed inset-y-0 ltr:right-0 rtl:left-0 mb-0 xl:mb-space z-[1050] xl:z-0 xl:static ltr:xl:rounded-r-none rtl:xl:rounded-l-none w-80 card" x-show="isMenuOpen">
                <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="w-full dropdown">
                    <button x-ref="button" x-on:click="toggle()" :aria-expanded="open.toString()" type="button" class="flex items-center w-full gap-2 text-start card-header">
                        <img :src="activeUserEmail.image" alt="" class="size-9">
                        <div class="grow">
                            <h6 x-text="activeUserEmail.name">SRBThemes Account</h6>
                            <p class="text-gray-500 dark:text-dark-500" x-text="activeUserEmail.email"><EMAIL></p>
                        </div>
                        <i data-lucide="chevron-down" class="size-4 shrink-0"></i>
                    </button>
                    <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed p-2 !w-72 dropdown-menu">
                        <ul class="space-y-2">
                            <template x-for="email of userEmail">
                                <li>
                                    <button type="button" @click="setActiveUserEmail(email); close()" class="dropdown-item ltr:text-left rtl:text-right">
                                        <img :src="email.image" alt="" class="size-9">
                                        <div class="grow">
                                            <h6 x-text="email.name">Domiex</h6>
                                            <p class="text-gray-500 dark:text-dark-500" x-text="email.email"><EMAIL></p>
                                        </div>
                                    </button>
                                </li>
                            </template>
                        </ul>
                    </div>
                </div>
                <div class="card-body">
                    <a href="#!" class="float-end xl:hidden" @click="isMenuOpen = !isMenuOpen"><i data-lucide="x" class="size-4"></i></a>
                    <h6 class="mb-1">Inbox Messages</h6>
                    <p class="text-gray-500 dark:text-dark-500">1487 messages - 26 unread</p>
                    <div data-simplebar class="max-h-[calc(100vh_-_14.5rem)] -mx-space px-space">
                        <ul class="mt-5 space-y-3">
                            <li>
                                <a href="#!" @click="filterEmails('all')" :class="{ 'active': activeTab === 'all' }" class="flex items-center gap-2 link link-primary [&.active]:text-primary-500"><i data-lucide="inbox" class="size-4"></i> Inbox <span class="ml-auto shrink-0 badge badge-gray" x-text="emails.length"></span></a>
                            <li>
                                <a href="#!" @click="filterEmails('starred')" :class="{ 'active': activeTab === 'starred' }" class="flex items-center gap-2 link link-primary [&.active]:text-primary-500"><i data-lucide="star" class="size-4"></i> Starred <span class="ml-auto shrink-0 badge badge-gray">2</span></a>
                            </li>
                            <li>
                                <a href="#!" @click="filterEmails('sent')" :class="{ 'active': activeTab === 'sent' }" class="flex items-center gap-2 link link-primary [&.active]:text-primary-500"><i data-lucide="send-horizontal" class="size-4"></i> Sent</a>
                            </li>
                            <li>
                                <a href="#!" @click="filterEmails('drafts')" :class="{ 'active': activeTab === 'drafts' }" class="flex items-center gap-2 link link-primary [&.active]:text-primary-500"><i data-lucide="square-pen" class="size-4"></i> Drafts <span class="ml-auto shrink-0 badge badge-gray">2</span></a>
                            </li>
                            <li>
                                <a href="#!" @click="filterEmails('spam')" :class="{ 'active': activeTab === 'spam' }" class="flex items-center gap-2 link link-primary [&.active]:text-primary-500"><i data-lucide="circle-alert" class="size-4"></i> Spam</a>
                            </li>
                            <li>
                                <a href="#!" @click="filterEmails('trash')" :class="{ 'active': activeTab === 'trash' }" class="flex items-center gap-2 link link-red [&.active]:text-red-500"><i data-lucide="trash-2" class="size-4"></i> Trash</a>
                            </li>
                            <li>
                                <a href="#!" @click="filterEmails('important')" :class="{ 'active': activeTab === 'important' }" class="flex items-center gap-2 link link-primary [&.active]:text-primary-500"><i data-lucide="step-forward" class="size-4"></i> Important</a>
                            </li>
                            <li>
                                <a href="#!" @click="filterEmails('scheduled')" :class="{ 'active': activeTab === 'scheduled' }" class="flex items-center gap-2 link link-primary [&.active]:text-primary-500"><i data-lucide="calendar" class="size-4"></i> Scheduled</a>
                            </li>
                        </ul>

                        <div class="flex mt-4 mb-1">
                            <h6 class="grow">Labels</h6>
                            <a href="#!" class="inline-block shrink-0 link link-primary"><i data-lucide="plus" class="size-4"></i></a>
                        </div>
                        <ul class="mt-5 space-y-3">
                            <li>
                                <a href="#!" @click="filterEmails('all','Team Meetings')" class="flex items-center gap-2 link text-current link-primary [&.active]:text-primary-500"><i data-lucide="diamond" class="text-red-500 size-4"></i> Team Meetings <span class="ml-auto text-gray-500 dark:text-dark-500 shrink-0">3</span></a>
                            </li>
                            <li>
                                <a href="#!" @click="filterEmails('all','Application')" class="flex items-center gap-2 link text-current link-primary [&.active]:text-primary-500"><i data-lucide="diamond" class="text-green-500 size-4"></i> Application <span class="ml-auto text-gray-500 dark:text-dark-500 shrink-0">2</span></a>
                            </li>
                            <li>
                                <a href="#!" @click="filterEmails('all','Developers')" class="flex items-center gap-2 link text-current link-primary [&.active]:text-primary-500"><i data-lucide="diamond" class="text-yellow-500 size-4"></i> Developers <span class="ml-auto text-gray-500 dark:text-dark-500 shrink-0">4</span></a>
                            </li>
                            <li>
                                <a href="#!" @click="filterEmails('all','Photographer')" class="flex items-center gap-2 link text-current link-primary [&.active]:text-primary-500"><i data-lucide="diamond" class="text-primary-500 size-4"></i> Photographer <span class="ml-auto text-gray-500 dark:text-dark-500 shrink-0">1</span></a>
                            </li>
                        </ul>
                    </div>

                    <button type="button" class="w-full mt-4 btn btn-primary" data-modal-target="addComposeModals" @click="openComposeMail">Compose</button>
                </div>
            </div>
            <div class="backdrop-overlay backdrop-blur-xs xl:hidden" x-show="isMenuOpen" @click="isMenuOpen = false"></div>
            <!-- slider above -->
            <div class="xl:rounded-none xl:border-x-0 card grow" :class="showMailModal ? 'w-full xl:max-w-md' : ' w-full ' " x-show="showMailList">
                <div class="card-body">
                    <div data-simplebar>
                        <div class="flex gap-4 *:shrink-0">
                            <a href="#!" class="block w-24 p-2 text-center transition duration-300 ease-linear rounded-md hover:bg-gray-50 dark:hover:bg-dark-850">
                                <div class="relative flex items-center justify-center mx-auto mb-1 text-lg font-semibold text-gray-500 bg-white border border-gray-200 rounded-full dark:bg-dark-900 dark:text-dark-500 dark:border-dark-800 size-12">
                                    S
                                    <span class="absolute flex items-center justify-center !p-0 rounded-full !text-11 -bottom-0.5 -right-0.5 !border-2 !border-white size-5 badge dark:!border-dark-900 badge-solid-primary">5</span>
                                </div>
                                <h6 class="font-medium truncate">Shopia Mia</h6>
                            </a>
                            <a href="#!" class="block w-24 p-2 text-center transition duration-300 ease-linear rounded-md hover:bg-gray-50 dark:hover:bg-dark-850">
                                <div class="relative flex items-center justify-center mx-auto mb-1 text-lg font-semibold text-gray-500 bg-white border border-gray-200 rounded-full dark:bg-dark-900 dark:text-dark-500 dark:border-dark-800 size-12">
                                    <img src="assets/images/brands/img-06.png" alt="" class="size-6">
                                </div>
                                <h6 class="font-medium truncate">Windows Social Media</h6>
                            </a>
                            <a href="#!" class="block w-24 p-2 text-center transition duration-300 ease-linear rounded-md hover:bg-gray-50 dark:hover:bg-dark-850">
                                <div class="relative flex items-center justify-center mx-auto mb-1 text-lg font-semibold text-gray-500 bg-white border border-gray-200 rounded-full dark:bg-dark-900 dark:text-dark-500 dark:border-dark-800 size-12">
                                    <img src="assets/images/brands/img-08.png" alt="" class="size-6">
                                    <span class="absolute flex items-center justify-center !p-0 rounded-full !text-11 -bottom-0.5 -right-0.5 !border-2 !border-white size-5 badge dark:!border-dark-900 badge-solid-primary">2</span>
                                </div>
                                <h6 class="font-medium truncate">Shopify</h6>
                            </a>
                            <a href="#!" class="block w-24 p-2 text-center transition duration-300 ease-linear rounded-md hover:bg-gray-50 dark:hover:bg-dark-850">
                                <div class="relative flex items-center justify-center mx-auto mb-1 text-lg font-semibold text-gray-500 bg-white border border-gray-200 rounded-full dark:bg-dark-900 dark:text-dark-500 dark:border-dark-800 size-12">
                                    <img src="assets/images/brands/img-20.png" alt="" class="size-6">
                                </div>
                                <h6 class="font-medium truncate">Payment</h6>
                            </a>
                            <a href="#!" class="block w-24 p-2 text-center transition duration-300 ease-linear rounded-md hover:bg-gray-50 dark:hover:bg-dark-850">
                                <div class="relative flex items-center justify-center mx-auto mb-1 text-lg font-semibold text-gray-500 bg-white border border-gray-200 rounded-full dark:bg-dark-900 dark:text-dark-500 dark:border-dark-800 size-12">
                                    <img src="assets/images/brands/img-09.png" alt="" class="size-6">
                                </div>
                                <h6 class="font-medium truncate">Social Media</h6>
                            </a>
                            <a href="#!" class="block w-24 p-2 text-center transition duration-300 ease-linear rounded-md hover:bg-gray-50 dark:hover:bg-dark-850">
                                <div class="relative flex items-center justify-center mx-auto mb-1 text-lg font-semibold text-gray-500 bg-white border border-gray-200 rounded-full dark:bg-dark-900 dark:text-dark-500 dark:border-dark-800 size-12">
                                    <img src="assets/images/brands/img-17.png" alt="" class="size-6">
                                </div>
                                <h6 class="font-medium truncate">Romero</h6>
                            </a>
                            <a href="#!" class="block w-24 p-2 text-center transition duration-300 ease-linear rounded-md hover:bg-gray-50 dark:hover:bg-dark-850">
                                <div class="relative flex items-center justify-center mx-auto mb-1 text-lg font-semibold text-gray-500 bg-white border border-gray-200 rounded-full dark:bg-dark-900 dark:text-dark-500 dark:border-dark-800 size-12">
                                    <img src="assets/images/brands/img-16.png" alt="" class="size-6">
                                </div>
                                <h6 class="font-medium truncate">Romero</h6>
                            </a>
                        </div>
                    </div>

                    <div class="mt-3">
                        <div class="flex items-center gap-2 mb-4">
                            <div class="input-check-group shrink-0">
                                <!-- main checkbox -->
                                <input id="checkboxBasic1" class="input-check input-check-primary" type="checkbox" x-model="selectAll" @click="toggleAll" />
                            </div>
                            <h6 class="grow">Main Inbox <small class="font-normal text-gray-500 ltr:ml-1 rtl:mt-1 dark:text-dark-500 text-14" x-text="`${filteredEmails.length} messages`"> 46 messages</small><span x-show="selectedItems.length > 0" class="text-gray-400 ltr:ml-2 rtl:mr-2 text-14" x-text="selectedItems.length != 0 ? `${selectedItems.length} Rows Selected` : ''"></span></h6>
                            <div class="flex items-center gap-4 shrink-0">
                                <a href="#!" x-show="selectedItems.length > 0" @click="deleteSelectedItems()" class="link link-red"><i data-lucide="trash" class="size-4"></i></a>
                                <div x-data="dropdownBehavior()" x-on:keydown.escape.prevent.stop="close()" x-init="calculatePosition()" class="dropdown">
                                    <button x-ref="button" x-on:click="toggle()" :aria-expanded="open.toString()" type="button" class="flex items-center text-gray-500 dark:text-dark-500">
                                        <i data-lucide="ellipsis" class="size-5"></i>
                                    </button>
                                    <div x-ref="dropdown" x-show="open" x-transition.origin.top.right x-on:click.outside="close()" class="!fixed max-w-md p-2 dropdown-menu">
                                        <ul>
                                            <li>
                                                <a href="#!" class="dropdown-item">
                                                    <span>Show more messages</span>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="#!" class="dropdown-item">
                                                    <span>Hide section when empty</span>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="#!" class="dropdown-item">
                                                    <span>Manage Inbox settings</span>
                                                </a>
                                            </li>
                                            <li>
                                                <a href="#!" class="dropdown-item">
                                                    <span>Mark all as read</span>
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                                <a href="#!" class="link link-primary"><i data-lucide="rotate-ccw" class="size-4"></i></a>
                                <a href="#!" class="link link-primary" data-modal-target="searchMailModals"><i data-lucide="search" class="size-4"></i></a>
                            </div>
                        </div>
                        <div data-simplebar class="-mx-5 h-[35rem]">
                            <div class="border-t border-gray-200 divide-y divide-gray-200 dark:border-dark-800 dark:divide-dark-800">
                                <template x-for="email in filteredEmails" :key="email.id">
                                    <div class="flex gap-3 p-5 transition duration-300 ease-linear hover:bg-gray-50 dark:hover:bg-dark-850" @click="setActiveMail(email)">
                                        <div class="self-start mt-3 input-check-group shrink-0">
                                            <input id="checkboxBasic1" class="input-check input-check-primary" type="checkbox" @click.stop="toggleItem(email)" :checked="selectedItems.includes(email)" />
                                        </div>
                                        <div class="flex items-center justify-center text-red-500 rounded-full bg-red-500/10 shrink-0 size-10">
                                            <template x-if="email.avatarImage">
                                                <img :src="email.avatarImage" alt="" class="rounded-full">
                                            </template>
                                            <template x-if="email.avatarText">
                                                <span x-text="email.avatarText">BS</span>
                                            </template>
                                        </div>
                                        <div class="overflow-hidden grow">
                                            <p class="mb-1 md:float-end md:mb-0 text-14" x-text="email.date">10 May, <span class="text-gray-500 dark:text-dark-500">10:58AM</span></p>
                                            <h6 class="mb-1" x-text="email.sender">Barbara Sutton</h6>
                                            <a href="#!" class="link link-primary" x-text="email.email"><EMAIL></a>
                                            <a href="#!" class="block mt-3">
                                                <h6 class="mb-1" x-text="email.subject">You have a new subscriber</h6>
                                                <p class="truncate" x-text="email.message">Hey! You've just got a new
                                                    subscribe on your channel</p>
                                            </a>
                                            <div class="flex flex-wrap justify-end gap-2 mt-2">
                                                <template x-for="label in email.badges">
                                                    <a href="#!" class="badge " :class="label === 'Application' || label === 'Scheduled' || label === 'Important' ? 'badge-green' : 
                                                            label === 'Inbox' || label === 'Drafts' ? 'badge-gray' : 
                                                            label === 'Developers' ? 'badge-yellow' : 
                                                            label === 'Photographer' ? 'badge-sky' : 'badge-red'" x-text="label">Application</a>
                                                </template>
                                            </div>
                                        </div>
                                    </div>
                                </template>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- mail section -->
            <div class="ltr:xl:rounded-l-none rtl:xl:rounded-r-none card grow" x-show="showMailModal">
                <div class="flex flex-col gap-2 md:items-center md:flex-row card-header">
                    <div class="flex items-center gap-1">
                        <a href="#!" class="btn btn-icon btn-active-gray" @click="BackToList()"><i data-lucide="arrow-left" class="size-4"></i></a>
                        <a href="#!" class="btn btn-icon btn-active-gray"><i data-lucide="archive" class="size-4"></i></a>
                        <a href="#!" class="btn btn-icon btn-active-gray"><i data-lucide="octagon-alert" class="size-4"></i></a>
                    </div>
                    <div class="flex items-center gap-1 text-gray-500 md:mx-auto dark:text-dark-500">
                        <a href="#!" @click="prevButton()"><i data-lucide="chevron-left" class="size-4"></i></a>
                        <p><span x-text="activeEmailId"></span> of <span x-text="filteredEmails.length"></span></p>
                        <a href="#!" @click="nextButton()"><i data-lucide="chevron-right" class="size-4"></i></a>
                    </div>
                    <div class="flex items-center gap-1">
                        <a href="#!" class="btn btn-icon btn-active-gray"><i data-lucide="reply" class="size-4"></i></a>
                        <a href="#!" class="btn btn-icon btn-active-gray"><i data-lucide="clock-3" class="size-4"></i></a>
                        <a href="#!" data-modal-target="deleteModal" @click="deleteEmail = activeEmail" class="btn btn-icon btn-active-red"><i data-lucide="trash-2" class="size-4"></i></a>
                    </div>
                </div>
                <div data-simplebar class="h-[27rem]">
                    <div class="card-body">
                        <template x-if="openMails !== null">
                            <div>
                                <div>
                                    <div class="flex items-center gap-2">
                                        <div class="flex items-center justify-center text-red-500 rounded-full bg-red-500/10 shrink-0 size-10">
                                            <template x-if="openMails.avatarImage !== undefined">
                                                <img :src="openMails.avatarImage" alt="" class="rounded-full">
                                            </template>
                                            <template x-if="openMails.avatarText">
                                                <span x-text="openMails.avatarText">BS</span>
                                            </template>
                                        </div>
                                        <div class="grow">
                                            <h6 x-text="openMails.sender">Armand Noto</h6>
                                            <a href="#!" class="link link-primary" x-text="openMails.eopenMails"><EMAIL></a>
                                        </div>
                                        <p class="text-xs text-gray-500 dark:text-dark-500 shrink-0" x-text="openMails.date">11:32 AM (20 min ago)</p>
                                    </div>
                                    <div class="mt-5">
                                        <h6 class="mb-3" x-text="openMails.subject">Weekly Update on Domiex Admin & Dashboard Projects</h6>

                                        <div class="space-y-2">
                                            <p x-text="openMails.message">Hello Shopia,</p>
                                        </div>
                                        <div class="grid grid-cols-1 gap-5 mt-4 md:grid-cols-2" x-show="attachments">
                                            <a href="#!" class="flex items-center gap-2 p-2 transition duration-300 ease-linear border border-gray-200 border-dashed rounded-md dark:border-dark-800 hover:border-gray-300 dark:hover:border-dark-700 hover:shadow-lg hover:shadow-gray-200 dark:hover:shadow-dark-850">
                                                <div class="flex items-center justify-center font-semibold text-gray-500 transition duration-200 ease-linear bg-gray-100 rounded-full dark:text-dark-500 dark:bg-dark-850 shrink-0 size-10">
                                                    <i data-lucide="file-text" class="size-4"></i>
                                                </div>
                                                <div class="grow">
                                                    <h6>shopify-docs.txt</h6>
                                                    <p class="text-sm text-gray-500 dark:text-dark-500">154 kb</p>
                                                </div>
                                                <div class="shrink-0">
                                                    <i data-lucide="download" class="text-gray-500 dark:text-dark-500 dark:fill-dark-850 size-5 fill-gray-200"></i>
                                                </div>
                                            </a>
                                            <a href="#!" class="flex items-center gap-2 p-2 transition duration-300 ease-linear border border-gray-200 border-dashed rounded-md dark:border-dark-800 hover:border-gray-300 dark:hover:border-dark-700 hover:shadow-lg hover:shadow-gray-200 dark:hover:shadow-dark-850">
                                                <div class="flex items-center justify-center font-semibold text-gray-500 transition duration-200 ease-linear bg-gray-100 rounded-full dark:text-dark-500 dark:bg-dark-850 shrink-0 size-10">
                                                    <i data-lucide="image" class="size-4"></i>
                                                </div>
                                                <div class="grow">
                                                    <h6>main-logo.png</h6>
                                                    <p class="text-sm text-gray-500">467 kb</p>
                                                </div>
                                                <div class="shrink-0">
                                                    <i data-lucide="download" class="text-gray-500 size-5 fill-gray-200 dark:text-dark-500 dark:fill-dark-850"></i>
                                                </div>
                                            </a>
                                        </div>
                                        <p class="mt-4">Best regards,</p>
                                        <p x-text="openMails.sender">Armand Noto</p>
                                    </div>
                                </div>
                                <div>
                                    <template x-for="reply of openMails.replies">
                                        <div class="mt-4">
                                            <div class="flex items-center gap-2">
                                                <div class="flex items-center justify-center text-green-500 bg-green-100 rounded-full shrink-0 size-10">
                                                    <template x-if="reply.avatarImage !== undefined">
                                                        <img :src="reply.avatarImage" alt="" class="rounded-full">
                                                    </template>
                                                    <template x-if="reply.avatarText">
                                                        <span x-text="reply.avatarText">BS</span>
                                                    </template>
                                                </div>
                                                <div class="grow">
                                                    <h6 x-text="reply.sender">Shopia Mia</h6>
                                                    <a href="#!" class="link link-primary" x-text="reply.email"><EMAIL></a>
                                                </div>
                                                <p class="text-xs text-gray-500 shrink-0" x-text="reply.date">11:45 AM (5 min ago)</p>
                                            </div>
                                            <div class="mt-5">
                                                <h6 class="mb-3" x-text="reply.subject">Re: Weekly Update on Domiex Admin & Dashboard Projects</h6>

                                                <div class="space-y-2">
                                                    <p x-text="reply.message">Hello Armand,</p>
                                                </div>
                                                <p class="mt-4">Best regards,</p>
                                                <p x-text="openMails.sender">Shopia Mia</p>
                                            </div>
                                        </div>
                                    </template>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>
                <!-- Mail form for reply-->
                <div class="card-body">
                    <div class="mb-0 shadow-none card">
                        <form @submit.prevent="sendEmailReply()">
                            <div class="p-4">
                                <div class="flex items-center gap-2">
                                    <p class="text-gray-500 dark:text-dark-500">To:</p>
                                    <input type="text" class="h-auto p-0 border-0 form-input" placeholder="Type email" x-model="mailform.email">
                                    <a href="#!" class="link link-primary">Cc</a>
                                    <a href="#!" class="link link-primary">Bcc</a>
                                </div>
                            </div>
                            <div class="p-4 pt-1 card-body">
                                <textarea class="h-auto p-0 border-0 resize-none form-input" rows="3" placeholder="Type something ..." x-model="mailform.message"></textarea>
                                <div class="flex flex-wrap items-center gap-3">
                                    <div class="shrink-0">
                                        <label for="sendImages" class="btn btn-active-gray btn-icon">
                                            <i data-lucide="image" class="size-5"></i>
                                        </label>
                                        <input type="file" id="sendImages" class="hidden">
                                    </div>
                                    <button type="button" class="link link-primary shrink-0"><i data-lucide="link" class="size-5"></i></button>
                                    <button type="button" class="link link-primary shrink-0"><i data-lucide="pencil" class="size-5"></i></button>
                                    <button type="button" class="link link-yellow shrink-0"><i data-lucide="smile" class="size-5"></i></button>
                                    <button type="button" class="ml-auto btn btn-sub-gray shrink-0">Draft</button>
                                    <button type="submit" class="btn btn-primary shrink-0">Send Now</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <!-- search modals -->
            <div id="searchMailModals" class="!hidden modal show">
                <div class="modal-wrap modal-top">
                    <div class="modal-content">
                        <div class="relative group/form grow">
                            <input type="email" class="ltr:pl-9 rtl:pr-9 form-input ltr:group-[&.right]/form:pr-9 rtl:group-[&.right]/form:pl-9 ltr:group-[&.right]/form:pl-4 rtl:group-[&.right]/form:pr-4" placeholder="Search anythings ..." x-model="searchTerms">
                            <button class="absolute inset-y-0 flex items-center ltr:left-3 rtl:right-3 ltr:group-[&.right]/form:right-3 rtl:group-[&.right]/form:left-3 ltr:group-[&.right]/form:left-auto rtl:group-[&.right]/form:right-auto focus:outline-hidden">
                                <i data-lucide="search" class="text-gray-500 dark:text-dark-500 size-4 fill-gray-100 dark:fill-dark-850"></i>
                            </button>
                            <template x-if="searchTerms != ''">
                                <div class="absolute inset-x-0 bg-white border border-gray-200 rounded-md top-full dark:bg-dark-900 dark:border-dark-800">
                                    <div data-simplebar class="p-5 max-h-72">
                                        <p class="mb-2 text-sm text-gray-500 dark:text-dark-500">Last Search</p>
                                        <div class="flex flex-wrap gap-2">
                                            <div x-data="{ isVisible: true }">
                                                <span x-show="isVisible" class="badge badge-gray">
                                                    Chat Management
                                                    <a href="javascript:void(0);" @click="isVisible = false">
                                                        <i class="ml-1 align-middle ri-close-fill"></i>
                                                    </a>
                                                </span>
                                            </div>
                                            <div x-data="{ isVisible: true }">
                                                <span x-show="isVisible" class="badge badge-gray">
                                                    Projects Discuss
                                                    <a href="javascript:void(0);" @click="isVisible = false">
                                                        <i class="ml-1 align-middle ri-close-fill"></i>
                                                    </a>
                                                </span>
                                            </div>
                                            <div x-data="{ isVisible: true }">
                                                <span x-show="isVisible" class="badge badge-gray">
                                                    Subscriber
                                                    <a href="javascript:void(0);" @click="isVisible = false">
                                                        <i class="ml-1 align-middle ri-close-fill"></i>
                                                    </a>
                                                </span>
                                            </div>
                                            <div x-data="{ isVisible: true }">
                                                <span x-show="isVisible" class="badge badge-gray">
                                                    Reports
                                                    <a href="javascript:void(0);" @click="isVisible = false">
                                                        <i class="ml-1 align-middle ri-close-fill"></i>
                                                    </a>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </template>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Add compose modals -->
            <div id="addComposeModals" class="!hidden modal show" x-show="showComposeModal">
                <div class="modal-wrap modal-top modal-lg">
                    <div class="modal-content">
                        <div>
                            <div class="flex items-center gap-2">
                                <p class="text-gray-500 dark:text-dark-500" x>To:</p>
                                <input type="email" class="h-auto px-0 border-0 form-input" placeholder="Type email" x-model="emailForm.email">
                                <a href="#!" class="link link-primary">Cc</a>
                                <a href="#!" class="link link-primary">Bcc</a>
                            </div>
                        </div>
                        <div class="py-2 border-gray-200 dark:border-dark-800 border-y">
                            <input type="text" class="h-auto p-0 border-0 form-input" placeholder="Subject" x-model="emailForm.subject">
                        </div>
                        <div>
                            <textarea class="h-auto px-0 border-0 resize-none form-input" rows="3" placeholder="Type something ..." x-model="emailForm.message"></textarea>
                            <div class="flex items-center gap-3 mt-2">
                                <div class="shrink-0">
                                    <label for="sendImages" class="btn btn-active-gray btn-icon">
                                        <i data-lucide="image" class="size-5"></i>
                                    </label>
                                    <input type="file" id="sendImages" class="hidden">
                                </div>
                                <button type="button" class="link link-primary shrink-0"><i data-lucide="link" class="size-5"></i></button>
                                <button type="button" class="link link-primary shrink-0"><i data-lucide="pencil" class="size-5"></i></button>
                                <button type="button" class="mr-auto link link-yellow shrink-0"><i data-lucide="smile" class="size-5"></i></button>
                                <button type="button" class="btn btn-sub-gray shrink-0">Draft</button>
                                <button type="button" class="btn btn-primary shrink-0" @click="sendNewMail()">Send Now</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!--delete modals-->
            <div id="deleteModal" class="!hidden modal show">
                <div class="modal-wrap modal-xs modal-center">
                    <div class="text-center modal-content p-7">
                        <div class="flex items-center justify-center mx-auto mb-4 text-red-500 rounded-full bg-red-500/10 size-14 backdrop-blur-xl">
                            <i data-lucide="trash-2" class="size-6"></i>
                        </div>
                        <h5 class="mb-4">Are you sure you want to delete this email ?</h5>
                        <div class="flex items-center justify-center gap-2">
                            <button class="btn btn-red" @click="deleteMailItems()" data-modal-close="deleteModal">Delete</button>
                            <button data-modal-close="deleteModal" class="btn link link-primary">Cancel</button>
                        </div>
                    </div>
                </div>
            </div><!--end-->
        </div>
    </div>
    {{> partials/footer }}
</div>


{{> partials/vendor-scripts }}

<script type="module" src="assets/js/main.js"></script>
<script type="module" src="assets/js/apps/email/email.init.js"></script>

</body>

</html>