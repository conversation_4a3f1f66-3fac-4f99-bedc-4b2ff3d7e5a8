{{> partials/main }}

<head>

    {{> partials/title-meta title="Account Settings" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}

<div class="relative mb-6">
    <div class="relative overflow-hidden rounded-md h-44 bg-primary-500/10">
        <div class="border-[60px] border-t-primary-500 border-l-primary-500 absolute opacity-10 -top-2 left-0 rotate-45 size-96"></div>
        <div class="border-[60px] border-green-500 absolute opacity-10 top-20 left-8 rotate-45 size-80"></div>
        <div class="border-[60px] border-pink-500 absolute opacity-10 top-36 left-28 rotate-45 size-40"></div>
    </div>
    <div class="text-center">
        <div class="relative inline-block mx-auto">
            <div class="relative p-1 rounded-full bg-gradient-to-tr from-primary-300 via-red-300 to-green-300 -mt-14">
                <img src="assets/images/avatar/user-17.png" alt="" class="mx-auto border-4 border-white rounded-full dark:border-dark-900 size-28">
            </div>
            <div class="absolute border-2 border-white dark:border-dark-900 rounded-full size-4 bg-green-500 bottom-2.5 ltr:right-2.5 rtl:left-2.5"></div>
        </div>
        <h5 class="mt-2 mb-1">Sophia Mia <i data-lucide="badge-check" class="inline-block text-primary-500 fill-primary-500/20 size-5"></i></h5>
        <ul class="flex flex-wrap items-center justify-center gap-2 text-gray-500 dark:text-dark-500 text-14">
            <li><i data-lucide="building-2" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-middle">UI / UX Designer</span></li>
            <li><i data-lucide="map-pin" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-middle">Argentina</span></li>
            <li><i data-lucide="calendar-days" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-middle">24 April, 2024</span></li>
        </ul>
    </div>
</div>

<ul class="pb-2 overflow-x-auto tabs-pills lg:pb-0">
    <li>
        <a href="pages-account-settings.html" class="nav-item text-gray-500 dark:text-dark-500 [&.active]:bg-primary-500 [&.active]:text-primary-50 active">
            <i data-lucide="user-round" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-middle whitespace-nowrap">Account</span>
        </a>
    </li>
    <li>
        <a href="pages-account-security.html" class="nav-item text-gray-500 dark:text-dark-500 [&.active]:bg-primary-500 [&.active]:text-primary-50">
            <i data-lucide="shield-check" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-middle whitespace-nowrap">Security</span>
        </a>
    </li>
    <li>
        <a href="pages-account-billing-plan.html" class="nav-item text-gray-500 dark:text-dark-500 [&.active]:bg-primary-500 [&.active]:text-primary-50">
            <i data-lucide="gem" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-middle whitespace-nowrap">Billing & Plans</span>
        </a>
    </li>
    <li>
        <a href="pages-account-notification.html" class="nav-item text-gray-500 dark:text-dark-500 [&.active]:bg-primary-500 [&.active]:text-primary-50">
            <i data-lucide="bell" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-middle whitespace-nowrap">Notification</span>
        </a>
    </li>
    <li>
        <a href="pages-account-statements.html" class="nav-item text-gray-500 dark:text-dark-500 [&.active]:bg-primary-500 [&.active]:text-primary-50">
            <i data-lucide="list-tree" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-middle whitespace-nowrap">Statements</span>
        </a>
    </li>
    <li>
        <a href="pages-account-logs.html" class="nav-item text-gray-500 dark:text-dark-500 [&.active]:bg-primary-500 [&.active]:text-primary-50">
            <i data-lucide="log-out" class="inline-block ltr:mr-1 rtl:ml-1 size-4"></i> <span class="align-middle whitespace-nowrap">Logs</span>
        </a>
    </li>
</ul>

<div class="mt-5 card">
    <div class="card-body">
        <div class="grid grid-cols-12 gap-3">
            <div class="col-span-12 xl:col-span-3">
                <h6 class="card-title">Personal Information</h6>
            </div><!--end col-->
            <div class="col-span-12 xl:col-span-9">
                <form action="#!">
                    <div class="grid grid-cols-12 gap-5">
                        <div class="col-span-12">
                            <div x-data="{ photoPreview: null }">
                                <div class="shrink-0" x-show="!photoPreview">
                                    <img class="object-cover rounded-md size-20" src="assets/images/avatar/user-17.png" alt="Current profile photo" />
                                </div>
                                <div class="shrink-0" x-show="photoPreview">
                                    <img x-bind:src="photoPreview" class="object-cover rounded-md size-20" alt="Selected profile photo" />
                                </div>
                                <label class="block mt-4">
                                    <span class="sr-only">Choose profile photo</span>
                                    <input type="file" x-on:change="photoPreview = URL.createObjectURL($event.target.files[0])" class="hidden" />
                                    <span class="btn btn-sub-primary"><i data-lucide="upload" class="inline-block size-4 me-1"></i> <span class="align-middle">Upload Profile</span></span>
                                </label>
                            </div>
                        </div><!--end col-->
                        <div class="col-span-12 md:col-span-6">
                            <label for="firstNameInput" class="form-label">First Name</label>
                            <input type="text" id="firstNameInput" class="form-input" value="Sophia" placeholder="Enter your first name">
                        </div><!--end col-->
                        <div class="col-span-12 md:col-span-6">
                            <label for="lastNameInput" class="form-label">Last Name</label>
                            <input type="text" id="lastNameInput" class="form-input" value="Mia" placeholder="Enter your last name">
                        </div><!--end col-->
                        <div class="col-span-12 md:col-span-6 lg:col-span-4">
                            <label for="roleInput" class="form-label">Role</label>
                            <input type="text" id="roleInput" class="form-input" value="UI / UX Designer" placeholder="Enter your role">
                        </div><!--end col-->
                        <div class="col-span-12 md:col-span-6 lg:col-span-4">
                            <label for="birthDateInput" class="form-label">Birth of Date</label>
                            <input type="date" id="birthDateInput" class="form-input" placeholder="Select date">
                        </div><!--end col-->
                        <div class="col-span-12 md:col-span-6 lg:col-span-4">
                            <label for="joiningDateInput" class="form-label">Joining Date</label>
                            <input type="date" id="joiningDateInput" class="form-input" placeholder="Select date">
                        </div><!--end col-->
                        <div class="col-span-12 md:col-span-6 lg:col-span-4">
                            <label for="emailInput" class="form-label">Email Address</label>
                            <input type="email" id="emailInput" class="form-input" value="<EMAIL>" placeholder="<EMAIL>">
                        </div><!--end col-->
                        <div class="col-span-12 md:col-span-6 lg:col-span-4">
                            <label for="phoneNumberInput" class="form-label">Phone Number</label>
                            <input type="number" id="phoneNumberInput" class="form-input" value="919876543210" placeholder="000000000000">
                        </div><!--end col-->
                        <div class="col-span-12 md:col-span-6 lg:col-span-4">
                            <label for="locationInput" class="form-label">Location</label>
                            <input type="text" id="locationInput" class="form-input" value="Argentina" placeholder="Enter location">
                        </div><!--end col-->
                        <div class="col-span-12 md:col-span-6">
                            <label for="languageSelect" class="form-label">Language</label>
                            <div id="languageSelect"></div>
                        </div><!--end col-->
                        <div class="col-span-12 md:col-span-6">
                            <label for="currencySelect" class="form-label">Currency</label>
                            <div id="currencySelect"></div>
                        </div><!--end col-->
                        <div class="col-span-12">
                            <label for="textareaInput2" class="form-label">Objective</label>
                            <textarea name="textareaInput2" id="textareaInput2" rows="3" class="h-auto form-input" placeholder="Write your objective"></textarea>
                        </div><!--end col-->
                        <div class="col-span-12 text-right">
                            <button type="submit" class="btn btn-primary">Update Profile</button>
                        </div><!--end col-->
                    </div><!--end grid-->
                </form>
            </div><!--end col-->
        </div><!--end grid-->
    </div>
</div>

<div class="card">
    <div class="card-body">
        <div class="grid grid-cols-12 gap-3 lg:gap-0 ">
            <div class="col-span-12 xl:col-span-3">
                <h6 class="card-title">Public Account</h6>
            </div><!--end col-span-12 md:d col-->
            <div class="col-span-12 xl:col-span-9">
                <div class="items-center gap-3 md:flex">
                    <div for="publicProfile" class="grow">
                        <h6 class="mb-1">Publish your contact information publicly.</h6>
                        <p class="text-gray-500 dark:text-dark-500">Allow anyone viewing your profile to access your contact information.</p>
                    </div>
                    <label for="publicProfile" class="switch-group">
                        <div class="relative">
                            <input type="checkbox" id="publicProfile" class="hidden sr-only peer" />
                            <div class="switch-wrapper"></div>
                            <div class="switch-dot peer-checked:translate-x-full rtl:peer-checked:-translate-x-full switch-primary"></div>
                        </div>
                    </label>
                </div>
                <div class="items-center gap-3 mt-4 md:flex">
                    <div for="publicProfile2" class="grow">
                        <h6 class="mb-1">Make Contact Info Public</h6>
                        <p class="text-gray-500 dark:text-dark-500">Allow anyone viewing your profile to access your contact information.</p>
                    </div>
                    <label for="publicProfile2" class="switch-group">
                        <div class="relative">
                            <input type="checkbox" id="publicProfile2" class="hidden sr-only peer" />
                            <div class="switch-wrapper"></div>
                            <div class="switch-dot peer-checked:translate-x-full rtl:peer-checked:-translate-x-full switch-primary"></div>
                        </div>
                    </label>
                </div>
            </div><!--end col-->
        </div><!--end grid-->
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h6 class="card-title">Delete Account</h6>
    </div>
    <div class="card-body">
        <p class="mb-3 text-gray-500 dark:text-dark-500">Please proceed with caution, as deleting your account and all associated data from our organization is a permanent action and cannot be undone.</p>
        <button class="btn btn-red">Delete Account</button>
    </div>
</div>

</div>
{{> partials/footer }}
</div>

{{> partials/vendor-scripts }}

<script type="module" src="assets/js/pages/account-settings.init.js"></script>

<script type="module" src="assets/js/main.js"></script>

</body>
</html>