{{> partials/main }}

<head>

    {{> partials/title-meta title="Attendance" }}

    {{> partials/head-css }}

</head>

{{> partials/body }}

{{> partials/topbar }}
{{> partials/sidebar }}

{{> partials/page-wrapper }}
{{> partials/page-heading title="Attendance" sub-title="Staff" }}

<div class="grid grid-cols-12 gap-x-space">
    <div class="col-span-12 card">
        <div class="card-body">
            <div class="flex flex-col gap-5 2xl:flex-row">
                <div class="shrink-0">
                    <img src="assets/images/avatar/user-3.png" alt="" class="rounded-md size-40">
                </div>
                <div class="mt-5 grow 2xl:mt-0">
                    <h6 class="mb-1 text-16"><PERSON></h6>
                    <div class="flex flex-wrap gap-3 item-center *:flex *:items-center">
                        <p><i class="ltr:mr-1 rtl:ml-1 ri-user-3-line"></i> <span class="text-gray-500 dark:text-dark-500">Female</span></p>
                        <p><i class="ltr:mr-1 rtl:ml-1 ri-briefcase-line"></i> <span class="text-gray-500 dark:text-dark-500">Merchandiser, retail</span></p>
                        <p><i class="ltr:mr-1 rtl:ml-1 ri-map-pin-2-line"></i> <span class="text-gray-500 dark:text-dark-500">California</span></p>
                    </div>
                    <div class="flex flex-wrap items-center gap-3 mt-5 xl:mr-40 md:gap-space">
                        <div class="p-3 md:p-4 text-center border border-gray-200 dark:border-dark-800 border-dashed rounded-md w-[110px] md:w-36 shrink-0">
                            <h5 class="mb-1 text-base md:text-lg">1 <small class="text-sm font-normal text-gray-500 dark:text-dark-500">hr</small> 48 <small class="text-sm font-normal text-gray-500 dark:text-dark-500">m</small> 37 <small class="text-sm font-normal text-gray-500 dark:text-dark-500">s</small></h5>
                            <p class="text-gray-500 dark:text-dark-500">Today Timing</p>
                        </div>
                        <div class="p-3 md:p-4 text-center border border-gray-200 dark:border-dark-800 border-dashed rounded-md w-[110px] md:w-36 shrink-0">
                            <h4 class="mb-1 text-base md:text-lg">20 <small class="text-sm font-normal text-gray-500 dark:text-dark-500">Feb, 2021</small></h4>
                            <p class="text-gray-500 dark:text-dark-500">Joining Date</p>
                        </div>
                        <div class="p-3 md:p-4 text-center border border-gray-200 dark:border-dark-800 border-dashed rounded-md w-[110px] md:w-36 shrink-0">
                            <h4 class="mb-1 text-base md:text-lg">13 <small class="text-sm font-normal text-gray-500 dark:text-dark-500">Dec, 1998</small></h4>
                            <p class="text-gray-500 dark:text-dark-500">Birthday Date</p>
                        </div>
                        <div class="p-3 md:p-4 text-center border border-gray-200 dark:border-dark-800 border-dashed rounded-md w-[110px] md:w-36 shrink-0">
                            <h4 class="mb-1 text-base md:text-lg">25 <small class="text-sm font-normal text-gray-500 dark:text-dark-500">Years</small></h4>
                            <p class="text-gray-500 dark:text-dark-500">Age</p>
                        </div>
                        <div class="p-3 md:p-4 text-center border border-gray-200 dark:border-dark-800 border-dashed rounded-md w-[110px] md:w-36 shrink-0">
                            <h6 class="mb-1 text-base md:text-lg">Radiology</h6>
                            <p class="text-gray-500 dark:text-dark-500">Department</p>
                        </div>
                    </div>
                </div>
                <div class="mt-4 shrink-0 lg:mt-0">
                    <div class="flex items-center gap-2">
                        <button class="btn btn-sub-green btn-icon" title="phone"><i data-lucide="phone" class="size-4"></i></button>
                        <button class="btn btn-sub-purple btn-icon" title="messages-square"><i data-lucide="messages-square" class="size-4"></i></button>
                        <button type="button" class="btn btn-primary" title="edit"><i data-lucide="pencil" class="inline-block mr-1 size-4"></i> Edit</button>
                    </div>
                </div>
            </div>

            <h6 class="mt-5">Summary Overview</h6>

            <div class="grid grid-cols-12 mt-4 gap-space">
                <div class="col-span-12 mb-0 border-dashed md:col-span-6 xl:col-span-4 2xl:col-span-3 card">
                    <div class="card-body">
                        <h6 class="mb-2">208 hr 30 min (292hr)</h6>
                        <p class="text-gray-500 dark:text-dark-500">Total Working Time (Monthly)</p>
                    </div>
                </div><!--end col/card-->
                <div class="col-span-12 mb-0 border-dashed md:col-span-6 xl:col-span-4 2xl:col-span-3 card">
                    <div class="card-body">
                        <h6 class="mb-2">5 hr 15 min</h6>
                        <p class="text-gray-500 dark:text-dark-500">Total Delay (Monthly)</p>
                    </div>
                </div><!--end col/card-->
                <div class="col-span-12 mb-0 border-dashed md:col-span-6 xl:col-span-4 2xl:col-span-3 card">
                    <div class="card-body">
                        <h6 class="mb-2 text-red-500">-5 hr 15 min</h6>
                        <p class="text-gray-500 dark:text-dark-500">Total Delay (Monthly)</p>
                    </div>
                </div><!--end col/card-->
                <div class="col-span-12 md:col-span-6 xl:col-span-4 2xl:col-span-3">
                    <div class="grid grid-cols-2 gap-space">
                        <div class="mb-0 border-dashed card">
                            <div class="card-body">
                                <h6 class="mb-2">3</h6>
                                <p class="text-gray-500 dark:text-dark-500">Missed Shift</p>
                            </div>
                        </div>
                        <div class="mb-0 border-dashed card">
                            <div class="card-body">
                                <h6 class="mb-2">2</h6>
                                <p class="text-gray-500 dark:text-dark-500">Absence</p>
                            </div>
                        </div>
                    </div>
                </div><!--end col/card-->
            </div><!--end grid-->
        </div>
    </div><!--end col-->
    <div class="col-span-12 card" x-data="attendanceTable()">
        <div class="flex items-center gap-3 card-header">
            <h6 class="card-title grow">Attendance List</h6>
            <button type="button" class="btn btn-primary shrink-0">Start Timing</button>
        </div>
        <div class="pt-0 card-body">
            <div>
                <div data-simplebar   class="table-box">
                    <table class="table flush whitespace-nowrap">
                        <tbody>
                            <tr class="text-gray-500 dark:text-dark-500">
                                <th x-on:click="sort('date')" class="!font-medium cursor-pointer">Date <span x-show="sortBy === 'date'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span>
                                </th>
                                <th class="!font-medium">Shift Time</th>
                                <th x-on:click="sort('checkInTime')" class="!font-medium cursor-pointer">Check In <span x-show="sortBy === 'checkInTime'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th x-on:click="sort('checkOutTime')" class="!font-medium cursor-pointer">Check Out
                                    <span x-show="sortBy === 'checkOutTime'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span>
                                </th>
                                <th x-on:click="sort('workedTime')" class="!font-medium cursor-pointer">Worked Time
                                    <span x-show="sortBy === 'workedTime'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span>
                                </th>
                                <th x-on:click="sort('differenceTime')" class="!font-medium cursor-pointer">Difference
                                    <span x-show="sortBy === 'differenceTime'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span>
                                </th>
                                <th x-on:click="sort('status')" class="!font-medium cursor-pointer">Status <span x-show="sortBy === 'status'" x-text="sortDirection === 'asc' ? '↑' : '↓'"></span></th>
                                <th class="!font-medium">Action</th>
                            </tr>
                            <template x-for="(attendance, index) in displayedAttendances" :key="index">
                                <tr class="*:px-3 *:py-2.5">
                                    <td x-text="attendance.formattedDate"></td>
                                    <td>
                                        <div class="badge badge-gray">
                                            9 hrs Shift
                                        </div>
                                    </td>
                                    <td x-text="attendance.checkInTime"></td>
                                    <td x-text="attendance.checkOutTime"></td>
                                    <td x-text="attendance.workedTime"></td>
                                    <td x-text="attendance.differenceTime"></td>
                                    <td>
                                        <span x-text="attendance.status" :class="{
                                            'badge badge-green': attendance.status === 'Present',
                                            'badge badge-yellow': attendance.status === 'Late',
                                            'badge badge-red': attendance.status === 'Absent',
                                        }"></span>
                                    </td>
                                    <td>
                                        <div class="flex items-center gap-2">
                                            <button class="btn btn-sub-primary btn-icon !size-8" title="edit"><i class="ri-pencil-line"></i></button>
                                            <button class="btn btn-sub-red btn-icon !size-8" title="delete" @click="deletedAttendance = attendance.attendanceIDs" data-modal-target="deleteModal"><i class="ri-delete-bin-line"></i></button>
                                        </div>
                                    </td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                </div>
                <div class="grid grid-cols-12 gap-5 mt-5 items-center">
                    <div class="col-span-12 lg:col-span-6">
                        <p class="text-gray-500 dark:text-dark-500">Showing <b x-text="showingStart"></b> - <b x-text="showingEnd"></b> of <b x-text="attendances.length"></b> Results</p>
                    </div>
                    <div class="col-span-12 lg:col-span-6">
                        <div class="flex justify-start lg:justify-end pagination pagination-primary">
                            <button @click="prevPage()" :disabled="currentPage === 1" class="pagination-pre">
                                <i data-lucide="chevron-left" class="mr-1 ltr:inline-block rtl:hidden size-4"></i>
                                <i data-lucide="chevron-right" class="ml-1 ltr:hidden rtl:inline-block size-4"></i>
                                Prev
                            </button>
                            <template x-for="page in totalPages" :key="page">
                                <button @click="gotoPage(page)" :class="{ 'active': currentPage === page }" class="pagination-item">
                                    <span x-text="page"></span>
                                </button>
                            </template>
                            <button @click="nextPage()" :disabled="currentPage === totalPages" class="pagination-next">
                                Next
                                <i data-lucide="chevron-right" class="ml-1 ltr:inline-block rtl:hidden size-4"></i>
                                <i data-lucide="chevron-left" class="mr-1 ltr:hidden rtl:inline-block size-4"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--delete modal-->
        <div id="deleteModal" class="!hidden modal show">
            <div class="modal-wrap modal-xs modal-center">
                <div class="text-center modal-content p-7">
                    <div class="flex items-center justify-center mx-auto mb-4 text-red-500 rounded-full bg-red-500/10 size-14 backdrop-blur-xl">
                        <i data-lucide="trash-2" class="size-6"></i>
                    </div>
                    <h5 class="mb-4">Are you sure you want to delete this attendance ?</h5>
                    <div class="flex items-center justify-center gap-2">
                        <button class="btn btn-red" @click="deleteAttendance()" data-modal-close="deleteModal">Delete</button>
                        <button data-modal-close="deleteModal" class="btn link link-primary">Cancel</button>
                    </div>
                </div>
            </div>
        </div><!--end-->
    </div><!--end col-->
</div><!--end grid-->

</div>
{{> partials/footer }}
</div>



{{> partials/vendor-scripts }}

<script type="module" src="assets/js/apps/hospital/staff/attendance.init.js"></script>

<script type="module" src="assets/js/main.js"></script>

</body>
</html>